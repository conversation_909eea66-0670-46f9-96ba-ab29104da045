require('dotenv').config();
const mysql = require('mysql2/promise');

async function migrateGoogleModels() {
  console.log('🔄 Migrating Google models from gemini/ to google/ prefix...\n');

  // Create MySQL connection
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'root',
    password: process.env.MYSQL_PWD || 'Dmggg13031988***',
    database: 'justsimplechat_production'
  });

  try {
    // First, let's see what we have
    console.log('📋 Current Gemini models in database:');
    const [currentModels] = await connection.execute(
      `SELECT id, canonicalName, displayName 
       FROM AIModel 
       WHERE canonicalName LIKE 'gemini/%' 
       ORDER BY canonicalName`
    );

    currentModels.forEach(model => {
      console.log(`  - ${model.canonicalName} (${model.displayName})`);
    });

    console.log(`\nTotal: ${currentModels.length} models to migrate`);

    // Test Google API first
    console.log('\n🧪 Testing Google Generative AI API access...');
    const testResponse = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash', {
      headers: {
        'x-goog-api-key': process.env.GOOGLE_API_KEY
      }
    });

    if (!testResponse.ok) {
      throw new Error('Google API test failed - check API key');
    }
    console.log('✅ Google API access confirmed');

    // Now update the models
    console.log('\n🔄 Updating model prefixes...');
    
    for (const model of currentModels) {
      const newCanonicalName = model.canonicalName.replace('gemini/', 'google/');
      
      // Update the canonical name
      await connection.execute(
        `UPDATE AIModel 
         SET canonicalName = ?, 
             updatedAt = NOW() 
         WHERE id = ?`,
        [newCanonicalName, model.id]
      );
      
      console.log(`✅ Updated: ${model.canonicalName} → ${newCanonicalName}`);
    }

    // Verify the changes
    console.log('\n📋 Verifying migration:');
    const [updatedModels] = await connection.execute(
      `SELECT canonicalName, displayName 
       FROM AIModel 
       WHERE canonicalName LIKE 'google/%' 
       ORDER BY canonicalName`
    );

    console.log(`\nMigrated models (${updatedModels.length} total):`);
    updatedModels.forEach(model => {
      console.log(`  ✅ ${model.canonicalName} (${model.displayName})`);
    });

    // Check if any gemini/ models remain
    const [remainingGemini] = await connection.execute(
      `SELECT COUNT(*) as count FROM AIModel WHERE canonicalName LIKE 'gemini/%'`
    );

    if (remainingGemini[0].count > 0) {
      console.warn(`\n⚠️  Warning: ${remainingGemini[0].count} gemini/ models still remain`);
    } else {
      console.log('\n✅ All gemini/ models successfully migrated to google/');
    }

    // Update any related tables if needed
    console.log('\n🔄 Checking for related updates...');
    
    // Update ModelCapability if it exists
    try {
      await connection.execute(
        `UPDATE ModelCapability 
         SET modelId = REPLACE(modelId, 'gemini/', 'google/') 
         WHERE modelId LIKE 'gemini/%'`
      );
      console.log('✅ Updated ModelCapability table');
    } catch (e) {
      console.log('ℹ️  ModelCapability table not found or no updates needed');
    }

    // Update ConversationMessage if needed
    try {
      const [messageUpdates] = await connection.execute(
        `UPDATE ConversationMessage 
         SET model = REPLACE(model, 'gemini/', 'google/') 
         WHERE model LIKE 'gemini/%'`
      );
      console.log(`✅ Updated ${messageUpdates.affectedRows} conversation messages`);
    } catch (e) {
      console.log('ℹ️  No conversation messages needed updating');
    }

    console.log('\n🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
migrateGoogleModels().catch(console.error);