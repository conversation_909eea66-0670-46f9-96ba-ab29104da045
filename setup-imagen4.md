# Google Imagen 4 Setup Instructions

## 1. Enable Google Cloud APIs

```bash
# Set your project ID
export GCLOUD_PROJECT="your-project-id"

# Enable required APIs
gcloud services enable aiplatform.googleapis.com
gcloud services enable storage.googleapis.com
```

## 2. Set up Authentication

### Option A: Service Account (Recommended for Production)
```bash
# Create a service account
gcloud iam service-accounts create imagen4-service \
    --description="Service account for Imagen 4 image generation" \
    --display-name="Imagen 4 Service Account"

# Grant necessary permissions
gcloud projects add-iam-policy-binding $GCLOUD_PROJECT \
    --member="serviceAccount:imagen4-service@$GCLOUD_PROJECT.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

# Create and download key
gcloud iam service-accounts keys create imagen4-key.json \
    --iam-account=imagen4-service@$GCLOUD_PROJECT.iam.gserviceaccount.com

# Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/imagen4-key.json"
```

### Option B: User Credentials (for Development)
```bash
# Login and set application default credentials
gcloud auth application-default login

# Set project
gcloud config set project $GCLOUD_PROJECT
```

## 3. Environment Variables

Add to your `.env` file:
```env
GCLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/imagen4-key.json
```

## 4. Test Configuration

```bash
# Test if everything is working
node -e "
import { getImagen4Status } from './src/lib/ai/imagen4-generation.js';
console.log('Imagen 4 Status:', getImagen4Status());
"
```

## 5. Model Pricing

- **Imagen 4**: $0.04 USD per output image
- **Imagen 4 Ultra**: $0.06 USD per output image

## 6. Model Features

- **Better image quality** than DALL-E 3
- **Superior text rendering** in images
- **Optional digital watermarking**
- **Multiple aspect ratios**: 1:1, 9:16, 16:9, 4:3, 3:4
- **Safety filtering levels**
- **Google Cloud integration**

## 7. Usage in Code

```typescript
import { generateImage } from '@/lib/ai/image-generation';

const result = await generateImage({
  prompt: "A futuristic cityscape at dusk",
  model: 'imagen-4',
  aspectRatio: '16:9',
  quality: 'high',
  addWatermark: true
});
```

## Troubleshooting

1. **Permission Denied**: Check IAM roles and API enablement
2. **Project Not Found**: Verify GCLOUD_PROJECT environment variable
3. **Credentials Error**: Ensure GOOGLE_APPLICATION_CREDENTIALS path is correct
4. **Quota Exceeded**: Check your Google Cloud billing and quotas