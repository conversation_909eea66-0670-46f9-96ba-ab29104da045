#!/bin/bash
# Branch Check Helper - Shows current branch and provides guidance

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== JustSimpleChat Branch Status ===${NC}"
echo ""

# Get current branch
current_branch=$(git branch --show-current)
echo -e "Current branch: ${GREEN}$current_branch${NC}"
echo ""

# Provide guidance based on branch
if [ "$current_branch" = "main" ]; then
    echo -e "${RED}⚠️  WARNING: You are on the MAIN branch!${NC}"
    echo -e "${YELLOW}Main branch is for PRODUCTION deployments only.${NC}"
    echo ""
    echo "To switch to develop branch:"
    echo -e "  ${GREEN}git checkout develop${NC}"
    echo ""
    echo "Deployment commands:"
    echo -e "  Production: ${GREEN}./deploy-production-v3.sh${NC} (from main branch)"
elif [ "$current_branch" = "develop" ]; then
    echo -e "${GREEN}✓ You are on the develop branch${NC}"
    echo ""
    echo "Common commands:"
    echo -e "  Create feature: ${GREEN}git checkout -b feature/name${NC}"
    echo -e "  Deploy staging: ${GREEN}./deploy-staging-v3.sh${NC}"
    echo -e "  Update branch:  ${GREEN}git pull origin develop${NC}"
elif [[ "$current_branch" == feature/* ]] || [[ "$current_branch" == bugfix/* ]] || [[ "$current_branch" == hotfix/* ]]; then
    echo -e "${GREEN}✓ You are on a feature branch${NC}"
    echo ""
    echo "Common commands:"
    echo -e "  Deploy staging: ${GREEN}./deploy-staging-v3.sh --from-feature${NC}"
    echo -e "  Switch to dev:  ${GREEN}git checkout develop${NC}"
    echo -e "  Push changes:   ${GREEN}git push origin $current_branch${NC}"
elif [ "$current_branch" = "staging" ]; then
    echo -e "${YELLOW}⚠️  You are on the staging branch${NC}"
    echo "This branch is managed by deployment scripts."
    echo ""
    echo "To switch to develop:"
    echo -e "  ${GREEN}git checkout develop${NC}"
else
    echo -e "${YELLOW}⚠️  Unknown branch type${NC}"
    echo ""
    echo "Recommended: switch to develop"
    echo -e "  ${GREEN}git checkout develop${NC}"
fi

echo ""
echo -e "${BLUE}Branch Flow:${NC}"
echo "  feature/* → develop → staging → main"
echo "       ↓         ↓         ↓        ↓"
echo "     (dev)    (dev)   (staging) (production)"
echo ""
echo -e "See ${BLUE}/home/<USER>/deployments/BRANCH_STRATEGY.md${NC} for full details"