{"permissions": {"allow": ["Bash(pm2 restart:*)", "<PERSON><PERSON>(curl:*)", "mcp__perplexity-ask__deep_research", "Bash(find:*)", "Bash(git add:*)", "Bash(grep:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__perplexity-ask__search", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(diff:*)"], "deny": []}}