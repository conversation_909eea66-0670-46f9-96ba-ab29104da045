#!/usr/bin/env python3
"""
Download AIME 2025 Dataset
==========================

Downloads the actual AIME 2025 question/answer dataset from HuggingFace.
"""

from datasets import load_dataset
import pandas as pd

def download_aime_2025():
    """Download AIME 2025 questions and answers"""
    print("📊 Downloading AIME 2025 dataset from HuggingFace...")
    
    try:
        # Load both AIME 2025-I and 2025-II
        ds_i = load_dataset("opencompass/AIME2025", "AIME2025-I", split="test")
        ds_ii = load_dataset("opencompass/AIME2025", "AIME2025-II", split="test")
        
        # Convert to pandas DataFrames
        df_i = ds_i.to_pandas()
        df_ii = ds_ii.to_pandas()
        
        # Add exam identifier
        df_i['exam'] = 'AIME2025-I'
        df_ii['exam'] = 'AIME2025-II'
        
        # Add problem numbers
        df_i['problem_number'] = range(1, len(df_i) + 1)
        df_ii['problem_number'] = range(1, len(df_ii) + 1)
        
        # Combine both exams
        df_combined = pd.concat([df_i, df_ii], ignore_index=True)
        
        # Reorder columns
        df_combined = df_combined[['exam', 'problem_number', 'question', 'answer']]
        
        # Save to CSV
        output_file = "aime_2025_questions_answers.csv"
        df_combined.to_csv(output_file, index=False)
        
        print(f"✅ AIME 2025 dataset saved: {output_file}")
        print(f"   📝 {len(df_combined)} total problems")
        print(f"   📊 AIME 2025-I: {len(df_i)} problems")
        print(f"   📊 AIME 2025-II: {len(df_ii)} problems")
        
        # Show sample problems
        print(f"\n📋 Sample problems:")
        for i, row in df_combined.head(3).iterrows():
            print(f"   {row['exam']} Problem {row['problem_number']}: Answer = {row['answer']}")
            print(f"   Question: {row['question'][:100]}...")
            print()
        
        return df_combined
        
    except Exception as e:
        print(f"❌ Error downloading AIME 2025 dataset: {e}")
        return None

if __name__ == "__main__":
    df = download_aime_2025()
    if df is not None:
        print("🎉 AIME 2025 question/answer dataset ready for validation!")
    else:
        print("❌ Failed to download AIME 2025 dataset")