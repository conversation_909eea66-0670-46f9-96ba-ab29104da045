<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="alibaba-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="12" cy="12" r="11" fill="url(#alibaba-gradient)" opacity="0.1"/>
  <circle cx="12" cy="12" r="11" fill="none" stroke="url(#alibaba-gradient)" stroke-width="0.5" opacity="0.3"/>
  <title><PERSON>wen by <PERSON><PERSON><PERSON></title>
  <g transform="translate(2, 2) scale(0.83)">
    <!-- Qwen "Q" letterform with authentic purple branding -->
    <circle cx="12" cy="12" r="7" fill="none" stroke="url(#alibaba-gradient)" stroke-width="2.5"/>
    <circle cx="12" cy="12" r="4" fill="url(#alibaba-gradient)" opacity="0.3"/>

    <!-- "Q" tail extending from circle -->
    <path d="M16.5 16.5l2.5 2.5" stroke="url(#alibaba-gradient)" stroke-width="2.5" stroke-linecap="round"/>

    <!-- Inner design elements representing AI/language model -->
    <circle cx="10" cy="10" r="0.8" fill="url(#alibaba-gradient)"/>
    <circle cx="14" cy="10" r="0.8" fill="url(#alibaba-gradient)"/>
    <circle cx="12" cy="14" r="0.8" fill="url(#alibaba-gradient)"/>

    <!-- Subtle connection lines -->
    <path d="M10 10l4 0M12 10l0 4" stroke="url(#alibaba-gradient)" stroke-width="0.6" opacity="0.5"/>
  </g>
</svg>