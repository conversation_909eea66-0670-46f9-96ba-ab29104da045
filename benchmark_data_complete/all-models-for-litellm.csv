provider,model_id,display_name,api_base,api_key_env,notes
openai,gpt-4o,GPT-4o,https://api.openai.com/v1,OPENAI_API_KEY,
openai,gpt-4o-mini,GPT-4o Mini,https://api.openai.com/v1,OPENAI_API_KEY,
openai,gpt-4-turbo,GPT-4 Turbo,https://api.openai.com/v1,OPENAI_API_KEY,
openai,gpt-4-turbo-preview,GPT-4 Turbo Preview,https://api.openai.com/v1,OPENAI_API_KEY,
openai,gpt-4,GPT-4,https://api.openai.com/v1,OPENAI_API_KEY,
openai,gpt-3.5-turbo,GPT-3.5 Turbo,https://api.openai.com/v1,OPENAI_API_KEY,
openai,gpt-3.5-turbo-16k,GPT-3.5 Turbo 16K,https://api.openai.com/v1,OPENAI_API_KEY,
openai,o1,O1,https://api.openai.com/v1,OPENAI_API_KEY,Reasoning model
openai,o1-mini,O1 Mini,https://api.openai.com/v1,OPENAI_API_KEY,Reasoning model
openai,o1-preview,O1 Preview,https://api.openai.com/v1,OPENAI_API_KEY,Reasoning model
openai,o3-mini,O3 Mini,https://api.openai.com/v1,OPENAI_API_KEY,Reasoning model
anthropic,claude-3-opus-20240229,Claude 3 Opus,https://api.anthropic.com/v1,ANTHROPIC_API_KEY,
anthropic,claude-3-sonnet-20240229,Claude 3 Sonnet,https://api.anthropic.com/v1,ANTHROPIC_API_KEY,
anthropic,claude-3-haiku-20240307,Claude 3 Haiku,https://api.anthropic.com/v1,ANTHROPIC_API_KEY,
anthropic,claude-3-5-sonnet-20241022,Claude 3.5 Sonnet,https://api.anthropic.com/v1,ANTHROPIC_API_KEY,
anthropic,claude-3-5-haiku-20241022,Claude 3.5 Haiku,https://api.anthropic.com/v1,ANTHROPIC_API_KEY,
gemini,gemini-2.0-flash-exp,Gemini 2.0 Flash Experimental,https://generativelanguage.googleapis.com/v1beta,GOOGLE_API_KEY,
gemini,gemini-2.0-flash-thinking-exp,Gemini 2.0 Flash Thinking,https://generativelanguage.googleapis.com/v1beta,GOOGLE_API_KEY,
gemini,gemini-1.5-flash,Gemini 1.5 Flash,https://generativelanguage.googleapis.com/v1beta,GOOGLE_API_KEY,
gemini,gemini-1.5-flash-8b,Gemini 1.5 Flash 8B,https://generativelanguage.googleapis.com/v1beta,GOOGLE_API_KEY,
gemini,gemini-1.5-pro,Gemini 1.5 Pro,https://generativelanguage.googleapis.com/v1beta,GOOGLE_API_KEY,
gemini,gemini-1.0-pro,Gemini 1.0 Pro,https://generativelanguage.googleapis.com/v1beta,GOOGLE_API_KEY,
groq,llama-3.3-70b-versatile,Llama 3.3 70B Versatile,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.3-70b-specdec,Llama 3.3 70B SpecDec,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.1-70b-versatile,Llama 3.1 70B Versatile,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.1-8b-instant,Llama 3.1 8B Instant,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.2-1b-preview,Llama 3.2 1B Preview,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.2-3b-preview,Llama 3.2 3B Preview,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.2-11b-vision-preview,Llama 3.2 11B Vision,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama-3.2-90b-vision-preview,Llama 3.2 90B Vision,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,mixtral-8x7b-32768,Mixtral 8x7B,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,gemma2-9b-it,Gemma2 9B IT,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama3-groq-70b-8192-tool-use-preview,Llama3 70B Tool Use,https://api.groq.com/openai/v1,GROQ_API_KEY,
groq,llama3-groq-8b-8192-tool-use-preview,Llama3 8B Tool Use,https://api.groq.com/openai/v1,GROQ_API_KEY,
mistral,mistral-large-latest,Mistral Large,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,mistral-medium-latest,Mistral Medium,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,mistral-small-latest,Mistral Small,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,mistral-tiny-latest,Mistral Tiny,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,pixtral-large-latest,Pixtral Large,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,codestral-latest,Codestral,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,ministral-3b-latest,Ministral 3B,https://api.mistral.ai/v1,MISTRAL_API_KEY,
mistral,ministral-8b-latest,Ministral 8B,https://api.mistral.ai/v1,MISTRAL_API_KEY,
deepseek,deepseek-chat,DeepSeek Chat,https://api.deepseek.com/v1,DEEPSEEK_API_KEY,
deepseek,deepseek-reasoner,DeepSeek Reasoner,https://api.deepseek.com/v1,DEEPSEEK_API_KEY,
xai,grok-2-1212,Grok 2,https://api.x.ai/v1,XAI_API_KEY,
xai,grok-2-vision-1212,Grok 2 Vision,https://api.x.ai/v1,XAI_API_KEY,
xai,grok-3,Grok 3,https://api.x.ai/v1,XAI_API_KEY,
xai,grok-3-fast,Grok 3 Fast,https://api.x.ai/v1,XAI_API_KEY,
xai,grok-3-mini,Grok 3 Mini,https://api.x.ai/v1,XAI_API_KEY,
xai,grok-3-mini-fast,Grok 3 Mini Fast,https://api.x.ai/v1,XAI_API_KEY,
perplexity,llama-3.1-sonar-small-128k-online,Sonar Small Online,https://api.perplexity.ai,PERPLEXITY_API_KEY,
perplexity,llama-3.1-sonar-large-128k-online,Sonar Large Online,https://api.perplexity.ai,PERPLEXITY_API_KEY,
perplexity,llama-3.1-sonar-huge-128k-online,Sonar Huge Online,https://api.perplexity.ai,PERPLEXITY_API_KEY,
perplexity,llama-3.1-sonar-small-128k-chat,Sonar Small Chat,https://api.perplexity.ai,PERPLEXITY_API_KEY,
perplexity,llama-3.1-sonar-large-128k-chat,Sonar Large Chat,https://api.perplexity.ai,PERPLEXITY_API_KEY,
cohere,command-r-plus,Command R+,https://api.cohere.ai,COHERE_API_KEY,
cohere,command-r,Command R,https://api.cohere.ai,COHERE_API_KEY,
cohere,command,Command,https://api.cohere.ai,COHERE_API_KEY,
cohere,command-light,Command Light,https://api.cohere.ai,COHERE_API_KEY,
cohere,c4ai-aya-23-35b,Aya 23 35B,https://api.cohere.ai,COHERE_API_KEY,
cohere,c4ai-aya-23-8b,Aya 23 8B,https://api.cohere.ai,COHERE_API_KEY,
openrouter,mistralai/mistral-small-3.2-24b-instruct:free,Mistral Small 3.2 24B (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,meta-llama/llama-3.3-70b-instruct,Llama 3.3 70B Instruct,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,meta-llama/llama-3.2-3b-instruct:free,Llama 3.2 3B (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,microsoft/phi-3-medium-128k-instruct:free,Phi 3 Medium 128K (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,qwen/qwen-2-7b-instruct:free,Qwen 2 7B (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,google/gemini-flash-1.5,Gemini Flash 1.5,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,google/gemini-pro-1.5,Gemini Pro 1.5,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,openai/gpt-4o,GPT-4o (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,openai/gpt-4o-mini,GPT-4o Mini (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,anthropic/claude-3.5-sonnet,Claude 3.5 Sonnet (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,anthropic/claude-3-opus,Claude 3 Opus (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,anthropic/claude-3-haiku,Claude 3 Haiku (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,x-ai/grok-beta,Grok Beta (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,deepseek/deepseek-chat,DeepSeek Chat (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,mistralai/mistral-large,Mistral Large (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,cohere/command-r-plus,Command R+ (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,nvidia/llama-3.1-nemotron-70b-instruct,Llama 3.1 Nemotron 70B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,alibaba/qwen-2.5-coder-32b-instruct,Qwen 2.5 Coder 32B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
# Additional important OpenRouter models
openrouter,openai/o1,O1 (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Reasoning model
openrouter,openai/o1-mini,O1 Mini (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Reasoning model
openrouter,meta-llama/llama-3.1-405b-instruct,Llama 3.1 405B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,meta-llama/llama-3.1-70b-instruct,Llama 3.1 70B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,meta-llama/llama-3.1-8b-instruct,Llama 3.1 8B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,google/gemini-2.0-flash-exp:free,Gemini 2.0 Flash (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,google/gemini-2.0-flash-thinking-exp:free,Gemini 2.0 Thinking (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,deepseek/deepseek-r1,DeepSeek R1 (via OR),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Reasoning model
openrouter,deepseek/deepseek-r1-distill-llama-70b,DeepSeek R1 Distill 70B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,sao10k/l3.3-euryale-70b,Euryale 70B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
openrouter,liquid/lfm-40b:free,Liquid LFM 40B (free),https://openrouter.ai/api/v1,OPENROUTER_API_KEY,Free tier
openrouter,nousresearch/hermes-3-llama-3.1-405b,Hermes 3 405B,https://openrouter.ai/api/v1,OPENROUTER_API_KEY,
