model,status,response_time_ms,response_content,error_message,test_timestamp
"gpt-4-turbo","WORKING","938","Hello! How can I","","2025-06-25T21:23:43.384Z"
"gpt-4-turbo-preview","WORKING","1010","Hello! How can I","","2025-06-25T21:23:44.405Z"
"gpt-3.5-turbo","WORKING","516","Hello! How can I","","2025-06-25T21:23:44.924Z"
"gpt-3.5-turbo-0125","WORKING","948","Hello! How can I","","2025-06-25T21:23:45.873Z"
"o1-mini-2024-09-12","WORKING","990","No content","","2025-06-25T21:23:46.865Z"
"o1-preview-2024-09-12","WORKING","1587","No content","","2025-06-25T21:23:48.453Z"
"dall-e-3","FAILED","1334","","{""error"":{""message"":""litellm.BadRequestError: OpenAIException - You are not allowed to sample from this model. Received Model Group=dall-e-3\nAvailable Model Group Fallbacks=None"",""type"":""invalid_request_error"",""param"":null,""code"":""400""}}","2025-06-25T21:23:49.787Z"
"text-embedding-3-small","FAILED","1979","","{""error"":{""message"":""litellm.BadRequestError: OpenAIException - You are not allowed to sample from this model. Received Model Group=text-embedding-3-small\nAvailable Model Group Fallbacks=None"",""type"":""invalid_request_error"",""param"":null,""code"":""400""}}","2025-06-25T21:23:51.767Z"
"text-embedding-ada-002","FAILED","946","","{""error"":{""message"":""litellm.BadRequestError: OpenAIException - This is not a chat model and thus not supported in the v1/chat/completions endpoint. Did you mean to use v1/completions?. Received Model Group=text-embedding-ada-002\nAvailable Model Group Fallbacks=None"",""type"":""invalid_request_error"",""param"":""model"",""code"":""400""}}","2025-06-25T21:23:52.714Z"
"tts-1","FAILED","1532","","{""error"":{""message"":""litellm.BadRequestError: OpenAIException - This is not a chat model and thus not supported in the v1/chat/completions endpoint. Did you mean to use v1/completions?. Received Model Group=tts-1\nAvailable Model Group Fallbacks=None"",""type"":""invalid_request_error"",""param"":""model"",""code"":""400""}}","2025-06-25T21:23:54.246Z"
"gemini-1.5-pro-002","WORKING","6305","Hi there! How can","","2025-06-25T21:24:00.552Z"
"gemini-1.5-flash-002","WORKING","315","Hi there! How can","","2025-06-25T21:24:00.869Z"
"gemini-1.5-flash-8b-001","WORKING","287","Hello! How can I","","2025-06-25T21:24:01.157Z"
"gemini-2.0-flash-exp","WORKING","516","Hi there! How can","","2025-06-25T21:24:01.674Z"
"gemini-exp-1114","FAILED","104","","{""error"":{""message"":""litellm.NotFoundError: VertexAIException - {\n  \""error\"": {\n    \""code\"": 404,\n    \""message\"": \""models/gemini-exp-1114 is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\"",\n    \""status\"": \""NOT_FOUND\""\n  }\n}\n. Received Model Group=gemini-exp-1114\nAvailable Model Group Fallbacks=None"",""type"":null,""param"":null,""code"":""404""}}","2025-06-25T21:24:01.779Z"
"gemini-exp-1206","FAILED","1583","","{""error"":{""message"":""litellm.RateLimitError: litellm.RateLimitError: VertexAIException - {\n  \""error\"": {\n    \""code\"": 429,\n    \""message\"": \""You exceeded your current quota. Please migrate to Gemini 2.5 Pro Preview (models/gemini-2.5-pro-preview-03-25) for higher quota limits. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\"",\n    \""status\"": \""RESOURCE_EXHAUSTED\"",\n    \""details\"": [\n      {\n        \""@type\"": \""type.googleapis.com/google.rpc.QuotaFailure\"",\n        \""violations\"": [\n          {\n            \""quotaMetric\"": \""generativelanguage.googleapis.com/generate_requests_per_model_per_day\"",\n            \""quotaId\"": \""GenerateRequestsPerDayPerProjectPerModel\""\n          },\n          {\n            \""quotaMetric\"": \""generativelanguage.googleapis.com/generate_requests_per_model\"",\n            \""quotaId\"": \""GenerateRequestsPerMinutePerProjectPerModel\"",\n            \""quotaDimensions\"": {\n              \""location\"": \""global\"",\n              \""model\"": \""gemini-2.0-pro-exp\""\n            }\n          },\n          {\n            \""quotaMetric\"": \""generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count\"",\n            \""quotaId\"": \""GenerateContentPaidTierInputTokensPerModelPerMinute\"",\n            \""quotaDimensions\"": {\n              \""location\"": \""global\"",\n              \""model\"": \""gemini-2.0-pro-exp\""\n            }\n          }\n        ]\n      },\n      {\n        \""@type\"": \""type.googleapis.com/google.rpc.Help\"",\n        \""links\"": [\n          {\n            \""description\"": \""Learn more about Gemini API quotas\"",\n            \""url\"": \""https://ai.google.dev/gemini-api/docs/rate-limits\""\n          }\n        ]\n      },\n      {\n        \""@type\"": \""type.googleapis.com/google.rpc.RetryInfo\"",\n        \""retryDelay\"": \""58s\""\n      }\n    ]\n  }\n}\n. Received Model Group=gemini-exp-1206\nAvailable Model Group Fallbacks=None"",""type"":""throttling_error"",""param"":null,""code"":""429""}}","2025-06-25T21:24:03.362Z"
"text-embedding-004","FAILED","14","","{""error"":{""message"":""{'error': '/chat/completions: Invalid model name passed in model=text-embedding-004. Call `/v1/models` to view available models for your key.'}"",""type"":""None"",""param"":""None"",""code"":""400""}}","2025-06-25T21:24:03.377Z"
"text-multilingual-embedding-002","FAILED","13","","{""error"":{""message"":""{'error': '/chat/completions: Invalid model name passed in model=text-multilingual-embedding-002. Call `/v1/models` to view available models for your key.'}"",""type"":""None"",""param"":""None"",""code"":""400""}}","2025-06-25T21:24:03.391Z"
"gemma-2-27b-it","FAILED","1346","","{""error"":{""message"":""litellm.BadRequestError: Together_aiException - Unable to access model gemma-2-27b-it. Please visit https://api.together.ai/models to view the list of supported models.. Received Model Group=gemma-2-27b-it\nAvailable Model Group Fallbacks=None"",""type"":""invalid_request_error"",""param"":null,""code"":""400""}}","2025-06-25T21:24:04.738Z"
"learnlm-1.5-pro-experimental","FAILED","105","","{""error"":{""message"":""litellm.NotFoundError: VertexAIException - {\n  \""error\"": {\n    \""code\"": 404,\n    \""message\"": \""models/learnlm-1.5-pro-experimental is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\"",\n    \""status\"": \""NOT_FOUND\""\n  }\n}\n. Received Model Group=learnlm-1.5-pro-experimental\nAvailable Model Group Fallbacks=None"",""type"":null,""param"":null,""code"":""404""}}","2025-06-25T21:24:04.843Z"