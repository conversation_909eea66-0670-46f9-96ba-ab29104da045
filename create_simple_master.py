#!/usr/bin/env python3
"""
Create Simple Master Benchmark File
===================================

Creates a comprehensive benchmark file with all our collected data.
"""

import pandas as pd
from datetime import datetime

def create_simple_master():
    """Create simple master benchmark CSV"""
    print("🎯 Creating simple master benchmark CSV...")
    
    # Load individual datasets
    datasets = {}
    
    try:
        datasets['arena'] = pd.read_csv("lmsys_arena_2025-07-05.csv")
        print(f"   ✅ Arena: {len(datasets['arena'])} models")
    except:
        print("   ❌ Arena data not found")
    
    try:
        datasets['aime'] = pd.read_csv("aime_2025_results.csv")
        print(f"   ✅ AIME: {len(datasets['aime'])} models")
    except:
        print("   ❌ AIME data not found")
    
    try:
        datasets['gpqa'] = pd.read_csv("gpqa_diamond_2025.csv")
        print(f"   ✅ GPQA: {len(datasets['gpqa'])} models")
    except:
        print("   ❌ GPQA data not found")
    
    try:
        datasets['humaneval'] = pd.read_csv("humaneval_2025.csv")
        print(f"   ✅ HumanEval: {len(datasets['humaneval'])} models")
    except:
        print("   ❌ HumanEval data not found")
    
    try:
        datasets['math'] = pd.read_csv("math_gsm8k_2025.csv")
        print(f"   ✅ MATH/GSM8K: {len(datasets['math'])} models")
    except:
        print("   ❌ MATH/GSM8K data not found")
    
    try:
        datasets['mmlu'] = pd.read_csv("mmlu_2025.csv")
        print(f"   ✅ MMLU: {len(datasets['mmlu'])} models")
    except:
        print("   ❌ MMLU data not found")
    
    # Start with Arena data as base
    if 'arena' in datasets:
        master_df = datasets['arena'][['model', 'arena_score', 'votes', 'organization']].copy()
        master_df.rename(columns={'arena_score': 'arena_elo'}, inplace=True)
    else:
        master_df = pd.DataFrame(columns=['model', 'arena_elo', 'votes', 'organization'])
    
    # Merge other datasets on model name
    for dataset_name, df in datasets.items():
        if dataset_name == 'arena':
            continue
            
        # Rename columns to avoid conflicts
        df_merged = df.copy()
        for col in df_merged.columns:
            if col != 'model':
                df_merged.rename(columns={col: f"{dataset_name}_{col}"}, inplace=True)
        
        # Merge with master
        master_df = master_df.merge(df_merged, on='model', how='outer')
    
    # Clean up column names
    columns_to_keep = [
        'model', 'organization', 'arena_elo', 'votes',
        'aime_aime_2025_accuracy', 'aime_confidence',
        'gpqa_gpqa_diamond_accuracy', 'gpqa_confidence', 
        'humaneval_humaneval_pass1', 'humaneval_confidence',
        'math_math_accuracy', 'math_gsm8k_accuracy', 'math_confidence',
        'mmlu_mmlu_accuracy', 'mmlu_confidence'
    ]
    
    # Keep only columns that exist
    available_cols = [col for col in columns_to_keep if col in master_df.columns]
    master_df = master_df[available_cols]
    
    # Save master file
    timestamp = datetime.now().strftime("%Y-%m-%d")
    output_file = f"benchmark_master_{timestamp}.csv"
    master_df.to_csv(output_file, index=False, float_format='%.2f')
    
    print(f"✅ Master benchmark file saved: {output_file}")
    print(f"   📊 {len(master_df)} models")
    print(f"   📝 {len(master_df.columns)} metrics")
    print(f"   📋 Columns: {list(master_df.columns)}")
    
    return master_df

if __name__ == "__main__":
    master_df = create_simple_master()
    print(f"\n🎉 Ready for V6.8 validation with {len(master_df)} models!")