---
type: "always_apply"
---

# 🎯 Augment Rules and User Guidelines

**Purpose**: Comprehensive rules and guidelines for Augment Chat and Agent based on existing CLAUDE.md files across user, project, and directory levels.

## 🚨 CRITICAL DEPLOYMENT RULES

### Pre-Deployment Checklist
**ALWAYS execute before committing or deploying:**

1. **TypeScript validation**: `npm run typecheck` or `npx tsc --noEmit`
2. **Linting**: `npm run lint`
3. **Build verification**: `npm run build`
4. **Local testing**: `npm run dev` and test changed features
5. **Console error check**: Verify no errors in browser DevTools
6. **Security check**: Ensure no sensitive data is logged or exposed

**Only proceed with deployment if <PERSON><PERSON> checks pass!**

### Deployment Workflow (v3.2 Scripts)
**NEVER EDIT ON MAIN BRANCH** - Always work on develop or feature branches

```bash
cd /home/<USER>/deployments/dev/simplechat-ai

# ALWAYS check branch first
git branch --show-current

# Development work
git checkout develop
git pull origin develop
# Make changes, then:
git add . && git commit -m "feat: description" && git push

# Staging deployment (test first!)
export BASH_DEFAULT_TIMEOUT_MS=1200000  # CRITICAL: Set timeout
./deploy-staging-v3.sh

# Production deployment (after staging success)
git checkout main
git merge develop
git push origin main
export BASH_DEFAULT_TIMEOUT_MS=1200000
./deploy-production-v3.sh
```

## 🔧 Environment Configuration

### Development Environment
- **Working Directory**: `/home/<USER>/deployments/dev/simplechat-ai`
- **Branch**: `develop` (default working branch)
- **Database**: `justsimplechat_development`
- **PM2 Process**: `simplechat-dev`
- **Port**: 3005
- **Health Check**: `curl http://localhost:3005/api/health`

### Production Environment
- **Branch**: `main` only
- **Database**: `justsimplechat_production`
- **PM2 Process**: `simplechat-production`
- **Port**: 3006
- **Health Check**: `curl https://justsimple.chat/api/health`

## 🛠️ MCP (Model Context Protocol) Tools

### Essential MCP Servers
Always use MCP tools for latest documentation and research:

```bash
# Set timeout first
export BASH_DEFAULT_TIMEOUT_MS=600000

# Install core servers
claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest
claude mcp add sequential-thinking --scope user -- npx -y @modelcontextprotocol/server-sequential-thinking
```

### MCP Usage Guidelines
1. **Context7**: For library documentation (React 19, Next.js 15, etc.)
2. **Firecrawl**: For web scraping and content extraction
3. **Perplexity**: For current events and research
4. **Sequential Thinking**: For complex problem-solving

### Key MCP Functions
- **Firecrawl**: `firecrawl_scrape`, `firecrawl_map`, `firecrawl_crawl`, `firecrawl_search`
- **Context7**: `resolve-library-id`, `get-library-docs`
- **Perplexity**: `search`, `reason`, `deep_research`

## 📋 Development Workflow Preferences

### Git Workflow
- **Default branch**: `develop` for development work
- **Feature branches**: `update-feature-xyz` format
- **Branch checking**: Always run `git branch --show-current` before changes
- **Production**: Deploy only from `main` branch

### Database & Infrastructure
- **MySQL Host**: Use `127.0.0.1` (Docker container)
- **PM2 Logs**: Always use `--nostream` flag
- **Temporary Files**: Create in `/tmp/` to avoid cluttering workspace
- **Debugging**: Use command line tools directly, avoid scripts

### Code Quality Standards
- **React 19**: Prefer Actions over useEffect, use useOptimistic, Server Components
- **TypeScript**: Must pass type checking before deployment
- **ESLint**: Must pass linting before deployment
- **Testing**: Write and run tests for new features

## 🎯 Model Configuration Rules

### Database-First Architecture
- **Format**: Use `provider/model` format (e.g., `openai/gpt-4o`)
- **No transformation needed**: Database canonicalName maps directly to provider SDKs
- **Consistent prefixes**:
  - OpenAI: `openai/`
  - Anthropic: `anthropic/`
  - Google: `gemini/`
  - xAI: `xai/`
  - Together AI: `together_ai/`
  - OpenRouter: `openrouter/`
  - Alibaba: `alibaba/`

## 🚨 Important Usage Rules

### Documentation
1. **Always use MCP tools** for latest documentation - don't rely on outdated knowledge
2. **Verify before implementing** - check latest docs before coding
3. **Check capabilities** before assuming tool functionality

### Security & Best Practices
1. **No sensitive data** in logs or exposed endpoints
2. **Environment variables** for all API keys and secrets
3. **Health checks** before and after deployments
4. **Backup creation** before production deployments

### Deployment Safety
1. **Zero-downtime deployments** using PM2 cluster mode
2. **Automatic rollback** on failure
3. **Build validation** (lint, typecheck, tests)
4. **Comprehensive logging** to `/home/<USER>/deployments/logs/`

## 📚 Quick Reference Links

- **Server Infrastructure**: `~/CLAUDE.md`
- **Application Stack**: `~/deployments/CLAUDE.md`
- **Development Details**: `~/deployments/dev/simplechat-ai/CLAUDE.md`
- **Personal Preferences**: `~/.claude/CLAUDE.md`
- **Comprehensive Documentation**: `/DOCS/README.md`
- **System Architecture**: `./system_diagram.svg`

## 🔍 Troubleshooting Common Issues

1. **Timeout errors**: Always set `export BASH_DEFAULT_TIMEOUT_MS=1200000`
2. **Branch errors**: Check with `git branch --show-current`
3. **Uncommitted changes**: Commit first with `git add . && git commit -m "fix: ..."`
4. **TypeScript errors**: Fix with `npm run typecheck`
5. **ESLint errors**: Fix with `npm run lint`
6. **MCP tools missing**: Reinstall using installation commands above

---
**Last Updated**: July 12, 2025
**Based on**: User-level, Project-level, and Directory-level CLAUDE.md files
