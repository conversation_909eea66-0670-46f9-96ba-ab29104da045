#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

// Import the fresh comparison generator
const { generateModelComparison } = require('../src/lib/seo/ai-content-generator')
const { getAllModels } = require('../src/lib/models')

async function generatePopularComparisons() {
  console.log('🚀 Starting SEO comparison generation...')
  
  try {
    // Get all models
    const allModels = await getAllModels()
    console.log(`Found ${allModels.length} total models`)
    
    // Get top 20 models (same as your static generation logic)
    const topModels = allModels.slice(0, 20)
    console.log(`Generating comparisons for top ${topModels.length} models`)
    
    let generated = 0
    let cached = 0
    let errors = 0
    
    // Generate all combinations of top 20 models
    for (let i = 0; i < topModels.length; i++) {
      for (let j = i + 1; j < topModels.length; j++) {
        const model1 = topModels[i]
        const model2 = topModels[j]
        
        try {
          console.log(`\n[${generated + cached + errors + 1}] Processing: ${model1.name} vs ${model2.name}`)
          
          // Check if already exists
          const existing = await prisma.modelComparison.findFirst({
            where: {
              OR: [
                { model1Id: model1.id, model2Id: model2.id },
                { model1Id: model2.id, model2Id: model1.id }
              ]
            }
          })
          
          if (existing && !existing.isStale) {
            console.log(`  ✅ Already cached (${existing.generatedAt.toISOString()})`)
            cached++
            continue
          }
          
          // Generate comparison (this will automatically cache it)
          await generateModelComparison(model1, model2, true) // Force regenerate
          
          console.log(`  🎉 Generated and cached`)
          generated++
          
          // Rate limit: Wait 3 seconds between generations to respect API limits
          if (j < topModels.length - 1 || i < topModels.length - 2) {
            console.log(`  ⏳ Waiting 3s for rate limits...`)
            await new Promise(resolve => setTimeout(resolve, 3000))
          }
          
        } catch (error) {
          console.error(`  ❌ Error generating ${model1.name} vs ${model2.name}:`, error.message)
          errors++
        }
      }
    }
    
    console.log(`\n🏁 Generation complete!`)
    console.log(`  📊 Generated: ${generated}`)
    console.log(`  💾 Already cached: ${cached}`)
    console.log(`  ❌ Errors: ${errors}`)
    console.log(`  📈 Total combinations: ${generated + cached + errors}`)
    
    // Show cache stats
    const totalCached = await prisma.modelComparison.count()
    console.log(`  🗄️  Total comparisons in database: ${totalCached}`)
    
  } catch (error) {
    console.error('Fatal error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Add refresh stale comparisons function
async function refreshStaleComparisons() {
  console.log('🔄 Refreshing stale comparisons...')
  
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  
  const staleComparisons = await prisma.modelComparison.findMany({
    where: {
      OR: [
        { isStale: true },
        { updatedAt: { lt: oneWeekAgo } }
      ]
    },
    include: {
      model1: true,
      model2: true
    }
  })
  
  console.log(`Found ${staleComparisons.length} stale comparisons`)
  
  for (const comparison of staleComparisons) {
    try {
      console.log(`Refreshing: ${comparison.model1.displayName} vs ${comparison.model2.displayName}`)
      await generateModelComparison(comparison.model1, comparison.model2, true)
      await new Promise(resolve => setTimeout(resolve, 2000)) // Rate limit
    } catch (error) {
      console.error(`Failed to refresh ${comparison.id}:`, error.message)
    }
  }
}

// CLI interface
const command = process.argv[2]

if (command === 'refresh') {
  refreshStaleComparisons()
} else if (command === 'generate' || !command) {
  generatePopularComparisons()
} else {
  console.log('Usage:')
  console.log('  node scripts/generate-seo-comparisons.js generate   # Generate new comparisons')
  console.log('  node scripts/generate-seo-comparisons.js refresh    # Refresh stale comparisons')
}