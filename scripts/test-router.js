#!/usr/bin/env node

/**
 * Comprehensive Router Testing Tool
 * 
 * This script replicates the exact router logic from router.ts to test model selection
 * Usage: npm run test-router "your query here"
 * 
 * Features:
 * - Tests actual database mappings
 * - Shows fallback logic
 * - Compares with live router analysis
 * - Identifies optimization opportunities
 */

const { PrismaClient } = require('@prisma/client');
const path = require('path');

// Initialize Prisma with the correct schema location
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'mysql://root:Dmggg13031988***@127.0.0.1:3306/justsimplechat_production'
    }
  }
});

// Mock LiteLLM for router analysis
class MockLiteLLM {
  async chat(options) {
    // Simple mock analysis based on query patterns
    const query = options.messages.find(m => m.role === 'user')?.content || '';
    const analysis = this.analyzeQuery(query);
    
    return {
      choices: [{
        message: {
          content: JSON.stringify(analysis)
        }
      }]
    };
  }
  
  analyzeQuery(query) {
    const wordCount = query.split(' ').length;
    const lowerQuery = query.toLowerCase();
    
    // Real-time detection
    const realtimeKeywords = [
      'today', 'current', 'latest', 'now', 'happening',
      'news', 'price', 'weather', 'stock', 'market'
    ];
    const needsWebSearch = realtimeKeywords.some(keyword => lowerQuery.includes(keyword));
    
    // Category detection (matches router.ts logic)
    let category = 'general_chat';
    let complexity = 'standard';
    
    if (needsWebSearch) {
      category = 'current_events';
    } else if (/code|function|debug|implement|python|javascript/.test(lowerQuery)) {
      category = 'coding';
      complexity = wordCount > 50 ? 'complex' : 'standard';
    } else if (/write|story|poem|creative|essay|article/.test(lowerQuery)) {
      category = 'creative_writing';
    } else if (/math|calculate|solve|equation|derivative|integral/.test(lowerQuery)) {
      category = 'math';
    } else if (/analyze|explain|reason|why|how|compare|pros.*cons/.test(lowerQuery)) {
      category = 'reasoning';
    } else if (wordCount <= 5) {
      complexity = 'simple';  // Updated to match our router changes
    }
    
    // Cost tolerance logic (matches router.ts)
    let maxCostTolerance = 'medium';
    if (complexity === 'simple' || /hello|hi|thanks|yes|no/i.test(lowerQuery)) {
      maxCostTolerance = 'low';
    } else if (complexity === 'complex' || /analyze|research|complex|detailed/.test(lowerQuery)) {
      maxCostTolerance = 'high';
    }
    
    return {
      primary_category: category,
      complexity,
      specific_attributes: {},
      requirements: {
        needs_web_search: needsWebSearch,
        needs_vision: false,
        needs_large_context: false,
        needs_reasoning: category === 'reasoning' || category === 'coding',
        needs_citations: needsWebSearch,
        expected_output_tokens: wordCount * 10,
        latency_sensitivity: complexity === 'simple' ? 'high' : 'medium',
        max_cost_tolerance: maxCostTolerance
      },
      confidence: 0.85,
      reasoning: `Analyzed as ${category}/${complexity} with ${maxCostTolerance} cost tolerance`
    };
  }
}

// Replicate exact router logic
class RouterTester {
  constructor() {
    this.mockLLM = new MockLiteLLM();
  }
  
  async getModelMappings(category, complexity) {
    try {
      const mappings = await prisma.model_mappings.findMany({
        where: {
          category,
          enabled: true,
          OR: [
            { complexity_level: complexity },
            { complexity_level: 'all' }
          ]
        },
        orderBy: [
          { score: 'desc' },
          { avg_user_rating: 'desc' },
          { usage_count: 'desc' }
        ],
        take: 10
      });
      
      // Get model details
      const enrichedMappings = await Promise.all(mappings.map(async (mapping) => {
        const model = await prisma.aIModel.findUnique({
          where: { id: mapping.model_id },
          include: { provider: true }
        });
        
        return {
          ...mapping,
          model
        };
      }));
      
      return enrichedMappings.filter(m => m.model); // Only return mappings with valid models
    } catch (error) {
      console.error('Database error in getModelMappings:', error.message);
      return [];
    }
  }
  
  async getFallbackSelection(analysis) {
    try {
      // Get all active models (mimics getModelsForPlan)
      const models = await prisma.aIModel.findMany({
        where: { isActive: true },
        include: { provider: true },
        orderBy: { displayName: 'asc' }
      });
      
      if (models.length === 0) {
        throw new Error('No models available');
      }
      
      let eligibleModels = models;
      
      // Apply filters (mimics router.ts filtering logic)
      if (analysis.requirements.needs_vision) {
        eligibleModels = eligibleModels.filter(m => 
          m.metadata?.supportsVision || 
          m.capabilities?.includes('VISION')
        );
      }
      
      if (analysis.requirements.needs_web_search) {
        const webSearchModels = [
          'gpt-4o-search-preview', 'grok-3-mini', 'grok-2', 'grok-3', 
          'perplexity-sonar-pro', 'perplexity-sonar', 'perplexity-fast'
        ];
        const searchEligible = eligibleModels.filter(m => 
          webSearchModels.some(searchModel => m.canonicalName.includes(searchModel)) ||
          m.metadata?.supportsWebSearch
        );
        
        if (searchEligible.length > 0) {
          eligibleModels = searchEligible;
        }
      }
      
      if (eligibleModels.length === 0) {
        eligibleModels = models; // Fall back to all models
      }
      
      // Selection logic (exact copy from router.ts lines 1498-1540)
      let selected;
      
      if (analysis.complexity === 'simple') {
        // Pick cheapest/fastest - prefer known good budget models (UPDATED ORDER)
        const budgetModels = eligibleModels.filter(m => 
          m.canonicalName.includes('gemini-2.5-flash-lite') ||  // MOVED TO FIRST
          m.canonicalName.includes('gemini-1.5-flash-8b') ||
          m.canonicalName.includes('deepseek-chat') ||
          m.canonicalName.includes('gemini-1.5-flash')
        );
        
        if (budgetModels.length > 0) {
          // Sort budget models by cost
          selected = budgetModels.sort((a, b) => {
            const aCost = ((a.metadata?.inputCost || 0) + (a.metadata?.outputCost || 0)) / 2;
            const bCost = ((b.metadata?.inputCost || 0) + (b.metadata?.outputCost || 0)) / 2;
            return aCost - bCost;
          })[0];
        } else {
          // Fallback to cheapest available
          selected = eligibleModels.sort((a, b) => {
            const aCost = ((a.metadata?.inputCost || 0) + (a.metadata?.outputCost || 0)) / 2;
            const bCost = ((b.metadata?.inputCost || 0) + (b.metadata?.outputCost || 0)) / 2;
            return aCost - bCost;
          })[0];
        }
      } else if (analysis.complexity === 'complex') {
        // Pick a powerful model
        const powerfulModels = eligibleModels.filter(m => 
          m.displayName.includes('gpt-4o') ||
          m.displayName.includes('claude-3.5-sonnet') ||
          m.displayName.includes('gemini-2.0-pro') ||
          m.displayName.includes('deepseek-v3')
        );
        selected = powerfulModels[0] || eligibleModels[0];
      } else {
        // Pick a balanced mid-tier model
        const midTier = eligibleModels.filter(m => 
          m.displayName.includes('gemini-2.5-flash-lite') ||
          m.displayName.includes('gpt-4o-mini') ||
          m.displayName.includes('claude-3-haiku') ||
          m.displayName.includes('gemini-2.0-flash')
        );
        selected = midTier[0] || eligibleModels[0];
      }
      
      return {
        source: 'fallback',
        model: selected,
        reason: `Fallback: ${analysis.complexity} complexity, no mappings found`,
        eligibleCount: eligibleModels.length,
        budgetModelsFound: analysis.complexity === 'simple' ? 
          eligibleModels.filter(m => 
            m.canonicalName.includes('gemini-2.5-flash-lite') ||
            m.canonicalName.includes('gemini-1.5-flash-8b') ||
            m.canonicalName.includes('deepseek-chat') ||
            m.canonicalName.includes('gemini-1.5-flash')
          ).length : 0
      };
      
    } catch (error) {
      console.error('Fallback selection error:', error.message);
      return null;
    }
  }
  
  async testSelection(query, userPlan = 'free') {
    console.log('🎯 COMPREHENSIVE ROUTER TEST');
    console.log('='.repeat(60));
    console.log(`Query: "${query}"`);
    console.log(`User Plan: ${userPlan}`);
    console.log();
    
    // Step 1: Analyze query (mimics router analysis)
    const analysis = this.mockLLM.analyzeQuery(query);
    
    console.log('📊 QUERY ANALYSIS:');
    console.log(`  Category: ${analysis.primary_category}`);
    console.log(`  Complexity: ${analysis.complexity}`);
    console.log(`  Cost Tolerance: ${analysis.requirements.max_cost_tolerance}`);
    console.log(`  Needs Web Search: ${analysis.requirements.needs_web_search}`);
    console.log(`  Latency Sensitivity: ${analysis.requirements.latency_sensitivity}`);
    console.log(`  Confidence: ${analysis.confidence}`);
    console.log(`  Reasoning: ${analysis.reasoning}`);
    console.log();
    
    // Step 2: Check database mappings
    console.log('🗃️  DATABASE MAPPING CHECK:');
    console.log(`  Looking for: category='${analysis.primary_category}', complexity='${analysis.complexity}'`);
    
    const mappings = await this.getModelMappings(analysis.primary_category, analysis.complexity);
    
    if (mappings.length > 0) {
      console.log(`  ✅ Found ${mappings.length} mappings!`);
      console.log();
      console.log('  Top 5 mappings:');
      mappings.slice(0, 5).forEach((mapping, i) => {
        const model = mapping.model;
        const costCategory = model.metadata?.costCategory || 'Unknown';
        const chatScore = model.metadata?.taskScores?.cha || 'N/A';
        console.log(`    ${i + 1}. ${model.canonicalName}`);
        console.log(`       Display: ${model.displayName}`);
        console.log(`       Score: ${mapping.score}, Cost: ${costCategory}, Chat: ${chatScore}`);
        console.log(`       Usage: ${mapping.usage_count}, Success Rate: ${mapping.success_count}/${mapping.success_count + mapping.failure_count}`);
      });
      
      console.log();
      console.log('🏆 MAPPING SELECTION RESULT:');
      const topMapping = mappings[0];
      console.log(`  Selected: ${topMapping.model.canonicalName}`);
      console.log(`  Reason: Top-scored mapping (${topMapping.score})`);
      console.log(`  Cost Category: ${topMapping.model.metadata?.costCategory}`);
      console.log(`  Chat Score: ${topMapping.model.metadata?.taskScores?.cha || 'N/A'}`);
      
    } else {
      console.log('  ❌ NO MAPPINGS FOUND! Will use fallback logic...');
      console.log();
      
      // Step 3: Fallback selection (exact router.ts logic)
      console.log('🔄 FALLBACK SELECTION:');
      const fallback = await this.getFallbackSelection(analysis);
      
      if (fallback) {
        console.log(`  Source: ${fallback.source}`);
        console.log(`  Eligible Models: ${fallback.eligibleCount}`);
        if (fallback.budgetModelsFound > 0) {
          console.log(`  Budget Models Found: ${fallback.budgetModelsFound}`);
        }
        console.log();
        console.log('🏆 FALLBACK SELECTION RESULT:');
        console.log(`  Selected: ${fallback.model.canonicalName}`);
        console.log(`  Display: ${fallback.model.displayName}`);
        console.log(`  Reason: ${fallback.reason}`);
        console.log(`  Cost Category: ${fallback.model.metadata?.costCategory || 'Unknown'}`);
        console.log(`  Chat Score: ${fallback.model.metadata?.taskScores?.cha || 'N/A'}`);
        
        const inputCost = fallback.model.metadata?.inputCost || 0;
        const outputCost = fallback.model.metadata?.outputCost || 0;
        console.log(`  Pricing: $${inputCost}/$${outputCost} per 1M tokens`);
      }
    }
    
    console.log();
    console.log('🔍 DEBUGGING INFO:');
    
    // Check what complexity levels are available for this category
    try {
      const complexityStats = await prisma.model_mappings.groupBy({
        by: ['complexity_level'],
        where: {
          category: analysis.primary_category,
          enabled: true
        },
        _count: true
      });
      
      console.log(`  Available complexity levels for '${analysis.primary_category}':`);
      complexityStats.forEach(stat => {
        const isMatch = stat.complexity_level === analysis.complexity;
        console.log(`    ${isMatch ? '✅' : '  '} ${stat.complexity_level}: ${stat._count} mappings`);
      });
      
      if (!complexityStats.some(s => s.complexity_level === analysis.complexity)) {
        console.log(`  ⚠️  No mappings for complexity '${analysis.complexity}' - this triggers fallback!`);
      }
      
    } catch (error) {
      console.log(`  Error checking complexity levels: ${error.message}`);
    }
    
    // Show router vs database complexity mapping issue
    console.log();
    console.log('💡 OPTIMIZATION RECOMMENDATIONS:');
    
    if (mappings.length === 0) {
      console.log('  1. Add model mappings for missing complexity levels');
      console.log('  2. Consider mapping complexity level alignment');
      console.log('  3. Review fallback model preferences');
    } else {
      console.log('  1. Mappings are working correctly');
      console.log('  2. Consider tuning mapping scores for better selection');
    }
  }
}

// CLI interface
async function main() {
  const query = process.argv[2];
  const userPlan = process.argv[3] || 'free';
  
  if (!query) {
    console.log('Usage: npm run test-router "your query here" [user_plan]');
    console.log('');
    console.log('Examples:');
    console.log('  npm run test-router "Hello"');
    console.log('  npm run test-router "who would win for general chat" free');
    console.log('  npm run test-router "debug this Python function" pro');
    console.log('  npm run test-router "what is the weather today" enterprise');
    process.exit(1);
  }
  
  const tester = new RouterTester();
  
  try {
    await tester.testSelection(query, userPlan);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Handle CLI execution
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { RouterTester };