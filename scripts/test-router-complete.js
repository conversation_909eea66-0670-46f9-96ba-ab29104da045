#!/usr/bin/env node

/**
 * COMPLETE Router Testing Tool - Matches router.ts EXACTLY
 * 
 * This replicates the EXACT logic from router.ts including:
 * - <PERSON> Sampling with Beta distribution
 * - Cost penalty calculations  
 * - Contextual scoring
 * - Latency scoring
 * - Task-specific scores
 * - selectBestModelForSimpleQuery logic
 * - All filtering and validation
 */

const { PrismaClient } = require('@prisma/client');

// Initialize Prisma correctly
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'mysql://root:Dmggg13031988***@127.0.0.1:3306/justsimplechat_production'
    }
  }
});

class CompleteRouterTester {
  constructor() {
    this.isDebugEnabled = true;
  }

  // EXACT COPY: Sample from Beta distribution for Thompson Sampling (lines 1564-1576)
  sampleBeta(alpha, beta) {
    // Using <PERSON><PERSON><PERSON>'s method for Beta sampling
    let u, v, x, y;
    
    do {
      u = Math.random();
      v = Math.random();
      x = Math.pow(u, 1 / alpha);
      y = Math.pow(v, 1 / beta);
    } while (x + y > 1);
    
    return x / (x + y);
  }

  // EXACT COPY: Get cost multiplier based on user plan (lines 958-976)
  getCostMultiplier(userPlan) {
    switch (userPlan.toLowerCase()) {
      case 'free':
      case 'trial':
        return 0.5; // Heavy cost penalty for free users
      case 'basic':
      case 'starter':
        return 0.7; // Moderate cost penalty
      case 'pro':
      case 'premium':
        return 0.9; // Light cost penalty
      case 'enterprise':
      case 'unlimited':
        return 1.0; // No cost penalty
      default:
        return 0.8; // Default moderate penalty
    }
  }

  // EXACT COPY: Get task-specific performance score (lines 1030-1070)
  getTaskSpecificScore(model, category) {
    if (!model?.metadata?.taskScores) {
      return 0; // No task scores available
    }
    
    const taskScores = model.metadata.taskScores;
    
    // Map analysis categories to task score keys
    const categoryMapping = {
      'coding': 'cod',
      'debugging': 'cod',
      'creative_writing': 'cre',
      'reasoning': 'rea',
      'math': 'mat',
      'analysis': 'ana',
      'data_analysis': 'ana',
      'translation': 'lng',
      'general_chat': 'cha',
      'question_answering': 'cha',
      'summarization': 'ana',
      'technical_writing': 'cre',
      'academic_writing': 'cre',
      'business_writing': 'cre',
      'scientific': 'rea',
      'philosophical': 'rea',
      'image_analysis': 'vis',
      'multimodal': 'vis',
      'current_events': 'ana',
      'tutorial': 'cha',
      'brainstorming': 'cre',
      'role_play': 'cha'
    };
    
    const taskKey = categoryMapping[category];
    if (!taskKey || !taskScores[taskKey]) {
      // Fallback to general chat score if no specific mapping
      return taskScores.cha || 0;
    }
    
    return taskScores[taskKey] || 0;
  }

  // EXACT COPY: Calculate contextual score (lines 981-1025)
  calculateContextualScore(mapping, analysis, model, input) {
    let score = 0.3; // Lower base score to make room for task-specific scoring
    
    // TASK-SPECIFIC SCORING (40% of contextual score)
    const taskScore = this.getTaskSpecificScore(model, analysis.primary_category);
    if (taskScore > 0) {
      score += (taskScore / 10) * 0.4; // Convert 0-10 scale to 0-0.4 contribution
    }
    
    // Check if model has required capabilities (30% of contextual score)
    if (analysis.requirements.needs_vision && model?.capabilities?.includes('vision')) {
      score += 0.3;
    }
    
    if (analysis.requirements.needs_large_context && model?.contextLength > 100000) {
      score += 0.2;
    }
    
    if (analysis.requirements.needs_function_calling && model?.capabilities?.includes('function_calling')) {
      score += 0.2;
    }
    
    // Check specific attributes match (20% of contextual score)
    if (mapping.specific_attributes) {
      const attrs = analysis.specific_attributes;
      if (attrs?.language && mapping.specific_attributes[attrs.language]) {
        score += 0.15;
      }
      if (attrs?.framework && mapping.specific_attributes[attrs.framework]) {
        score += 0.1;
      }
    }
    
    // Conversation length considerations (10% of contextual score)
    if (input.conversationLength > 10 && model?.contextLength > 50000) {
      score += 0.1; // Bonus for long conversations with large context models
    }
    
    return Math.min(score, 1.0);
  }

  // EXACT COPY: Calculate cost penalty (lines 1075-1205)
  async calculateCostPenalty(model, costMultiplier, maxCostTolerance = 'medium', requestId, complexity) {
    if (!model?.metadata) {
      console.warn('Model missing metadata', { modelId: model?.id, canonicalName: model?.canonicalName });
      return 1.0;
    }
    
    // Check multiple locations for pricing data - prioritize inputCost/outputCost as more accurate
    const inputCost = model.metadata?.inputCost || model.inputCost || model.pricing?.input || 0;
    const outputCost = model.metadata?.outputCost || model.outputCost || model.pricing?.output || 0;
    const avgCost = (inputCost + outputCost) / 2;
    
    // Check for different model categories requiring different penalties
    const costCategory = model.metadata?.costCategory;
    const isFrontierModel = costCategory === 'FRONTIER' || avgCost > 10.0;
    const isReasoningModel = costCategory === 'REASONING';
    
    // Define cost thresholds (per 1M tokens)
    const lowCost = 0.5;    // $0.50
    const mediumCost = 2.0; // $2.00  
    const highCost = 10.0;  // $10.00
    const frontierCost = 30.0; // $30.00+
    
    let penalty = 1.0;
    let penaltyReason = 'no_penalty';
    
    // Apply cost penalties based on model category and user tolerance
    if (costCategory === 'FREE') {
      // FREE models: Always preferred for low/medium cost tolerance
      if (maxCostTolerance === 'low') {
        penalty = 2.0; // 100% bonus for simple queries - strong preference
        penaltyReason = 'free_model_strong_bonus';
      } else if (maxCostTolerance === 'medium') {
        penalty = 1.3; // 30% bonus for medium tolerance
        penaltyReason = 'free_model_medium_bonus';
      } else {
        penalty = 1.0; // No penalty for high tolerance
        penaltyReason = 'free_model_no_penalty';
      }
    } else if (isFrontierModel) {
      // FRONTIER models: Heavy penalties except for complex tasks with high tolerance
      if (maxCostTolerance === 'high') {
        penalty = 0.6; // Still substantial penalty even with high tolerance
        penaltyReason = 'frontier_high_tolerance';
      } else if (maxCostTolerance === 'medium') {
        penalty = 0.1; // Heavy penalty for medium tolerance
        penaltyReason = 'frontier_medium_tolerance';
      } else {
        penalty = 0.02; // Very heavy penalty for low tolerance
        penaltyReason = 'frontier_low_tolerance';
      }
    } else if (isReasoningModel) {
      // REASONING models: Complexity-aware penalties
      if (complexity === 'complex') {
        // Complex tasks: reasoning models are well-suited
        penalty = maxCostTolerance === 'high' ? 1.1 : (maxCostTolerance === 'medium' ? 0.8 : 0.4);
        penaltyReason = `reasoning_complex_${maxCostTolerance || 'medium'}`;
      } else if (complexity === 'difficult') {
        // Difficult tasks: reasoning models are good but not essential
        penalty = maxCostTolerance === 'high' ? 0.9 : (maxCostTolerance === 'medium' ? 0.5 : 0.2);
        penaltyReason = `reasoning_difficult_${maxCostTolerance || 'medium'}`;
      } else if (complexity === 'standard') {
        // Standard tasks: reasoning models are overkill but acceptable for high tolerance
        penalty = maxCostTolerance === 'high' ? 0.6 : (maxCostTolerance === 'medium' ? 0.15 : 0.05);
        penaltyReason = `reasoning_standard_${maxCostTolerance || 'medium'}`;
      } else {
        // Simple tasks: reasoning models are wasteful
        penalty = maxCostTolerance === 'high' ? 0.3 : 0.02;
        penaltyReason = `reasoning_simple_${maxCostTolerance || 'low'}`;
      }
    } else if (avgCost > highCost) {
      penalty = maxCostTolerance === 'high' ? 0.7 : 0.1; // Stronger penalties
      penaltyReason = 'high_cost';
    } else if (avgCost > mediumCost) {
      penalty = maxCostTolerance === 'low' ? 0.1 : 0.5; // Much more aggressive for low tolerance
      penaltyReason = 'medium_cost';
    } else if (avgCost > lowCost) {
      penalty = maxCostTolerance === 'low' ? 0.7 : 0.95; // Penalize even standard cost models for low tolerance
      penaltyReason = 'low_cost';
    }
    
    const finalPenalty = penalty * costMultiplier;
    
    if (this.isDebugEnabled) {
      console.log(`    Cost Penalty: ${model.canonicalName}`);
      console.log(`      Input/Output: $${inputCost}/$${outputCost}, Avg: $${avgCost.toFixed(3)}`);
      console.log(`      Category: ${costCategory}, Tolerance: ${maxCostTolerance}`);
      console.log(`      Penalty: ${penalty.toFixed(3)} × ${costMultiplier} = ${finalPenalty.toFixed(3)} (${penaltyReason})`);
    }
    
    return finalPenalty;
  }

  // EXACT COPY: Calculate latency score (lines 1210-1292)
  calculateLatencyScore(model, latencySensitivity) {
    let latencyScore = 0.5; // Default neutral score
    
    // Use real speed category data from metadata if available
    const speedCategory = model?.metadata?.speed;
    if (speedCategory) {
      switch (speedCategory) {
        case 'ultra-fast':
          latencyScore = 1.0;  // Flash Lite models - excellent speed
          break;
        case 'very-fast':
          latencyScore = 0.9;  // Flash 8B models - very good speed
          break;
        case 'fast':
          latencyScore = 0.8;  // Regular Flash models - good speed
          break;
        case 'medium':
          latencyScore = 0.6;  // Thinking models - decent speed
          break;
        case 'slow':
          latencyScore = 0.3;  // Poor speed
          break;
        default:
          latencyScore = 0.5;  // Unknown speed
      }
    } 
    // Use numerical speed score if available (0-100 scale)
    else if (model?.metadata?.speedScore) {
      const speedScore = parseFloat(model.metadata.speedScore);
      latencyScore = speedScore / 100; // Convert 0-100 to 0-1 scale
    }
    
    // Adjust based on user's latency sensitivity
    switch (latencySensitivity) {
      case 'high':
        // High sensitivity: full weight to speed, penalty for slow models
        return latencyScore < 0.5 ? latencyScore * 0.5 : latencyScore;
      case 'medium':
        // Medium sensitivity: blend with neutral
        return 0.3 + (latencyScore * 0.7); // 30% neutral + 70% speed score
      case 'low':
      default:
        // Low sensitivity: mostly neutral with slight speed preference
        return 0.7 + (latencyScore * 0.3); // 70% neutral + 30% speed score
    }
  }

  // EXACT COPY: selectBestModelForSimpleQuery (lines 803-885)
  selectBestModelForSimpleQuery(scoredMappings, analysis) {
    // Define ideal models for simple queries (fast + cheap + reliable)
    const idealForSimple = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Perfect middle ground
      'google/gemini-2.0-flash-exp',                // Fast and capable
      'deepseek/deepseek-chat',                     // Fast and cheap
      'anthropic/claude-3.5-haiku',                 // Fast Anthropic
      'openai/gpt-4o-mini',                         // Fast OpenAI (not O4!)
    ];
    
    // First: Check if any ideal models are available
    const idealMatches = scoredMappings.filter(s => 
      idealForSimple.includes(s.model?.canonicalName) && 
      s.finalScore > 10
    );
    
    if (idealMatches.length > 0) {
      // Prefer Gemini 2.5 Flash Lite for simple queries (great UX)
      const geminiLite = idealMatches.find(m => 
        m.model?.canonicalName === 'google/gemini-2.5-flash-lite-preview-06-17'
      );
      
      if (geminiLite) {
        console.log(`    ✅ Selected ideal model: ${geminiLite.model?.canonicalName} (Gemini 2.5 Flash Lite preferred)`);
        return geminiLite;
      }
      
      // Otherwise pick randomly from ideal matches for variety
      const selected = idealMatches[Math.floor(Math.random() * idealMatches.length)];
      console.log(`    ✅ Selected ideal model: ${selected.model?.canonicalName} (random from ${idealMatches.length} ideal matches)`);
      return selected;
    }
    
    // Second: Exclude expensive models and pick from remaining
    const excludedCategories = ['REASONING', 'FRONTIER'];
    const affordableOptions = scoredMappings.filter(s => 
      !excludedCategories.includes(s.model?.metadata?.costCategory) &&
      s.finalScore > 10
    );
    
    if (affordableOptions.length > 0) {
      // Prefer FREE models, but allow STANDARD if they're much better
      const freeModels = affordableOptions.filter(s => s.model?.metadata?.costCategory === 'FREE');
      const standardModels = affordableOptions.filter(s => s.model?.metadata?.costCategory === 'STANDARD');
      
      let selected;
      if (freeModels.length > 0 && (standardModels.length === 0 || freeModels[0].finalScore >= standardModels[0].finalScore * 0.8)) {
        // Use FREE model if it's competitive
        selected = freeModels[Math.floor(Math.random() * Math.min(3, freeModels.length))];
        console.log(`    ✅ Selected FREE model: ${selected.model?.canonicalName}`);
      } else if (standardModels.length > 0) {
        // Use STANDARD model if significantly better
        selected = standardModels[0];
        console.log(`    ✅ Selected STANDARD model: ${selected.model?.canonicalName}`);
      } else {
        selected = affordableOptions[0];
        console.log(`    ✅ Selected affordable model: ${selected.model?.canonicalName}`);
      }
      
      return selected;
    }
    
    // Last resort: use original top choice but log warning
    console.log(`    ⚠️  No ideal models found for simple query, using top scorer: ${scoredMappings[0].model?.canonicalName}`);
    return scoredMappings[0];
  }

  // Query analysis (matches router prompt logic)
  analyzeQuery(query, conversationLength = 0, hasCode = false, webSearchEnabled = false) {
    const wordCount = query.split(' ').length;
    const lowerQuery = query.toLowerCase();
    
    // Real-time detection (exact keywords from router.ts)
    const realtimeKeywords = [
      'today', 'current', 'latest', 'now', 'happening',
      'news', 'price', 'weather', 'stock market', 'recent'
    ];
    const needsWebSearch = realtimeKeywords.some(keyword => lowerQuery.includes(keyword)) || webSearchEnabled;
    
    // Category detection (matches router.ts examples)
    let category = 'general_chat';
    let complexity = 'standard';
    
    if (needsWebSearch) {
      category = 'current_events';
    } else if (hasCode || /code|function|debug|implement|python|javascript/.test(lowerQuery)) {
      category = 'coding';
      complexity = wordCount > 50 ? 'complex' : 'standard';
    } else if (/write|story|poem|creative|essay|article/.test(lowerQuery)) {
      category = 'creative_writing';
    } else if (/math|calculate|solve|equation|derivative|integral/.test(lowerQuery)) {
      category = 'math';
    } else if (/analyze|explain|reason|why|how|compare|pros.*cons/.test(lowerQuery)) {
      category = 'reasoning';
    } else if (wordCount <= 5) {
      complexity = 'simple';  // Updated to match our changes
    }
    
    // Cost tolerance logic (exact from router.ts examples)
    let maxCostTolerance = 'medium';
    if (complexity === 'simple' || /hello|hi|thanks|yes|no/i.test(lowerQuery)) {
      maxCostTolerance = 'low';
    } else if (complexity === 'complex' || /analyze.*document|research|multi-step|advanced/.test(lowerQuery)) {
      maxCostTolerance = 'high';
    }
    
    return {
      primary_category: category,
      complexity,
      specific_attributes: {},
      requirements: {
        needs_web_search: needsWebSearch,
        needs_vision: false,
        needs_large_context: conversationLength > 10,
        needs_reasoning: category === 'reasoning' || category === 'coding',
        needs_citations: needsWebSearch,
        expected_output_tokens: wordCount * 10,
        latency_sensitivity: complexity === 'simple' ? 'high' : 'medium',
        max_cost_tolerance: maxCostTolerance
      },
      confidence: 0.85,
      reasoning: `${category}/${complexity} with ${maxCostTolerance} cost tolerance`
    };
  }

  // Get model mappings (exact database query from router.ts)
  async getModelMappings(category, complexity) {
    try {
      const mappings = await prisma.model_mappings.findMany({
        where: {
          category,
          enabled: true,
          OR: [
            { complexity_level: complexity },
            { complexity_level: 'all' }
          ]
        },
        orderBy: [
          { score: 'desc' },
          { avg_user_rating: 'desc' },
          { usage_count: 'desc' }
        ]
      });
      
      return mappings;
    } catch (error) {
      console.error('Failed to get model mappings:', error.message);
      return [];
    }
  }

  // Get model details (exact from router.ts)
  async getModelDetails(modelId) {
    try {
      const model = await prisma.aIModel.findFirst({
        where: { id: modelId },
        include: { provider: true }
      });
      return model;
    } catch (error) {
      console.warn('Failed to get model details:', { modelId, error: error.message });
      return null;
    }
  }

  // COMPLETE Thompson Sampling selection (exact copy from router.ts lines 651-798)
  async selectModelWithThompsonSampling(mappings, analysis, input, requestId) {
    console.log(`  🧮 Thompson Sampling with ${mappings.length} mappings`);
    
    // CRITICAL: Filter out models without valid scoring data
    const validMappings = mappings.filter(mapping => {
      const hasValidScore = mapping.score != null && mapping.score > 0;
      if (!hasValidScore) {
        console.log(`    ❌ Filtered out ${mapping.model_id}: invalid score (${mapping.score})`);
      }
      return hasValidScore;
    });
    
    if (validMappings.length === 0) {
      throw new Error(`No valid model mappings with scoring data found for category: ${analysis.primary_category}, complexity: ${analysis.complexity}`);
    }
    
    console.log(`  ✅ ${validMappings.length} valid mappings (filtered out ${mappings.length - validMappings.length})`);
    
    // Get user plan cost multiplier
    const costMultiplier = this.getCostMultiplier(input.userPlan);
    console.log(`  💰 Cost multiplier for '${input.userPlan}' plan: ${costMultiplier}`);
    
    // For each VALID model, calculate contextual score
    const scoredMappings = await Promise.all(validMappings.map(async mapping => {
      const alpha = mapping.success_count + 1; // +1 for Laplace smoothing
      const beta = mapping.failure_count + 1;
      
      // Thompson Sampling: sample from Beta(alpha, beta)
      const thompsonSample = this.sampleBeta(alpha, beta);
      
      // Base scores
      const baseScore = mapping.score / 100; // Normalize to 0-1
      const ratingBoost = mapping.avg_user_rating ? mapping.avg_user_rating / 5 : 0.5;
      
      // Get model details for contextual scoring
      const model = await this.getModelDetails(mapping.model_id);
      
      if (!model) {
        console.log(`    ❌ Skipping mapping: model ${mapping.model_id} not found`);
        return null;
      }
      
      // Contextual adjustments
      const contextualScore = this.calculateContextualScore(mapping, analysis, model, input);
      
      // Cost penalty based on user plan and complexity
      const costPenalty = await this.calculateCostPenalty(model, costMultiplier, analysis.requirements.max_cost_tolerance || 'medium', requestId, analysis.complexity);
      
      // Latency bonus/penalty
      const latencyScore = this.calculateLatencyScore(model, analysis.requirements.latency_sensitivity);
      
      // Final weighted combination (EXACT from router.ts)
      const finalScore = (
        thompsonSample * 0.3 +      // 30% Thompson sampling (exploration)
        baseScore * 0.25 +          // 25% manual curation
        ratingBoost * 0.15 +        // 15% user ratings
        contextualScore * 0.2 +     // 20% contextual fit
        latencyScore * 0.1          // 10% latency considerations
      ) * costPenalty; // Apply cost penalty as multiplier
      
      return {
        mapping,
        model,
        finalScore,
        thompsonSample,
        contextualScore,
        costPenalty,
        latencyScore,
        breakdown: {
          thompson: thompsonSample * 0.3,
          base: baseScore * 0.25,
          rating: ratingBoost * 0.15,
          contextual: contextualScore * 0.2,
          latency: latencyScore * 0.1,
          costMultiplier: costPenalty
        }
      };
    }));
    
    // Filter out null results
    const validScoredMappings = scoredMappings.filter(Boolean);
    
    // Sort by final score and select best
    validScoredMappings.sort((a, b) => b.finalScore - a.finalScore);
    
    let selected = validScoredMappings[0];
    
    // Smart selection for simple queries (EXACT from router.ts lines 755-759)
    if (analysis.complexity === 'simple' || 
        (analysis.complexity === 'standard' && analysis.primary_category === 'general_chat')) {
      
      console.log(`  🎯 Using simple query optimization...`);
      selected = this.selectBestModelForSimpleQuery(validScoredMappings, analysis);
    }
    
    return { selected, allScored: validScoredMappings };
  }

  // Main test function
  async testCompleteSelection(query, userPlan = 'free', conversationLength = 0) {
    console.log('🎯 COMPLETE ROUTER TEST (EXACT ROUTER.TS LOGIC)');
    console.log('='.repeat(70));
    console.log(`Query: "${query}"`);
    console.log(`User Plan: ${userPlan}`);
    console.log(`Conversation Length: ${conversationLength}`);
    console.log();
    
    // Step 1: Query Analysis
    const analysis = this.analyzeQuery(query, conversationLength);
    console.log('📊 QUERY ANALYSIS:');
    console.log(`  Category: ${analysis.primary_category}`);
    console.log(`  Complexity: ${analysis.complexity}`);
    console.log(`  Cost Tolerance: ${analysis.requirements.max_cost_tolerance}`);
    console.log(`  Latency Sensitivity: ${analysis.requirements.latency_sensitivity}`);
    console.log(`  Needs Web Search: ${analysis.requirements.needs_web_search}`);
    console.log(`  Reasoning: ${analysis.reasoning}`);
    console.log();
    
    // Step 2: Database Mappings
    console.log('🗃️  DATABASE MAPPINGS:');
    const mappings = await this.getModelMappings(analysis.primary_category, analysis.complexity);
    
    if (mappings.length > 0) {
      console.log(`  ✅ Found ${mappings.length} mappings for ${analysis.primary_category}/${analysis.complexity}`);
      
      // Step 3: Thompson Sampling Selection
      const input = { 
        query, 
        userPlan, 
        conversationLength,
        hasCode: false,
        webSearchEnabled: false 
      };
      
      const result = await this.selectModelWithThompsonSampling(mappings, analysis, input, 'test-123');
      
      console.log();
      console.log('🏆 THOMPSON SAMPLING RESULT:');
      const selected = result.selected;
      console.log(`  Selected: ${selected.model.canonicalName}`);
      console.log(`  Display: ${selected.model.displayName}`);
      console.log(`  Final Score: ${selected.finalScore.toFixed(4)}`);
      console.log(`  Mapping Score: ${selected.mapping.score}`);
      console.log(`  Cost Category: ${selected.model.metadata?.costCategory}`);
      console.log(`  Chat Score: ${selected.model.metadata?.taskScores?.cha || 'N/A'}`);
      
      console.log();
      console.log('📊 SCORE BREAKDOWN:');
      Object.entries(selected.breakdown).forEach(([key, value]) => {
        console.log(`    ${key}: ${typeof value === 'number' ? value.toFixed(4) : value}`);
      });
      
      console.log();
      console.log('🥇 TOP 5 ALTERNATIVES:');
      result.allScored.slice(0, 5).forEach((item, i) => {
        const taskScore = this.getTaskSpecificScore(item.model, analysis.primary_category);
        console.log(`  ${i + 1}. ${item.model.canonicalName}`);
        console.log(`     Final: ${item.finalScore.toFixed(4)}, Task: ${taskScore}, Cost: ${item.model.metadata?.costCategory}`);
      });
      
    } else {
      console.log('  ❌ No mappings found - would trigger fallback logic');
      console.log();
      console.log('🔄 FALLBACK LOGIC:');
      console.log('  This would use the hardcoded selection in getFallbackSelection()');
      console.log('  For simple queries, would prefer: gemini-2.5-flash-lite, gemini-1.5-flash-8b, etc.');
    }
    
    console.log();
    console.log('🔍 DEBUGGING INFO:');
    
    // Show available complexity levels
    const complexityStats = await prisma.model_mappings.groupBy({
      by: ['complexity_level'],
      where: {
        category: analysis.primary_category,
        enabled: true
      },
      _count: true
    });
    
    console.log(`  Available complexity levels for '${analysis.primary_category}':`);
    complexityStats.forEach(stat => {
      const isMatch = stat.complexity_level === analysis.complexity;
      console.log(`    ${isMatch ? '✅' : '  '} ${stat.complexity_level}: ${stat._count} mappings`);
    });
  }
}

// CLI interface
async function main() {
  const query = process.argv[2];
  const userPlan = process.argv[3] || 'free';
  const conversationLength = parseInt(process.argv[4]) || 0;
  
  if (!query) {
    console.log('Usage: node test-router-complete.js "query" [userPlan] [conversationLength]');
    console.log('');
    console.log('Examples:');
    console.log('  node test-router-complete.js "Hello"');
    console.log('  node test-router-complete.js "who would win" free 0');
    console.log('  node test-router-complete.js "debug this Python code" pro 5');
    process.exit(1);
  }
  
  const tester = new CompleteRouterTester();
  
  try {
    await tester.testCompleteSelection(query, userPlan, conversationLength);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { CompleteRouterTester };