#!/usr/bin/env tsx

/**
 * Integration Test Script for AI SDK Migration
 * Tests complete flow: Router → Provider → Generation
 * Created in Phase 8.4 for production readiness validation
 */

import { intelligentRouter } from '../src/lib/ai/router';
import { createAISDKProvider } from '../src/lib/ai/providers';
import { titleGenerator } from '../src/lib/ai/title-generator-ai-sdk';
import { modelTester } from '../src/lib/ai/model-tester';
import { config } from 'dotenv';

// Load environment variables
config();

interface IntegrationTestResult {
  testName: string;
  status: 'passed' | 'failed';
  duration: number;
  details?: any;
  error?: string;
}

class IntegrationTester {
  private results: IntegrationTestResult[] = [];

  /**
   * Test 1: Router Model Selection
   */
  async testRouterSelection(): Promise<IntegrationTestResult> {
    const startTime = Date.now();
    
    try {
      console.log('🎯 Testing router model selection...');
      
      const routerInput = {
        query: 'Write a simple Hello World function in Python',
        conversationLength: 1,
        hasCode: true,
        userPlan: 'FREE'
      };
      
      const result = await intelligentRouter.route(routerInput);
      
      const duration = Date.now() - startTime;
      
      if (result && result.selectedModel) {
        console.log(`✅ Router selected model: ${result.selectedModel}`);
        return {
          testName: 'Router Model Selection',
          status: 'passed',
          duration,
          details: {
            selectedModel: result.selectedModel,
            confidence: result.confidence,
            reasoning: result.reasoning
          }
        };
      } else {
        throw new Error('Router returned invalid result');
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: 'Router Model Selection',
        status: 'failed',
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test 2: AI SDK Provider Generation
   */
  async testProviderGeneration(): Promise<IntegrationTestResult> {
    const startTime = Date.now();
    
    try {
      console.log('🤖 Testing AI SDK provider generation...');
      
      // Use a fast, reliable provider for testing
      const provider = createAISDKProvider('groq');
      await provider.validateConfig();
      
      const response = await provider.generateCompletion({
        model: 'llama3-8b-8192',
        messages: [
          { role: 'user', content: 'Respond with exactly "Integration test successful"' }
        ],
        temperature: 0.1,
        maxTokens: 10
      });
      
      const duration = Date.now() - startTime;
      
      if (response && response.toLowerCase().includes('integration test successful')) {
        console.log('✅ Provider generation successful');
        return {
          testName: 'AI SDK Provider Generation',
          status: 'passed',
          duration,
          details: {
            provider: 'groq',
            model: 'llama3-8b-8192',
            response: response.trim()
          }
        };
      } else {
        throw new Error(`Unexpected response: ${response}`);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: 'AI SDK Provider Generation',
        status: 'failed',
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test 3: Title Generation
   */
  async testTitleGeneration(): Promise<IntegrationTestResult> {
    const startTime = Date.now();
    
    try {
      console.log('📝 Testing AI SDK title generation...');
      
      const messages = [
        { role: 'user' as const, content: 'How do I create a React component?' },
        { role: 'assistant' as const, content: 'To create a React component, you can use either function components or class components...' }
      ];
      
      const title = await titleGenerator.generateTitle(messages, {
        maxLength: 50,
        style: 'auto',
        includeEmoji: true
      });
      
      const duration = Date.now() - startTime;
      
      if (title && title.length > 0 && title !== 'New Conversation') {
        console.log(`✅ Title generated: "${title}"`);
        return {
          testName: 'Title Generation',
          status: 'passed',
          duration,
          details: {
            title,
            messageCount: messages.length
          }
        };
      } else {
        throw new Error(`Invalid title generated: ${title}`);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: 'Title Generation',
        status: 'failed',
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test 4: Model Testing Utility
   */
  async testModelTester(): Promise<IntegrationTestResult> {
    const startTime = Date.now();
    
    try {
      console.log('🧪 Testing model tester utility...');
      
      const result = await modelTester.testModel(
        'groq/llama3-8b-8192',
        'Respond with "Model tester working"'
      );
      
      const duration = Date.now() - startTime;
      
      if (result.success && result.response) {
        console.log('✅ Model tester working correctly');
        return {
          testName: 'Model Tester Utility',
          status: 'passed',
          duration,
          details: {
            latency: result.latency,
            response: result.response.substring(0, 50)
          }
        };
      } else {
        throw new Error(`Model test failed: ${result.error}`);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: 'Model Tester Utility',
        status: 'failed',
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test 5: End-to-End Flow
   */
  async testEndToEndFlow(): Promise<IntegrationTestResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔄 Testing complete end-to-end flow...');
      
      // Step 1: Router selects model
      const routerResult = await intelligentRouter.route({
        query: 'Explain async/await in JavaScript',
        conversationLength: 1,
        hasCode: true,
        userPlan: 'STARTER'
      });
      
      if (!routerResult.selectedModel) {
        throw new Error('Router failed to select model');
      }
      
      // Step 2: Generate response with selected model
      const selectedModel = routerResult.selectedModel;
      
      // Extract provider from model name
      const providerName = selectedModel.includes('/') 
        ? selectedModel.split('/')[0] 
        : 'openai'; // fallback
      
      const provider = createAISDKProvider(providerName as any);
      await provider.validateConfig();
      
      const modelId = selectedModel.includes('/') 
        ? selectedModel.split('/').slice(1).join('/') 
        : selectedModel;
      
      const response = await provider.generateCompletion({
        model: modelId,
        messages: [
          { role: 'user', content: 'Explain async/await in JavaScript briefly' }
        ],
        temperature: 0.3,
        maxTokens: 100
      });
      
      // Step 3: Generate title
      const title = await titleGenerator.generateTitle([
        { role: 'user', content: 'Explain async/await in JavaScript' },
        { role: 'assistant', content: response || 'Response about async/await' }
      ]);
      
      const duration = Date.now() - startTime;
      
      if (response && title) {
        console.log('✅ End-to-end flow successful');
        return {
          testName: 'End-to-End Flow',
          status: 'passed',
          duration,
          details: {
            routerModel: selectedModel,
            responseLength: response.length,
            title: title.substring(0, 30),
            confidence: routerResult.confidence
          }
        };
      } else {
        throw new Error('End-to-end flow incomplete');
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        testName: 'End-to-End Flow',
        status: 'failed',
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Run all integration tests
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Integration Test Suite');
    console.log('==================================\n');
    
    const tests = [
      () => this.testRouterSelection(),
      () => this.testProviderGeneration(),
      () => this.testTitleGeneration(),
      () => this.testModelTester(),
      () => this.testEndToEndFlow()
    ];
    
    for (const test of tests) {
      const result = await test();
      this.results.push(result);
      
      if (result.status === 'passed') {
        console.log(`✅ ${result.testName} passed (${result.duration}ms)\n`);
      } else {
        console.log(`❌ ${result.testName} failed: ${result.error}\n`);
      }
      
      // Add delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    this.printSummary();
  }

  /**
   * Print test summary
   */
  private printSummary(): void {
    console.log('📊 INTEGRATION TEST SUMMARY');
    console.log('===========================');
    
    const passed = this.results.filter(r => r.status === 'passed');
    const failed = this.results.filter(r => r.status === 'failed');
    
    console.log(`✅ Passed: ${passed.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log(`📊 Total: ${this.results.length}`);
    
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    
    if (passed.length > 0) {
      console.log('\n🎉 PASSED TESTS:');
      passed.forEach(result => {
        console.log(`  ✅ ${result.testName} (${result.duration}ms)`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ FAILED TESTS:');
      failed.forEach(result => {
        console.log(`  ❌ ${result.testName}: ${result.error}`);
      });
    }
    
    // Calculate success rate
    const successRate = (passed.length / this.results.length * 100).toFixed(1);
    console.log(`\n📈 Success Rate: ${successRate}%`);
    
    if (failed.length === 0) {
      console.log('\n🎉 ALL INTEGRATION TESTS PASSED!');
      console.log('✨ AI SDK migration is production-ready');
      process.exit(0);
    } else {
      console.log('\n⚠️  Some integration tests failed. Review before production deployment.');
      process.exit(1);
    }
  }
}

// Main execution
async function main() {
  const tester = new IntegrationTester();
  await tester.runAllTests();
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Integration test suite failed:', error);
    process.exit(1);
  });
}

export { IntegrationTester };