#!/usr/bin/env tsx

/**
 * AI SDK Provider Test Script
 * Tests all configured AI SDK providers for basic functionality
 * Created in Phase 8.2 as part of LiteLLM migration
 */

import { createAISDKProvider, AI_SDK_PROVIDERS, type AISDKProviderName } from '../src/lib/ai/providers';
import { apiLogger } from '../src/lib/logger';
import { config } from 'dotenv';

// Load environment variables
config();

interface TestResult {
  provider: string;
  status: 'success' | 'failed' | 'skipped';
  latency?: number;
  error?: string;
  modelsTested?: number;
}

class AISDKProviderTester {
  private results: TestResult[] = [];

  /**
   * Test a single provider
   */
  async testProvider(providerName: AISDKProviderName): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`\n🔧 Testing provider: ${providerName}`);
      
      // Initialize provider
      const provider = createAISDKProvider(providerName);
      
      // Validate configuration
      const isValid = await provider.validateConfig();
      if (!isValid) {
        return {
          provider: providerName,
          status: 'skipped',
          error: 'Configuration invalid or API key missing'
        };
      }
      
      console.log(`✅ Provider ${providerName} configuration valid`);
      
      // Get available models
      const models = await provider.listModels();
      console.log(`📋 Found ${models.length} models for ${providerName}`);
      
      if (models.length === 0) {
        return {
          provider: providerName,
          status: 'failed',
          error: 'No models available'
        };
      }
      
      // Test with first available model
      const testModel = models[0];
      console.log(`🧪 Testing model: ${testModel.id}`);
      
      const response = await provider.generateCompletion({
        model: testModel.id,
        messages: [
          { role: 'user', content: 'Hello, respond with "Test successful" if you can read this.' }
        ],
        temperature: 0.1,
        maxTokens: 20
      });
      
      const latency = Date.now() - startTime;
      
      if (response && response.trim().length > 0) {
        console.log(`🎉 Provider ${providerName} test successful!`);
        console.log(`📝 Response: "${response.trim().substring(0, 50)}..."`);
        
        return {
          provider: providerName,
          status: 'success',
          latency,
          modelsTested: models.length
        };
      } else {
        return {
          provider: providerName,
          status: 'failed',
          latency,
          error: 'Empty response received'
        };
      }
      
    } catch (error) {
      const latency = Date.now() - startTime;
      console.error(`❌ Provider ${providerName} test failed:`, error);
      
      return {
        provider: providerName,
        status: 'failed',
        latency,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test all providers
   */
  async testAllProviders(): Promise<void> {
    console.log('🚀 Starting AI SDK Provider Test Suite');
    console.log('=====================================\n');
    
    const providers = Object.values(AI_SDK_PROVIDERS);
    
    for (const providerName of providers) {
      const result = await this.testProvider(providerName as AISDKProviderName);
      this.results.push(result);
      
      // Add delay between tests to avoid rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    this.printSummary();
  }

  /**
   * Print test summary
   */
  private printSummary(): void {
    console.log('\n📊 TEST SUMMARY');
    console.log('===============');
    
    const successful = this.results.filter(r => r.status === 'success');
    const failed = this.results.filter(r => r.status === 'failed');
    const skipped = this.results.filter(r => r.status === 'skipped');
    
    console.log(`✅ Successful: ${successful.length}`);
    console.log(`❌ Failed: ${failed.length}`);
    console.log(`⏭️  Skipped: ${skipped.length}`);
    console.log(`📊 Total: ${this.results.length}`);
    
    if (successful.length > 0) {
      console.log('\n🎉 WORKING PROVIDERS:');
      successful.forEach(result => {
        console.log(`  ✅ ${result.provider} (${result.latency}ms, ${result.modelsTested} models)`);
      });
    }
    
    if (failed.length > 0) {
      console.log('\n❌ FAILED PROVIDERS:');
      failed.forEach(result => {
        console.log(`  ❌ ${result.provider}: ${result.error}`);
      });
    }
    
    if (skipped.length > 0) {
      console.log('\n⏭️  SKIPPED PROVIDERS (missing config):');
      skipped.forEach(result => {
        console.log(`  ⏭️  ${result.provider}: ${result.error}`);
      });
    }
    
    // Calculate success rate
    const totalConfigured = successful.length + failed.length;
    const successRate = totalConfigured > 0 ? (successful.length / totalConfigured * 100).toFixed(1) : '0';
    
    console.log(`\n📈 Success Rate: ${successRate}% (${successful.length}/${totalConfigured} configured providers)`);
    
    // Exit with appropriate code
    if (failed.length > 0) {
      console.log('\n⚠️  Some providers failed. Check configurations and API keys.');
      process.exit(1);
    } else if (successful.length === 0) {
      console.log('\n⚠️  No providers are working. Check your environment configuration.');
      process.exit(1);
    } else {
      console.log('\n🎉 All configured providers are working correctly!');
      process.exit(0);
    }
  }

  /**
   * Test specific providers
   */
  async testSpecificProviders(providerNames: string[]): Promise<void> {
    console.log(`🔍 Testing specific providers: ${providerNames.join(', ')}`);
    console.log('================================================\n');
    
    for (const providerName of providerNames) {
      if (Object.values(AI_SDK_PROVIDERS).includes(providerName as AISDKProviderName)) {
        const result = await this.testProvider(providerName as AISDKProviderName);
        this.results.push(result);
        
        // Add delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        console.log(`❌ Unknown provider: ${providerName}`);
        this.results.push({
          provider: providerName,
          status: 'failed',
          error: 'Unknown provider'
        });
      }
    }
    
    this.printSummary();
  }
}

// Main execution
async function main() {
  const tester = new AISDKProviderTester();
  
  // Get command line arguments
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // Test specific providers
    await tester.testSpecificProviders(args);
  } else {
    // Test all providers
    await tester.testAllProviders();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

export { AISDKProviderTester };