/**
 * COMPREHENSIVE ROUTER ANALYSIS WITH O3-PRO EXPERT CONSULTATION
 * 
 * This script performs a complete analysis of our AI router system including:
 * 1. Current router scoring mechanisms
 * 2. Model database analysis
 * 3. Cost penalty calculations
 * 4. Performance testing
 * 5. O3-pro expert consultation for optimization recommendations
 */

import OpenAI from 'openai';
import mysql from 'mysql2/promise';
import { config } from 'dotenv';

config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Database connection
const dbConfig = {
  host: '127.0.0.1',
  user: 'root',
  password: process.env.MYSQL_PWD,
  database: 'justsimplechat_production'
};

class RouterAnalyzer {
  constructor() {
    this.connection = null;
    this.analysisData = {
      models: [],
      mappings: [],
      userPlans: [],
      currentLogic: {},
      performanceMetrics: {},
      issues: [],
      recommendations: []
    };
  }

  async initialize() {
    console.log('🔍 Initializing Comprehensive Router Analysis...\n');
    this.connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established\n');
  }

  async analyzeModelDatabase() {
    console.log('📊 Analyzing Model Database...\n');

    // Get all models with complete data
    const [models] = await this.connection.execute(`
      SELECT 
        id,
        canonicalName,
        displayName,
        provider,
        costCategory,
        avgCost,
        planAccessLevel,
        isEnabled,
        capabilities,
        maxInputTokens,
        maxOutputTokens,
        supportsStreaming,
        supportsVision,
        supportsImageGeneration,
        supportsWebSearch,
        trustScore,
        createdAt,
        updatedAt
      FROM AIModel 
      WHERE isEnabled = 1
      ORDER BY provider, canonicalName
    `);

    this.analysisData.models = models;

    console.log(`📈 Model Statistics:`);
    console.log(`- Total Active Models: ${models.length}`);
    
    // Provider distribution
    const providerStats = {};
    const costCategoryStats = {};
    const planStats = {};
    
    models.forEach(model => {
      providerStats[model.provider] = (providerStats[model.provider] || 0) + 1;
      costCategoryStats[model.costCategory] = (costCategoryStats[model.costCategory] || 0) + 1;
      planStats[model.planAccessLevel] = (planStats[model.planAccessLevel] || 0) + 1;
    });

    console.log('\n📦 Provider Distribution:');
    Object.entries(providerStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([provider, count]) => {
        console.log(`  ${provider}: ${count} models (${((count/models.length)*100).toFixed(1)}%)`);
      });

    console.log('\n💰 Cost Category Distribution:');
    Object.entries(costCategoryStats).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} models (${((count/models.length)*100).toFixed(1)}%)`);
    });

    console.log('\n🎯 Plan Access Distribution:');
    Object.entries(planStats).forEach(([plan, count]) => {
      console.log(`  ${plan}: ${count} models (${((count/models.length)*100).toFixed(1)}%)`);
    });

    // Analyze capabilities
    const capabilityStats = {
      vision: models.filter(m => m.supportsVision).length,
      imageGeneration: models.filter(m => m.supportsImageGeneration).length,
      webSearch: models.filter(m => m.supportsWebSearch).length,
      streaming: models.filter(m => m.supportsStreaming).length
    };

    console.log('\n🔧 Capability Distribution:');
    Object.entries(capabilityStats).forEach(([capability, count]) => {
      console.log(`  ${capability}: ${count} models (${((count/models.length)*100).toFixed(1)}%)`);
    });

    // Cost analysis
    const costsWithData = models.filter(m => m.avgCost !== null && m.avgCost > 0);
    if (costsWithData.length > 0) {
      const costs = costsWithData.map(m => m.avgCost).sort((a,b) => a-b);
      console.log('\n💲 Cost Analysis:');
      console.log(`  Models with cost data: ${costsWithData.length}/${models.length} (${((costsWithData.length/models.length)*100).toFixed(1)}%)`);
      console.log(`  Cost range: $${costs[0]} - $${costs[costs.length-1]}`);
      console.log(`  Median cost: $${costs[Math.floor(costs.length/2)]}`);
    }

    return models;
  }

  async analyzeModelMappings() {
    console.log('\n🗺️ Analyzing Model Mappings...\n');

    const [mappings] = await this.connection.execute(`
      SELECT 
        category,
        complexity,
        COUNT(*) as mapping_count,
        AVG(score) as avg_score,
        MIN(score) as min_score,
        MAX(score) as max_score,
        GROUP_CONCAT(DISTINCT modelCanonicalName LIMIT 10) as sample_models
      FROM model_mappings 
      GROUP BY category, complexity
      ORDER BY category, 
        CASE complexity 
          WHEN 'simple' THEN 1 
          WHEN 'standard' THEN 2 
          WHEN 'difficult' THEN 3 
          WHEN 'complex' THEN 4 
          ELSE 5 
        END
    `);

    this.analysisData.mappings = mappings;

    console.log('📊 Model Mapping Statistics:');
    console.log(`- Total Category/Complexity Combinations: ${mappings.length}`);

    // Group by category
    const categoryGroups = {};
    mappings.forEach(mapping => {
      if (!categoryGroups[mapping.category]) {
        categoryGroups[mapping.category] = [];
      }
      categoryGroups[mapping.category].push(mapping);
    });

    console.log('\n📋 Top Categories by Mapping Count:');
    Object.entries(categoryGroups)
      .sort(([,a], [,b]) => b.reduce((sum, m) => sum + m.mapping_count, 0) - a.reduce((sum, m) => sum + m.mapping_count, 0))
      .slice(0, 10)
      .forEach(([category, categoryMappings]) => {
        const totalCount = categoryMappings.reduce((sum, m) => sum + m.mapping_count, 0);
        const avgScore = categoryMappings.reduce((sum, m) => sum + (m.avg_score * m.mapping_count), 0) / totalCount;
        console.log(`  ${category}: ${totalCount} mappings (avg score: ${avgScore.toFixed(1)})`);
        
        categoryMappings.forEach(mapping => {
          console.log(`    ${mapping.complexity}: ${mapping.mapping_count} models, score ${mapping.avg_score.toFixed(1)} (${mapping.min_score}-${mapping.max_score})`);
        });
      });

    return mappings;
  }

  async analyzeCurrentRouterLogic() {
    console.log('\n🧠 Analyzing Current Router Logic...\n');

    // Read the router.ts file to analyze current logic
    const fs = await import('fs/promises');
    const routerPath = '/home/<USER>/deployments/dev/simplechat-ai/src/lib/ai/router.ts';
    
    try {
      const routerContent = await fs.readFile(routerPath, 'utf8');
      
      // Extract key components
      const costPenaltyRegex = /calculateCostPenalty[^}]+}/gs;
      const costToleranceRegex = /determineCostTolerance[^}]+}/gs;
      const complexityFirstRegex = /pickBestModelWithComplexityFirst[^}]+}/gs;
      
      const costPenaltyMatch = routerContent.match(costPenaltyRegex);
      const costToleranceMatch = routerContent.match(costToleranceRegex);
      const complexityFirstMatch = routerContent.match(complexityFirstRegex);

      this.analysisData.currentLogic = {
        hasCostPenalty: !!costPenaltyMatch,
        hasCostTolerance: !!costToleranceMatch,
        hasComplexityFirst: !!complexityFirstMatch,
        costPenaltyLogic: costPenaltyMatch ? costPenaltyMatch[0] : null,
        costToleranceLogic: costToleranceMatch ? costToleranceMatch[0] : null,
        complexityFirstLogic: complexityFirstMatch ? complexityFirstMatch[0] : null
      };

      console.log('✅ Router Logic Analysis:');
      console.log(`- Cost Penalty Logic: ${this.analysisData.currentLogic.hasCostPenalty ? 'Present' : 'Missing'}`);
      console.log(`- Cost Tolerance Logic: ${this.analysisData.currentLogic.hasCostTolerance ? 'Present' : 'Missing'}`);
      console.log(`- Complexity-First Selection: ${this.analysisData.currentLogic.hasComplexityFirst ? 'Present' : 'Missing'}`);

    } catch (error) {
      console.log(`❌ Error reading router file: ${error.message}`);
      this.analysisData.issues.push(`Cannot read router.ts file: ${error.message}`);
    }
  }

  async testRouterScenarios() {
    console.log('\n🧪 Testing Router Scenarios...\n');

    const testCases = [
      {
        name: 'Complex Coding Task',
        query: 'Debug this complex Python algorithm that implements a graph traversal with dynamic programming optimization',
        expectedCategory: 'coding',
        expectedComplexity: 'complex',
        userPlan: 'MAX'
      },
      {
        name: 'Simple Chat',
        query: 'Hello, how are you?',
        expectedCategory: 'general_chat',
        expectedComplexity: 'simple',
        userPlan: 'FREE'
      },
      {
        name: 'Image Generation',
        query: 'Create an image of a futuristic cityscape at sunset',
        expectedCategory: 'image_generation',
        expectedComplexity: 'standard',
        userPlan: 'PLUS'
      },
      {
        name: 'Complex Analysis',
        query: 'Analyze the economic implications of quantum computing on financial markets, considering regulatory frameworks and technological adoption curves',
        expectedCategory: 'analysis',
        expectedComplexity: 'complex',
        userPlan: 'MAX'
      },
      {
        name: 'Mathematical Reasoning',
        query: 'Prove that the sum of angles in any polygon with n sides equals (n-2) × 180 degrees using mathematical induction',
        expectedCategory: 'math',
        expectedComplexity: 'complex',
        userPlan: 'ADVANCED'
      }
    ];

    // Here we would normally call the actual router, but since we're analyzing,
    // we'll simulate the expected behavior based on current logic
    this.analysisData.performanceMetrics = {
      testCases: testCases,
      results: testCases.map(testCase => ({
        ...testCase,
        // Simulated results - in real implementation, we'd call the router
        simulatedResult: {
          detectedCorrectly: true, // We'd need to implement actual testing
          selectedModel: 'unknown', // Would come from actual router call
          processingTime: 0, // Would measure actual time
          costPenaltyApplied: 'unknown' // Would analyze actual penalty
        }
      }))
    };

    console.log('📝 Test Scenarios Prepared:');
    testCases.forEach((testCase, index) => {
      console.log(`${index + 1}. ${testCase.name}`);
      console.log(`   Query: "${testCase.query.slice(0, 80)}..."`);
      console.log(`   Expected: ${testCase.expectedCategory}/${testCase.expectedComplexity} (${testCase.userPlan} plan)`);
    });
  }

  async consultO3Pro() {
    console.log('\n🤖 Consulting O3-Pro for Expert Analysis...\n');
    console.log('⏳ This may take 2-5 minutes due to o3-pro\'s deep reasoning...\n');

    const analysisPrompt = this.buildO3ProPrompt();

    try {
      const startTime = Date.now();
      
      const response = await openai.chat.completions.create({
        model: 'o3-pro',
        messages: [
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        reasoning_effort: 'high',
        temperature: 0.2,
        max_tokens: 4000
      });

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      console.log(`✅ O3-Pro Analysis Complete (${duration.toFixed(1)}s)\n`);

      const o3Analysis = response.choices[0].message.content;
      this.analysisData.o3ProRecommendations = {
        fullAnalysis: o3Analysis,
        processingTime: duration,
        reasoningTokens: response.usage?.reasoning_tokens || 'unknown',
        totalTokens: response.usage?.total_tokens || 'unknown'
      };

      // Parse key recommendations from the response
      this.parseO3ProRecommendations(o3Analysis);

      return o3Analysis;

    } catch (error) {
      console.log(`❌ Error consulting O3-Pro: ${error.message}`);
      this.analysisData.issues.push(`O3-Pro consultation failed: ${error.message}`);
      return null;
    }
  }

  buildO3ProPrompt() {
    const modelSummary = this.summarizeModels();
    const mappingSummary = this.summarizeMappings();
    const currentLogic = this.summarizeCurrentLogic();

    return `# AI Router Optimization Analysis Request

You are an expert AI system architect analyzing a production AI routing system that intelligently selects the best AI model for user queries. The system needs to balance performance, cost, and user experience.

## Current System Overview

### Model Inventory
${modelSummary}

### Scoring & Mapping System
${mappingSummary}

### Current Router Logic
${currentLogic}

## User Experience Goals
1. **Complex coding tasks** should get premium models (Claude Sonnet, GPT-4o, O3-mini) for MAX plan users
2. **Simple queries** should use cost-effective models to control expenses
3. **Specialized tasks** (image generation, analysis) should route to capable models
4. **Free/Basic users** should get good experience within budget constraints

## Current Issues Identified
- Users report premium models not being selected for complex coding tasks
- Cost penalty logic may be too aggressive for MAX plan users  
- Router selects Codestral instead of GPT-4o/Claude for "complex coding" tasks
- Need to optimize cost vs. quality balance

## Analysis Request

Please provide a comprehensive analysis and optimization strategy covering:

### 1. Current Logic Assessment
Analyze the existing cost penalty and model selection logic. What are the strengths and weaknesses?

### 2. Cost vs. Quality Optimization
How can we better balance cost control with quality? Consider different user plans and task complexities.

### 3. Model Selection Strategy
Given our model inventory, what selection strategy would optimize user experience while controlling costs?

### 4. Implementation Recommendations
Provide specific code changes or algorithm improvements with rationale.

### 5. Testing & Validation
How should we test and validate router improvements?

Focus on practical, implementable solutions that work with our existing data structure and don't require expensive model re-scoring or database changes.`;
  }

  summarizeModels() {
    const models = this.analysisData.models;
    if (!models.length) return 'No model data available';

    const totalModels = models.length;
    const providers = [...new Set(models.map(m => m.provider))];
    const costCategories = [...new Set(models.map(m => m.costCategory))];
    
    // Top models by category
    const premiumModels = models.filter(m => m.costCategory === 'PREMIUM').slice(0, 5);
    const freeModels = models.filter(m => m.costCategory === 'FREE').slice(0, 5);

    return `
**${totalModels} active models** across ${providers.length} providers
**Providers**: ${providers.join(', ')}
**Cost Categories**: ${costCategories.join(', ')}

**Sample Premium Models** (for complex tasks):
${premiumModels.map(m => `- ${m.canonicalName} (${m.provider}, cost: $${m.avgCost || 'TBD'})`).join('\n')}

**Sample Free Models** (for simple tasks):
${freeModels.map(m => `- ${m.canonicalName} (${m.provider})`).join('\n')}`;
  }

  summarizeMappings() {
    const mappings = this.analysisData.mappings;
    if (!mappings.length) return 'No mapping data available';

    const totalMappings = mappings.reduce((sum, m) => sum + m.mapping_count, 0);
    const categories = [...new Set(mappings.map(m => m.category))];

    // Find scoring patterns
    const codingMappings = mappings.filter(m => m.category === 'coding');
    const chatMappings = mappings.filter(m => m.category === 'general_chat');

    return `
**${totalMappings} total model mappings** across ${categories.length} categories
**Categories**: ${categories.slice(0, 10).join(', ')}${categories.length > 10 ? '...' : ''}

**Coding Task Scores** (current issue area):
${codingMappings.map(m => `- ${m.complexity}: avg ${m.avg_score.toFixed(1)} (${m.mapping_count} models)`).join('\n')}

**General Chat Scores** (baseline):
${chatMappings.map(m => `- ${m.complexity}: avg ${m.avg_score.toFixed(1)} (${m.mapping_count} models)`).join('\n')}`;
  }

  summarizeCurrentLogic() {
    const logic = this.analysisData.currentLogic;
    
    return `
**Current Router Architecture**:
- Cost Penalty System: ${logic.hasCostPenalty ? '✅ Active' : '❌ Missing'}
- Cost Tolerance Logic: ${logic.hasCostTolerance ? '✅ Active' : '❌ Missing'}  
- Complexity-First Selection: ${logic.hasComplexityFirst ? '✅ Active' : '❌ Missing'}

**Key Functions Implemented**:
- \`determineCostTolerance()\`: Maps task complexity to budget tolerance
- \`calculateCostPenalty()\`: Applies cost multipliers based on model category
- \`pickBestModelWithComplexityFirst()\`: O3-optimized cost matrix selection

**Recent Changes Made**:
- Added intelligent cost tolerance (high for complex coding tasks)
- Reduced PREMIUM model penalty from 0.7x to 0.95x for high tolerance
- Implemented category-specific cost caps`;
  }

  parseO3ProRecommendations(analysis) {
    // Extract key recommendations from the O3-Pro response
    // This would be more sophisticated in practice
    this.analysisData.recommendations = [
      'O3-Pro analysis completed - see full response for detailed recommendations'
    ];
  }

  async generateReport() {
    console.log('\n📊 Generating Comprehensive Analysis Report...\n');

    const report = `
# COMPREHENSIVE AI ROUTER ANALYSIS REPORT
Generated: ${new Date().toISOString()}

## Executive Summary
- **Models Analyzed**: ${this.analysisData.models.length} active models
- **Mappings Analyzed**: ${this.analysisData.mappings.length} category/complexity combinations
- **Router Logic**: ${this.analysisData.currentLogic.hasCostPenalty ? 'Functional' : 'Needs Implementation'}
- **O3-Pro Consultation**: ${this.analysisData.o3ProRecommendations ? 'Completed' : 'Failed'}

## Model Inventory Analysis
${this.summarizeModels()}

## Mapping System Analysis  
${this.summarizeMappings()}

## Current Router Logic
${this.summarizeCurrentLogic()}

## Issues Identified
${this.analysisData.issues.map(issue => `- ${issue}`).join('\n')}

## O3-Pro Expert Recommendations
${this.analysisData.o3ProRecommendations ? 
  `Processing Time: ${this.analysisData.o3ProRecommendations.processingTime.toFixed(1)}s
Reasoning Tokens: ${this.analysisData.o3ProRecommendations.reasoningTokens}
Total Tokens: ${this.analysisData.o3ProRecommendations.totalTokens}

${this.analysisData.o3ProRecommendations.fullAnalysis}` : 
  'O3-Pro consultation was not completed successfully.'}

## Next Steps
1. Review O3-Pro recommendations
2. Implement suggested optimizations
3. Test router improvements
4. Monitor user experience metrics
5. Iterate based on performance data

---
Report generated by Comprehensive Router Analysis System
`;

    // Save report to file
    const fs = await import('fs/promises');
    const reportPath = '/tmp/comprehensive_router_analysis_report.md';
    await fs.writeFile(reportPath, report);
    
    console.log(`📄 Full report saved to: ${reportPath}`);
    console.log('\n🎯 Analysis Complete!');
    
    return report;
  }

  async cleanup() {
    if (this.connection) {
      await this.connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

// Main execution
async function main() {
  const analyzer = new RouterAnalyzer();
  
  try {
    await analyzer.initialize();
    await analyzer.analyzeModelDatabase();
    await analyzer.analyzeModelMappings();
    await analyzer.analyzeCurrentRouterLogic();
    await analyzer.testRouterScenarios();
    
    // The big moment - O3-Pro expert consultation
    console.log('🎯 Beginning O3-Pro Expert Consultation...');
    console.log('This is where we get professional AI system architecture advice!\n');
    
    const o3Analysis = await analyzer.consultO3Pro();
    
    if (o3Analysis) {
      console.log('💡 O3-Pro Expert Analysis:\n');
      console.log(o3Analysis);
    }
    
    const report = await analyzer.generateReport();
    
  } catch (error) {
    console.error('❌ Analysis failed:', error);
  } finally {
    await analyzer.cleanup();
  }
}

// Execute the analysis
main().catch(console.error);