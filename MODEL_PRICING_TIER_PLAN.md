# Model Pricing Tier Implementation Plan

## Current Analysis (654 Total Models)

### Model Tiers by Cost
1. **Frontier Tier** (49 models) - Output ≥ $15/1M
   - Examples: GPT-4.5 Preview ($150/1M output), O3-Pro ($80/1M), Claude 4 Opus ($75/1M)
   - **Should be**: MAX plan only
   - **Currently**: Many incorrectly available to lower plans

2. **Budget Tier** (209 models) - Combined ≤ $1/1M  
   - Examples: Gemini Flash Lite ($0.50), DeepSeek Chat V3 ($0.42), Llama models ($0.13-$0.65)
   - **Should be**: Available to ALL plans (FREE/FREEMIUM/PLUS/ADVANCED/MAX)
   - **Currently**: Many missing from FREE/FREEMIUM access

3. **Standard Tier** (396 models) - Everything else
   - Examples: O1 Mini ($15), GPT-4o variants ($12.50), Claude 3.5 Sonnet ($15)
   - **Should be**: PLUS/ADVANCED/MAX only
   - **Currently**: Some incorrectly available to FREE/FREEMIUM

## Proposed Plan Structure

### Plan Access Rules
1. **FREE/FREEMIUM** (Budget users)
   - Access to: Budget models ONLY (combined cost ≤ $1/1M)
   - ~209 models available
   - Examples: Gemini Flash variants, Llama models, DeepSeek V3

2. **PLUS/ADVANCED** (Standard users)  
   - Access to: Budget + Standard models
   - ~605 models available (everything except frontier)
   - Examples: All budget models + GPT-4o, Claude 3.5 Sonnet, standard models

3. **MAX** (Premium users)
   - Access to: ALL models (Budget + Standard + Frontier)
   - All 654 models available
   - Includes frontier models like GPT-4.5 Preview, O3-Pro, Claude 4 Opus

## Ultra Mode Clarification

**Ultra Mode is NOT about plan access!** It's about optimization strategy:

### Without Ultra Mode (Default):
- Router optimizes for COST within the user's allowed models
- Simple tasks → cheapest suitable model
- Standard tasks → balanced cost/quality
- Complex tasks → quality-focused but still cost-aware

### With Ultra Mode Enabled:
- **For PLUS/ADVANCED users**: Ignores cost, picks BEST model by score (within their tier)
- **For MAX users**: Picks absolute best model regardless of cost
- **For FREE/FREEMIUM**: No effect (still limited to budget models)

### Example:
- PLUS user asks coding question
- Without Ultra: Might get DeepSeek Chat V3 ($0.42) - good & cheap
- With Ultra: Gets Claude 3.5 Sonnet ($15) - best available in their tier

## Implementation Steps

### 1. Create Model ID Lists
```sql
-- Get frontier model IDs (output >= $15)
SELECT id FROM Models WHERE outputCostPer1M >= 15;

-- Get budget model IDs (combined <= $1)  
SELECT id FROM Models WHERE (inputCostPer1M + outputCostPer1M) <= 1;

-- Get standard model IDs (everything else)
SELECT id FROM Models 
WHERE (inputCostPer1M + outputCostPer1M) > 1 
AND outputCostPer1M < 15;
```

### 2. Update ModelPlanRules
```sql
-- Frontier models: MAX only
UPDATE ModelPlanRules 
SET planIds = '["plan_max"]'
WHERE modelId IN (frontier_model_ids);

-- Budget models: All plans
UPDATE ModelPlanRules 
SET planIds = '["plan_free", "plan_freemium", "plan_plus", "plan_advanced", "plan_max", "plan_enterprise"]'
WHERE modelId IN (budget_model_ids);

-- Standard models: PLUS and above
UPDATE ModelPlanRules 
SET planIds = '["plan_plus", "plan_advanced", "plan_max", "plan_enterprise"]'
WHERE modelId IN (standard_model_ids);
```

### 3. Update Router Logic

The ultra mode logic in `getBestModelForCategory` should be:

```typescript
// For ultra mode, change scoring strategy but NOT plan access
if (options?.ultraMode && userPlan !== UserPlan.FREE && userPlan !== UserPlan.FREEMIUM) {
  // Ultra mode: Ignore cost, maximize quality
  selected = models[0]; // Already sorted by score DESC
} else {
  // Normal mode: Consider cost based on complexity
  switch (complexity) {
    case 'simple':
      // Prefer cheaper models
      selected = models.sort((a, b) => a.inputCost - b.inputCost)[0];
      break;
    case 'complex':
      // Prefer quality but still consider cost
      selected = models[0];
      break;
    default:
      // Balanced approach
      // ...existing logic
  }
}
```

## Testing Strategy

1. **Verify Plan Access**:
   - FREE user: Should only see ~209 budget models
   - PLUS user: Should see ~605 models (no frontier)
   - MAX user: Should see all 654 models

2. **Test Ultra Mode**:
   - PLUS user + Ultra: Gets best model within their tier
   - MAX user + Ultra: Gets absolute best model
   - FREE user + Ultra: No effect

3. **Cost Optimization**:
   - Without Ultra: Verify cost-aware selection
   - With Ultra: Verify quality-first selection

## Benefits

1. **Clear Tier Structure**: Users understand what they're paying for
2. **Fair Pricing**: Expensive models properly gated to premium plans
3. **Ultra Mode Value**: Helps users get better results when quality matters
4. **Cost Control**: Default mode keeps costs reasonable
5. **Plan Incentive**: Clear upgrade path (more models at each tier)

## Notes
- Enterprise plan treated same as MAX
- Some models have duplicate entries (different providers) - need deduplication
- Pricing should be regularly reviewed as model costs change