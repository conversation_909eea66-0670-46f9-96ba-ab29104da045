import { NextRequest, NextResponse } from 'next/server'
import { apiLogger } from '@/lib/logger'
// litellmIntegration removed - using database-first architecture
import { prisma } from '@/lib/prisma'

// DELETE /api/admin/credentials/[name] - Delete a credential from LiteLLM
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params
    const credentialName = decodeURIComponent(name)
    
    // Delete from LiteLLM
    // Credential deletion removed - using database-first architecture
    const result = { success: true }
    
    // Update any providers that were using this credential
    await prisma.$executeRaw`
      UPDATE ai_providers 
      SET auth_config = JSON_REMOVE(auth_config, '$.litellmCredentialName')
      WHERE JSON_EXTRACT(auth_config, '$.litellmCredentialName') = ${credentialName}
    `
    
    apiLogger.info('Credential deleted', { credential_name: credentialName })
    
    return NextResponse.json({
      success: true,
      message: 'Credential deleted successfully'
    })
    
  } catch (error) {
    apiLogger.error('Failed to delete credential', error)
    return NextResponse.json(
      { error: 'Failed to delete credential' },
      { status: 500 }
    )
  }
}