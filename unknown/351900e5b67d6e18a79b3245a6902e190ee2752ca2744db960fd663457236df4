import { NextRequest, NextResponse } from 'next/server'
import { api<PERSON>ogger } from '@/lib/logger'
// litellmIntegration removed - using database-first architecture
import { prisma } from '@/lib/prisma'

// GET /api/admin/credentials - Get all credentials from LiteLLM
export async function GET(request: NextRequest) {
  try {
    // Credential retrieval removed - using database-first architecture
    const credentials: any[] = []
    
    // Get provider info to enrich credentials
    const providers = await prisma.aIProvider.findMany({
      select: {
        id: true,
        name: true,
        slug: true
      }
    })
    
    // Map credentials with provider info
    const enrichedCredentials = credentials.map((cred: any) => {
      // Try to match credential to provider
      const provider = providers.find((p: any) => 
        cred.credential_name.toLowerCase().includes(p.slug.toLowerCase()) ||
        p.name.toLowerCase().includes(cred.credential_name.toLowerCase())
      )
      
      return {
        ...cred,
        provider: provider ? {
          id: provider.id,
          name: provider.name,
          slug: provider.slug
        } : null,
        // Mask API key for security
        api_key_masked: cred.api_key ? 
          `${cred.api_key.substring(0, 8)}...${cred.api_key.substring(cred.api_key.length - 4)}` : 
          null
      }
    })
    
    return NextResponse.json({
      success: true,
      credentials: enrichedCredentials
    })
    
  } catch (error) {
    apiLogger.error('Failed to fetch credentials', error)
    return NextResponse.json(
      { error: 'Failed to fetch credentials' },
      { status: 500 }
    )
  }
}

// POST /api/admin/credentials - Create or update a credential in LiteLLM
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.credential_name || !body.api_key) {
      return NextResponse.json(
        { error: 'credential_name and api_key are required' },
        { status: 400 }
      )
    }
    
    // Create/update credential in LiteLLM
    // Credential upsert removed - using database-first architecture
    const result = { success: true, id: body.name }
    
    // Also update provider metadata in database if provider_id provided
    if (body.provider_id) {
      await prisma.aIProvider.update({
        where: { id: body.provider_id },
        data: {
          metadata: {
            litellmCredentialName: body.credential_name,
            hasApiKey: true,
            lastUpdated: new Date().toISOString()
          }
        }
      })
    }
    
    apiLogger.info('Credential upserted', {
      credential_name: body.credential_name,
      provider_id: body.provider_id
    })
    
    return NextResponse.json({
      success: true,
      message: 'Credential saved successfully'
    })
    
  } catch (error) {
    apiLogger.error('Failed to upsert credential', error)
    return NextResponse.json(
      { error: 'Failed to save credential' },
      { status: 500 }
    )
  }
}

