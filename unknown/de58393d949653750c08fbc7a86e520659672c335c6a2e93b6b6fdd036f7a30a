import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'

// GET /api/admin/providers - Get all providers with model counts
export async function GET(request: NextRequest) {
  try {
    const providers = await prisma.providers.findMany({
      include: {
        _count: {
          select: {
            models: true
          }
        },
        models: {
          where: { isEnabled: true },
          select: {
            id: true,
            canonicalName: true,
            isEnabled: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })
    
    // Transform providers with additional stats
    const providersWithStats = providers.map((provider: any) => ({
      id: provider.id,
      name: provider.name,
      slug: provider.slug,
      description: provider.description,
      websiteUrl: provider.websiteUrl,
      docsUrl: provider.docsUrl,
      apiDocsUrl: provider.apiDocsUrl,
      statusPageUrl: provider.statusPageUrl,
      logoUrl: provider.logoUrl,
      auth_type: provider.authType,
      auth_config: provider.authConfig,
      base_url: provider.baseUrl,
      features: provider.features,
      isActive: provider.isActive,
      isVerified: provider.isVerified,
      lastVerifiedAt: provider.lastVerifiedAt,
      metadata: provider.metadata,
      createdAt: provider.createdAt,
      updatedAt: provider.updatedAt,
      modelCount: provider._count.models,
      stats: {
        totalModels: provider._count.models,
        enabledModels: provider.models.filter((m: any) => m.isEnabled).length,
        disabledModels: provider.models.filter((m: any) => !m.isEnabled).length
      }
    }))
    
    return NextResponse.json({
      success: true,
      providers: providersWithStats
    })
    
  } catch (error) {
    apiLogger.error('Failed to fetch providers', error)
    return NextResponse.json(
      { error: 'Failed to fetch providers' },
      { status: 500 }
    )
  }
}

// POST /api/admin/providers - Create a new provider
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Check if provider already exists
    const existing = await prisma.providers.findUnique({
      where: { slug: body.slug }
    })
    
    if (existing) {
      return NextResponse.json(
        { error: 'Provider with this slug already exists' },
        { status: 400 }
      )
    }
    
    const provider = await prisma.providers.create({
      data: {
        name: body.name,
        slug: body.slug,
        package: body.package || 'unknown',
        configKeys: body.configKeys || {},
        isAiSdk: body.isAiSdk ?? true,
        capabilities: body.capabilities || {},
        baseUrl: body.baseUrl,
        authHeader: body.authHeader || 'Authorization',
        authPrefix: body.authPrefix || 'Bearer',
        rateLimit: body.rateLimit,
        isActive: body.isActive ?? true
      }
    })
    
    apiLogger.info('Provider created', {
      providerId: provider.id,
      name: provider.name,
      slug: provider.slug
    })
    
    return NextResponse.json({
      success: true,
      provider
    })
    
  } catch (error) {
    apiLogger.error('Failed to create provider', error)
    return NextResponse.json(
      { error: 'Failed to create provider' },
      { status: 500 }
    )
  }
}