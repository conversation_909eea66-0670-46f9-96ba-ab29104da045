import { NextRequest, NextResponse } from 'next/server'
import { apiLogger } from '@/lib/logger'
import { modelTester } from '@/lib/ai/model-tester'

// POST /api/admin/credentials/test - Test a credential
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { credential_name, test_model } = body
    
    if (!credential_name) {
      return NextResponse.json(
        { error: 'credential_name is required' },
        { status: 400 }
      )
    }
    
    // Check provider health to validate credentials
    const providerHealth = await modelTester.getProviderHealth()
    
    // Map credential names to provider names
    const credentialToProvider: Record<string, string> = {
      'openai-api-key': 'openai',
      'anthropic-api-key': 'anthropic', 
      'google-api-key': 'google',
      'groq-api-key': 'groq',
      'mistral-api-key': 'mistral',
      'deepseek-api-key': 'deepseek',
      'xai-api-key': 'xai',
      'cohere-api-key': 'cohere',
      'perplexity-api-key': 'perplexity',
      'openrouter': 'openrouter',
      'qwen-api-key': 'qwen'
    }
    
    const providerName = credentialToProvider[credential_name.toLowerCase()]
    
    if (!providerName) {
      return NextResponse.json(
        { error: `Unknown credential type: ${credential_name}` },
        { status: 400 }
      )
    }
    
    // Determine test model based on provider
    let modelToTest = test_model
    if (!modelToTest) {
      const testModels: Record<string, string> = {
        'openai': 'openai/gpt-4o-mini',
        'anthropic': 'anthropic/claude-3-haiku-20240307',
        'google': 'google/gemini-1.5-flash',
        'groq': 'groq/llama3-8b-8192',
        'mistral': 'mistral/mistral-small-latest',
        'deepseek': 'deepseek/deepseek-chat',
        'xai': 'xai/grok-3-mini',
        'cohere': 'cohere/command-r',
        'perplexity': 'perplexity/llama-3.1-sonar-small-128k-online',
        'openrouter': 'openrouter/google/gemini-flash-1.5',
        'qwen': 'qwen/qwen-turbo'
      }
      
      modelToTest = testModels[providerName]
      
      if (!modelToTest) {
        return NextResponse.json(
          { error: `No test model configured for provider: ${providerName}` },
          { status: 400 }
        )
      }
    }
    
    // Test the credential by making a simple request
    const testResult = await modelTester.testModel(
      modelToTest,
      'Test prompt: Please respond with "OK" if you receive this.'
    )
    
    apiLogger.info('Credential tested', {
      credential_name,
      model: modelToTest,
      success: testResult.success
    })
    
    return NextResponse.json({
      success: testResult.success,
      credential_name,
      model_tested: modelToTest,
      latency: testResult.latency,
      error: testResult.error
    })
    
  } catch (error) {
    apiLogger.error('Failed to test credential', error)
    return NextResponse.json(
      { error: 'Failed to test credential' },
      { status: 500 }
    )
  }
}