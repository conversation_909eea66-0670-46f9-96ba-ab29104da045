'use client'

import React, { useState, useEffect } from 'react'
import { 
  Database, Plus, Search, Filter, Edit3, Trash2, 
  Toggle<PERSON>eft, ToggleRight, Save, X, <PERSON>ert<PERSON>ircle,
  ChevronDown, ChevronUp, ChevronRight, Copy, RefreshCw, Settings,
  DollarSign, Zap, Brain, Globe, FileText, Clock,
  CheckCircle, XCircle, AlertTriangle
} from 'lucide-react'
import { format } from 'date-fns'
import { UserPlan } from '@/types'
interface AIModel {
  id: string
  providerId: string
  providerName: string
  canonicalName: string
  displayName: string
  description?: string
  family: string
  generation?: string
  modelType: 'TEXT_GENERATION' | 'CHAT' | 'COMPLETION' | 'EMBEDDING' | 'IMAGE_GENERATION' | 'AUDIO_GENERATION' | 'VIDEO_GENERATION' | 'CODE_GENERATION' | 'MULTIMODAL'
  isEnabled: boolean
  disabledReason?: string
  disabledAt?: string
  disabledBy?: string
  extendedMetadata?: any
  
  // Capabilities
  capabilities: {
    vision: boolean
    functionCalling: boolean
    toolUse: boolean
    webSearch: boolean
    streaming: boolean
    jsonMode: boolean
    codeExecution: boolean
  }
  
  // Context & Limits
  contextWindow: number
  maxOutputTokens: number
  optimalContextTokens?: number
  
  // Pricing
  pricing: {
    inputCostPer1M: number
    outputCostPer1M: number
    cachedInputCostPer1M?: number
    batchInputCostPer1M?: number
    trainingCostPer1M?: number
  }
  
  // Performance
  performance: {
    ttftMs: number
    tokensPerSecond: number
    intelligenceScore: number
    costScore: number
    speedScore: number
  }
  
  // Plan Access
  planAccess: {
    [UserPlan.FREE]: boolean
    [UserPlan.FREEMIUM]: boolean
    [UserPlan.PLUS]: boolean
    [UserPlan.ADVANCED]: boolean
    [UserPlan.MAX]: boolean
    [UserPlan.ENTERPRISE]: boolean
  }
  
  // Router Configuration
  routerConfig: {
    priority: number
    routingGroup?: string
    categoryScores: Record<string, number>
    specialization?: string[]
  }
  
  // Metadata
  metadata: {
    releaseDate?: string
    deprecationDate?: string
    replacedBy?: string
    documentationUrl?: string
    changelogUrl?: string
    tags: string[]
  }
  
  createdAt: string
  updatedAt: string
}

interface ModelFormData extends Partial<AIModel> {
  // For creating new models
  providerSlug?: string
}

const MODEL_TYPES = [
  { value: 'CHAT', label: 'Chat', icon: '💬' },
  { value: 'TEXT_GENERATION', label: 'Text Generation', icon: '📝' },
  { value: 'CODE_GENERATION', label: 'Code', icon: '👨‍💻' },
  { value: 'MULTIMODAL', label: 'Multimodal', icon: '🧠' },
  { value: 'IMAGE_GENERATION', label: 'Image', icon: '🖼️' },
  { value: 'AUDIO_GENERATION', label: 'Audio', icon: '🎵' },
  { value: 'EMBEDDING', label: 'Embedding', icon: '🔢' }
]

const CAPABILITY_ICONS = {
  vision: { icon: '👁️', label: 'Vision' },
  functionCalling: { icon: '🔧', label: 'Functions' },
  toolUse: { icon: '🛠️', label: 'Tools' },
  webSearch: { icon: '🌐', label: 'Web Search' },
  streaming: { icon: '📡', label: 'Streaming' },
  jsonMode: { icon: '{}', label: 'JSON Mode' },
  codeExecution: { icon: '▶️', label: 'Code Exec' }
}

export default function ModelManagementPage() {
  const [models, setModels] = useState<AIModel[]>([])
  const [providers, setProviders] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [showDisabled, setShowDisabled] = useState(true)
  const [editingModel, setEditingModel] = useState<AIModel | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState<ModelFormData>({})
  const [expandedModels, setExpandedModels] = useState<Set<string>>(new Set())
  const [savingModel, setSavingModel] = useState(false)
  const [deletingModel, setDeletingModel] = useState<string | null>(null)
  
  useEffect(() => {
    fetchData()
  }, [])
  
  const fetchData = async () => {
    setLoading(true)
    try {
      const [modelsRes, providersRes] = await Promise.all([
        fetch('/api/admin/models'),
        fetch('/api/admin/providers')
      ])
      
      if (!modelsRes.ok) {
        throw new Error(`Failed to fetch models: ${modelsRes.status} ${modelsRes.statusText}`)
      }
      if (!providersRes.ok) {
        throw new Error(`Failed to fetch providers: ${providersRes.status} ${providersRes.statusText}`)
      }
      
      const modelsData = await modelsRes.json()
      const providersData = await providersRes.json()
      
      setModels(modelsData.models || [])
      setProviders(providersData.providers || [])
    } catch (error) {
      console.error('Failed to fetch data:', error)
      alert('Failed to fetch models. Please refresh the page.')
    } finally {
      setLoading(false)
    }
  }
  
  const filteredModels = models.filter(model => {
    if (!showDisabled && !model.isEnabled) return false
    if (selectedProvider !== 'all' && model.providerId !== selectedProvider) return false
    if (selectedType !== 'all' && model.modelType !== selectedType) return false
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        model.displayName?.toLowerCase().includes(query) ||
        model.canonicalName?.toLowerCase().includes(query) ||
        model.description?.toLowerCase().includes(query) ||
        model.family?.toLowerCase().includes(query) ||
        model.providerName?.toLowerCase().includes(query)
      )
    }
    return true
  })
  
  const handleCreateModel = () => {
    setIsCreating(true)
    setEditingModel(null)
    setFormData({
      modelType: 'CHAT',
      isEnabled: true,
      capabilities: {
        vision: false,
        functionCalling: false,
        toolUse: false,
        webSearch: false,
        streaming: true,
        jsonMode: false,
        codeExecution: false
      },
      contextWindow: 8192,
      maxOutputTokens: 4096,
      pricing: {
        inputCostPer1M: 0,
        outputCostPer1M: 0
      },
      performance: {
        ttftMs: 500,
        tokensPerSecond: 50,
        intelligenceScore: 50,
        costScore: 50,
        speedScore: 50
      },
      planAccess: {
        [UserPlan.FREE]: false,
        [UserPlan.FREEMIUM]: true,
        [UserPlan.PLUS]: true,
        [UserPlan.ADVANCED]: true,
        [UserPlan.MAX]: true,
        [UserPlan.ENTERPRISE]: true
      },
      routerConfig: {
        priority: 100,
        categoryScores: {}
      },
      metadata: {
        tags: []
      }
    })
  }
  
  const handleEditModel = (model: AIModel) => {
    setEditingModel(model)
    setIsCreating(false)
    // Find the provider slug from the providers list
    const provider = providers.find(p => p.id === model.providerId)
    setFormData({
      ...model,
      providerSlug: provider?.slug || model.providerId
    })
  }
  
  const handleSaveModel = async () => {
    setSavingModel(true)
    try {
      const url = isCreating ? '/api/admin/models' : `/api/admin/models/${editingModel?.id}`
      const method = isCreating ? 'POST' : 'PUT'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (!response.ok) throw new Error('Failed to save model')
      
      await fetchData()
      setEditingModel(null)
      setIsCreating(false)
      setFormData({})
    } catch (error) {
      console.error('Failed to save model:', error)
      alert('Failed to save model. Please try again.')
    } finally {
      setSavingModel(false)
    }
  }
  
  const handleDeleteModel = async (modelId: string) => {
    if (!confirm('Are you sure you want to delete this model? This action cannot be undone.')) {
      return
    }
    
    setDeletingModel(modelId)
    try {
      const response = await fetch(`/api/admin/models/${modelId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) throw new Error('Failed to delete model')
      
      await fetchData()
    } catch (error) {
      console.error('Failed to delete model:', error)
      alert('Failed to delete model. Please try again.')
    } finally {
      setDeletingModel(null)
    }
  }
  
  const handleToggleModel = async (model: AIModel) => {
    try {
      const response = await fetch(`/api/admin/models/${model.id}/toggle`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          isEnabled: !model.isEnabled,
          disabledReason: !model.isEnabled ? null : 'Manually disabled via admin'
        })
      })
      
      if (!response.ok) throw new Error('Failed to toggle model')
      
      await fetchData()
    } catch (error) {
      console.error('Failed to toggle model:', error)
    }
  }
  
  const toggleExpandModel = (modelId: string) => {
    const newExpanded = new Set(expandedModels)
    if (newExpanded.has(modelId)) {
      newExpanded.delete(modelId)
    } else {
      newExpanded.add(modelId)
    }
    setExpandedModels(newExpanded)
  }
  
  const updateFormField = (field: string, value: any) => {
    setFormData(prev => {
      const keys = field.split('.')
      const newData = { ...prev }
      let current: any = newData
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {}
        current = current[keys[i]]
      }
      
      current[keys[keys.length - 1]] = value
      return newData
    })
  }
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-white">Model Management</h1>
          <p className="text-gray-300 mt-1">
            Configure AI models, capabilities, pricing, and access controls
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={fetchData}
            className="flex items-center gap-2 px-4 py-2 bg-gray-800 border rounded-lg hover:bg-gray-700"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button
            onClick={handleCreateModel}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Model
          </button>
        </div>
      </div>
      
      {/* Filters */}
      <div className="bg-gray-800 p-4 rounded-lg border">
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search models..."
              className="w-full pl-10 pr-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={selectedProvider}
            onChange={(e) => setSelectedProvider(e.target.value)}
            className="px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white"
          >
            <option value="all">All providers</option>
            {providers.map(provider => (
              <option key={provider.id} value={provider.id}>{provider.name}</option>
            ))}
          </select>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white"
          >
            <option value="all">All types</option>
            {MODEL_TYPES.map(type => (
              <option key={type.value} value={type.value}>
                {type.icon} {type.label}
              </option>
            ))}
          </select>
          
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={showDisabled}
              onChange={(e) => setShowDisabled(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-300">Show disabled</span>
          </label>
        </div>
      </div>
      
      {/* Stats */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-gray-800 p-4 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Total Models</span>
            <Database className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold">{models.length}</div>
          <div className="text-xs text-gray-500 mt-1">
            {models.filter(m => m.isEnabled).length} enabled
          </div>
        </div>
        
        <div className="bg-gray-800 p-4 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Providers</span>
            <Globe className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold">{providers.length}</div>
          <div className="text-xs text-gray-500 mt-1">Active providers</div>
        </div>
        
        <div className="bg-gray-800 p-4 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Model Types</span>
            <Brain className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold">{MODEL_TYPES.length}</div>
          <div className="text-xs text-gray-500 mt-1">Categories</div>
        </div>
        
        <div className="bg-gray-800 p-4 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Avg Cost</span>
            <DollarSign className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold">
            ${(models.reduce((sum: number, m: any) => sum + (m.pricing?.inputCostPer1M || 0), 0) / models.length).toFixed(2)}
          </div>
          <div className="text-xs text-gray-500 mt-1">Per 1M tokens</div>
        </div>
      </div>
      
      {/* Models List */}
      <div className="bg-gray-800 rounded-lg border">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[1200px]">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 w-64">Model</th>
                <th className="text-left p-3 w-24">Type</th>
                <th className="text-left p-3 w-32">Capabilities</th>
                <th className="text-center p-3 w-20">Context</th>
                <th className="text-center p-3 w-24">Pricing</th>
                <th className="text-center p-3 w-20">Plans</th>
                <th className="text-center p-3 w-24">Status</th>
                <th className="text-right p-3 w-24">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredModels.length === 0 ? (
                <tr>
                  <td colSpan={8} className="p-8 text-center text-gray-400">
                    {models.length === 0 ? 'No models found' : 'No models match your filters'}
                  </td>
                </tr>
              ) : (
                filteredModels.map(model => (
                  <React.Fragment key={model.id}>
                    <tr className="border-b hover:bg-gray-700">
                      <td className="p-3">
                        <div 
                          className="cursor-pointer"
                          onClick={() => toggleExpandModel(model.id)}
                        >
                          <div className="flex items-center gap-2">
                            <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform flex-shrink-0 ${
                              expandedModels.has(model.id) ? 'rotate-90' : ''
                            }`} />
                            <div className="min-w-0">
                              <div className="font-medium text-white truncate">{model.displayName}</div>
                              <div className="text-sm text-gray-500 truncate">{model.providerName} • {model.family}</div>
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      <td className="p-3">
                        <span className="text-xs">
                          {MODEL_TYPES.find(t => t.value === model.modelType)?.icon} {model.modelType}
                        </span>
                      </td>
                      
                      <td className="p-3">
                        <div className="flex gap-1 flex-wrap">
                          {Object.entries(model.capabilities || {}).slice(0, 4).map(([key, enabled]) => 
                            enabled && CAPABILITY_ICONS[key as keyof typeof CAPABILITY_ICONS] ? (
                              <span 
                                key={key}
                                className="text-xs px-1 py-0.5 bg-blue-800 text-blue-300 rounded"
                                title={CAPABILITY_ICONS[key as keyof typeof CAPABILITY_ICONS].label}
                              >
                                {CAPABILITY_ICONS[key as keyof typeof CAPABILITY_ICONS].icon}
                              </span>
                            ) : null
                          )}
                        </div>
                      </td>
                      
                      <td className="p-3 text-center">
                        <div className="text-xs">
                          {(model.contextWindow / 1000).toFixed(0)}k
                        </div>
                        <div className="text-xs text-gray-500">
                          {(model.maxOutputTokens / 1000).toFixed(0)}k
                        </div>
                      </td>
                      
                      <td className="p-3 text-center">
                        <div className="text-xs">
                          ${model.pricing?.inputCostPer1M?.toFixed(1) || '0'}
                        </div>
                        <div className="text-xs text-gray-500">
                          ${model.pricing?.outputCostPer1M?.toFixed(1) || '0'}
                        </div>
                      </td>
                      
                      <td className="p-3">
                        <div className="flex justify-center gap-0.5">
                          {Object.entries(model.planAccess || {}).map(([plan, enabled]) => (
                            <span
                              key={plan}
                              className={`w-5 h-5 rounded text-xs flex items-center justify-center ${
                                enabled ? 'bg-green-800 text-green-300' : 'bg-gray-700 text-gray-400'
                              }`}
                              title={plan}
                            >
                              {plan.charAt(0)}
                            </span>
                          ))}
                        </div>
                      </td>
                      
                      <td className="p-3 text-center">
                        <button
                          onClick={() => handleToggleModel(model)}
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                            model.isEnabled 
                              ? 'bg-green-800 text-green-300' 
                              : 'bg-red-800 text-red-300'
                          }`}
                        >
                          {model.isEnabled ? (
                            <>
                              <CheckCircle className="w-3 h-3" />
                              On
                            </>
                          ) : (
                            <>
                              <XCircle className="w-3 h-3" />
                              Off
                            </>
                          )}
                        </button>
                      </td>
                      
                      <td className="p-3">
                        <div className="flex justify-end gap-1">
                          <button
                            onClick={() => handleEditModel(model)}
                            className="p-1 text-gray-300 hover:text-blue-600"
                            title="Edit model"
                          >
                            <Edit3 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => navigator.clipboard.writeText(model.id)}
                            className="p-1 text-gray-300 hover:text-white"
                            title="Copy ID"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteModel(model.id)}
                            disabled={deletingModel === model.id}
                            className="p-1 text-gray-300 hover:text-red-600 disabled:opacity-50"
                            title="Delete model"
                          >
                            {deletingModel === model.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-600 border-t-transparent" />
                            ) : (
                              <Trash2 className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  
                  {/* Expanded Details */}
                  {expandedModels.has(model.id) && (
                    <tr className="bg-gray-800">
                      <td colSpan={8} className="p-4">
                        <div className="grid grid-cols-3 gap-6">
                          {/* Performance Metrics */}
                          <div>
                            <h4 className="font-medium text-white mb-3">Performance Metrics</h4>
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-300">Intelligence Score</span>
                                <div className="flex items-center gap-2">
                                  <div className="w-24 bg-gray-200 rounded-full h-2">
                                    <div 
                                      className="bg-blue-600 h-2 rounded-full" 
                                      style={{ width: `${model.performance?.intelligenceScore || 0}%` }}
                                    />
                                  </div>
                                  <span className="font-medium">{model.performance?.intelligenceScore || 0}</span>
                                </div>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-300">Cost Score</span>
                                <div className="flex items-center gap-2">
                                  <div className="w-24 bg-gray-200 rounded-full h-2">
                                    <div 
                                      className="bg-green-600 h-2 rounded-full" 
                                      style={{ width: `${model.performance?.costScore || 0}%` }}
                                    />
                                  </div>
                                  <span className="font-medium">{model.performance?.costScore || 0}</span>
                                </div>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-300">Speed Score</span>
                                <div className="flex items-center gap-2">
                                  <div className="w-24 bg-gray-200 rounded-full h-2">
                                    <div 
                                      className="bg-yellow-600 h-2 rounded-full" 
                                      style={{ width: `${model.performance?.speedScore || 0}%` }}
                                    />
                                  </div>
                                  <span className="font-medium">{model.performance?.speedScore || 0}</span>
                                </div>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-300">TTFT</span>
                                <span className="font-medium">{model.performance?.ttftMs || 0}ms</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-300">Tokens/sec</span>
                                <span className="font-medium">{model.performance?.tokensPerSecond || 0}</span>
                              </div>
                            </div>
                          </div>
                          
                          {/* Router Configuration */}
                          <div>
                            <h4 className="font-medium text-white mb-3">Router Configuration</h4>
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-300">Priority</span>
                                <span className="font-medium">{model.routerConfig?.priority || 100}</span>
                              </div>
                              {model.routerConfig?.routingGroup && (
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-300">Routing Group</span>
                                  <span className="font-medium">{model.routerConfig.routingGroup}</span>
                                </div>
                              )}
                              {model.routerConfig?.specialization && model.routerConfig.specialization.length > 0 && (
                                <div className="text-sm">
                                  <span className="text-gray-300">Specializations:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {model.routerConfig.specialization.map(spec => (
                                      <span key={spec} className="px-2 py-0.5 bg-purple-800 text-purple-300 rounded text-xs">
                                        {spec}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          {/* Metadata */}
                          <div>
                            <h4 className="font-medium text-white mb-3">Additional Info</h4>
                            <div className="space-y-2 text-sm">
                              {model.description && (
                                <div>
                                  <span className="text-gray-300">Description:</span>
                                  <p className="text-gray-800 mt-1">{model.description}</p>
                                </div>
                              )}
                              {model.metadata?.releaseDate && (
                                <div className="flex justify-between">
                                  <span className="text-gray-300">Released</span>
                                  <span>{format(new Date((model.extendedMetadata as any)?.releaseDate), 'MMM d, yyyy')}</span>
                                </div>
                              )}
                              {model.metadata?.tags && (model.extendedMetadata as any)?.tags?.length > 0 && (
                                <div>
                                  <span className="text-gray-300">Tags:</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {(model.extendedMetadata as any)?.tags?.map((tag: any) => (
                                      <span key={tag} className="px-2 py-0.5 bg-gray-700 text-gray-300 rounded text-xs">
                                        {tag}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}
                              <div className="flex justify-between">
                                <span className="text-gray-300">Created</span>
                                <span>{format(new Date(model.createdAt), 'MMM d, yyyy')}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Edit/Create Modal */}
      {(editingModel || isCreating) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold">
                  {isCreating ? 'Create New Model' : 'Edit Model'}
                </h2>
                <button
                  onClick={() => {
                    setEditingModel(null)
                    setIsCreating(false)
                    setFormData({})
                  }}
                  className="text-gray-400 hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <div className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h3 className="font-medium text-white mb-4">Basic Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Provider
                      </label>
                      <select
                        value={formData.providerSlug || formData.providerId || ''}
                        onChange={(e) => updateFormField('providerSlug', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="">Select provider...</option>
                        {providers.map(provider => (
                          <option key={provider.id} value={provider.slug}>
                            {provider.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Canonical Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.canonicalName || ''}
                        onChange={(e) => updateFormField('canonicalName', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., gpt-4o"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Display Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.displayName || ''}
                        onChange={(e) => updateFormField('displayName', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., GPT-4o"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Model Type <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={formData.modelType || 'CHAT'}
                        onChange={(e) => updateFormField('modelType', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        {MODEL_TYPES.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.icon} {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Family <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={formData.family || ''}
                        onChange={(e) => updateFormField('family', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., GPT-4"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Generation
                      </label>
                      <input
                        type="text"
                        value={formData.generation || ''}
                        onChange={(e) => updateFormField('generation', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g., Turbo"
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Description
                      </label>
                      <textarea
                        value={formData.description || ''}
                        onChange={(e) => updateFormField('description', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={2}
                        placeholder="Brief description of the model..."
                      />
                    </div>
                  </div>
                </div>
                
                {/* Capabilities */}
                <div>
                  <h3 className="font-medium text-white mb-4">Capabilities</h3>
                  <div className="grid grid-cols-4 gap-4">
                    {Object.entries(CAPABILITY_ICONS).map(([key, config]) => (
                      <label key={key} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={formData.capabilities?.[key as keyof typeof CAPABILITY_ICONS] || false}
                          onChange={(e) => updateFormField(`capabilities.${key}`, e.target.checked)}
                          className="rounded"
                        />
                        <span className="text-sm">
                          {config.icon} {config.label}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
                
                {/* Context & Limits */}
                <div>
                  <h3 className="font-medium text-white mb-4">Context & Limits</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Context Window <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        value={formData.contextWindow || 8192}
                        onChange={(e) => updateFormField('contextWindow', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={1}
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Max Output Tokens <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        value={formData.maxOutputTokens || 4096}
                        onChange={(e) => updateFormField('maxOutputTokens', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={1}
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Optimal Context
                      </label>
                      <input
                        type="number"
                        value={formData.optimalContextTokens || ''}
                        onChange={(e) => updateFormField('optimalContextTokens', e.target.value ? parseInt(e.target.value) : null)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={1}
                        placeholder="Optional"
                      />
                    </div>
                  </div>
                </div>
                
                {/* Pricing */}
                <div>
                  <h3 className="font-medium text-white mb-4">Pricing (per 1M tokens)</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Input Cost <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input
                          type="number"
                          value={formData.pricing?.inputCostPer1M || 0}
                          onChange={(e) => updateFormField('pricing.inputCostPer1M', parseFloat(e.target.value))}
                          className="w-full pl-8 pr-3 py-2 border rounded-lg"
                          min={0}
                          step={0.01}
                          required
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Output Cost <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input
                          type="number"
                          value={formData.pricing?.outputCostPer1M || 0}
                          onChange={(e) => updateFormField('pricing.outputCostPer1M', parseFloat(e.target.value))}
                          className="w-full pl-8 pr-3 py-2 border rounded-lg"
                          min={0}
                          step={0.01}
                          required
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Cached Input Cost
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input
                          type="number"
                          value={formData.pricing?.cachedInputCostPer1M || ''}
                          onChange={(e) => updateFormField('pricing.cachedInputCostPer1M', e.target.value ? parseFloat(e.target.value) : null)}
                          className="w-full pl-8 pr-3 py-2 border rounded-lg"
                          min={0}
                          step={0.01}
                          placeholder="Optional"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Batch Input Cost
                      </label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input
                          type="number"
                          value={formData.pricing?.batchInputCostPer1M || ''}
                          onChange={(e) => updateFormField('pricing.batchInputCostPer1M', e.target.value ? parseFloat(e.target.value) : null)}
                          className="w-full pl-8 pr-3 py-2 border rounded-lg"
                          min={0}
                          step={0.01}
                          placeholder="Optional"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Performance */}
                <div>
                  <h3 className="font-medium text-white mb-4">Performance Metrics</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Time to First Token (ms)
                      </label>
                      <input
                        type="number"
                        value={formData.performance?.ttftMs || 500}
                        onChange={(e) => updateFormField('performance.ttftMs', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={0}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Tokens per Second
                      </label>
                      <input
                        type="number"
                        value={formData.performance?.tokensPerSecond || 50}
                        onChange={(e) => updateFormField('performance.tokensPerSecond', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={1}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Intelligence Score (0-100)
                      </label>
                      <input
                        type="number"
                        value={formData.performance?.intelligenceScore || 50}
                        onChange={(e) => updateFormField('performance.intelligenceScore', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={0}
                        max={100}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Cost Score (0-100)
                      </label>
                      <input
                        type="number"
                        value={formData.performance?.costScore || 50}
                        onChange={(e) => updateFormField('performance.costScore', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={0}
                        max={100}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Speed Score (0-100)
                      </label>
                      <input
                        type="number"
                        value={formData.performance?.speedScore || 50}
                        onChange={(e) => updateFormField('performance.speedScore', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={0}
                        max={100}
                      />
                    </div>
                  </div>
                </div>
                
                {/* Plan Access */}
                <div>
                  <h3 className="font-medium text-white mb-4">Plan Access</h3>
                  <div className="grid grid-cols-4 gap-4">
                    {Object.values(UserPlan).map(plan => (
                      <label key={plan} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={formData.planAccess?.[plan] || false}
                          onChange={(e) => updateFormField(`planAccess.${plan}`, e.target.checked)}
                          className="rounded"
                        />
                        <span className="text-sm">{plan}</span>
                      </label>
                    ))}
                  </div>
                </div>
                
                {/* Router Configuration */}
                <div>
                  <h3 className="font-medium text-white mb-4">Router Configuration</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Priority (0-1000)
                      </label>
                      <input
                        type="number"
                        value={formData.routerConfig?.priority || 100}
                        onChange={(e) => updateFormField('routerConfig.priority', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min={0}
                        max={1000}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Routing Group
                      </label>
                      <input
                        type="text"
                        value={formData.routerConfig?.routingGroup || ''}
                        onChange={(e) => updateFormField('routerConfig.routingGroup', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Optional grouping"
                      />
                    </div>
                  </div>
                </div>
                
                {/* Status */}
                <div>
                  <h3 className="font-medium text-white mb-4">Status</h3>
                  <div className="space-y-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.isEnabled !== false}
                        onChange={(e) => updateFormField('isEnabled', e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">Model is enabled</span>
                    </label>
                    
                    {!formData.isEnabled && (
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">
                          Disabled Reason
                        </label>
                        <input
                          type="text"
                          value={formData.disabledReason || ''}
                          onChange={(e) => updateFormField('disabledReason', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Why is this model disabled?"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-6 border-t bg-gray-800">
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setEditingModel(null)
                    setIsCreating(false)
                    setFormData({})
                  }}
                  className="px-4 py-2 border rounded-lg hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveModel}
                  disabled={savingModel}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {savingModel ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      {isCreating ? 'Create Model' : 'Save Changes'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}