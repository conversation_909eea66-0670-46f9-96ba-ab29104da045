import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'

// POST /api/admin/router/config/import - Import router configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { config, options = {} } = body
    const {
      overwriteExisting = true,
      preserveIds = false,
      backupBeforeImport = true
    } = options
    
    const results = {
      imported: {
        categories: 0,
        mappings: 0,
        models: 0,
        providers: 0,
        systemConfig: 0
      },
      errors: [] as string[],
      backupId: null as string | null
    }
    
    // Create backup if requested
    if (backupBeforeImport) {
      try {
        const backupId = `backup_${Date.now()}`
        
        // Create backup records (simplified - in production you'd create full backup)
        await prisma.systemConfig.upsert({
          where: { key: `backup_metadata_${backupId}` },
          create: {
            key: `backup_metadata_${backupId}`,
            value: {
              timestamp: new Date().toISOString(),
              reason: 'Pre-import backup',
              importedBy: 'admin'
            }
          },
          update: {}
        })
        
        results.backupId = backupId
      } catch (error) {
        results.errors.push('Failed to create backup - import aborted')
        return NextResponse.json(results, { status: 500 })
      }
    }
    
    // Use transaction for atomic imports
    const importResult = await prisma.$transaction(async (tx: any) => {
      const transactionResults = { ...results }
      
      // Import providers first (dependencies)
      if (config.providers) {
        for (const providerData of config.providers) {
          try {
            await tx.aIProvider.upsert({
              where: { slug: providerData.slug },
              create: {
                ...providerData,
                id: preserveIds ? providerData.id : undefined
              },
              update: overwriteExisting ? {
                name: providerData.name,
                description: providerData.description,
                isActive: providerData.isActive,
                authType: providerData.authType,
                authConfig: providerData.authConfig,
                baseUrl: providerData.baseUrl,
                features: providerData.features,
                metadata: providerData.metadata
              } : {}
            })
            transactionResults.imported.providers++
          } catch (error) {
            transactionResults.errors.push(`Failed to import provider: ${providerData.name}`)
          }
        }
      }
      
      // Import categories
      if (config.categories) {
        for (const categoryData of config.categories) {
          try {
            await tx.promptCategory.upsert({
              where: { name: categoryData.name },
              create: {
                ...categoryData,
                id: preserveIds ? categoryData.id : undefined
              },
              update: overwriteExisting ? {
                description: categoryData.description,
                examples: categoryData.examples,
                keywords: categoryData.keywords,
                complexity: categoryData.complexity,
                isActive: categoryData.isActive,
                metadata: categoryData.metadata
              } : {}
            })
            transactionResults.imported.categories++
          } catch (error) {
            transactionResults.errors.push(`Failed to import category: ${categoryData.name}`)
          }
        }
      }
      
      // Import models
      if (config.models) {
        for (const modelData of config.models) {
          try {
            // Find or create provider
            const provider = await tx.aIProvider.findFirst({
              where: { 
                OR: [
                  { slug: modelData.provider.slug },
                  { name: modelData.provider.name }
                ]
              }
            })
            
            if (!provider) {
              transactionResults.errors.push(`Provider not found for model: ${modelData.canonicalName}`)
              continue
            }
            
            // Create or update model
            const model = await tx.aIModel.upsert({
              where: { 
                providerId_canonicalName: {
                  providerId: provider.id,
                  canonicalName: modelData.canonicalName
                }
              },
              create: {
                ...modelData,
                id: preserveIds ? modelData.id : undefined,
                providerId: provider.id,
                provider: undefined // Remove nested provider data
              },
              update: overwriteExisting ? {
                displayName: modelData.displayName,
                description: modelData.description,
                family: modelData.family,
                generation: modelData.generation,
                modelType: modelData.modelType,
                isEnabled: modelData.isEnabled,
                metadata: modelData.metadata
              } : {}
            })
            
            // Import model versions and endpoints
            if (modelData.versions) {
              for (const versionData of modelData.versions) {
                const version = await tx.modelVersion.upsert({
                  where: {
                    modelId_version: {
                      modelId: model.id,
                      version: versionData.generation
                    }
                  },
                  create: {
                    ...versionData,
                    id: preserveIds ? versionData.id : undefined,
                    modelId: model.id,
                    endpoints: undefined
                  },
                  update: overwriteExisting ? {
                    releaseDate: versionData.releaseDate,
                    deprecatedDate: versionData.deprecatedDate
                  } : {}
                })
                
                // Import endpoints
                if (versionData.endpoints) {
                  for (const endpointData of versionData.endpoints) {
                    await tx.aIEndpoint.upsert({
                      where: {
                        versionId_region_sku: {
                          versionId: version.id,
                          region: endpointData.region || 'global',
                          sku: endpointData.sku || null
                        }
                      },
                      create: {
                        ...endpointData,
                        id: preserveIds ? endpointData.id : undefined,
                        versionId: version.id
                      },
                      update: overwriteExisting ? endpointData : {}
                    })
                  }
                }
              }
            }
            
            transactionResults.imported.models++
          } catch (error) {
            transactionResults.errors.push(`Failed to import model: ${modelData.canonicalName}`)
          }
        }
      }
      
      // Import model mappings
      if (config.modelMappings) {
        for (const mappingData of config.modelMappings) {
          try {
            // Find model and category
            const model = await tx.models.findFirst({
              where: { canonicalName: mappingData.model?.canonicalName }
            })
            const category = await tx.promptCategory.findUnique({
              where: { name: mappingData.category?.name }
            })
            
            if (!model || !category) {
              transactionResults.errors.push(`Missing model or category for mapping`)
              continue
            }
            
            await tx.modelMapping.upsert({
              where: {
                category_modelId_complexityLevel: {
                  category: mappingData.category?.name,
                  modelId: model.id,
                  complexityLevel: mappingData.complexityLevel || 'all'
                }
              },
              create: {
                ...mappingData,
                id: preserveIds ? mappingData.id : undefined,
                modelId: model.id,
                categoryId: category.id,
                model: undefined,
                category: undefined
              },
              update: overwriteExisting ? {
                score: mappingData.score || 70,
                complexityLevel: mappingData.complexityLevel || 'all',
                specificAttributes: mappingData.specificAttributes || null
              } : {}
            })
            transactionResults.imported.mappings++
          } catch (error) {
            transactionResults.errors.push(`Failed to import mapping`)
          }
        }
      }
      
      // Import Thompson Sampling configuration
      if (config.thompsonSampling) {
        try {
          await tx.systemConfig.upsert({
            where: { key: 'thompson_sampling' },
            create: {
              key: 'thompson_sampling',
              value: config.thompsonSampling,
              description: 'Thompson Sampling configuration (imported)'
            },
            update: overwriteExisting ? {
              value: config.thompsonSampling
            } : {}
          })
          transactionResults.imported.systemConfig++
        } catch (error) {
          transactionResults.errors.push('Failed to import Thompson Sampling configuration')
        }
      }
      
      // Import other system configurations
      if (config.systemConfig) {
        for (const configData of config.systemConfig) {
          try {
            await tx.systemConfig.upsert({
              where: { key: configData.key },
              create: configData,
              update: overwriteExisting ? {
                value: configData.value,
                description: configData.description
              } : {}
            })
            transactionResults.imported.systemConfig++
          } catch (error) {
            transactionResults.errors.push(`Failed to import system config: ${configData.key}`)
          }
        }
      }
      
      return transactionResults
    })
    
    apiLogger.info('Configuration imported', {
      imported: importResult.imported,
      errors: importResult.errors.length,
      backupId: importResult.backupId
    })
    
    return NextResponse.json(importResult)
    
  } catch (error) {
    apiLogger.error('Failed to import configuration', error)
    return NextResponse.json(
      { 
        error: 'Failed to import configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}