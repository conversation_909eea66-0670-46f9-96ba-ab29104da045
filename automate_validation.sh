#!/bin/bash
# Automated V6.9.1 Router-Optimized Model Validation via Claude Code
# Executes one model validation with database integration every 5 minutes via systemd

set -euo pipefail

# Configuration
PROJECT_ROOT="/home/<USER>/deployments/dev/simplechat-ai"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
LOG_FILE="/home/<USER>/deployments/logs/validation_${TIMESTAMP}_$$.log"
MAIN_LOG="/home/<USER>/deployments/logs/validation_automation.log"
MAX_DURATION=7200  # 120 minutes max per task (very generous timeout for thorough validation)

# Ensure logs directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE" -a "$MAIN_LOG"
}

# Failsafe: Check for too many concurrent instances
RUNNING_COUNT=$(ps aux | grep -c "automate_validation.sh" | grep -v grep || echo "0")
MAX_CONCURRENT=20

if [ "$RUNNING_COUNT" -gt "$MAX_CONCURRENT" ]; then
    log "🚨 FAILSAFE: Too many concurrent instances ($RUNNING_COUNT > $MAX_CONCURRENT). Aborting."
    exit 1
fi

# No lock file needed - we support up to 20 concurrent instances
# Just log that we're starting
log "INFO: Starting instance (PID: $$)"

log "🚀 Starting automated model validation"
log "📁 Detailed log file: $LOG_FILE"

# Change to project directory
cd "$PROJECT_ROOT" || {
    log "ERROR: Cannot change to project directory $PROJECT_ROOT"
    exit 1
}

# Get next pending task using Task Master AI
log "📋 Getting next pending validation task..."

# Read the V6.9.1 methodology prompt
PROMPT_FILE="$PROJECT_ROOT/.taskmaster/docs/ULTIMATE_SUBAGENT_PROMPT_V6.9.1_CRITICAL_FIXES.md"

if [ ! -f "$PROMPT_FILE" ]; then
    log "ERROR: Prompt file not found: $PROMPT_FILE"
    exit 1
fi

log "📋 Reading V6.9.1 methodology from: $PROMPT_FILE"

# Create the automation-specific prompt
AUTOMATION_PROMPT="
🤖 AUTOMATED V6.9.1 ROUTER-OPTIMIZED SINGLE MODEL VALIDATION

You are running automated validation for EXACTLY ONE MODEL using the V6.9.1 methodology.

📁 YOUR LOG FILE: $LOG_FILE

🚨 AUTOMATION CONSTRAINTS:
1. Process EXACTLY ONE MODEL ONLY via database query - no batches
2. Use 120-minute maximum execution time for thorough work
3. Use COMPREHENSIVE MCP research - multiple turns until ALL fields verified
4. Exit immediately after completing one model

📋 FOLLOW THE COMPLETE V6.9.1 METHODOLOGY:
Please read and follow the complete methodology file that follows this message.

🎯 FINAL OUTPUT REQUIREMENT:
At the end, provide the log file path: $LOG_FILE

---

$(cat "$PROMPT_FILE")
"

# Log start of Claude execution
log "📊 Launching Claude with ${MAX_DURATION}s timeout..."
START_TIME=$(date +%s)

# Use timeout with kill-after for graceful shutdown
# --preserve-status keeps original exit code
# -k 60s gives 60 seconds for cleanup after initial signal
# CRITICAL: Use proper MCP permissions, generous timeouts, and specific model for thorough validation
env BASH_DEFAULT_TIMEOUT_MS=3600000 BASH_MAX_TIMEOUT_MS=3600000 BASH_MAX_OUTPUT_LENGTH=200000 MCP_TIMEOUT=3600000 MCP_TOOL_TIMEOUT=3600000 timeout -k 60s --preserve-status $MAX_DURATION claude --model claude-sonnet-4-20250514 --dangerously-skip-permissions --allowedTools "*:*" -p "$AUTOMATION_PROMPT" 2>&1 | while IFS= read -r line; do
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $line" >> "$LOG_FILE"
    echo "$line"
done

CLAUDE_EXIT_CODE=${PIPESTATUS[0]}  # Get exit code from timeout command, not while loop

# Calculate execution time
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
DURATION_MIN=$((DURATION / 60))
DURATION_SEC=$((DURATION % 60))

if [ $CLAUDE_EXIT_CODE -eq 124 ]; then
    log "⏰ Validation task timed out after ${DURATION_MIN}m ${DURATION_SEC}s"
    log "🔄 Timeout handling: Task will remain in-progress for manual review"
elif [ $CLAUDE_EXIT_CODE -eq 0 ]; then
    log "✅ Validation task completed successfully in ${DURATION_MIN}m ${DURATION_SEC}s"
else
    log "❌ Validation task failed with exit code $CLAUDE_EXIT_CODE after ${DURATION_MIN}m ${DURATION_SEC}s"
    log "🔄 Error handling: Task will remain in-progress for manual review"
    
    # Log error details for debugging
    if [ -f "/tmp/claude_error_$$.log" ]; then
        log "📋 Error details logged to /tmp/claude_error_$$.log"
    fi
fi

log "🏁 Automation cycle complete (PID: $$)"
log "📄 COMPLETE VALIDATION LOG: $LOG_FILE"
log "📋 View detailed results: cat $LOG_FILE"

# For timeouts and errors, exit with 0 to prevent systemd from considering it a failure
# This allows the automation to continue with the next task
if [ $CLAUDE_EXIT_CODE -eq 124 ] || [ $CLAUDE_EXIT_CODE -ne 0 ]; then
    log "🎯 Automation will continue with next task (error handling mode)"
    exit 0
else
    exit $CLAUDE_EXIT_CODE
fi