require('dotenv').config();

async function testGoogleModels() {
  console.log('🔍 Checking Google Generative AI Models...\n');
  
  const apiKey = process.env.GOOGLE_API_KEY;
  if (!apiKey) {
    console.error('❌ GOOGLE_API_KEY not found in environment');
    return;
  }

  try {
    // Test 1: List models via Generative AI API
    console.log('📋 Fetching models from Google Generative AI API...');
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
      headers: {
        'x-goog-api-key': apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('\n✅ Available models:');
    
    const models = data.models || [];
    models.forEach(model => {
      console.log(`\n📦 ${model.name}`);
      console.log(`   Display: ${model.displayName}`);
      console.log(`   Description: ${model.description || 'N/A'}`);
      console.log(`   Supported Methods: ${model.supportedGenerationMethods?.join(', ') || 'N/A'}`);
      console.log(`   Input Token Limit: ${model.inputTokenLimit || 'N/A'}`);
      console.log(`   Output Token Limit: ${model.outputTokenLimit || 'N/A'}`);
      console.log(`   Supported Types: ${model.supportedMimeTypes?.join(', ') || 'text only'}`);
    });
    
    // Group by category
    console.log('\n📊 Models by Category:');
    
    const textModels = models.filter(m => m.supportedGenerationMethods?.includes('generateContent'));
    const embeddingModels = models.filter(m => m.supportedGenerationMethods?.includes('embedContent'));
    const codeModels = models.filter(m => m.supportedGenerationMethods?.includes('generateCode'));
    
    console.log(`\n💬 Text Generation (${textModels.length} models):`);
    textModels.forEach(m => console.log(`   - ${m.name.replace('models/', '')}: ${m.displayName}`));
    
    console.log(`\n🔢 Embeddings (${embeddingModels.length} models):`);
    embeddingModels.forEach(m => console.log(`   - ${m.name.replace('models/', '')}: ${m.displayName}`));
    
    if (codeModels.length > 0) {
      console.log(`\n💻 Code Generation (${codeModels.length} models):`);
      codeModels.forEach(m => console.log(`   - ${m.name.replace('models/', '')}: ${m.displayName}`));
    }

    // Test 2: Check specific model details
    console.log('\n\n🔍 Testing specific model access...');
    const testModel = 'gemini-1.5-flash';
    const modelResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${testModel}`, {
      headers: {
        'x-goog-api-key': apiKey
      }
    });

    if (modelResponse.ok) {
      const modelData = await modelResponse.json();
      console.log(`\n✅ Model ${testModel} details:`);
      console.log(JSON.stringify(modelData, null, 2));
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testGoogleModels();