'use client';

import { usePathname } from 'next/navigation';
import { ConversationSidebar } from '@/components/sidebar/ConversationSidebar';
import { Header } from '@/components/layout/Header';
import { useConversationStore } from '@/stores/conversationStore';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const pathname = usePathname();
  const { sidebarCollapsed, sidebarWidth } = useConversationStore();
  const [isClient, setIsClient] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
    
    // Check if mobile on mount and window resize
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);
  
  // Determine if sidebar should be shown - keep consistent for SSR
  const showSidebar = pathname === '/' || pathname === '/chat' || pathname.startsWith('/chat?');
  
  // Debug logging for scrolling issues
  useEffect(() => {
    console.log(`[MainLayout] pathname: ${pathname}, showSidebar: ${showSidebar}, isMobile: ${isMobile}`);
  }, [pathname, showSidebar, isMobile]);
  
  // Calculate main content styles - only apply after client mount to avoid hydration mismatch
  const getMainStyles = () => {
    if (!isClient || !showSidebar || isMobile) return {};
    
    // Only apply margin on desktop (lg and above) and after client mount
    return {
      marginLeft: sidebarCollapsed ? '64px' : `${sidebarWidth}px`,
      transition: 'margin-left 0.3s ease-in-out',
    };
  };

  return (
    <div className="relative min-h-screen bg-black">
      {/* Header - fixed at top */}
      <Header />
      
      {/* Sidebar */}
      {showSidebar && <ConversationSidebar />}
      
      {/* Main Content - positioned below header with proper top spacing */}
      <main 
        className={cn(
          "relative",
          // Different behavior for chat pages vs other pages
          showSidebar ? [
            // Chat pages: fixed positioning to fill viewport
            "fixed top-16 bottom-0 left-0 right-0",
            "overflow-hidden"
          ] : [
            // Other pages: normal flow for scrolling
            "pt-16",
            "min-h-[calc(100vh-4rem)]"
          ],
          // Remove extra padding that was causing space
          // Use suppressHydrationWarning on dynamic classes that differ between server/client
          (!showSidebar || isMobile) && "left-0 right-0"
        )}
        style={getMainStyles()}
        suppressHydrationWarning
      >
        <div className={cn(
          showSidebar ? "h-full flex flex-col" : "min-h-full overflow-y-auto"
        )}>
          {children}
        </div>
      </main>
    </div>
  );
}