'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Crown, 
  Zap, 
  DollarSign, 
  Clock, 
  Brain, 
  CheckCircle2,
  TrendingUp,
  Gauge
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Model } from '@/types'

interface ModelRecommendationsProps {
  models: Model[]
  task: string
  industry: string
  industryColor: string
}

interface ModelFeature {
  icon: React.ComponentType<{ className?: string }>
  label: string
  getValue: (model: Model) => string | number | boolean
  format?: (value: any) => string
  priority?: 'high' | 'medium' | 'low'
}

const modelFeatures: ModelFeature[] = [
  {
    icon: Clock,
    label: 'Speed',
    getValue: (m) => m.processingSpeed || 'Standard',
    priority: 'high'
  },
  {
    icon: Brain,
    label: 'Context',
    getValue: (m) => m.contextLength || 0,
    format: (v) => v ? `${(v / 1000).toFixed(0)}K tokens` : 'N/A',
    priority: 'high'
  },
  {
    icon: DollarSign,
    label: 'Input Cost',
    getValue: (m) => m.inputPricePerMillion,
    format: (v) => `$${v}/M`,
    priority: 'medium'
  },
  {
    icon: TrendingUp,
    label: 'Output Cost',
    getValue: (m) => m.outputPricePerMillion,
    format: (v) => `$${v}/M`,
    priority: 'medium'
  }
]

const getModelRecommendation = (model: Model, task: string, industry: string) => {
  const taskLower = task.toLowerCase()
  const modelName = model.name.toLowerCase()
  
  // Task-specific recommendations
  if (taskLower.includes('code') || taskLower.includes('debug') || taskLower.includes('programming')) {
    if (modelName.includes('claude')) return { reason: 'Excellent at understanding code context and providing detailed explanations', score: 95 }
    if (modelName.includes('deepseek')) return { reason: 'Specialized for algorithmic thinking and complex problem solving', score: 90 }
    if (modelName.includes('qwen') && modelName.includes('coder')) return { reason: 'Purpose-built for code generation and review', score: 88 }
  }
  
  if (taskLower.includes('research') || taskLower.includes('analysis')) {
    if (modelName.includes('perplexity')) return { reason: 'Real-time web search with accurate citations', score: 95 }
    if (modelName.includes('claude')) return { reason: 'Superior at analyzing long documents and extracting insights', score: 90 }
    if (modelName.includes('gpt-4')) return { reason: 'Comprehensive reasoning and synthesis capabilities', score: 85 }
  }
  
  if (taskLower.includes('writing') || taskLower.includes('content')) {
    if (modelName.includes('gpt-4')) return { reason: 'Creative and engaging writing with natural flow', score: 95 }
    if (modelName.includes('claude')) return { reason: 'Excellent for long-form content and maintaining consistency', score: 90 }
    if (modelName.includes('gemini')) return { reason: 'Great for multimodal content with images and text', score: 85 }
  }
  
  if (taskLower.includes('business') || taskLower.includes('report') || taskLower.includes('meeting')) {
    if (modelName.includes('claude')) return { reason: 'Professional tone and excellent document summarization', score: 95 }
    if (modelName.includes('gpt-4')) return { reason: 'Business-oriented responses with clear structure', score: 90 }
    if (modelName.includes('mistral')) return { reason: 'Efficient and cost-effective for business workflows', score: 85 }
  }
  
  // Default recommendation
  return { reason: `Strong general capabilities for ${task.toLowerCase()}`, score: 75 }
}

export function ModelRecommendations({ models, task, industry, industryColor }: ModelRecommendationsProps) {
  const [selectedModel, setSelectedModel] = useState<Model | null>(models[0] || null)
  
  // Sort models by recommendation score
  const sortedModels = models
    .map(model => ({
      ...model,
      recommendation: getModelRecommendation(model, task, industry)
    }))
    .sort((a, b) => b.recommendation.score - a.recommendation.score)
  
  const topModel = sortedModels[0]
  
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">
          Best AI Models for {task}
        </h2>
        <p className="text-lg text-gray-400 max-w-2xl mx-auto">
          Our AI analyzed performance data and user feedback to recommend the optimal models for your {task.toLowerCase()} workflow
        </p>
      </div>
      
      {/* Top Recommendation */}
      {topModel && (
        <Card className={cn(
          "relative overflow-hidden border-2 bg-gray-900/80 backdrop-blur-xl",
          "border-yellow-600/50 shadow-xl shadow-yellow-600/20"
        )}>
          <div className="absolute top-4 right-4">
            <Badge className="bg-yellow-600/20 text-yellow-300 border-yellow-600/40 flex items-center gap-1">
              <Crown className="w-4 h-4" />
              Top Choice
            </Badge>
          </div>
          
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl text-white mb-2">{topModel.name}</CardTitle>
                <CardDescription className="text-lg">
                  {topModel.recommendation.reason}
                </CardDescription>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-yellow-400 mb-1">
                  {topModel.recommendation.score}%
                </div>
                <div className="text-sm text-gray-400">Match Score</div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {modelFeatures.map((feature, index) => {
                const Icon = feature.icon
                const value = feature.getValue(topModel)
                const displayValue = feature.format ? feature.format(value) : value?.toString() || 'N/A'
                
                return (
                  <div key={index} className="flex items-center gap-2">
                    <Icon className="w-4 h-4 text-yellow-400" />
                    <div>
                      <div className="text-sm text-gray-400">{feature.label}</div>
                      <div className="font-semibold text-white">{displayValue}</div>
                    </div>
                  </div>
                )
              })}
            </div>
            
            <div className="mt-6 flex flex-wrap gap-2">
              {topModel.capabilities?.map((capability, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {capability}
                </Badge>
              )) || []}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* All Model Recommendations */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedModels.slice(1).map((model, index) => (
          <Card 
            key={model.id} 
            className={cn(
              "cursor-pointer transition-all duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800",
              "hover:border-gray-700 hover:bg-gray-900/80",
              selectedModel?.id === model.id && "border-purple-600/50 bg-purple-900/20"
            )}
            onClick={() => setSelectedModel(model)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-1">{model.name}</CardTitle>
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="outline" className="text-xs">
                      {model.provider}
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs",
                        model.tier === 'pro' && "border-yellow-600/40 text-yellow-300",
                        model.tier === 'enterprise' && "border-purple-600/40 text-purple-300"
                      )}
                    >
                      {model.tier}
                    </Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {model.recommendation.reason}
                  </CardDescription>
                </div>
                <div className="text-right ml-4">
                  <div className="text-xl font-bold text-purple-400">
                    {model.recommendation.score}%
                  </div>
                  <div className="text-xs text-gray-400">Match</div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                {modelFeatures.slice(0, 2).map((feature, featureIndex) => {
                  const Icon = feature.icon
                  const value = feature.getValue(model)
                  const displayValue = feature.format ? feature.format(value) : value?.toString() || 'N/A'
                  
                  return (
                    <div key={featureIndex} className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-sm text-gray-400">
                        <Icon className="w-4 h-4" />
                        {feature.label}
                      </div>
                      <div className="font-medium text-white text-sm">{displayValue}</div>
                    </div>
                  )
                })}
              </div>
              
              {/* Key capabilities */}
              <div className="mt-4 flex flex-wrap gap-1">
                {model.capabilities?.slice(0, 3).map((capability, capIndex) => (
                  <Badge key={capIndex} variant="outline" className="text-xs">
                    {capability}
                  </Badge>
                )) || []}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Performance Comparison */}
      <Card className="bg-gray-900/60 backdrop-blur-xl border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="w-6 h-6 text-purple-400" />
            Performance Comparison for {task}
          </CardTitle>
          <CardDescription>
            Based on benchmarks and user feedback from {industry.toLowerCase()}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedModels.slice(0, 5).map((model, index) => (
              <div key={model.id} className="flex items-center gap-4">
                <div className="w-8 text-center">
                  <span className={cn(
                    "text-lg font-bold",
                    index === 0 && "text-yellow-400",
                    index === 1 && "text-gray-300",
                    index === 2 && "text-orange-400",
                    index > 2 && "text-gray-500"
                  )}>
                    #{index + 1}
                  </span>
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-white">{model.name}</span>
                    <span className="text-sm text-gray-400">{model.recommendation.score}%</span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-2">
                    <div 
                      className={cn(
                        "h-2 rounded-full transition-all duration-1000",
                        index === 0 && "bg-gradient-to-r from-yellow-600 to-yellow-400",
                        index === 1 && "bg-gradient-to-r from-gray-600 to-gray-400",
                        index === 2 && "bg-gradient-to-r from-orange-600 to-orange-400",
                        index > 2 && "bg-gradient-to-r from-gray-700 to-gray-600"
                      )}
                      style={{ width: `${model.recommendation.score}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-600/20">
            <div className="flex items-start gap-3">
              <CheckCircle2 className="w-5 h-5 text-purple-400 mt-0.5" />
              <div>
                <p className="text-sm text-white font-medium mb-1">
                  Smart Routing Enabled
                </p>
                <p className="text-xs text-gray-400">
                  JustSimpleChat automatically selects the best model for each task. 
                  You get optimal results without manually choosing models.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}