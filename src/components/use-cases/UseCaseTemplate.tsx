'use client'

import Link from 'next/link'
import { 
  ArrowRight, 
  Sparkles, 
  CheckCircle2, 
  Star,
  Users,
  Clock,
  Zap,
  Trophy,
  TrendingUp,
  Brain,
  Rocket,
  Shield
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Markdown } from '@/components/Markdown'
import { ModelRecommendations } from './ModelRecommendations'
import { FAQ } from '@/components/seo/FAQ'
import { cn } from '@/lib/utils'
import type { Model } from '@/types'
import { industryMap, type IndustryData, type TaskData } from '@/data/industry-map'

interface UseCaseTemplateProps {
  industry: { name: string; color: string }
  task: TaskData
  models: Model[]
  content: string
  faqItems: Array<{ question: string; answer: string }>
  industryKey: string
  taskKey: string
}

export function UseCaseTemplate({
  industry,
  task,
  models,
  content,
  faqItems,
  industryKey,
  taskKey
}: UseCaseTemplateProps) {
  // Get the icon from the industry map using the industryKey
  const industryData = industryMap[industryKey as keyof typeof industryMap]
  const Icon = industryData?.icon || Brain
  
  const benefits = [
    {
      icon: Clock,
      title: 'Save 5+ Hours/Week',
      description: `Automate repetitive ${task.name.toLowerCase()} tasks and focus on high-value work`
    },
    {
      icon: TrendingUp,
      title: '90% Faster Results',
      description: `Get ${task.name.toLowerCase()} done in minutes instead of hours with AI assistance`
    },
    {
      icon: Brain,
      title: 'Best-in-Class Models',
      description: `Access to ${models.length}+ specialized models including GPT-4, Claude, and Gemini`
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'SOC2 compliant with end-to-end encryption for all your sensitive data'
    }
  ]
  
  const competitors = [
    { name: 'ChatGPT Plus', price: '$20/month', models: '1 model', limitations: 'GPT-4 only' },
    { name: 'Claude Pro', price: '$20/month', models: '1 model', limitations: 'Claude only' },
    { name: 'Gemini Advanced', price: '$20/month', models: '1 model', limitations: 'Gemini only' },
    { name: 'JustSimpleChat', price: 'From $7.99', models: `${models.length}+ models`, limitations: 'All models included' }
  ]
  
  return (
    <div className="min-h-screen bg-gray-950">
      {/* Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-purple-900/20 via-gray-950 to-blue-900/20 pointer-events-none" />
      <div className="fixed inset-0 bg-[url('/grid.svg')] bg-center opacity-10 pointer-events-none" />
      
      {/* Floating Particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className={cn(
          "absolute w-96 h-96 rounded-full blur-3xl animate-float top-20 -left-48 bg-gradient-to-r opacity-20",
          industry.color
        )} />
        <div className="absolute w-96 h-96 bg-blue-600/20 rounded-full blur-3xl animate-float animation-delay-2000 bottom-20 -right-48" />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Link href="/" className="hover:text-white transition-colors">Home</Link>
            <span>/</span>
            <Link href="/use-cases" className="hover:text-white transition-colors">Use Cases</Link>
            <span>/</span>
            <Link href={`/use-cases/${industryKey}`} className="hover:text-white transition-colors">{industry.name}</Link>
            <span>/</span>
            <span className="text-white">{task.name}</span>
          </div>
        </nav>

        {/* Hero Section */}
        <header className="text-center mb-16 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 blur-3xl opacity-20 -z-10" />
          
          {/* Industry Icon */}
          <div className="inline-flex items-center justify-center mb-8 relative">
            <div className="relative">
              <div className={cn(
                "absolute inset-0 bg-gradient-to-r rounded-2xl blur-xl opacity-50",
                industry.color
              )} />
              <div className="relative bg-gray-900 rounded-2xl p-6 border border-gray-800">
                <Icon className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
          
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-xl text-purple-300 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-purple-600/30">
            <Sparkles className="w-4 h-4" />
            AI for {industry.name}
            <Badge className="bg-green-600/20 text-green-300 border-green-600/30 ml-2">
              Proven Results
            </Badge>
          </div>
          
          <h1 className="text-5xl sm:text-6xl font-bold mb-6">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
              AI-Powered
            </span>
            <br />
            <span className="text-white">
              {task.name} for {industry.name}
            </span>
          </h1>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
            {task.description} using the world&apos;s best AI models. 
            Get professional results in minutes with GPT-4, Claude 3.5, Gemini, and {models.length}+ specialized models.
          </p>
          
          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center gap-6 mb-12">
            <div className="flex items-center gap-2 text-gray-400">
              <Users className="w-5 h-5 text-purple-400" />
              <span>Used by 10K+ {industry.name}</span>
            </div>
            <div className="flex items-center gap-2 text-gray-400">
              <Star className="w-5 h-5 text-yellow-400" />
              <span>4.9/5 Industry Rating</span>
            </div>
            <div className="flex items-center gap-2 text-gray-400">
              <Trophy className="w-5 h-5 text-green-400" />
              <span>Best AI Platform 2025</span>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup">
              <Button 
                size="lg" 
                className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-6 text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
              >
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="#models">
              <Button 
                size="lg" 
                variant="outline" 
                className="w-full sm:w-auto border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-8 py-6 text-lg rounded-xl"
              >
                View Best Models
              </Button>
            </Link>
          </div>
        </header>

        {/* Benefits Grid */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            Why {industry.name} Choose JustSimpleChat for {task.name}
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => {
              const BenefitIcon = benefit.icon
              return (
                <Card key={index} className="group hover:scale-105 transition-transform duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-purple-600/50">
                  <CardHeader>
                    <div className={cn(
                      "p-3 rounded-xl w-fit mb-4 bg-gradient-to-r",
                      industry.color
                    )}>
                      <BenefitIcon className="w-8 h-8 text-white" />
                    </div>
                    <CardTitle className="text-xl">{benefit.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-400">
                      {benefit.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </section>

        {/* Model Recommendations */}
        <section id="models" className="mb-16">
          <ModelRecommendations 
            models={models} 
            task={task.name} 
            industry={industry.name}
            industryColor={industry.color}
          />
        </section>

        {/* AI-Generated Content */}
        <section className="mb-16">
          <Card className="bg-gray-900/60 backdrop-blur-xl border-gray-800">
            <CardHeader>
              <CardTitle className="text-3xl flex items-center gap-3">
                <div className={cn(
                  "p-2 rounded-lg bg-gradient-to-r",
                  industry.color
                )}>
                  <Brain className="w-8 h-8 text-white" />
                </div>
                Complete Guide: {task.name} for {industry.name}
              </CardTitle>
              <CardDescription className="text-lg text-gray-400">
                Step-by-step workflows, best practices, and proven prompts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose prose-invert prose-lg max-w-none">
                <Markdown content={content} />
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Comparison Table */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Compare AI Platforms for {task.name}
            </h2>
            <p className="text-lg text-gray-400">
              See why {industry.name} choose JustSimpleChat over single-model platforms
            </p>
          </div>
          
          <Card className="bg-gray-900/80 backdrop-blur-xl border-gray-800 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-800">
                    <th className="text-left p-6 text-gray-400 font-medium">Platform</th>
                    <th className="text-center p-6 text-gray-400 font-medium">Price</th>
                    <th className="text-center p-6 text-gray-400 font-medium">Models</th>
                    <th className="text-center p-6 text-gray-400 font-medium">Best For {task.name}</th>
                  </tr>
                </thead>
                <tbody>
                  {competitors.map((competitor, index) => (
                    <tr 
                      key={index} 
                      className={cn(
                        "border-b border-gray-800/50",
                        competitor.name === 'JustSimpleChat' && "bg-purple-900/20"
                      )}
                    >
                      <td className="p-6 font-semibold">
                        {competitor.name === 'JustSimpleChat' && (
                          <Badge className="bg-purple-600/20 text-purple-300 border-purple-600/40 mr-2">
                            Best Choice
                          </Badge>
                        )}
                        {competitor.name}
                      </td>
                      <td className="text-center p-6">{competitor.price}</td>
                      <td className="text-center p-6">{competitor.models}</td>
                      <td className="text-center p-6">
                        {competitor.name === 'JustSimpleChat' ? (
                          <span className="text-green-400 font-semibold">
                            All specialized models
                          </span>
                        ) : (
                          <span className="text-gray-400">{competitor.limitations}</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </section>

        {/* FAQ Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            Frequently Asked Questions
          </h2>
          <div className="max-w-3xl mx-auto">
            <FAQ items={faqItems} />
          </div>
        </section>

        {/* Related Use Cases */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            More AI Solutions for {industry.name}
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(industryData?.tasks || {})
              .filter(([key]) => key !== taskKey)
              .slice(0, 3)
              .map(([key, relatedTask]) => {
                const task = relatedTask as TaskData;
                return (
                <Link key={key} href={`/use-cases/${industryKey}/${key}`}>
                  <Card className="h-full bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-gray-700 transition-all group cursor-pointer">
                    <CardHeader>
                      <CardTitle className="text-lg group-hover:text-purple-400 transition-colors">
                        {task.name}
                      </CardTitle>
                      <CardDescription>
                        {task.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center text-sm text-purple-400 group-hover:text-purple-300 transition-colors">
                        <span>Learn more</span>
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
                );
              })}
          </div>
        </section>

        {/* CTA Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-10 blur-3xl" />
          <div className="relative bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-3xl p-1">
            <div className="bg-gray-900 rounded-3xl p-12 text-center">
              <div className="inline-flex items-center justify-center mb-6">
                <div className={cn(
                  "p-3 rounded-2xl bg-gradient-to-r",
                  industry.color
                )}>
                  <Rocket className="w-10 h-10 text-white" />
                </div>
              </div>
              
              <h2 className="text-4xl font-bold text-white mb-4">
                Ready to Transform Your {task.name} Workflow?
              </h2>
              
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Join 10,000+ {industry.name} who save 5+ hours per week with AI-powered {task.name.toLowerCase()}. 
                Try all models free for 3 days.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <Link href="/signup">
                  <Button 
                    size="lg" 
                    className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-10 py-7 text-xl rounded-xl shadow-2xl hover:shadow-3xl transition-all transform hover:scale-105"
                  >
                    Start Free Trial
                    <ArrowRight className="ml-3 h-6 w-6" />
                  </Button>
                </Link>
                <Link href="/pricing">
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="w-full sm:w-auto border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-10 py-7 text-xl rounded-xl"
                  >
                    View Pricing
                  </Button>
                </Link>
              </div>
              
              <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-400" />
                  <span>No credit card required</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-400" />
                  <span>All models included</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="w-4 h-4 text-green-400" />
                  <span>Cancel anytime</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}