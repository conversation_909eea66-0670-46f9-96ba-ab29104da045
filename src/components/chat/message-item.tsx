/**
 * MessageItem Component - Renders individual chat messages
 * 
 * @description
 * This component displays a single message in the chat interface, handling:
 * - User and assistant messages with different styles
 * - Streaming text with smooth animations
 * - O-series and thinking model reasoning displays
 * - Web search results and sources
 * - File attachments with icons
 * - Copy, feedback, and retry actions
 * - Model indicators and router decisions
 * 
 * @component
 */

'use client';

import { useState, useEffect, memo } from 'react';
import { Message, MessageRole, AttachmentType } from '@/types';
import { cn } from '@/lib/utils';
import { 
  User, 
  Bot, 
  Copy, 
  Check, 
  Volume2,
  RotateCcw,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  FileText,
  Image as ImageIcon,
  FileCode,
  FileAudio,
  FileVideo,
  File
} from 'lucide-react';
import { RouterCard } from './router-card';
import { ModelIndicator } from './model-indicator';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { CodeBlock, InlineCode } from './code-block';
import { UltraSmoothStreamingV2 } from './ultra-smooth-streaming-v2';
import { LoadingDots } from './loading-dots';
import { BubbleLoadingAnimation } from './BubbleLoadingAnimation';
import { WowLoadingDots } from './WowLoadingDots';
import { ThinkingDisplay } from './thinking-display';
import { EnhancedThinkingDisplay } from './enhanced-thinking-display';
import { UnifiedMessageDisplay } from './unified-message-display';
import { UniversalReasoningDisplay } from './universal-reasoning-display';
import { CondensedSources } from './condensed-sources';
import { supportsThinking, getThinkingConfig, getModelDisplayName, supportsWebSearch } from '@/lib/thinking-models';
import { isOSeriesModel } from '@/lib/ai/o-series-models';
import { SmoothStreamingContainer } from './smooth-streaming-container';
import { SmoothStreamingMessage } from './smooth-streaming-message';
import { EnhancedErrorDisplay } from './enhanced-error-display';
import { ImageDisplay } from './image-display';

/**
 * Props for the MessageItem component
 * 
 * @interface MessageItemProps
 * @property {Message} message - The message data to display
 * @property {boolean} [isStreaming] - Whether the message is currently streaming
 * @property {Function} [onRetry] - Callback to retry generating the message
 * @property {boolean} [webSearchActive] - Whether web search is active for this message
 * @property {string} [webSearchQuery] - The search query used (if any)
 * @property {string[]} [webSearchQueries] - Router-generated search queries
 * @property {number} [webSearchResults] - Number of search results found
 */
interface MessageItemProps {
  message: Message;
  isStreaming?: boolean;
  onRetry?: () => void;
  webSearchActive?: boolean;
  webSearchQuery?: string;
  webSearchQueries?: string[]; // Router-generated search queries
  webSearchResults?: number;
}

/**
 * MessageItem - A memoized component for rendering chat messages
 * 
 * @param {MessageItemProps} props - Component props
 * @returns {JSX.Element} The rendered message item
 */
export const MessageItem = memo(function MessageItem({ 
  message, 
  isStreaming = false, 
  onRetry,
  webSearchActive = false,
  webSearchQuery,
  webSearchQueries,
  webSearchResults
}: MessageItemProps) {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<'up' | 'down' | null>(null);
  const [streamingDuration, setStreamingDuration] = useState(0);
  
  // Track streaming duration
  useEffect(() => {
    if (isStreaming) {
      const startTime = Date.now();
      const interval = setInterval(() => {
        setStreamingDuration(Date.now() - startTime);
      }, 1000);
      
      return () => clearInterval(interval);
    }
  }, [isStreaming]);

  const isUser = message.role === MessageRole.USER;
  const isError = !!message.error;
  
  // Detect if this is a non-streaming message based on model or message properties
  const isNonStreamingModel = message.model && (
    message.model.includes('o1') || 
    message.model.includes('o3') ||
    message.streaming === false
  );

  async function copyToClipboard() {
    await navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  }

  // Extract images from content and return both content and images
  const extractImages = (content: string): { content: string; images: Array<{ src: string; alt: string }> } => {
    const images: Array<{ src: string; alt: string }> = [];
    
    // Extract [IMAGE_BASE64:data] format
    let processedContent = content.replace(/\[IMAGE_BASE64:([\s\S]*?)\]/g, (match, base64Data) => {
      const cleanedBase64 = base64Data.replace(/\s/g, '');
      console.log('[MessageItem] Processing base64 image, length:', cleanedBase64.length);
      const src = `data:image/png;base64,${cleanedBase64}`;
      images.push({ src, alt: 'Generated Image' });
      return `[IMAGE_PLACEHOLDER_${images.length - 1}]`;
    });
    
    // Extract markdown images
    processedContent = processedContent.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, src) => {
      if (src.startsWith('data:image') || src.startsWith('http')) {
        images.push({ src, alt: alt || 'Image' });
        return `[IMAGE_PLACEHOLDER_${images.length - 1}]`;
      }
      return match;
    });
    
    // Remove image placeholders from final content
    processedContent = processedContent.replace(/\[IMAGE_PLACEHOLDER_\d+\]/g, '');
    
    return { content: processedContent, images };
  };

  // Render message content with extracted images
  const renderMessageContent = () => {
    if (!message.content && isStreaming) {
      // Use modern animation for non-streaming messages, WowLoadingDots for streaming
      if (isNonStreamingModel) {
        // Cycle through different variants based on model or random
        const variants: Array<'quantum' | 'neural' | 'cosmic' | 'matrix' | 'pulse'> = 
          ['quantum', 'neural', 'cosmic', 'matrix', 'pulse'];
        const variantIndex = message.model ? 
          message.model.charCodeAt(0) % variants.length : 
          // Use message ID for deterministic variant selection
          (message.id ? message.id.charCodeAt(0) % variants.length : 0);
        
        return (
          <div className="py-2">
            <WowLoadingDots variant="quantum" size="md" />
          </div>
        );
      } else {
        return (
          <div className="py-2">
            <WowLoadingDots variant="quantum" size="md" />
          </div>
        );
      }
    }

    // Extract images from content
    const { content, images } = extractImages(message.content || '');

    if (message.streaming !== false && content.trim()) {
      return (
        <SmoothStreamingContainer isStreaming={isStreaming}>
          {/* Render extracted images first */}
          {images.map((image, index) => (
            <div key={index} className="mb-3">
              <ImageDisplay
                src={image.src}
                alt={image.alt}
                className="rounded-lg overflow-hidden shadow-lg"
              />
            </div>
          ))}
          
          <UltraSmoothStreamingV2
            content={content.trim()}
            isStreaming={isStreaming}
            className={cn(
              "prose-p:leading-relaxed prose-pre:p-0",
              "prose-headings:font-semibold",
              "assistant-message text-sm text-gray-200",
              isError && "text-red-400"
            )}
            speed="normal"
          />
        </SmoothStreamingContainer>
      );
    }

    return (
      <SmoothStreamingContainer isStreaming={false}>
        {/* Render extracted images first */}
        {images.map((image, index) => (
          <div key={index} className="mb-3">
            <ImageDisplay
              src={image.src}
              alt={image.alt}
              className="rounded-lg overflow-hidden shadow-lg"
            />
          </div>
        ))}
        
        {/* Only render markdown if there's content */}
        {content.trim() && (
          <div className={cn(
            "prose prose-invert max-w-none",
            "prose-p:leading-relaxed prose-pre:p-0",
            "prose-headings:font-semibold",
            "assistant-message text-sm text-gray-200",
            isError && "text-red-400"
          )}>
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                code({ node, inline, className, children, ...props }: any) {
                  const match = /language-(\w+)/.exec(className || '');
                  const filename = props['data-filename'] || undefined;
                  
                  return !inline && match ? (
                    <CodeBlock
                      language={match[1]}
                      value={String(children).replace(/\n$/, '')}
                      filename={filename}
                      showLineNumbers={true}
                    />
                  ) : (
                    <InlineCode>{children}</InlineCode>
                  );
                }
              }}
            >
              {content.trim()}
            </ReactMarkdown>
          </div>
        )}
      </SmoothStreamingContainer>
    );
  };

  return (
    <div className={cn(
      "group relative mb-3",
      isUser ? "flex justify-end" : "flex justify-start"
    )}>
      <div className={cn(
        "flex gap-3",
        // Responsive width constraints
        isUser ? "max-w-[85%] sm:max-w-[70%]" : 
        message.model && isOSeriesModel(message.model) ? "w-full" : "max-w-[95%] sm:max-w-[85%]",
        isUser && "flex-row-reverse"
      )}>
        {/* Avatar */}
        <div className="flex-shrink-0">
          {isUser ? (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-500 to-green-400 flex items-center justify-center shadow-lg ring-1 ring-emerald-500/50">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
          )}
        </div>

        {/* Content Container */}
        <div className={cn(
          "flex-1 space-y-1 min-w-0",
          // Ensure proper overflow handling
          "overflow-hidden"
        )}>
          
          {/* Enhanced Error Display for error messages */}
          {!isUser && (message.error || message.metadata?.errorDisplay) ? (
            <EnhancedErrorDisplay
              error={message.metadata?.errorDisplay?.error || message.error?.message || 'An error occurred'}
              errorCode={message.metadata?.errorDisplay?.errorCode || message.error?.code}
              onRetry={onRetry}
              model={message.metadata?.errorDisplay?.model || message.model}
              className="mb-2"
            />
          ) : !isUser && (message.reasoningContent || (isStreaming && message.model && supportsThinking(message.model))) ? (
            // Universal Reasoning Display - Works for ALL reasoning models with integrated message content
            <UniversalReasoningDisplay
              reasoning={message.reasoningContent || ""}
              isStreaming={isStreaming}
              modelName={message.model ? getModelDisplayName(message.model) || "" : ""}
              model={message.model || ""}
              provider={message.metadata?.routerDecision?.provider || ""}
              thinkingType={message.model ? getThinkingConfig(message.model)?.thinkingType || 'reasoning' : 'reasoning'}
              mainContentStarted={message.metadata?.mainContentStarted || false}
              mainContentStartTime={message.metadata?.mainContentStartTime}
            >
              {/* Condensed Sources */}
              {message.metadata?.searchResults && (
                <CondensedSources 
                  searchResults={message.metadata.searchResults}
                  className="mb-2"
                />
              )}
              
              {/* Message Content */}
              {renderMessageContent()}
            </UniversalReasoningDisplay>
          ) : !isUser && !message.content && isStreaming ? (
            // Show thinking animation for models without reasoning support
            <div className="relative">
              {isNonStreamingModel ? (
                <WowLoadingDots variant="quantum" size="md" />
              ) : (
                <BubbleLoadingAnimation duration={streamingDuration} />
              )}
            </div>
          ) : (
            <>
              {/* Model Indicator for non-reasoning models */}
              {!isUser && message.model && (!message.reasoningContent && !(isStreaming && supportsThinking(message.model))) && (
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-xs font-semibold text-gray-300 tracking-wide uppercase">
                    Assistant
                  </span>
                  <ModelIndicator 
                    model={message.model} 
                    provider={message.metadata?.routerDecision?.provider}
                    displayName={message.metadata?.routerDecision?.model?.name}
                  />
                  {isStreaming && (
                    <motion.span 
                      animate={{ opacity: [0.5, 1, 0.5] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className="text-xs text-gray-400"
                    >
                      Streaming...
                    </motion.span>
                  )}
                </div>
              )}
              
              {/* Display generated image above message if present */}
              {!isUser && message.metadata?.imageGeneration && message.metadata?.image && (() => {
                const imageSrc = message.metadata.image.type === 'base64' 
                  ? (message.metadata.image.data?.startsWith('data:') 
                      ? message.metadata.image.data  // Already has data URL prefix
                      : `data:image/webp;base64,${message.metadata.image.data}`) // Add WebP prefix
                  : message.metadata.image.url || '';
                
                // Only render if we have a valid src
                if (!imageSrc || imageSrc.trim() === '') {
                  return null;
                }
                
                return (
                  <div className="mb-3">
                    <ImageDisplay
                      src={imageSrc}
                      alt={`Generated image: ${message.metadata.image.prompt || 'AI generated'}`}
                      className="rounded-lg overflow-hidden shadow-lg"
                    />
                  
                    {/* Display prompt enhancement details if available */}
                    {(message.metadata.originalPrompt || message.metadata.enhancementReasoning) && (
                      <div className="mt-2 p-2 bg-gray-800/50 rounded-lg border border-gray-700 text-xs">
                        <div className="text-gray-400 mb-1">
                          <span className="font-medium">🎨 AI Enhanced Prompt</span>
                        </div>
                        {message.metadata.originalPrompt && (
                          <div className="mb-1">
                            <span className="text-gray-500">Original:</span> {message.metadata.originalPrompt}
                          </div>
                        )}
                        {message.metadata.enhancementReasoning && (
                          <div className="text-gray-400 italic">
                            {message.metadata.enhancementReasoning}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })()}
              
              {/* Standard Message Bubble */}
              <div className={cn(
                  "block rounded-2xl px-4 py-3",
                  // Ensure minimum dimensions to prevent squashing
                  "min-w-[120px] min-h-[2.5rem]",
                  // Responsive width and overflow handling
                  "w-full max-w-full overflow-hidden",
                  isUser 
                    ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white ml-auto" 
                    : "bg-gradient-to-r from-gray-800 to-gray-900 border border-gray-700",
                  // Smooth transitions for size changes
                  "transition-all duration-200 ease-out"
                )}>
                {/* User name for user messages */}
                {isUser && (
                  <div className="text-xs font-medium text-purple-100 mb-1">
                    You
                  </div>
                )}

                {/* Condensed Sources for Assistant Messages */}
                {!isUser && message.metadata?.searchResults && (
                  <CondensedSources 
                    searchResults={message.metadata.searchResults}
                    className="mb-2"
                  />
                )}

                {/* Message Content */}
                {!isUser && message.reasoningContent && message.model && supportsThinking(message.model) ? (
                  renderMessageContent()
                ) : !isUser && !message.content && isStreaming ? (
                  // Show thinking animation for models without reasoning support
                  <div className="relative">
                    {isNonStreamingModel ? (
                      <WowLoadingDots variant="quantum" size="md" />
                    ) : (
                      <BubbleLoadingAnimation />
                    )}
                  </div>
                ) : message.content ? (
                  renderMessageContent()
                ) : null}

                {/* Attachments */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {message.attachments.map((attachment) => {
                      const getAttachmentIcon = (type: AttachmentType) => {
                        switch (type) {
                          case AttachmentType.IMAGE:
                            return <ImageIcon className="w-4 h-4 text-green-400" />;
                          case AttachmentType.CODE:
                            return <FileCode className="w-4 h-4 text-purple-400" />;
                          case AttachmentType.AUDIO:
                            return <FileAudio className="w-4 h-4 text-orange-400" />;
                          case AttachmentType.VIDEO:
                            return <FileVideo className="w-4 h-4 text-red-400" />;
                          case AttachmentType.DOCUMENT:
                            return <FileText className="w-4 h-4 text-blue-400" />;
                          default:
                            return <File className="w-4 h-4 text-gray-400" />;
                        }
                      };
                      
                      return (
                        <div
                          key={attachment.id}
                          className="flex items-center gap-2 bg-gray-900 rounded-lg px-3 py-2 text-sm border border-gray-700"
                        >
                          {getAttachmentIcon(attachment.type)}
                          <span className="text-gray-200">{attachment.name}</span>
                          <span className="text-gray-500 text-xs">
                            ({(attachment.size / 1024).toFixed(1)}KB)
                          </span>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </>
          )}
          
          
          {/* Actions - Outside bubble */}
          {!isUser && !isStreaming && (
            <div className="flex items-center gap-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="ghost"
                onClick={copyToClipboard}
                className="h-7 px-2 text-gray-400 hover:text-gray-300"
              >
                {copied ? (
                  <Check className="w-3 h-3" />
                ) : (
                  <Copy className="w-3 h-3" />
                )}
              </Button>

              <Button
                size="sm"
                variant="ghost"
                className="h-7 px-2 text-gray-400 hover:text-gray-300"
              >
                <Volume2 className="w-3 h-3" />
              </Button>

              {onRetry && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-7 px-2 text-gray-400 hover:text-gray-300"
                  onClick={onRetry}
                  title="Retry with a different model"
                >
                  <RotateCcw className="w-3 h-3" />
                  <span className="ml-1 text-xs">Retry</span>
                </Button>
              )}

              <div className="ml-2 flex items-center gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setFeedback('up')}
                  className={cn(
                    "h-7 px-2",
                    feedback === 'up' ? "text-green-400" : "text-gray-400 hover:text-gray-300"
                  )}
                >
                  <ThumbsUp className="w-3 h-3" />
                </Button>

                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setFeedback('down')}
                  className={cn(
                    "h-7 px-2",
                    feedback === 'down' ? "text-red-400" : "text-gray-400 hover:text-gray-300"
                  )}
                >
                  <ThumbsDown className="w-3 h-3" />
                </Button>
              </div>
            </div>
          )}

          {/* Metadata - Commented out token and cost display
          {message.tokens && (
            <div className="flex items-center gap-3 text-xs text-gray-500">
              {message.tokens && (
                <span>
                  {message.tokens.total} tokens
                </span>
              )}
              {message.cost && (
                <span>
                  {message.cost} credits
                </span>
              )}
            </div>
          )}
          */}
        </div>
      </div>
    </div>
  );
});

