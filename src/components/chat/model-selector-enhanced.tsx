/**
 * Enhanced Model Selector Component
 * 
 * @description
 * A comprehensive model selection interface that allows users to:
 * - Browse 210+ AI models organized by provider
 * - Switch between automatic and manual model selection
 * - Filter models by capabilities (vision, code, reasoning)
 * - See model availability based on subscription tier
 * - Search for specific models
 * 
 * The component features:
 * - Provider grouping with custom icons
 * - Model capability badges
 * - Performance indicators (speed tiers)
 * - Subscription tier restrictions
 * - Smooth animations and transitions
 * 
 * @component
 */

'use client';

import { useState, useMemo, useEffect } from 'react'
import * as React from 'react'
import { createPortal } from 'react-dom'
import { Model, ModelCapability, UserPlan } from '@/types';
import { useModels } from '@/hooks/use-models';
import { useModelCount } from '@/hooks/useModelStats';
import { motion, AnimatePresence } from 'framer-motion';
import { groupModelsForDisplay } from '@/lib/ai/models/model-ordering-service';
import { getActualProvider, getProviderDisplayName, groupModelsByActualProvider, sortProviders, shouldHideProvider } from '@/lib/ai/models/provider-mapping';
import { 
  ChevronDown,
  CheckCircle2,
  Lock,
  Bot,
  Search,
  Zap,
  Clock,
  Brain,
  X,
  Code2,
  Sparkles,
  MessageSquare,
  Eye,
  TrendingUp,
  Cpu,
  Gauge,
  Globe,
  Star,
  Crown,
  Rocket,
  Filter,
  BrainCircuit,
  Code,
  ImageIcon,
  BookOpen
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { cleanModelName, getModelDisplayName } from '@/lib/utils/model-display';
import ProviderIcon from '@/components/ui/provider-icon';



interface ModelSelectorEnhancedProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  userPlan?: UserPlan;
  className?: string;
  isAutoMode?: boolean;
  onModeToggle?: (auto: boolean) => void;
}

// Provider brand colors and info
const providerInfo: Record<string, { 
  name: string; 
  color: string; 
  bgColor: string;
  iconBg: string;
}> = {
  openai: { 
    name: 'OpenAI', 
    color: 'text-emerald-400', 
    bgColor: 'bg-emerald-500/10 border-emerald-500/20',
    iconBg: 'bg-emerald-500'
  },
  anthropic: { 
    name: 'Anthropic', 
    color: 'text-orange-400', 
    bgColor: 'bg-orange-500/10 border-orange-500/20',
    iconBg: 'bg-orange-500'
  },
  google: { 
    name: 'Google', 
    color: 'text-blue-400', 
    bgColor: 'bg-blue-500/10 border-blue-500/20',
    iconBg: 'bg-blue-500'
  },
  meta: { 
    name: 'Meta', 
    color: 'text-purple-400', 
    bgColor: 'bg-purple-500/10 border-purple-500/20',
    iconBg: 'bg-purple-500'
  },
  mistral: { 
    name: 'Mistral', 
    color: 'text-amber-400', 
    bgColor: 'bg-amber-500/10 border-amber-500/20',
    iconBg: 'bg-amber-500'
  },
  deepseek: { 
    name: 'DeepSeek', 
    color: 'text-cyan-400', 
    bgColor: 'bg-cyan-500/10 border-cyan-500/20',
    iconBg: 'bg-cyan-500'
  },
  xai: { 
    name: 'xAI', 
    color: 'text-gray-400', 
    bgColor: 'bg-gray-500/10 border-gray-500/20',
    iconBg: 'bg-gray-500'
  },
  qwen: { 
    name: 'Qwen', 
    color: 'text-indigo-400', 
    bgColor: 'bg-indigo-500/10 border-indigo-500/20',
    iconBg: 'bg-indigo-500'
  },
  cohere: { 
    name: 'Cohere', 
    color: 'text-rose-400', 
    bgColor: 'bg-rose-500/10 border-rose-500/20',
    iconBg: 'bg-rose-500'
  },
  perplexity: { 
    name: 'Perplexity', 
    color: 'text-violet-400', 
    bgColor: 'bg-violet-500/10 border-violet-500/20',
    iconBg: 'bg-violet-500'
  },
  nova: { 
    name: 'Nova', 
    color: 'text-cyan-400', 
    bgColor: 'bg-cyan-500/10 border-cyan-500/20',
    iconBg: 'bg-cyan-500'
  },
  groq: { 
    name: 'Groq', 
    color: 'text-red-400', 
    bgColor: 'bg-red-500/10 border-red-500/20',
    iconBg: 'bg-red-500'
  },
  openrouter: { 
    name: 'OpenRouter', 
    color: 'text-teal-400', 
    bgColor: 'bg-teal-500/10 border-teal-500/20',
    iconBg: 'bg-teal-500'
  },
  microsoft: { 
    name: 'Microsoft', 
    color: 'text-sky-400', 
    bgColor: 'bg-sky-500/10 border-sky-500/20',
    iconBg: 'bg-sky-500'
  },
  together: { 
    name: 'Together AI', 
    color: 'text-green-400', 
    bgColor: 'bg-green-500/10 border-green-500/20',
    iconBg: 'bg-green-500'
  },
  together_ai: { 
    name: 'Together AI', 
    color: 'text-green-400', 
    bgColor: 'bg-green-500/10 border-green-500/20',
    iconBg: 'bg-green-500'
  },
  alibaba: { 
    name: 'Alibaba', 
    color: 'text-orange-400', 
    bgColor: 'bg-orange-500/10 border-orange-500/20',
    iconBg: 'bg-orange-500'
  }
};

// Define types for icons to ensure they are components
type IconComponent = React.ComponentType<{ className?: string }>;

const speedInfo: Record<string, { label: string; icon: IconComponent; color: string }> = {
  fast: { label: 'Fast', icon: Zap, color: 'text-green-400' },
  medium: { label: 'Balanced', icon: BrainCircuit, color: 'text-blue-400' },
  slow: { label: 'Deep', icon: BrainCircuit, color: 'text-purple-400' },
};

const capabilityIcons: Record<string, { label: string; icon: IconComponent; color: string }> = {
  [ModelCapability.REASONING]: { label: 'Reasoning', icon: BrainCircuit, color: 'text-purple-400' },
  [ModelCapability.CODE_GENERATION]: { label: 'Coding', icon: Code, color: 'text-blue-400' },
  [ModelCapability.VISION]: { label: 'Vision', icon: ImageIcon, color: 'text-green-400' },
  [ModelCapability.CREATIVE_WRITING]: { label: 'Writing', icon: BookOpen, color: 'text-orange-400' },
  [ModelCapability.ANALYSIS]: { label: 'Analysis', icon: Zap, color: 'text-yellow-400' },
};

const planBadges: Record<UserPlan, { label: string; icon: IconComponent; className: string }> = {
  [UserPlan.FREE]: { label: 'Free', icon: Sparkles, className: 'text-gray-400' },
  [UserPlan.FREEMIUM]: { label: 'Freemium', icon: Star, className: 'text-blue-400' },
  [UserPlan.PLUS]: { label: 'Plus', icon: Star, className: 'text-purple-400' },
  [UserPlan.ADVANCED]: { label: 'Advanced', icon: Rocket, className: 'text-orange-400' },
  [UserPlan.MAX]: { label: 'Max', icon: Crown, className: 'text-yellow-400' },
  [UserPlan.ENTERPRISE]: { label: 'Enterprise', icon: Crown, className: 'text-gray-900' },
};

const planHierarchy: Record<UserPlan, number> = {
  [UserPlan.FREE]: 0,
  [UserPlan.FREEMIUM]: 1,
  [UserPlan.PLUS]: 2,
  [UserPlan.ADVANCED]: 3,
  [UserPlan.MAX]: 4,
  [UserPlan.ENTERPRISE]: 5,
};

// Get minimum required plan for a model based on cost
function getRequiredPlan(model: Model): UserPlan {
  // Use the minimumPlan field if present
  if (model.minimumPlan) {
    return model.minimumPlan;
  }
  
  // Fallback to cost-based calculation using new pricing tiers
  const inputCost = model.inputCost || 0;
  const outputCost = model.outputCost || 0;
  const maxCost = Math.max(inputCost, outputCost);
  
  // Define FRONTIER models (ultra-premium)
  const frontierModelIds = [
    'o3-pro', 'o3-pro-2025-04-14',
    'gpt-4.5-research-preview', 'gpt-4.5-preview',
    'claude-4-opus', 'claude-3-opus',
    'o1-pro', 'o1-pro-2025-03-19'
  ];
  
  // Check if it's a FRONTIER model first
  if (frontierModelIds.some(id => 
    model.id?.toLowerCase().includes(id.toLowerCase()) ||
    model.name?.toLowerCase().includes(id.toLowerCase())
  )) {
    return UserPlan.MAX; // FRONTIER models require MAX plan
  }
  
  // FREE: < $0.50 (max of input/output)
  if (maxCost < 0.5) {
    return UserPlan.FREE;
  }
  
  // STANDARD: ≤ $5 input AND ≤ $10 output (PLUS plan)
  if (inputCost <= 5.0 && outputCost <= 10.0) {
    return UserPlan.PLUS;
  }
  
  // PREMIUM: > $5 input OR > $10 output (ADVANCED plan)
  return UserPlan.ADVANCED;
}

// Get message credits from model costs
function getMessageCredits(model: Model): number {
  // Use cost-based calculation
  const totalCost = (model.inputCost || 0) + (model.outputCost || 0);
  
  if (totalCost === 0) return 0.5;
  if (totalCost < 0.5) return 0.5;
  if (totalCost < 2) return 1;
  if (totalCost < 10) return 2;
  if (totalCost < 25) return 3;
  return 4;
}

// Check if user can access model
function canUserAccessModel(model: Model, userPlan: UserPlan): boolean {
  // Use the isAvailableForUser flag if present (from API)
  if (model.isAvailableForUser !== undefined) {
    return model.isAvailableForUser;
  }
  
  // Fallback to cost-based check
  const requiredPlan = getRequiredPlan(model);
  
  // User can access if their plan level is >= required plan level
  return planHierarchy[userPlan] >= planHierarchy[requiredPlan];
}

export function ModelSelectorEnhanced({ 
  selectedModel, 
  onModelChange, 
  userPlan = UserPlan.FREE,
  className,
  isAutoMode = true,
  onModeToggle
}: ModelSelectorEnhancedProps) {
  const [showModal, setShowModal] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterProvider, setFilterProvider] = useState<string>('all');
  
  // Get all models with access info
  const { models: allModels, isLoading: isLoadingModels } = useModels(userPlan);
  
  // Get dynamic model count
  const { totalRounded } = useModelCount();
  
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Filter models by search and provider
  const filteredModels = useMemo(() => {
    return allModels.filter(model => {
      // Search query
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase().trim();
        const actualProvider = getActualProvider(model);
        const matchesSearch = 
          model.name?.toLowerCase().includes(query) ||
          model.description?.toLowerCase().includes(query) ||
          actualProvider?.toLowerCase().includes(query) ||
          getProviderDisplayName(actualProvider)?.toLowerCase().includes(query) ||
          (model.tags && Array.isArray(model.tags) && model.tags.some(tag => tag?.toLowerCase().includes(query)));
        if (!matchesSearch) return false;
      }
      
      // Provider filter (using actual provider)
      if (filterProvider !== 'all' && getActualProvider(model) !== filterProvider) {
        return false;
      }
      
      return true;
    });
  }, [allModels, searchQuery, filterProvider]);

  // Get unique providers for filter (using actual providers, not inference providers)
  const providers = useMemo(() => {
    const providerSet = new Set(
      allModels
        .map(m => getActualProvider(m))
        .filter(provider => provider && !shouldHideProvider(provider))
    );
    return sortProviders(Array.from(providerSet));
  }, [allModels]);

  // Group models using intelligent ordering
  const modelGroups = useMemo(() => {
    return groupModelsForDisplay(filteredModels);
  }, [filteredModels]);

  // Get selected model info
  const selectedModelInfo = useMemo(() => {
    return allModels.find(m => m.id === selectedModel);
  }, [allModels, selectedModel]);

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    onModelChange(modelId);
    setShowModal(false);
    if (onModeToggle) {
      onModeToggle(false); // Switch to manual mode when selecting a model
    }
  };

  // Handle auto mode selection
  const handleAutoMode = () => {
    if (onModeToggle) {
      onModeToggle(true);
      setShowModal(false);
    }
  };

  const modalContent = (
    <AnimatePresence>
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6 md:p-8">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/80 backdrop-blur-sm"
            onClick={() => setShowModal(false)}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", duration: 0.5, bounce: 0.1 }}
            className={cn(
              "relative w-full max-w-[95vw] sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-5xl mx-auto",
              "max-h-[85vh] sm:max-h-[90vh]",
              "flex flex-col",
              "bg-gray-900/95 backdrop-blur-xl rounded-2xl border border-gray-700",
              "shadow-2xl shadow-black/50"
            )}
          >
            {/* Header */}
            <div className="p-4 sm:p-6 border-b border-gray-700 bg-gradient-to-r from-blue-600/10 to-purple-600/10 flex-shrink-0">
              <div className="flex items-center justify-between gap-2">
                <div className="min-w-0">
                  <h2 className="text-xl sm:text-2xl font-bold text-white">Choose Your AI Model</h2>
                  <p className="text-sm sm:text-base text-gray-400 mt-1">Select from {totalRounded} cutting-edge AI models</p>
                </div>
                <button
                  onClick={() => setShowModal(false)}
                  className="p-1.5 sm:p-2 hover:bg-gray-800 rounded-lg transition-colors flex-shrink-0"
                >
                  <X className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                </button>
              </div>

              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-3 mt-4 sm:mt-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search models..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className={cn(
                      "w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-2 sm:py-3 rounded-lg sm:rounded-xl",
                      "text-sm sm:text-base text-white placeholder-gray-400",
                      "bg-gray-800/50 border border-gray-600 focus:border-blue-500",
                      "focus:outline-none focus:ring-2 focus:ring-blue-500/20"
                    )}
                  />
                </div>
                
                <select
                  value={filterProvider}
                  onChange={(e) => setFilterProvider(e.target.value)}
                  className={cn(
                    "px-3 sm:px-4 py-2 sm:py-3 rounded-lg sm:rounded-xl",
                    "text-sm sm:text-base bg-gray-800/50 border border-gray-600",
                    "text-white focus:border-blue-500 focus:outline-none"
                  )}
                >
                  <option value="all">All Providers</option>
                  {providers.map(provider => (
                    <option key={provider} value={provider} className="capitalize">
                      {getProviderDisplayName(provider)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Content - Custom Scrollbar */}
            <div className="overflow-y-auto flex-1" style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#4B5563 #1F2937'
            }}>
              <div className="p-3 sm:p-4">
                {/* Auto Mode Option */}
                <motion.div
                  className="mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <h3 className="text-lg font-semibold text-white mb-3">Recommended</h3>
                  <motion.button
                    onClick={handleAutoMode}
                    className={cn(
                      "w-full p-4 rounded-xl transition-all duration-200 text-left",
                      "bg-gradient-to-br from-blue-600/10 to-purple-600/10 border-2",
                      isAutoMode 
                        ? "border-blue-500/50 bg-blue-500/10" 
                        : "border-gray-700 hover:border-blue-500/30"
                    )}
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <Bot className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1 flex-wrap">
                          <h4 className="text-lg font-bold text-white">Smart AI Router</h4>
                          <span className="px-2 py-0.5 text-xs bg-green-500/20 text-green-400 rounded-full border border-green-500/30">
                            Recommended
                          </span>
                        </div>
                        <p className="text-gray-400 text-sm line-clamp-2">
                          Automatically selects the best AI model for each query. Uses advanced routing 
                          to match your request with the optimal model for superior results.
                        </p>
                        <div className="flex items-center gap-3 mt-2 text-xs text-gray-300 flex-wrap">
                          <span>✨ Intelligent routing</span>
                          <span>⚡ Optimized performance</span>
                          <span className="hidden sm:inline">🎯 Best results</span>
                        </div>
                      </div>
                      {isAutoMode && (
                        <CheckCircle2 className="w-5 h-5 text-blue-400" />
                      )}
                    </div>
                  </motion.button>
                </motion.div>

                {/* Models by Category */}
                <div className="space-y-6">
                  {isLoadingModels ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
                      <span className="ml-3 text-gray-400">Loading models...</span>
                    </div>
                  ) : (
                    modelGroups.map((group, index) => {
                      return (
                        <motion.div
                          key={group.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 + index * 0.05 }}
                        >
                          <div className="mb-3">
                            <h3 className="text-base font-semibold text-white">
                              {group.title}
                            </h3>
                            <p className="text-xs text-gray-400 mt-0.5">
                              {group.description}
                            </p>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                            {group.models.map((model, modelIndex) => {
                              const actualProvider = getActualProvider(model);
                              const providerData = providerInfo[actualProvider];
                              const canAccess = canUserAccessModel(model, userPlan);
                              const isSelected = selectedModel === model.id;
                              const credits = getMessageCredits(model);
                              const requiredPlan = getRequiredPlan(model);
                              const speed = speedInfo[model.speed || 'medium'];

                              return (
                                <motion.button
                                  key={`${actualProvider}-${model.id}-${modelIndex}`}
                                  onClick={() => canAccess && handleModelSelect(model.id)}
                                  disabled={!canAccess}
                                  className={cn(
                                    "p-3 rounded-lg transition-all duration-200 text-left border",
                                    canAccess 
                                      ? "hover:scale-[1.02] border-gray-700 hover:border-gray-600"
                                      : "opacity-50 cursor-not-allowed border-gray-800",
                                    isSelected && !isAutoMode && "border-blue-500/50 bg-blue-500/10",
                                    providerData?.bgColor || "bg-gray-800/30"
                                  )}
                                  whileHover={canAccess ? { y: -1 } : {}}
                                  whileTap={{ scale: 0.99 }}
                                >
                                  <div className="space-y-2">
                                    {/* Header */}
                                    <div className="flex items-start gap-2">
                                      {/* Provider Icon */}
                                      <div
                                        className={cn(
                                          'flex h-8 w-8 items-center justify-center rounded-full',
                                          providerData?.bgColor
                                        )}
                                      >
                                        <ProviderIcon provider={actualProvider} width={20} height={20} className="text-white" />
                                      </div>

                                      <div className="flex-1 min-w-0">
                                        <h4 className="font-semibold text-white text-sm truncate">
                                          {cleanModelName(model.name)}
                                        </h4>
                                        <p className="text-[11px] text-gray-400 mt-0.5 line-clamp-1">
                                          {model.description}
                                        </p>
                                      </div>
                                      
                                      <div className="flex items-center gap-1">
                                        {!canAccess && <Lock className="w-3 h-3 text-gray-500" />}
                                        {isSelected && !isAutoMode && (
                                          <CheckCircle2 className="w-3 h-3 text-blue-400" />
                                        )}
                                      </div>
                                    </div>

                                    {/* Stats */}
                                    <div className="flex items-center gap-2 text-[11px] text-gray-400">
                                      <div className="flex items-center gap-0.5">
                                        <Sparkles className="w-2.5 h-2.5" />
                                        <span>{credits} credits</span>
                                      </div>
                                      {speed && (
                                        <div className={cn("flex items-center gap-0.5", speed.color)}>
                                          <Zap className="w-2.5 h-2.5" />
                                          <span>{speed.label}</span>
                                        </div>
                                      )}
                                    </div>

                                    {/* Capabilities - More compact */}
                                    {model.capabilities && Array.isArray(model.capabilities) && model.capabilities.length > 0 && (
                                      <div className="flex items-center gap-1 flex-wrap">
                                        {(model.capabilities || []).slice(0, 3).map((capability, capIndex) => {
                                          const capInfo = capabilityIcons[capability];
                                          if (!capInfo) return null;
                                          const Icon = capInfo.icon;
                                          return (
                                            <div
                                              key={`${model.id}-${capability}-${capIndex}`}
                                              className={cn(
                                                "flex items-center gap-0.5 px-1.5 py-0.5 rounded text-[10px]",
                                                "bg-gray-800/50 border border-gray-700",
                                                capInfo.color
                                              )}
                                              title={capInfo.label}
                                            >
                                              <Icon className="w-2.5 h-2.5" />
                                              <span className="hidden sm:inline">{capInfo.label}</span>
                                            </div>
                                          );
                                        })}
                                        {model.capabilities.length > 3 && (
                                          <span className="text-[10px] text-gray-500">
                                            +{model.capabilities.length - 3}
                                          </span>
                                        )}
                                      </div>
                                    )}

                                    {/* Plan Badge - More Visible */}
                                    {!canAccess && (
                                      <div className="flex items-center gap-1.5 mt-1">
                                        <span className="text-xs text-gray-400">Requires:</span>
                                        <div className={cn(
                                          "flex items-center gap-1 px-2 py-1 rounded-md text-xs border",
                                          planBadges[requiredPlan].className
                                        )}>
                                          <Sparkles className="w-3.5 h-3.5" />
                                          <span>{planBadges[requiredPlan].label}</span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </motion.button>
                              );
                            })}
                          </div>
                        </motion.div>
                      );
                    })
                  )}
                </div>

                {modelGroups.length === 0 && (
                  <div className="text-center py-12 text-gray-400">
                    <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold text-white mb-2">No models found</h3>
                    <p>Try adjusting your search query or filters</p>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 sm:p-6 border-t border-gray-700 bg-gray-900/50 flex-shrink-0">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-2 text-xs sm:text-sm text-gray-400">
                <span>
                  Showing {filteredModels.length} of {allModels.length} models
                </span>
                <span className="text-center sm:text-right">
                  💡 <span className="text-blue-400">Smart AI Router</span> is recommended
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );

  return (
    <>
      {/* Simple Clean Button - inspired by simplelook.png */}
      <motion.button
        onClick={() => setShowModal(true)}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200",
          "bg-gray-800/70 hover:bg-gray-700/70 border border-gray-600 hover:border-green-500/50",
          "text-sm font-medium text-white min-w-[200px]",
          className
        )}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Icon and Text */}
        <div className="flex items-center gap-2 flex-1">
          {isAutoMode ? (
            <>
              <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
              <span>Smart AI Router</span>
            </>
          ) : selectedModelInfo ? (
            <>
              <div
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full",
                  providerInfo[getActualProvider(selectedModelInfo)]?.bgColor || "bg-gray-800/30"
                )}
              >
                <ProviderIcon provider={getActualProvider(selectedModelInfo)} width={20} height={20} className="text-white" />
              </div>
              <span>{cleanModelName(selectedModelInfo.name)}</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 rounded-full bg-gray-500" />
              <span>Select Model</span>
            </>
          )}
        </div>

        <ChevronDown className="w-4 h-4 text-gray-400" />
      </motion.button>

      {isMounted && document.body ? createPortal(modalContent, document.body) : null}
    </>
  );
}