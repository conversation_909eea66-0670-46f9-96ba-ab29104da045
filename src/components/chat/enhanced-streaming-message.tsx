'use client';

import { useState, useEffect, useCallback, startTransition } from 'react';
import { Message, MessageRole } from '@/types';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { MessageItem } from './message-item';
import { StreamingSkeleton } from './message-skeleton';

interface EnhancedStreamingMessageProps {
  message: Message;
  onContentUpdate?: (content: string) => void;
  onComplete?: () => void;
  className?: string;
}

export function EnhancedStreamingMessage({
  message,
  onContentUpdate,
  onComplete,
  className
}: EnhancedStreamingMessageProps) {
  const [displayContent, setDisplayContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  // Word-by-word streaming animation
  useEffect(() => {
    if (!message.content) {
      setDisplayContent('');
      return;
    }

    // If message is marked as streaming, animate the content
    if (message.streaming && message.content !== displayContent) {
      setIsStreaming(true);
      
      const words = message.content.split(' ');
      const currentWords = displayContent.split(' ');
      
      // Only animate new words
      if (words.length > currentWords.length) {
        const newWords = words.slice(currentWords.length);
        let wordIndex = 0;
        
        const animateWords = () => {
          if (wordIndex < newWords.length) {
            startTransition(() => {
              const newContent = currentWords.length === 1 && currentWords[0] === '' 
                ? newWords.slice(0, wordIndex + 1).join(' ')
                : [...currentWords, ...newWords.slice(0, wordIndex + 1)].join(' ');
              
              setDisplayContent(newContent);
              onContentUpdate?.(newContent);
            });
            
            wordIndex++;
            setTimeout(animateWords, 50); // 50ms delay between words - good typing feel
          } else {
            setIsStreaming(false);
            if (!message.streaming) {
              setIsComplete(true);
              onComplete?.();
            }
          }
        };
        
        animateWords();
      }
    } else if (!message.streaming) {
      // Message is complete, show all content immediately
      startTransition(() => {
        setDisplayContent(message.content);
        setIsStreaming(false);
        setIsComplete(true);
        onContentUpdate?.(message.content);
        onComplete?.();
      });
    }
  }, [message.content, message.streaming, displayContent, onContentUpdate, onComplete]);

  // Show skeleton while waiting for first content
  if (!message.content && message.streaming) {
    return (
      <div className={className}>
        <StreamingSkeleton />
      </div>
    );
  }

  // Create display message with current content
  const displayMessage: Message = {
    ...message,
    content: displayContent,
    streaming: isStreaming || message.streaming
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={`${message.id}-${isComplete ? 'complete' : 'streaming'}`}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -5 }}
        transition={{ 
          duration: 0.2,
          ease: 'easeOut'
        }}
        className={cn(
          "relative",
          isStreaming && "animate-pulse",
          className
        )}
      >
        <MessageItem
          message={displayMessage}
          isStreaming={isStreaming || message.streaming}
        />
        
        {/* Typing indicator for active streaming */}
        {isStreaming && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute bottom-2 right-4 flex items-center gap-1"
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-1 h-1 bg-blue-400 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}

// Hook for managing streaming message state
export function useStreamingMessage(initialMessage?: Message) {
  const [message, setMessage] = useState<Message | null>(initialMessage || null);
  const [isStreaming, setIsStreaming] = useState(false);

  const startStreaming = useCallback((newMessage: Message) => {
    startTransition(() => {
      setMessage({ ...newMessage, streaming: true });
      setIsStreaming(true);
    });
  }, []);

  const updateContent = useCallback((content: string) => {
    if (!message) return;
    
    startTransition(() => {
      setMessage(prev => prev ? { ...prev, content, streaming: true } : null);
    });
  }, [message]);

  const completeStreaming = useCallback(() => {
    if (!message) return;
    
    startTransition(() => {
      setMessage(prev => prev ? { ...prev, streaming: false } : null);
      setIsStreaming(false);
    });
  }, [message]);

  const clearMessage = useCallback(() => {
    startTransition(() => {
      setMessage(null);
      setIsStreaming(false);
    });
  }, []);

  return {
    message,
    isStreaming,
    startStreaming,
    updateContent,
    completeStreaming,
    clearMessage
  };
}