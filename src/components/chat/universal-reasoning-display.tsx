'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Brain, ChevronDown, ChevronRight, Sparkles, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';
import { ModelIndicator } from './model-indicator';
// Removed ReactMarkdown imports to fix TypeScript issues

interface UniversalReasoningDisplayProps {
  // Reasoning content
  reasoning: string;
  isStreaming: boolean;
  
  // Message content (for integrated display)
  children: React.ReactNode;
  
  // Model info (all optional to handle undefined values)
  modelName?: string;
  model?: string;
  provider?: string;
  
  // Transition control
  mainContentStarted?: boolean;
  mainContentStartTime?: number;
  
  // Styling
  className?: string;
  thinkingType?: 'reasoning' | 'analysis' | 'search' | 'general';
}

export function UniversalReasoningDisplay({ 
  reasoning,
  isStreaming,
  children,
  modelName,
  model,
  provider,
  mainContentStarted = false,
  mainContentStartTime,
  className,
  thinkingType = 'reasoning'
}: UniversalReasoningDisplayProps) {
  const [isReasoningExpanded, setIsReasoningExpanded] = useState(false);
  const [showReasoning, setShowReasoning] = useState(false);
  const [isManuallyOpened, setIsManuallyOpened] = useState(false);
  const [isActiveConversation, setIsActiveConversation] = useState(false);
  const [thinkingTextIndex, setThinkingTextIndex] = useState(0);
  const [typingText, setTypingText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [reasoningComplete, setReasoningComplete] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  const thinkingTexts = [
    "Reasoning through your request",
    "Analyzing the problem", 
    "Thinking step by step",
    "Processing your query"
  ];

  // Track if this is an active streaming conversation (not a reload)
  useEffect(() => {
    if (isStreaming && reasoning && reasoning.trim().length > 0) {
      setIsActiveConversation(true);
      setShowReasoning(true);
      setIsReasoningExpanded(true); // Auto-expand ONLY during active streaming
    } else if (reasoning && reasoning.trim().length > 0 && !isActiveConversation) {
      // This is a loaded conversation - show reasoning exists but don't auto-expand
      setShowReasoning(true);
      setIsReasoningExpanded(false); // Collapsed by default on reload
    }
  }, [isStreaming, reasoning, isActiveConversation]);

  // Always show reasoning if we have content and manual interaction
  useEffect(() => {
    if (reasoning && reasoning.trim().length > 0) {
      setShowReasoning(true);
    }
  }, [reasoning, isManuallyOpened]);

  // Smooth close when main content starts (but keep it reopenable)
  useEffect(() => {
    if (mainContentStarted && isReasoningExpanded && !isManuallyOpened && reasoning) {
      // Wait 1.5 seconds after main content starts, then smoothly close
      const timer = setTimeout(() => {
        setIsReasoningExpanded(false);
        // Keep showReasoning true so it can be manually reopened
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [mainContentStarted, isReasoningExpanded, isManuallyOpened, reasoning]);

  // Streaming typing effect for reasoning content
  useEffect(() => {
    if (isStreaming && reasoning) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => {
          if (prev < reasoning.length) {
            setTypingText(reasoning.slice(0, prev + 1));
            return prev + 1;
          }
          clearInterval(interval);
          return prev;
        });
      }, 15); // Fast typing speed for reasoning

      return () => clearInterval(interval);
    } else {
      setTypingText(reasoning);
    }
  }, [reasoning, isStreaming]);

  // Detect when reasoning is complete but still streaming (DeepSeek behavior)
  useEffect(() => {
    if (isStreaming && reasoning && !mainContentStarted) {
      // Check if reasoning hasn't grown for 2 seconds (DeepSeek pause)
      let lastLength = reasoning.length;
      let stableCount = 0;
      
      const checkInterval = setInterval(() => {
        if (reasoning.length === lastLength) {
          stableCount++;
          // If reasoning stable for 2 checks (2 seconds), mark as complete
          if (stableCount >= 2 && isStreaming && !mainContentStarted) {
            setReasoningComplete(true);
            console.log('[DeepSeek UI] Reasoning appears complete, waiting for main content');
          }
        } else {
          // Reset if reasoning is still growing
          lastLength = reasoning.length;
          stableCount = 0;
          setReasoningComplete(false);
        }
      }, 1000);

      return () => clearInterval(checkInterval);
    } else if (mainContentStarted) {
      setReasoningComplete(false);
    }
  }, [isStreaming, reasoning, mainContentStarted]);

  // Auto-scroll during reasoning streaming
  useEffect(() => {
    if (isStreaming && scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [reasoning, isStreaming]);
  
  // Cycle through thinking texts during streaming
  useEffect(() => {
    if (isStreaming && !reasoning) {
      const interval = setInterval(() => {
        setThinkingTextIndex((prev) => (prev + 1) % thinkingTexts.length);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [isStreaming, reasoning, thinkingTexts.length]);

  const getThinkingConfig = () => {
    switch (thinkingType) {
      case 'reasoning':
        return {
          icon: Brain,
          title: 'AI Reasoning Process',
          gradient: 'from-purple-500/20 via-pink-500/20 to-purple-600/20',
          borderColor: 'border-purple-500/30',
          iconColor: 'text-purple-400',
          titleColor: 'text-purple-300'
        };
      case 'analysis':
        return {
          icon: Sparkles,
          title: 'Deep Analysis',
          gradient: 'from-blue-500/20 via-cyan-500/20 to-blue-600/20',
          borderColor: 'border-blue-500/30',
          iconColor: 'text-blue-400',
          titleColor: 'text-blue-300'
        };
      case 'search':
        return {
          icon: Zap,
          title: 'Search Analysis',
          gradient: 'from-emerald-500/20 via-green-500/20 to-emerald-600/20',
          borderColor: 'border-emerald-500/30',
          iconColor: 'text-emerald-400',
          titleColor: 'text-emerald-300'
        };
      default:
        return {
          icon: Brain,
          title: 'AI Thinking',
          gradient: 'from-gray-500/20 via-slate-500/20 to-gray-600/20',
          borderColor: 'border-gray-500/30',
          iconColor: 'text-gray-400',
          titleColor: 'text-gray-300'
        };
    }
  };

  const config = getThinkingConfig();
  const Icon = config.icon;

  return (
    <div className={cn("space-y-4 relative", className)} style={{ zIndex: isReasoningExpanded ? 50 : 1, position: 'relative' }}>
      {/* Header with Model Info */}
      <div className="flex items-center gap-2 mb-1">
        <span className="text-xs font-semibold text-gray-300 tracking-wide uppercase">
          Assistant
        </span>
        <ModelIndicator 
          model={model || ''}
          provider={provider}
        />
        {isStreaming && (
          <motion.span 
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="text-xs text-gray-400"
          >
            Streaming...
          </motion.span>
        )}
      </div>

      {/* Combined Container - Always start full width to prevent text squashing */}
      <motion.div 
        className={cn(
          "rounded-2xl overflow-hidden border backdrop-blur-sm",
          "bg-gradient-to-br from-gray-900/80 to-gray-800/60",
          config.borderColor,
          // Always full width - prevent any text squashing
          "w-full"
        )}
        initial={{ opacity: 0, y: 10, scale: 0.98 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        style={{
          // Ensure minimum width to prevent collapse during streaming
          minWidth: '100%',
          // Prevent layout shift
          contain: 'layout'
        }}
      >
        {/* Animated background shimmer - only during streaming */}
        {isStreaming && (
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <motion.div
              animate={{ x: ['-100%', '200%'] }}
              transition={{ 
                duration: 3, 
                repeat: Infinity, 
                ease: "linear",
                repeatDelay: 1
              }}
              className={cn(
                "absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent",
                "opacity-50"
              )}
            />
          </div>
        )}

        {/* Reasoning Section */}
        <AnimatePresence>
          {reasoning && reasoning.trim().length > 0 && (
            <motion.div 
              initial={{ opacity: 1 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="border-b border-gray-700/50">
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const newExpanded = !isReasoningExpanded;
                  setIsReasoningExpanded(newExpanded);
                  
                  // Track manual interaction to prevent auto-close
                  if (newExpanded) {
                    setIsManuallyOpened(true);
                  } else {
                    setIsManuallyOpened(false);
                  }
                }}
                className="w-full p-3 justify-between h-auto hover:bg-gray-800/50 rounded-none"
              >
                <div className="flex items-center gap-2 text-indigo-300">
                  <motion.div
                    animate={isStreaming ? { 
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    } : {}}
                    transition={{ 
                      duration: 2, 
                      repeat: isStreaming ? Infinity : 0,
                      ease: "easeInOut"
                    }}
                    className={cn(
                      "p-2 rounded-lg bg-black/20 backdrop-blur-sm",
                      config.iconColor
                    )}
                  >
                    <Icon className="w-4 h-4" />
                  </motion.div>
                  <span className="text-sm font-medium">
                    {isStreaming ? thinkingTexts[thinkingTextIndex] : 'See reasoning process'}
                  </span>
                </div>
                {isReasoningExpanded ? (
                  <ChevronDown className="w-4 h-4 text-indigo-400" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-indigo-400" />
                )}
              </Button>

              <AnimatePresence mode="wait">
                {isReasoningExpanded && (
                  <motion.div
                    key="reasoning-content"
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ 
                      height: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
                      opacity: { duration: 0.5, ease: "easeOut" }
                    }}
                    className="overflow-hidden"
                  >
                    <div 
                      ref={scrollRef}
                      className="px-4 py-3 bg-indigo-950/20 max-h-[400px] overflow-y-auto"
                    >
                      <div className="text-sm text-gray-300 whitespace-pre-wrap font-mono leading-relaxed">
                        {typingText || reasoning}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Message Content */}
        <div className="px-4 py-3 min-w-0 overflow-hidden">
          {/* Show loading state immediately when streaming with no content */}
          {isStreaming && !children && (
            reasoningComplete ? (
              // Enhanced loading when reasoning is done but waiting for content
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-3"
              >
                {/* Animated typing indicator */}
                <div className="flex items-center gap-3">
                  <div className="flex gap-1">
                    {[0, 1, 2].map((i) => (
                      <motion.div
                        key={i}
                        className="w-2 h-2 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full"
                        animate={{
                          y: ["0%", "-50%", "0%"],
                          scale: [1, 1.2, 1],
                        }}
                        transition={{
                          duration: 0.6,
                          repeat: Infinity,
                          delay: i * 0.1,
                          ease: "easeInOut"
                        }}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-400">Formulating response based on analysis...</span>
                </div>
                
                {/* Progress bar */}
                <div className="w-full h-1 bg-gray-800 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-green-500 to-emerald-500"
                    animate={{
                      x: ["-100%", "100%"],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                    style={{ width: "50%" }}
                  />
                </div>
              </motion.div>
            ) : (
              // Simple indicator while reasoning is still happening
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-gray-600 border-t-indigo-400 rounded-full"
                />
                <span>Analyzing your request...</span>
              </div>
            )
          )}
          {children}
        </div>
      </motion.div>
    </div>
  );
}