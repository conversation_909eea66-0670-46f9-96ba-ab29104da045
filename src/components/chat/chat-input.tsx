'use client';

import { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Paperclip, 
  Mic, 
  StopCircle,
  Image as ImageIcon,
  FileText,
  X,
  Loader2,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateFileSize, formatFileSize, MAX_FILE_SIZE_DISPLAY } from '@/lib/upload-constants';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { AnimatedPlaceholder } from './AnimatedPlaceholder';
import { ModelSelectorEnhanced } from './model-selector-enhanced';
import { WebSearchButton } from './web-search-button';
import { UltraThinkToggle } from '@/components/ui/ultra-think-toggle';
import { UserPlan } from '@/types';
import { useSession, signIn } from 'next-auth/react';
import { supportsWebSearch } from '@/lib/thinking-models';
import { KeyboardShortcut } from '@/components/ui/keyboard-shortcut';
import toast from 'react-hot-toast';
import { FREE_MESSAGE_LIMITS } from '@/lib/model-stats';
import Link from 'next/link';
import { LEGAL_TEXT, LEGAL_LINKS } from '@/lib/constants/legal';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useConversationStore } from '@/stores/conversationStore';

interface ChatInputProps {
  onSendMessage: (content: string, attachments?: File[], webSearchEnabled?: boolean, ultraThink?: boolean) => void;
  onAttachmentsChange: (attachments: File[]) => void;
  onUltraThink?: (content: string, attachments?: File[]) => void;
  onUpgrade?: () => void;
  userPlan?: UserPlan;
  isStreaming: boolean;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
  sendDisabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  showAnimatedPlaceholder?: boolean;
  isAutoMode?: boolean;
  selectedModel?: string | null;
  onModelSelect?: (modelId: string | null) => void;
  onModeToggle?: (auto: boolean) => void;
  enableWebSearch?: boolean;
  onWebSearchToggle?: (enabled: boolean) => void;
  onInputChange?: (value: string) => void;
  value?: string;
}

export function ChatInput({ 
  onSendMessage, 
  onAttachmentsChange,
  onUltraThink,
  onUpgrade,
  userPlan = UserPlan.FREE,
  isStreaming,
  isLoading = false,
  disabled = false,
  sendDisabled = false,
  placeholder = "Type a message...",
  maxLength = 4000,
  showAnimatedPlaceholder = false,
  isAutoMode = true,
  selectedModel = null,
  onModelSelect,
  onModeToggle,
  enableWebSearch = false,
  onWebSearchToggle,
  onInputChange,
  value
}: ChatInputProps) {
  const [message, setMessage] = useState(value || '');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [isMobileKeyboardOpen, setIsMobileKeyboardOpen] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [ultraThinkEnabled, setUltraThinkEnabled] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: session } = useSession();
  const { activeConversationId } = useConversationStore();

  // Check localStorage after mount to avoid hydration mismatch
  useEffect(() => {
    const storedInteraction = localStorage.getItem('hasInteracted');
    if (storedInteraction === 'true') {
      setHasInteracted(true);
    }
  }, []);

  // Check for active conversation
  useEffect(() => {
    if (activeConversationId && !hasInteracted) {
      setHasInteracted(true);
      localStorage.setItem('hasInteracted', 'true');
    }
  }, [activeConversationId, hasInteracted]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 200)}px`;
    }
  }, [message]);

  // Focus on mount
  useEffect(() => {
    textareaRef.current?.focus();
  }, []);

  // Update message when value prop changes (for applying suggestions)
  useEffect(() => {
    if (value !== undefined && value !== message) {
      setMessage(value);
    }
  }, [value, message]);

  useEffect(() => {
    onAttachmentsChange(attachments);
  }, [attachments, onAttachmentsChange]);

  function handleSubmit() {
    if (message.trim() && !isLoading && !disabled) {
      console.log('[ChatInput] Sending message with attachments:', attachments.length, 'ultraThink:', ultraThinkEnabled);
      onSendMessage(message.trim(), attachments, enableWebSearch, ultraThinkEnabled);
      setMessage('');
      setAttachments([]);
      // Reset ULTRA THINK after use (like web search stays persistent)
      // setUltraThinkEnabled(false); // Keep it enabled for convenience
      textareaRef.current?.focus();
      
      // Mark that user has interacted
      if (!hasInteracted) {
        setHasInteracted(true);
        localStorage.setItem('hasInteracted', 'true');
      }
    }
  }

  // Remove handleUltraThink - now handled by toggle state

  function handleKeyDown(e: KeyboardEvent<HTMLTextAreaElement>) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  }

  function handleFileSelect(files: FileList | null) {
    // Check if user is logged in for file uploads
    if (!session?.user) {
      toast('🎯 Sign in to unlock file uploads!', {
        icon: '📎',
        duration: 4000,
        style: {
          borderRadius: '10px',
          background: '#1f2937',
          color: '#fff',
          border: '1px solid #7c3aed',
          padding: '12px 16px',
        }
      });
      return;
    }

    if (files) {
      console.log('[ChatInput] Files selected:', files.length);
      const newFiles = Array.from(files).filter(file => {
        // Validate file size using centralized validation
        const validation = validateFileSize(file);
        if (!validation.valid) {
          alert(validation.error);
          return false;
        }
        console.log('[ChatInput] File validated:', file.name, file.type, file.size);
        return true;
      });
      
      setAttachments(prev => {
        const updated = [...prev, ...newFiles];
        console.log('[ChatInput] Attachments updated:', updated.length, 'files');
        return updated;
      });
    }
  }

  function removeAttachment(index: number) {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  }

  // Drag and drop handlers
  function handleDrag(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }

  function handleDrop(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  }

  // Voice recording (placeholder)
  function toggleRecording() {
    // Show coming soon message
    toast('Voice input is on our roadmap!', {
      icon: '🎤',
      duration: 3000,
      style: {
        borderRadius: '10px',
        background: '#333',
        color: '#fff',
      },
    });
  }

  return (
    <div 
      className={cn(
        "relative p-2 sm:p-3",
        dragActive && "bg-purple-500/5"
      )}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {/* Rate Limit Message */}
      {disabled && placeholder?.includes('reached the free message limit') && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-2 p-3 bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/30 rounded-lg"
        >
          <div className="flex items-center justify-between flex-wrap gap-3">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white mb-1">
                🎆 You{"'"}ve discovered all {FREE_MESSAGE_LIMITS.ANONYMOUS} free messages!
              </p>
              <p className="text-xs text-gray-300">
                                  Sign in now to unlock your VIP pass: {FREE_MESSAGE_LIMITS.LOGGED_IN} messages/day, no waiting!
              </p>
            </div>
            <Button
              onClick={() => signIn('google', { callbackUrl: '/chat' })}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 whitespace-nowrap"
            >
                                  Sign in for 2x more →
            </Button>
          </div>
        </motion.div>
      )}
      {/* Attachments Preview */}
      <AnimatePresence>
        {attachments.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
          >
            <div className="mb-2 flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-2 bg-gray-900 rounded-lg px-3 py-2 text-sm"
              >
                {file.type.startsWith('image/') ? (
                  <ImageIcon className="w-4 h-4 text-purple-400" />
                ) : (
                  <FileText className="w-4 h-4 text-blue-400" />
                )}
                <span className="max-w-[200px] truncate">{file.name}</span>
                <span className="text-gray-500">
                  {formatFileSize(file.size)}
                </span>
                <button
                  onClick={() => removeAttachment(index)}
                  className="ml-1 hover:text-red-400 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Model Selector */}
      {onModelSelect && onModeToggle && (
        <div className="mb-2">
          <ModelSelectorEnhanced
            selectedModel={selectedModel || ''}
            onModelChange={onModelSelect}
            isAutoMode={isAutoMode}
            onModeToggle={onModeToggle}
            userPlan={
              session?.user?.plan ? 
                UserPlan[session.user.plan as keyof typeof UserPlan] || UserPlan.FREE : 
                UserPlan.FREE
            }
            className="w-full sm:w-auto"
          />
        </div>
      )}

      {/* Main Input Area */}
      <div className="space-y-2 bg-gray-800/30 rounded-lg p-3 border border-gray-700/50 shadow-lg">
        {/* Mobile Layout: Stacked */}
        <div className="block sm:hidden">
          {/* Textarea - Full Width on Mobile */}
          <div className="relative mb-3">
            {showAnimatedPlaceholder && !message && (
              <div className="absolute left-3 top-3 pointer-events-none z-10">
                <AnimatedPlaceholder 
                  prompts={[
                    "What's the capital of France?",
                    "Build a React component with TypeScript",
                    "Write a short story about time travel",
                    "Explain quantum computing in simple terms",
                    "How do I make the perfect omelette?",
                    "Debug this JavaScript code for me",
                    "What are the benefits of meditation?",
                    "Create a Python script to sort files"
                  ]}
                  typingSpeed={80}
                  deletingSpeed={40}
                  pauseDuration={1500}
                />
              </div>
            )}
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                onInputChange?.(e.target.value);
              }}
              onKeyDown={handleKeyDown}
              placeholder={showAnimatedPlaceholder ? "" : placeholder}
              disabled={isLoading || disabled}
              maxLength={maxLength}
              className={cn(
                "min-h-[44px] max-h-[200px] resize-none w-full text-base",
                "bg-gray-950/50 backdrop-blur-sm",
                "border border-gray-700 hover:border-gray-600",
                "focus:border-purple-500 focus:ring-1 focus:ring-purple-500/20",
                "text-white placeholder:text-gray-400 py-3 px-3",
                "transition-all duration-200",
                isLoading && "opacity-50 cursor-not-allowed",
                disabled && placeholder?.includes('reached the free message limit') && "opacity-60 bg-gray-900/50 border-purple-500/30 placeholder:text-purple-400/60"
              )}
              rows={1}
              autoComplete="off"
              autoCorrect="on"
              autoCapitalize="sentences"
              spellCheck="true"
              inputMode="text"
              enterKeyHint="send"
            />
            
            {/* Character Count */}
            {message.length > maxLength * 0.8 && (
              <div className={cn(
                "absolute bottom-2 right-2 text-xs",
                message.length >= maxLength ? "text-red-400" : "text-gray-500"
              )}>
                {message.length}/{maxLength}
              </div>
            )}
          </div>

          {/* Action Buttons Row - Mobile */}
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              {/* ULTRA THINK Toggle - Mobile */}
              <UltraThinkToggle
                isEnabled={ultraThinkEnabled}
                onToggle={setUltraThinkEnabled}
                userPlan={userPlan as 'FREE' | 'PLUS' | 'ADVANCED' | 'MAX' | 'ENTERPRISE'}
                onUpgrade={onUpgrade}
                disabled={isLoading || disabled}
                className="h-11"
              />
              
              {/* Web Search Button - Compact on Mobile */}
              {onWebSearchToggle && (
                <WebSearchButton
                  isEnabled={enableWebSearch}
                  onToggle={onWebSearchToggle}
                  modelSupportsNativeSearch={selectedModel ? supportsWebSearch(selectedModel) : false}
                  disabled={isLoading || disabled}
                  className="h-11 px-3"
                />
              )}
              
              {/* File Upload */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => session?.user ? fileInputRef.current?.click() : handleFileSelect(null)}
                      disabled={isLoading || disabled}
                      className={cn(
                        "h-11 w-11 p-0 transition-colors",
                        session?.user 
                          ? "hover:bg-purple-600/20 text-gray-300 hover:text-white" 
                          : "hover:bg-blue-600/20 text-gray-500 hover:text-blue-300"
                      )}
                    >
                      <Paperclip className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                    <p className="text-sm">
                      {session?.user 
                        ? "Attach files (images, PDFs, documents)" 
                        : "🔒 Sign in to upload files"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Voice Input */}
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={toggleRecording}
                disabled={isLoading || disabled}
                className={cn(
                  "h-11 w-11 p-0 hover:bg-purple-600/20",
                  isRecording && "text-red-500"
                )}
                title="Voice input"
              >
                {isRecording ? (
                  <StopCircle className="w-5 h-5" />
                ) : (
                  <Mic className="w-5 h-5" />
                )}
              </Button>
            </div>

            {/* Send Button - Prominent on Mobile */}
            <Button
              onClick={handleSubmit}
              disabled={!message.trim() || isLoading || disabled || sendDisabled}
              size="default"
              className={cn(
                "h-11 px-5 min-w-[80px]",
                "bg-purple-600 hover:bg-purple-700",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "text-base font-medium"
              )}
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <>
                  <Send className="w-5 h-5 mr-1.5" />
                  Send
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Desktop Layout: Horizontal */}
        <div className="hidden sm:flex items-end gap-2">
          {/* Textarea Container */}
          <div className="flex-1 relative group">
            {showAnimatedPlaceholder && !message && (
              <div className="absolute left-3 top-3 pointer-events-none z-10">
                <AnimatedPlaceholder 
                  prompts={[
                    "What's the capital of France?",
                    "Build a React component with TypeScript",
                    "Write a short story about time travel",
                    "Explain quantum computing in simple terms",
                    "How do I make the perfect omelette?",
                    "Debug this JavaScript code for me",
                    "What are the benefits of meditation?",
                    "Create a Python script to sort files"
                  ]}
                  typingSpeed={80}
                  deletingSpeed={40}
                  pauseDuration={1500}
                />
              </div>
            )}
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                onInputChange?.(e.target.value);
              }}
              onKeyDown={handleKeyDown}
              placeholder={showAnimatedPlaceholder ? "" : placeholder}
              disabled={isLoading || disabled}
              maxLength={maxLength}
              className={cn(
                "min-h-[44px] max-h-[200px] pr-10 resize-none",
                "bg-gray-950/50 backdrop-blur-sm",
                "border border-gray-700 hover:border-gray-600",
                "focus:border-purple-500 focus:ring-1 focus:ring-purple-500/20",
                "[&]:text-white [&]:placeholder:text-gray-400",
                "transition-all duration-200",
                isLoading && "opacity-50 cursor-not-allowed",
                disabled && placeholder?.includes('reached the free message limit') && "opacity-60 bg-gray-900/50 border-purple-500/30 placeholder:text-purple-400/60"
              )}
              rows={1}
              autoComplete="off"
              autoCorrect="on"
              autoCapitalize="sentences"
              spellCheck="true"
              inputMode="text"
              enterKeyHint="send"
              title="Press Enter to send, Shift+Enter for new line"
            />
            
            {/* Character Count */}
            {message.length > maxLength * 0.8 && (
              <div className={cn(
                "absolute bottom-2 right-12 text-xs",
                message.length >= maxLength ? "text-red-400" : "text-gray-500"
              )}>
                {message.length}/{maxLength}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            {/* ULTRA THINK Toggle */}
            <UltraThinkToggle
              isEnabled={ultraThinkEnabled}
              onToggle={setUltraThinkEnabled}
              userPlan={userPlan as 'FREE' | 'PLUS' | 'ADVANCED' | 'MAX' | 'ENTERPRISE'}
              onUpgrade={onUpgrade}
              disabled={isLoading || disabled}
              className="h-10"
            />
            
            {/* Web Search Button */}
            {onWebSearchToggle && (
              <WebSearchButton
                isEnabled={enableWebSearch}
                onToggle={onWebSearchToggle}
                modelSupportsNativeSearch={selectedModel ? supportsWebSearch(selectedModel) : false}
                disabled={isLoading || disabled}
                className="h-10 px-3"
              />
            )}
            
            {/* File Upload */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type="button"
                    size="sm"
                    variant="ghost"
                    onClick={() => session?.user ? fileInputRef.current?.click() : handleFileSelect(null)}
                    disabled={isLoading || disabled}
                    className={cn(
                      "h-10 w-10 p-0 transition-colors",
                      session?.user 
                        ? "hover:bg-purple-600/20 text-gray-400 hover:text-white" 
                        : "hover:bg-blue-600/20 text-gray-500 hover:text-blue-300"
                    )}
                  >
                    <Paperclip className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                  <p className="text-sm">
                    {session?.user 
                      ? `Attach files (images, PDFs, documents) - Max size: ${MAX_FILE_SIZE_DISPLAY}` 
                      : "🔒 Sign in to upload files"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Voice Input */}
            <Button
              type="button"
              size="sm"
              variant="ghost"
              onClick={toggleRecording}
              disabled={isLoading || disabled}
              className={cn(
                "h-10 w-10 p-0 hover:bg-purple-600/20 transition-colors",
                "text-gray-400 hover:text-white",
                isRecording && "text-red-500"
              )}
              title="Voice input (coming soon)"
            >
              {isRecording ? (
                <StopCircle className="w-4 h-4" />
              ) : (
                <Mic className="w-4 h-4" />
              )}
            </Button>

            {/* Send Button */}
            <Button
              onClick={handleSubmit}
              disabled={!message.trim() || isLoading || disabled || sendDisabled}
              size="sm"
              className={cn(
                "h-10 w-10 p-0",
                "bg-purple-600 hover:bg-purple-700 active:bg-purple-800",
                "shadow-md hover:shadow-lg",
                "transition-all duration-200 hover:scale-105 active:scale-95",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              )}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf,.doc,.docx,.txt,.csv,.xlsx"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Drag Overlay */}
      <AnimatePresence>
        {dragActive && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'absolute',
              inset: 0,
              backgroundColor: 'rgba(168, 85, 247, 0.1)',
              border: '2px dashed rgb(168, 85, 247)',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <div className="text-purple-400 text-sm font-medium text-center">
              {session?.user ? (
                <>
                  <div>Drop files here</div>
                  <div className="text-xs text-gray-400 mt-1">Max size: {MAX_FILE_SIZE_DISPLAY}</div>
                </>
              ) : (
                <>
                  <div>🔒 Sign in to upload files</div>
                  <div className="text-xs text-gray-400 mt-1">File uploads require login</div>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Terms text only - hide after interaction */}
      {!hasInteracted && (
        <div className="mt-2 text-xs text-gray-400">
          <div className="text-center">
            <span className="hidden sm:inline">
              {LEGAL_TEXT.desktop.prefix}{" "}
              <Link 
                href={LEGAL_LINKS.terms} 
                className="text-blue-400 hover:text-blue-300 underline underline-offset-2 transition-colors"
              >
                {LEGAL_TEXT.desktop.terms}
              </Link>{" "}
              {LEGAL_TEXT.desktop.and}{" "}
              <Link 
                href={LEGAL_LINKS.privacy} 
                className="text-blue-400 hover:text-blue-300 underline underline-offset-2 transition-colors"
              >
                {LEGAL_TEXT.desktop.privacy}
              </Link>
            </span>
            
            {/* Mobile version - Ultra compact */}
            <span className="sm:hidden">
              {LEGAL_TEXT.mobile.prefix}{" "}
              <Link 
                href={LEGAL_LINKS.terms} 
                className="text-blue-400 underline"
              >
                {LEGAL_TEXT.mobile.terms}
              </Link>{" "}
              {LEGAL_TEXT.mobile.and}{" "}
              <Link 
                href={LEGAL_LINKS.privacy} 
                className="text-blue-400 underline"
              >
                {LEGAL_TEXT.mobile.privacy}
              </Link>
            </span>
          </div>
        </div>
      )}
    </div>
  );
}