'use client';

import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  Search, 
  Sparkles, 
  Zap, 
  Bot,
  Cpu,
  Brain,
  Gauge,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { cleanModelName } from '@/lib/utils/model-display';
import { Model, ModelCapability, UserPlan } from '@/types';
import ProviderIcon from '@/components/ui/provider-icon';
import { useModels } from '@/hooks/use-models';
import { useSession } from 'next-auth/react';
import * as Popover from '@radix-ui/react-popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { supportsMultimodal } from '@/lib/ai/multimodal-utils';

interface ModelSelectorProps {
  models: Model[];
  selectedModel?: string;
  onModelSelect: (modelId: string | null) => void;
  disabled?: boolean;
  className?: string;
  hasAttachments?: boolean;
  isAutoMode?: boolean;
  onModeToggle?: (autoMode: boolean) => void;
}

// Group models by provider
function groupModelsByProvider(models: (Model & { userCanAccess?: boolean })[]) {
  return models.reduce((acc: any, model: any) => {
    const provider = model.provider;
    if (!acc[provider]) {
      acc[provider] = [];
    }
    acc[provider].push(model);
    return acc;
  }, {} as Record<string, (Model & { userCanAccess?: boolean })[]>);
}

// Provider display names and colors - Updated for brand accuracy
const providerInfo: Record<string, { name: string; color: string; icon: string }> = {
  openai: { name: 'OpenAI', color: 'from-emerald-400 to-green-400', icon: '🟢' },
  anthropic: { name: 'Anthropic', color: 'from-orange-400 to-amber-400', icon: '🟠' },
  google: { name: 'Google', color: 'from-blue-400 to-sky-400', icon: '🔵' },
  meta: { name: 'Meta', color: 'from-blue-500 to-indigo-500', icon: '🔵' },
  mistral: { name: 'Mistral', color: 'from-purple-400 to-violet-400', icon: '🟣' },
  deepseek: { name: 'DeepSeek', color: 'from-indigo-400 to-blue-500', icon: '🔵' },
  groq: { name: 'Groq', color: 'from-orange-500 to-red-500', icon: '🔴' },
  qwen: { name: 'Qwen', color: 'from-orange-400 to-yellow-400', icon: '🟡' },
  xai: { name: 'xAI', color: 'from-red-500 to-rose-500', icon: '🔴' },
  cohere: { name: 'Cohere', color: 'from-rose-500 to-pink-500', icon: '🔴' },
  perplexity: { name: 'Perplexity', color: 'from-teal-400 to-cyan-400', icon: '🔵' },
  nova: { name: 'Nova', color: 'from-orange-500 to-amber-500', icon: '🟠' },
};

// Model capability icons
const capabilityIcons: Record<string, React.ReactElement> = {
  code_generation: <Cpu className="w-3 h-3" />,
  creative_writing: <Sparkles className="w-3 h-3" />,
  analysis: <Brain className="w-3 h-3" />,
  fast_response: <Zap className="w-3 h-3" />,
  long_context: <Bot className="w-3 h-3" />,
};

export function ModelSelector({
  selectedModel,
  onModelSelect,
  isAutoMode,
  onModeToggle,
  disabled,
  className,
  hasAttachments = false
}: ModelSelectorProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const { data: session } = useSession();

  // Get available models based on user tier
  const userPlan = (session?.user?.plan || UserPlan.FREE) as UserPlan;
  const { models: availableModels, isLoading: isLoadingModels } = useModels(userPlan);

  // Filter models based on search
  const filteredModels = useMemo(() => {
    if (!search) return availableModels;
    
    const searchLower = search.toLowerCase();
    return availableModels.filter(model => 
      model.name.toLowerCase().includes(searchLower) ||
      model.provider.toLowerCase().includes(searchLower) ||
      model.description?.toLowerCase().includes(searchLower) ||
      (model.tags && model.tags.some(tag => tag.toLowerCase().includes(searchLower)))
    );
  }, [availableModels, search]);

  // Group filtered models
  const groupedModels = useMemo(() => {
    return groupModelsByProvider(filteredModels);
  }, [filteredModels]);

  // Get selected model info
  const selectedModelInfo = selectedModel 
    ? availableModels.find(m => m.id === selectedModel)
    : null;

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <button
          disabled={disabled}
          className={cn(
            "flex items-center gap-2 px-3 py-2 rounded-lg transition-all",
            "bg-gray-900 hover:bg-gray-800 border border-gray-700",
            "disabled:opacity-50 disabled:cursor-not-allowed",
            className
          )}
        >
          <div className="flex items-center gap-2">
            {isAutoMode ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <Sparkles className="w-4 h-4 text-green-400" />
                </motion.div>
                <span className="text-sm font-medium text-green-400">Auto Router</span>
              </>
            ) : selectedModelInfo ? (
              <>
                <ProviderIcon
                  provider={selectedModelInfo.provider}
                  width={16}
                  height={16}
                  className="flex-shrink-0"
                />
                <span className="text-sm font-medium text-white">{cleanModelName(selectedModelInfo.name)}</span>
              </>
            ) : (
              <>
                <Bot className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-400">Select Model</span>
              </>
            )}
          </div>
          <ChevronDown className={cn(
            "w-4 h-4 text-gray-400 transition-transform",
            open && "rotate-180"
          )} />
        </button>
      </Popover.Trigger>

      <Popover.Portal>
        <Popover.Content
          align="end"
          sideOffset={8}
          className="z-50 w-[400px] max-h-[600px] overflow-hidden rounded-xl bg-gray-900 border border-gray-700 shadow-2xl"
        >
          <div className="sticky top-0 z-10 bg-gray-900 border-b border-gray-800">
            {/* Mode Toggle */}
            <div className="p-3 border-b border-gray-800">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-300">Model Selection Mode</span>
                <div className="flex items-center gap-2 p-1 bg-gray-800 rounded-lg">
                  <button
                    onClick={() => {
                      onModeToggle?.(true);
                      onModelSelect(null);
                    }}
                    className={cn(
                      "px-3 py-1 rounded text-xs font-medium transition-all",
                      isAutoMode 
                        ? "bg-green-500/20 text-green-400 border border-green-500/30"
                        : "text-gray-400 hover:text-gray-300"
                    )}
                  >
                    <div className="flex items-center gap-1">
                      <Sparkles className="w-3 h-3" />
                      Auto
                    </div>
                  </button>
                  <button
                    onClick={() => onModeToggle?.(false)}
                    className={cn(
                      "px-3 py-1 rounded text-xs font-medium transition-all",
                      !isAutoMode
                        ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                        : "text-gray-400 hover:text-gray-300"
                    )}
                  >
                    <div className="flex items-center gap-1">
                      <Bot className="w-3 h-3" />
                      Manual
                    </div>
                  </button>
                </div>
              </div>
              {isAutoMode && (
                <p className="text-xs text-gray-500 mt-2">
                  The AI router will automatically select the best model for each message
                </p>
              )}
            </div>

            {/* Search */}
            {!isAutoMode && (
              <div className="p-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder="Search models..."
                    className="w-full pl-9 pr-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-sm text-white placeholder-gray-500 focus:outline-none focus:border-purple-500"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Model List */}
          {!isAutoMode && (
            <div className="overflow-y-auto max-h-[400px] p-3">
              <AnimatePresence mode="wait">
                {Object.entries(groupedModels).map(([provider, models]: [string, any]) => (
                  <motion.div
                    key={provider}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="mb-4"
                  >
                    <div className="flex items-center gap-2 mb-2 px-2">
                      <ProviderIcon
                        provider={provider}
                        width={20}
                        height={20}
                      />
                      <span className="text-xs font-medium text-gray-400 uppercase tracking-wider">
                        {providerInfo[provider]?.name || provider}
                      </span>
                      <span className="text-xs text-gray-600">({models.length})</span>
                    </div>
                    
                    <div className="space-y-1">
                      {models.map((model: any) => {
                        const canAccess = model.userCanAccess !== false;
                        const isDisabled = !canAccess;
                        
                        return (
                          <button
                            key={model.id}
                            onClick={() => {
                              onModelSelect(model.id);
                              setOpen(false);
                            }}
                            disabled={isDisabled}
                            className={cn(
                              "w-full text-left px-2 py-2 rounded-md transition-all flex items-center justify-between",
                              "hover:bg-gray-800",
                              selectedModel === model.id ? "bg-purple-500/20" : "bg-transparent",
                              !model.userCanAccess && "opacity-50 cursor-not-allowed"
                            )}
                          >
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center gap-3">
                                    <ProviderIcon
                                      provider={model.provider}
                                      width={24}
                                      height={24}
                                      className="flex-shrink-0"
                                    />
                                    <span className="truncate text-sm text-gray-200">{cleanModelName(model.name)}</span>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent side="right" className="max-w-xs bg-gray-800 border-gray-700 text-white">
                                  {cleanModelName(model.name)}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </button>
                        );
                      })}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {filteredModels.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500 text-sm">No models found</p>
                </div>
              )}
            </div>
          )}

          {/* Footer */}
          <div className="sticky bottom-0 bg-gray-900 border-t border-gray-800 p-3">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{availableModels.length} models available</span>
              <span className="text-purple-400">{userPlan} plan</span>
            </div>
          </div>
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}