'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  WifiOff, 
  ServerCrash, 
  Clock, 
  Key, 
  Ban, 
  RefreshCw,
  Info,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface EnhancedErrorDisplayProps {
  error: string | Error;
  errorCode?: string | number;
  onRetry?: () => void;
  onDismiss?: () => void;
  model?: string;
  className?: string;
}

interface ErrorDetails {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  iconColor: string;
  bgGradient: string;
  suggestions: string[];
  canRetry: boolean;
}

export function EnhancedErrorDisplay({
  error,
  errorCode,
  onRetry,
  onDismiss,
  model,
  className
}: EnhancedErrorDisplayProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [retrying, setRetrying] = useState(false);
  
  const errorMessage = typeof error === 'string' ? error : error?.message;
  const code = errorCode || (typeof error === 'object' && 'code' in error ? error.code : null);

  const getErrorDetails = (): ErrorDetails => {
    // Check error code first
    if (code) {
      switch (code) {
        case 500:
        case '500':
          return {
            title: "Internal Server Error",
            description: "The AI service encountered an unexpected error. Our team has been notified.",
            icon: ServerCrash,
            iconColor: "text-red-400",
            bgGradient: "from-red-500/20 to-orange-500/20",
            suggestions: [
              "Try again in a few moments",
              "Use a different AI model",
              "Simplify your request"
            ],
            canRetry: true
          };

        case 502:
        case '502':
          return {
            title: "Service Unavailable",
            description: "Unable to reach the AI service. This might be due to high demand or maintenance.",
            icon: WifiOff,
            iconColor: "text-orange-400",
            bgGradient: "from-orange-500/20 to-yellow-500/20",
            suggestions: [
              "Check if the service is under maintenance",
              "Try a different model provider",
              "Wait a few minutes before retrying"
            ],
            canRetry: true
          };

        case 503:
        case '503':
          return {
            title: "Service Overloaded",
            description: "The AI service is experiencing high traffic. Please be patient.",
            icon: Clock,
            iconColor: "text-yellow-400",
            bgGradient: "from-yellow-500/20 to-amber-500/20",
            suggestions: [
              "Wait 30-60 seconds before retrying",
              "Try during off-peak hours",
              "Consider using a different model"
            ],
            canRetry: true
          };

        case 429:
        case '429':
          return {
            title: "Rate Limit Exceeded",
            description: "You've made too many requests. Please slow down.",
            icon: Ban,
            iconColor: "text-purple-400",
            bgGradient: "from-purple-500/20 to-pink-500/20",
            suggestions: [
              "Wait a minute before sending another message",
              "Reduce the frequency of your requests",
              "Upgrade your plan for higher limits"
            ],
            canRetry: true
          };

        case 401:
        case '401':
        case 403:
        case '403':
          return {
            title: "Authentication Error",
            description: "There's an issue with your access credentials or permissions.",
            icon: Key,
            iconColor: "text-blue-400",
            bgGradient: "from-blue-500/20 to-indigo-500/20",
            suggestions: [
              "Refresh the page and try again",
              "Check if your subscription is active",
              "Contact support if the issue persists"
            ],
            canRetry: false
          };
      }
    }

    // Check error message patterns
    if (errorMessage?.toLowerCase().includes('timeout')) {
      return {
        title: "Request Timeout",
        description: "The request took too long to process. This can happen with complex queries.",
        icon: Clock,
        iconColor: "text-cyan-400",
        bgGradient: "from-cyan-500/20 to-teal-500/20",
        suggestions: [
          "Try a shorter or simpler message",
          "Break your request into smaller parts",
          "Use a faster model"
        ],
        canRetry: true
      };
    }

    if (errorMessage?.toLowerCase().includes('api key') || errorMessage?.toLowerCase().includes('api_key')) {
      return {
        title: "API Configuration Error",
        description: "There's an issue with the API configuration for this model.",
        icon: Key,
        iconColor: "text-emerald-400",
        bgGradient: "from-emerald-500/20 to-green-500/20",
        suggestions: [
          "Try a different AI model",
          "Contact support to report this issue",
          "Check the service status page"
        ],
        canRetry: false
      };
    }

    if (errorMessage?.toLowerCase().includes('connection')) {
      return {
        title: "Connection Error",
        description: "Unable to establish a connection to the AI service.",
        icon: WifiOff,
        iconColor: "text-gray-400",
        bgGradient: "from-gray-500/20 to-slate-500/20",
        suggestions: [
          "Check your internet connection",
          "Try refreshing the page",
          "Disable VPN if you're using one"
        ],
        canRetry: true
      };
    }

    // Default error
    return {
      title: "Something Went Wrong",
      description: errorMessage || "An unexpected error occurred. Please try again.",
      icon: AlertTriangle,
      iconColor: "text-amber-400",
      bgGradient: "from-amber-500/20 to-orange-500/20",
      suggestions: [
        "Try again with a different approach",
        "Select a different model",
        "Contact support if this persists"
      ],
      canRetry: true
    };
  };

  const details = getErrorDetails();
  const Icon = details.icon;

  const handleRetry = async () => {
    if (!onRetry) return;
    
    setRetrying(true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Brief delay for UX
    onRetry();
    setRetrying(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10, scale: 0.98 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -10, scale: 0.98 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "relative rounded-2xl border backdrop-blur-sm overflow-hidden",
        "bg-gradient-to-br from-gray-900/90 to-gray-800/90",
        "border-red-500/30",
        className
      )}
    >
      {/* Animated background gradient */}
      <motion.div
        className={cn(
          "absolute inset-0 opacity-20 bg-gradient-to-br",
          details.bgGradient
        )}
        animate={{
          opacity: [0.1, 0.3, 0.1],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Main content */}
      <div className="relative z-10 p-4">
        {/* Header */}
        <div className="flex items-start gap-3">
          <motion.div
            animate={{
              rotate: [0, 5, -5, 0],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className={cn(
              "p-2 rounded-lg bg-black/30 backdrop-blur-sm",
              details.iconColor
            )}
          >
            <Icon className="w-5 h-5" />
          </motion.div>

          <div className="flex-1">
            <h3 className="font-semibold text-white mb-1">
              {details.title}
            </h3>
            <p className="text-sm text-gray-300 leading-relaxed">
              {details.description}
            </p>

            {/* Model info if available */}
            {model && (
              <p className="text-xs text-gray-500 mt-1">
                Model: {model}
              </p>
            )}
          </div>

          {/* Close button if dismissible */}
          {onDismiss && (
            <Button
              size="sm"
              variant="ghost"
              onClick={onDismiss}
              className="h-6 w-6 p-0 text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Expandable details section */}
        <div className="mt-3">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs text-gray-400 hover:text-white p-0 h-auto"
          >
            <span className="flex items-center gap-1">
              {showDetails ? (
                <>
                  <ChevronUp className="w-3 h-3" />
                  Hide suggestions
                </>
              ) : (
                <>
                  <ChevronDown className="w-3 h-3" />
                  Show suggestions
                </>
              )}
            </span>
          </Button>

          <AnimatePresence>
            {showDetails && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="mt-3 space-y-2">
                  <p className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                    Suggestions:
                  </p>
                  <ul className="space-y-1">
                    {details.suggestions.map((suggestion, index) => (
                      <motion.li
                        key={index}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start gap-2 text-sm text-gray-300"
                      >
                        <span className="text-gray-500 mt-0.5">•</span>
                        <span>{suggestion}</span>
                      </motion.li>
                    ))}
                  </ul>

                  {code ? (
                    <div className="mt-3 pt-3 border-t border-gray-700/50">
                      <p className="text-xs text-gray-500">
                        Error code: {String(code)}
                      </p>
                    </div>
                  ) : null}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Action buttons */}
        {(details.canRetry && onRetry) && (
          <div className="mt-4 flex items-center gap-2">
            <Button
              size="sm"
              onClick={handleRetry}
              disabled={retrying}
              className={cn(
                "bg-gradient-to-r from-blue-600 to-purple-600",
                "hover:from-blue-700 hover:to-purple-700",
                "text-white font-medium",
                "transition-all duration-200",
                retrying && "opacity-50 cursor-not-allowed"
              )}
            >
              {retrying ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <RefreshCw className="w-3 h-3 mr-1.5" />
                  </motion.div>
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="w-3 h-3 mr-1.5" />
                  Try Again
                </>
              )}
            </Button>

            <Button
              size="sm"
              variant="ghost"
              className="text-gray-400 hover:text-white"
              onClick={() => window.open('/help/errors', '_blank')}
            >
              <Info className="w-3 h-3 mr-1.5" />
              Get Help
            </Button>
          </div>
        )}
      </div>
    </motion.div>
  );
} 