'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Brain, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion, AnimatePresence } from 'framer-motion';
import { ModelIndicator } from './model-indicator';

interface UnifiedMessageDisplayProps {
  reasoning: string;
  isStreaming: boolean;
  modelName?: string;
  model?: string;
  provider?: string;
  children: React.ReactNode;
  className?: string;
  mainContentStarted?: boolean;
  mainContentStartTime?: number;
}

export function UnifiedMessageDisplay({ 
  reasoning, 
  isStreaming,
  modelName,
  model,
  provider,
  children,
  className,
  mainContentStarted = false,
  mainContentStartTime
}: UnifiedMessageDisplayProps) {
  const [isReasoningExpanded, setIsReasoningExpanded] = useState(false);
  const [showReasoning, setShowReasoning] = useState(false);
  const [hasStreamedBefore, setHasStreamedBefore] = useState(!isStreaming);
  const [isAboutToCollapse, setIsAboutToCollapse] = useState(false);
  const [reasoningStartTime, setReasoningStartTime] = useState<number | null>(null);
  const [thinkingTextIndex, setThinkingTextIndex] = useState(0);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  const thinkingTexts = [
    "Reasoning through your request",
    "Analyzing the problem",
    "Thinking step by step",
    "Processing your query"
  ];

  // Show reasoning when streaming or when content exists
  useEffect(() => {
    if (isStreaming || (reasoning && reasoning.trim().length > 0)) {
      setShowReasoning(true);
      setIsReasoningExpanded(true); // Auto-expand when reasoning content arrives
      setHasStreamedBefore(true);
      if (!reasoningStartTime && isStreaming) {
        setReasoningStartTime(Date.now());
      }
    }
  }, [isStreaming, reasoning, reasoningStartTime]);

  // Auto-collapse after streaming completes with smart timing
  useEffect(() => {
    if (!isStreaming && reasoning && showReasoning && hasStreamedBefore && reasoningStartTime) {
      // Calculate how long reasoning has been visible
      const elapsedTime = Date.now() - reasoningStartTime;
      const minimumDisplayTime = 4000; // 4 seconds minimum
      const graceTime = 1500; // 1.5s grace period after streaming stops
      const indicatorWarningTime = 2000; // 2s warning before collapse
      
      // Calculate remaining time to meet minimum display
      const remainingMinTime = Math.max(0, minimumDisplayTime - elapsedTime);
      
      // Total delay = remaining minimum time + grace period
      const totalDelay = remainingMinTime + graceTime;
      
      // Show indicator before collapse
      const indicatorTimer = setTimeout(() => {
        setIsAboutToCollapse(true);
      }, totalDelay + indicatorWarningTime);
      
      // Collapse after total delay + indicator time
      const collapseTimer = setTimeout(() => {
        setShowReasoning(false);
        setIsAboutToCollapse(false);
        setReasoningStartTime(null);
      }, totalDelay + indicatorWarningTime + 2000);
      
      return () => {
        clearTimeout(indicatorTimer);
        clearTimeout(collapseTimer);
      };
    }
  }, [isStreaming, reasoning, showReasoning, hasStreamedBefore, reasoningStartTime]);

  // Smooth close when main content starts (enhanced UX)
  useEffect(() => {
    if (mainContentStarted && showReasoning && isReasoningExpanded && reasoning) {
      // Wait 1.5 seconds after main content starts, then smoothly close
      const timer = setTimeout(() => {
        setShowReasoning(false);
        setIsReasoningExpanded(false);
        setIsAboutToCollapse(false);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [mainContentStarted, showReasoning, isReasoningExpanded, reasoning]);

  // Auto-scroll to bottom during streaming
  useEffect(() => {
    if (isStreaming && scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [reasoning, isStreaming]);
  
  // Cycle through thinking texts during streaming
  useEffect(() => {
    if (isStreaming) {
      const interval = setInterval(() => {
        setThinkingTextIndex((prev) => (prev + 1) % thinkingTexts.length);
      }, 3000);
      return () => clearInterval(interval);
    } else {
      setThinkingTextIndex(0);
    }
  }, [isStreaming, thinkingTexts.length]);

  // Get lines for display
  const lines = reasoning.split('\n').filter(line => line.trim());
  const visibleLines = 4;

  return (
    <div className={cn("space-y-0 w-full", className)}>
      {/* Header with Assistant label and Model */}
      <div className="flex items-center gap-2 mb-2">
        <span className="text-xs font-semibold text-gray-300 tracking-wide uppercase">
          Assistant
        </span>
        {model && (
          <ModelIndicator 
            model={model} 
            provider={provider}
          />
        )}
        {isStreaming && (
          <motion.span 
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="text-xs text-gray-400"
          >
            Streaming...
          </motion.span>
        )}
      </div>

      {/* Combined Container with dynamic width */}
      <motion.div 
        className={cn(
          "rounded-2xl overflow-hidden border border-gray-700",
          // Dynamic background based on streaming state
          isStreaming 
            ? "bg-gradient-to-r from-blue-950/30 to-purple-950/30 border-blue-500/30" 
            : "bg-gradient-to-r from-gray-800 to-gray-900",
          // Dynamic width: full during streaming, fit content when complete
          isStreaming ? "w-full" : "w-fit min-w-[300px] max-w-full"
        )}
        layout // Enable smooth width animations
        layoutId="unified-message-container"
        transition={{ 
          layout: { duration: 0.8, ease: "easeInOut" }
        }}
      >
        {/* Reasoning Section */}
        <AnimatePresence>
          {reasoning && (
            <motion.div 
              initial={{ opacity: 1 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="border-b border-purple-500/30 bg-gradient-to-r from-purple-950/20 to-indigo-950/20">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsReasoningExpanded(!isReasoningExpanded);
                // If about to collapse, cancel it
                if (isAboutToCollapse) {
                  setIsAboutToCollapse(false);
                  setShowReasoning(true);
                  setReasoningStartTime(Date.now()); // Reset timer
                }
              }}
              className="w-full p-3 justify-between h-auto hover:bg-purple-800/30 rounded-none"
            >
              <div className="flex items-center gap-2 text-purple-300">
                <Brain className="w-4 h-4 text-purple-400" />
                <span className="text-sm font-medium">
                  {isStreaming ? '🧠 Thinking...' : '🧠 See reasoning process'}
                </span>
              </div>
              {isReasoningExpanded ? (
                <ChevronDown className="w-4 h-4 text-indigo-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-indigo-400" />
              )}
            </Button>

            <AnimatePresence mode="wait">
              {(showReasoning || isReasoningExpanded) && (
                <motion.div
                  key={isReasoningExpanded ? 'expanded' : 'streaming'}
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ 
                    height: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
                    opacity: { duration: 0.5, ease: "easeOut" }
                  }}
                  className="overflow-hidden"
                >
                  <div 
                    className="px-3 pb-3 bg-indigo-950/20 cursor-pointer"
                    onClick={() => {
                      if (isAboutToCollapse) {
                        setIsAboutToCollapse(false);
                        setShowReasoning(true);
                        setReasoningStartTime(Date.now()); // Reset timer
                      }
                    }}
                  >
                    {isReasoningExpanded ? (
                      // Full reasoning when expanded
                      <div className="text-sm text-gray-300 font-mono leading-relaxed p-3 rounded-md bg-gray-900/30">
                        <pre className="whitespace-pre-wrap break-words">
                          {reasoning}
                        </pre>
                      </div>
                    ) : (
                      // Streaming view with fixed height and fade effect
                      <div className="relative h-[88px] overflow-hidden rounded-md bg-gray-900/30">
                        {/* Enhanced gradient overlay for smoother fade effect */}
                        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/0 via-gray-900/20 via-40% to-gray-900/95 pointer-events-none z-10" />
                        
                        {/* Scrolling content */}
                        <div 
                          ref={scrollRef}
                          className="h-full overflow-hidden relative"
                        >
                          <div className="p-3 text-sm text-gray-300 font-mono leading-[22px]">
                            {!reasoning || lines.length === 0 ? (
                              // Modern thinking lines animation
                              <div className="space-y-2 relative">
                                {[0, 1, 2, 3].map((i) => (
                                  <div key={i} className="relative h-4 overflow-hidden">
                                    <motion.div 
                                      className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-600/20 to-transparent rounded"
                                      initial={{ x: -100, opacity: 0 }}
                                      animate={{ 
                                        x: ['-100%', '200%'],
                                        opacity: [0, 1, 0]
                                      }}
                                      transition={{ 
                                        duration: 2,
                                        repeat: Infinity,
                                        delay: i * 0.3,
                                        ease: "easeInOut"
                                      }}
                                    />
                                    <div 
                                      className="h-full bg-gray-700/10 rounded"
                                      style={{ width: `${70 - i * 10}%` }}
                                    />
                                  </div>
                                ))}
                              </div>
                            ) : lines.length > visibleLines ? (
                              // Show with scroll effect
                              <motion.div
                                animate={{ y: -(lines.length - visibleLines) * 22 }}
                                transition={{ 
                                  duration: 0.8,
                                  ease: [0.25, 0.1, 0.25, 1]
                                }}
                              >
                                {lines.map((line, i) => (
                                  <div 
                                    key={i} 
                                    className={cn(
                                      "transition-all duration-700 ease-out",
                                      i < lines.length - visibleLines && "opacity-20 blur-[0.5px]"
                                    )}
                                  >
                                    {line || '\u00A0'}
                                  </div>
                                ))}
                              </motion.div>
                            ) : (
                              // Show all lines if less than visible
                              lines.map((line, i) => (
                                <motion.div 
                                  key={i}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: i * 0.1 }}
                                >
                                  {line || '\u00A0'}
                                </motion.div>
                              ))
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Streaming or collapse indicator */}
                    {!isReasoningExpanded && (
                      <div className="mt-2">
                        {isStreaming ? (
                          <div className="flex items-center gap-3">
                            {/* Modern AI thinking indicator */}
                            <div className="relative h-4 w-24 overflow-hidden rounded-full bg-gray-800/50">
                              <motion.div
                                className="absolute inset-y-0 w-full bg-gradient-to-r from-transparent via-indigo-400/30 to-transparent"
                                animate={{ x: [-96, 96] }}
                                transition={{
                                  duration: 1.8,
                                  repeat: Infinity,
                                  ease: "linear"
                                }}
                              />
                            </div>
                            <AnimatePresence mode="wait">
                              <motion.span 
                                key={thinkingTextIndex}
                                className="text-xs text-indigo-400/80"
                                initial={{ opacity: 0, y: 5 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -5 }}
                                transition={{ duration: 0.3 }}
                              >
                                {thinkingTexts[thinkingTextIndex]}
                              </motion.span>
                            </AnimatePresence>
                          </div>
                        ) : isAboutToCollapse && (
                          <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="text-xs text-indigo-400/60 italic"
                          >
                            Hiding reasoning soon • Click to keep open
                          </motion.div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
        </AnimatePresence>

        {/* Message Content */}
        <div className={cn(
          "px-4 py-3 min-w-0 overflow-hidden",
          "border-l-4 border-blue-500/50 bg-gradient-to-r from-blue-950/10 to-transparent"
        )}>
          {children}
        </div>
      </motion.div>
    </div>
  );
}