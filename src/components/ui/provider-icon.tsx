import React from 'react';
import Image from 'next/image';

interface ProviderIconProps {
  provider: string;
  width?: number;
  height?: number;
  className?: string;
}

const ProviderIcon: React.FC<ProviderIconProps> = ({
  provider,
  width = 24,
  height = 24,
  className = '',
}) => {
  // Model provider mapping - prioritizes actual model creators over serving platforms
  const getModelProvider = (provider: string): string => {
    const normalized = provider.toLowerCase().replace(/\s+/g, '-');

    // Model-based detection (prioritize actual model creators)
    // Meta/Llama models (including Llama 3, Llama 3.1, Llama 3.3, etc.)
    if (normalized.includes('llama') || normalized.includes('meta') ||
        normalized.match(/llama[-\s]*3(\.\d+)?/)) return 'meta';

    // OpenAI models
    if (normalized.includes('gpt') || normalized.includes('openai')) return 'openai';

    // Anthropic models
    if (normalized.includes('claude') || normalized.includes('anthropic')) return 'anthropic';

    // Google models (including Gemma 2)
    if (normalized.includes('gemini') || normalized.includes('bard') ||
        normalized.includes('gemma') || normalized.match(/gemma[-\s]*2/)) return 'google';

    // Microsoft models (including Phi-4)
    if (normalized.includes('phi') || normalized.match(/phi[-\s]*4/) ||
        normalized.includes('microsoft')) return 'microsoft';

    // Qwen/Alibaba models
    if (normalized.includes('qwen') || normalized.includes('alibaba')) return 'qwen';

    // Other model providers
    if (normalized.includes('mistral')) return 'mistral';
    if (normalized.includes('deepseek')) return 'deepseek';
    if (normalized.includes('grok') || normalized.includes('xai')) return 'xai';
    if (normalized.includes('cohere')) return 'cohere';
    if (normalized.includes('perplexity')) return 'perplexity';
    if (normalized.includes('nova')) return 'nova';

    // Direct provider mapping for when we have explicit provider names
    const providerMapping: { [key: string]: string } = {
      'qwen': 'qwen',
      'alibaba': 'qwen',
      'alibaba-cloud': 'qwen',
      'openai': 'openai',
      'anthropic': 'anthropic',
      'claude': 'anthropic',
      'google': 'google',
      'gemini': 'google',
      'gemma': 'google',
      'microsoft': 'microsoft',
      'phi': 'microsoft',
      'meta': 'meta',
      'llama': 'meta',
      'mistral': 'mistral',
      'mistral-ai': 'mistral',
      'deepseek': 'deepseek',
      'xai': 'xai',
      'grok': 'xai',
      'cohere': 'cohere',
      'perplexity': 'perplexity',
      'perplexity-ai': 'perplexity',
      'groq': 'groq',
      'nova': 'nova',
      // Serving platforms fall back to generic when no model is detected
      'openrouter': 'openrouter',
      'together': 'openrouter', // Use generic for serving platforms
      'together-ai': 'openrouter',
    };

    return providerMapping[normalized] || 'default';
  };

  // Get the appropriate logo based on model provider (not serving platform)
  const logoName = getModelProvider(provider);
  const logoPath = `/logos/providers/${logoName}.svg`;

  // Add styles to brighten and enhance the logos with circular backgrounds
  const enhancedClassName = `${className}
    contrast-110
    saturate-110
    brightness-105
    hover:brightness-115
    hover:saturate-125
    hover:scale-105
    transition-all
    duration-200
    drop-shadow-md
    rounded-full
  `.replace(/\s+/g, ' ').trim();

  return (
    <Image
      src={logoPath}
      alt={`${provider} Logo`}
      width={width}
      height={height}
      className={enhancedClassName}
      unoptimized
      style={{
        filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15))',
        borderRadius: '50%',
      }}
      onError={(e) => {
        // Fallback to default logo if the specific logo fails to load
        const target = e.target as HTMLImageElement;
        if (target.src !== '/logos/providers/default.svg') {
          target.src = '/logos/providers/default.svg';
        }
      }}
    />
  );
};

export default ProviderIcon; 