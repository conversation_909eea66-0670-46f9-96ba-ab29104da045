/**
 * ULTRA THINK Toggle - Simplified for debugging
 */

'use client';

import { Brain, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface UltraThinkToggleProps {
  isEnabled: boolean;
  onToggle: (enabled: boolean) => void;
  userPlan: 'FREE' | 'PLUS' | 'ADVANCED' | 'MAX' | 'ENTERPRISE';
  onUpgrade?: () => void;
  disabled?: boolean;
  className?: string;
}

export function UltraThinkToggle({
  isEnabled,
  onToggle,
  userPlan,
  onUpgrade,
  disabled = false,
  className
}: UltraThinkToggleProps) {
  const hasAccess = ['PLUS', 'ADVANCED', 'MAX', 'ENTERPRISE'].includes(userPlan);
  
  const handleClick = () => {
    if (disabled) return;
    
    if (hasAccess) {
      console.log('[UltraThinkToggle] Toggling from', isEnabled, 'to', !isEnabled);
      onToggle(!isEnabled);
    } else {
      console.log('[UltraThinkToggle] No access, calling upgrade');
      onUpgrade?.();
    }
  };

  console.log('[UltraThinkToggle] Rendering:', { isEnabled, hasAccess, userPlan, disabled });

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClick}
            disabled={disabled}
            className={cn(
              "transition-all duration-300 gap-2",
              isEnabled && hasAccess 
                ? "bg-purple-500/20 border border-purple-500/40 text-purple-300" 
                : "hover:bg-gray-800/50 text-gray-400 hover:text-gray-300",
              className
            )}
          >
            {hasAccess ? (
              <>
                <Brain className="h-4 w-4" />
                <span className="text-sm">Ultra {isEnabled ? 'ON' : 'OFF'}</span>
              </>
            ) : (
              <>
                <div className="relative">
                  <Brain className="h-4 w-4" />
                  <Lock className="h-2.5 w-2.5 absolute -bottom-0.5 -right-0.5 text-amber-500" />
                </div>
                <span className="text-sm">Ultra</span>
                <span className="text-xs bg-amber-500/20 text-amber-400 px-1 py-0.5 rounded">
                  PLUS
                </span>
              </>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent 
          side="top" 
          className="bg-gray-800 border border-gray-700"
        >
          <p className="text-sm">
            {hasAccess 
              ? `ULTRA THINK: ${isEnabled ? 'Enabled' : 'Click to enable'}`
              : "ULTRA THINK requires PLUS plan or higher"}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}