'use client'

import { useState } from 'react'
import { Check, X, Info } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import type { Model } from '@/types'
import { ModelCapability } from '@/types'

interface ComparisonTableProps {
  model1: Model
  model2: Model
}

type ComparisonCategory = 'performance' | 'capabilities' | 'pricing' | 'technical'

interface ComparisonItem {
  label: string
  tooltip?: string
  getValue: (model: Model) => string | number | boolean | undefined
  format?: (value: any) => string
  type: 'boolean' | 'number' | 'string'
  category: ComparisonCategory
}

const comparisonItems: ComparisonItem[] = [
  // Performance
  {
    label: 'Context Window',
    tooltip: 'Maximum number of tokens the model can process',
    getValue: (m) => m.contextLength,
    format: (v) => v ? `${v.toLocaleString()} tokens` : 'N/A',
    type: 'number',
    category: 'performance'
  },
  {
    label: 'Processing Speed',
    getValue: (m) => m.processingSpeed || 'Standard',
    type: 'string',
    category: 'performance'
  },
  {
    label: 'Knowledge Cutoff',
    getValue: (m) => m.knowledgeCutoff || 'Unknown',
    type: 'string',
    category: 'performance'
  },
  
  // Capabilities
  {
    label: 'Vision Support',
    tooltip: 'Can analyze and understand images',
    getValue: (m) => m.capabilities?.includes(ModelCapability.VISION),
    type: 'boolean',
    category: 'capabilities'
  },
  {
    label: 'Function Calling',
    tooltip: 'Can call external functions and APIs',
    getValue: (m) => m.capabilities?.includes(ModelCapability.FUNCTION_CALLING),
    type: 'boolean',
    category: 'capabilities'
  },
  {
    label: 'Web Search',
    tooltip: 'Can search the internet for current information',
    getValue: (m) => m.capabilities?.includes(ModelCapability.WEB_SEARCH),
    type: 'boolean',
    category: 'capabilities'
  },
  {
    label: 'Code Generation',
    getValue: (m) => m.capabilities?.includes(ModelCapability.CODE_GENERATION),
    type: 'boolean',
    category: 'capabilities'
  },
  {
    label: 'Reasoning',
    tooltip: 'Advanced reasoning and problem-solving capabilities',
    getValue: (m) => m.capabilities?.includes(ModelCapability.REASONING),
    type: 'boolean',
    category: 'capabilities'
  },
  
  // Pricing
  {
    label: 'Input Cost',
    tooltip: 'Cost per million input tokens',
    getValue: (m) => m.inputPricePerMillion,
    format: (v) => `$${v}/M tokens`,
    type: 'number',
    category: 'pricing'
  },
  {
    label: 'Output Cost',
    tooltip: 'Cost per million output tokens',
    getValue: (m) => m.outputPricePerMillion,
    format: (v) => `$${v}/M tokens`,
    type: 'number',
    category: 'pricing'
  },
  {
    label: 'Tier',
    getValue: (m) => m.tier,
    format: (v) => v.charAt(0).toUpperCase() + v.slice(1),
    type: 'string',
    category: 'pricing'
  },
  
  // Technical
  {
    label: 'Provider',
    getValue: (m) => m.provider,
    type: 'string',
    category: 'technical'
  },
  {
    label: 'Model Family',
    getValue: (m) => m.family || 'N/A',
    type: 'string',
    category: 'technical'
  },
  {
    label: 'Max Output',
    tooltip: 'Maximum tokens that can be generated',
    getValue: (m) => m.maxOutput,
    format: (v) => v ? `${v.toLocaleString()} tokens` : 'N/A',
    type: 'number',
    category: 'technical'
  }
]

export function ComparisonTable({ model1, model2 }: ComparisonTableProps) {
  const [selectedCategory, setSelectedCategory] = useState<ComparisonCategory>('performance')
  
  const categories: { value: ComparisonCategory; label: string }[] = [
    { value: 'performance', label: 'Performance' },
    { value: 'capabilities', label: 'Capabilities' },
    { value: 'pricing', label: 'Pricing' },
    { value: 'technical', label: 'Technical' }
  ]
  
  const filteredItems = comparisonItems.filter(item => item.category === selectedCategory)
  
  const renderValue = (item: ComparisonItem, model: Model) => {
    const value = item.getValue(model)
    
    if (item.type === 'boolean') {
      return value ? (
        <Check className="w-5 h-5 text-green-400" />
      ) : (
        <X className="w-5 h-5 text-gray-600" />
      )
    }
    
    if (item.format && value !== undefined && value !== null) {
      return <span className="font-mono text-sm">{item.format(value)}</span>
    }
    
    return <span className="text-sm">{value?.toString() || 'N/A'}</span>
  }
  
  const getWinner = (item: ComparisonItem) => {
    const value1 = item.getValue(model1)
    const value2 = item.getValue(model2)
    
    if (item.type === 'boolean') {
      if (value1 && !value2) return 'model1'
      if (!value1 && value2) return 'model2'
      return null
    }
    
    if (item.type === 'number' && typeof value1 === 'number' && typeof value2 === 'number') {
      // For costs, lower is better
      if (item.label.includes('Cost')) {
        if (value1 < value2) return 'model1'
        if (value2 < value1) return 'model2'
      } else {
        // For other numbers, higher is usually better
        if (value1 > value2) return 'model1'
        if (value2 > value1) return 'model2'
      }
    }
    
    return null
  }
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Category Tabs */}
        <div className="flex flex-wrap gap-2 justify-center">
          {categories.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={cn(
                "px-4 py-2 rounded-lg font-medium transition-all",
                selectedCategory === category.value
                  ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white"
                  : "bg-gray-800 text-gray-400 hover:bg-gray-700 hover:text-gray-200"
              )}
            >
              {category.label}
            </button>
          ))}
        </div>
        
        {/* Comparison Table */}
        <Card className="bg-gray-900/80 backdrop-blur-xl border-gray-800">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-800">
                    <th className="text-left p-4 text-sm font-medium text-gray-400">
                      Feature
                    </th>
                    <th className="p-4 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Badge className="bg-purple-600/20 text-purple-300 border-purple-600/40">
                          {model1.name}
                        </Badge>
                      </div>
                    </th>
                    <th className="p-4 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Badge className="bg-blue-600/20 text-blue-300 border-blue-600/40">
                          {model2.name}
                        </Badge>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredItems.map((item, index) => {
                    const winner = getWinner(item)
                    
                    return (
                      <tr
                        key={index}
                        className="border-b border-gray-800/50 hover:bg-gray-800/20 transition-colors"
                      >
                        <td className="p-4">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-300">{item.label}</span>
                            {item.tooltip && (
                              <Tooltip>
                                <TooltipTrigger>
                                  <Info className="w-4 h-4 text-gray-500" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p className="max-w-xs">{item.tooltip}</p>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        </td>
                        <td className={cn(
                          "p-4 text-center",
                          winner === 'model1' && "bg-purple-600/10"
                        )}>
                          {renderValue(item, model1)}
                        </td>
                        <td className={cn(
                          "p-4 text-center",
                          winner === 'model2' && "bg-blue-600/10"
                        )}>
                          {renderValue(item, model2)}
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
        
        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4">
          <Card className="bg-purple-900/20 border-purple-600/20">
            <CardContent className="p-4">
              <h4 className="font-semibold text-purple-300 mb-2">{model1.name} Advantages</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                {comparisonItems.map((item) => {
                  const winner = getWinner(item)
                  if (winner === 'model1') {
                    return (
                      <li key={item.label} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                        {item.label}
                      </li>
                    )
                  }
                  return null
                })}
              </ul>
            </CardContent>
          </Card>
          
          <Card className="bg-blue-900/20 border-blue-600/20">
            <CardContent className="p-4">
              <h4 className="font-semibold text-blue-300 mb-2">{model2.name} Advantages</h4>
              <ul className="space-y-1 text-sm text-gray-300">
                {comparisonItems.map((item) => {
                  const winner = getWinner(item)
                  if (winner === 'model2') {
                    return (
                      <li key={item.label} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                        {item.label}
                      </li>
                    )
                  }
                  return null
                })}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </TooltipProvider>
  )
}