'use client'

import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import Link from 'next/link'

function ErrorContent() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')
  
  const getErrorDetails = (errorType: string | null) => {
    switch (errorType) {
      case 'Configuration':
        return {
          title: 'Authentication Configuration Error',
          description: 'There was a problem with the authentication setup. Please try signing in again.',
          suggestion: 'If this problem persists, please contact support.'
        }
      case 'AccessDenied':
        return {
          title: 'Access Denied',
          description: 'You do not have permission to sign in.',
          suggestion: 'Please contact an administrator if you believe this is an error.'
        }
      case 'Verification':
        return {
          title: 'Unable to sign in',
          description: 'The sign in link is no longer valid. It may have been used already or it may have expired.',
          suggestion: 'Please try signing in again.'
        }
      case 'Default':
      default:
        return {
          title: 'Sign in Error',
          description: 'An unexpected error occurred during sign in.',
          suggestion: 'Please try signing in again. If the problem persists, please contact support.'
        }
    }
  }

  const errorDetails = getErrorDetails(error)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 text-center">
        {/* Error Icon */}
        <div className="mb-6">
          <div className="w-16 h-16 mx-auto bg-red-500/20 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        </div>

        {/* Error Details */}
        <h1 className="text-2xl font-bold text-white mb-4">
          {errorDetails.title}
        </h1>
        
        <p className="text-gray-300 mb-4">
          {errorDetails.description}
        </p>
        
        <p className="text-sm text-gray-400 mb-8">
          {errorDetails.suggestion}
        </p>

        {/* Error Code */}
        {error && (
          <div className="mb-6 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p className="text-sm text-red-300">
              Error Code: <span className="font-mono">{error}</span>
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="space-y-4">
          <Link
            href="/"
            className="block w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105"
          >
            Try Signing In Again
          </Link>
          
          <Link
            href="/docs"
            className="block w-full text-gray-300 hover:text-white font-medium py-3 px-6 border border-gray-600 hover:border-gray-500 rounded-xl transition-colors duration-200"
          >
            View Documentation
          </Link>
        </div>

        {/* Debug Info (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
            <h3 className="text-sm font-semibold text-gray-300 mb-2">Debug Info</h3>
            <pre className="text-xs text-gray-400 text-left overflow-x-auto">
              {JSON.stringify({ 
                error
              }, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}

export default function AuthError() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    }>
      <ErrorContent />
    </Suspense>
  )
}