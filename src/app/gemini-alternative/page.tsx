import Link from 'next/link';
import { CheckCircle, XCircle, ArrowRight, Sparkles, DollarSign, Brain, Zap, Image, Search, Globe, Shield, Users, FileText, Camera } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TOTAL_MODELS_MARKETING } from '@/lib/model-stats';

// Metadata is exported from ./metadata.ts
export { metadata } from './metadata';

export default function GeminiAlternativePage() {
  return (
    <>
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-black">
        {/* Hero Section */}
        <section className="pt-20 pb-16 px-4">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Sparkles className="w-4 h-4" />
              The Smarter Gemini Alternative
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Get Gemini 2.5 Pro + <span className="text-blue-600">{TOTAL_MODELS_MARKETING} More Models</span>
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Keep using Google&apos;s Gemini 2.5 Pro Experimental, but also get Claude 4, GPT-4.5 Orion, DeepSeek, and {TOTAL_MODELS_MARKETING} other AI models 
              for <strong>the same price as Gemini Advanced</strong> - without Google&apos;s restrictions.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/">
                <Button size="lg" className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/">
                <Button size="lg" variant="outline" className="w-full sm:w-auto">
                  View Pricing
                </Button>
              </Link>
            </div>

            <p className="text-sm text-gray-500 dark:text-gray-400">
              Keep Gemini&apos;s multimodal power • Add {TOTAL_MODELS_MARKETING} more models • No Google ecosystem lock-in
            </p>
          </div>
        </section>

        {/* Comparison Table */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              JustSimpleChat vs Gemini Advanced
            </h2>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-lg">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left p-4">Feature</th>
                    <th className="text-center p-4">
                      <div className="font-bold text-blue-600">JustSimpleChat</div>
                      <div className="text-sm text-gray-500">From $19.99/month</div>
                    </th>
                    <th className="text-center p-4">
                      <div className="font-bold">Gemini Advanced</div>
                      <div className="text-sm text-gray-500">$19.99-$34.99/month</div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Access to Gemini 2.5 Pro Experimental</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Access to Latest Gemini Models</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
                    <td className="p-4 font-medium">Access to Claude & GPT-4</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">No Google Account Required</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
                    <td className="p-4 font-medium">Total AI Models</td>
                    <td className="text-center p-4 font-bold text-blue-600">{TOTAL_MODELS_MARKETING}</td>
                    <td className="text-center p-4">3-5</td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">No Usage Quotas (Advanced Plan)</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">API Access Without Restrictions</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Multimodal (Images, Video, Text)</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Real-Time Web Search</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="bg-green-50 dark:bg-green-900/20">
                    <td className="p-4 font-bold">Value</td>
                    <td className="text-center p-4 font-bold text-green-600">Same price, {TOTAL_MODELS_MARKETING} models, no Google lock-in</td>
                    <td className="text-center p-4">Google ecosystem required</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* Gemini-Specific Benefits */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Keep Gemini&apos;s Power, Escape Google&apos;s Limitations
            </h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <Camera className="w-10 h-10 text-blue-500 mb-2" />
                  <CardTitle>Same Multimodal Power</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Keep using Gemini 2.5 Pro Experimental for image analysis, document understanding, and video processing.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Shield className="w-10 h-10 text-green-500 mb-2" />
                  <CardTitle>No Google Tracking</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Use Gemini models without Google account requirements, data integration, or privacy concerns.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Zap className="w-10 h-10 text-purple-500 mb-2" />
                  <CardTitle>No Usage Quotas</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Escape Gemini Advanced&apos;s restrictive usage caps and API limitations that frustrate developers.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Brain className="w-10 h-10 text-orange-500 mb-2" />
                  <CardTitle>Better for Complex Tasks</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Use Gemini for visual tasks, Claude for reasoning, GPT-4 for creativity - best model for each job.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Gemini Users Enhanced Capabilities */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Gemini Users Get Even More Capabilities
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  {/* eslint-disable-next-line jsx-a11y/alt-text */}
                  <Image className="w-6 h-6 text-blue-500" />
                  For Visual Analysis (Enhanced)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Gemini 2.5 Pro:</strong> Your go-to for complex image and video analysis</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Claude:</strong> Superior document analysis and text reasoning</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>GPT-4V:</strong> Creative image generation and different perspectives</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Search className="w-6 h-6 text-green-500" />
                  For Research (Unrestricted)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Gemini:</strong> Real-time web search and current events</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Perplexity:</strong> Enhanced research with citations and sources</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>No quotas:</strong> Unlimited research without Google&apos;s restrictions</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Users className="w-6 h-6 text-purple-500" />
                  For Development (No API Limits)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Gemini:</strong> Multimodal app development and UI analysis</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Claude:</strong> Complex system architecture and code review</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>No restrictions:</strong> Full API access without Google&apos;s developer limits</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <FileText className="w-6 h-6 text-orange-500" />
                  For Content Creation (Accurate)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Gemini:</strong> Fact-checking and current information synthesis</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Claude:</strong> Long-form content without Gemini&apos;s accuracy issues</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Cross-verification:</strong> Check facts across multiple AI models</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Why Switch from Google */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Escape Google&apos;s Ecosystem Lock-in
            </h2>

            <div className="grid md:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <XCircle className="w-8 h-8 text-red-600" />
                  </div>
                  <CardTitle className="text-red-600">Google&apos;s Problem</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Requires Google account, tracks your usage, integrates with Gmail/Docs, has API restrictions and usage quotas.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <ArrowRight className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-blue-600">Easy Migration</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Keep using Gemini models you love, but without Google&apos;s restrictions. Same features, more freedom.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle className="text-green-600">Our Solution</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    No Google account needed, no tracking, no ecosystem lock-in. Plus {TOTAL_MODELS_MARKETING} additional AI models.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Recent User Complaints About Gemini */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Current Gemini Advanced Issues (July 2025)
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Usage Quotas & API Restrictions</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p>&quot;Free and lower-tier plans suffer from usage quotas and limited access to advanced features. Some API restrictions persist, frustrating developers.&quot; - Current user reviews</p>
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: No quotas on Advanced+ plans</p>
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Accuracy Issues with Recent Events</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p>&quot;Occasional accuracy issues with very recent events, despite real-time web integration, and slow rollout of advanced AI models.&quot; - July 2025 reviews</p>
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: Cross-verify with multiple models</p>
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Google Ecosystem Dependency</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p>&quot;Users outside the Google ecosystem may find integration underwhelming and feature set redundant.&quot; - Expert analysis</p>
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: Works independently of any ecosystem</p>
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Privacy & Data Integration Concerns</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p>&quot;Privacy concerns over integration with Google Workspace and data collection across Google services.&quot; - Security reviews</p>
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: No cross-service data sharing</p>
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold mb-6">
              Ready to Escape Google&apos;s Limitations?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Keep using Gemini 2.5 Pro Experimental, but add {TOTAL_MODELS_MARKETING} more AI models without Google&apos;s restrictions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/">
                <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                  Start 3-Day Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/">
                <Button size="lg" variant="outline" className="w-full sm:w-auto bg-transparent text-white border-white hover:bg-white hover:text-blue-600">
                  Compare Plans
                </Button>
              </Link>
            </div>
            <p className="mt-6 text-sm opacity-75">
              No Google account required • Keep your privacy • Cancel anytime
            </p>
          </div>
        </section>
      </div>
    </>
  );
}