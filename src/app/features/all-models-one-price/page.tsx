'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON>les, Check, X, Calculator, CreditCard, 
  TrendingUp, Shield, Users, Zap, DollarSign,
  BarChart3, PieChart, Wallet, Receipt
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

const FeatureCard = ({ icon: Icon, title, description }: any) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 hover:bg-gray-800/70 transition-all duration-300"
  >
    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5" />
    <div className="relative">
      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mb-4">
        <Icon className="w-6 h-6 text-white" />
      </div>
      <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
      <p className="text-gray-400">{description}</p>
    </div>
  </motion.div>
)

const PricingComparison = () => {
  const competitors = [
    { name: 'OpenAI', gpt4: 20, claude: 0, gemini: 0, others: 0, total: 20 },
    { name: 'Anthropic', gpt4: 0, claude: 20, gemini: 0, others: 0, total: 20 },
    { name: 'Google', gpt4: 0, claude: 0, gemini: 20, others: 0, total: 20 },
    { name: 'All Separate', gpt4: 20, claude: 20, gemini: 20, others: 50, total: 110 },
    { name: 'JustSimpleChat', gpt4: 19, claude: 19, gemini: 19, others: 19, total: 19 },
  ]

  return (
    <div className="relative overflow-x-auto">
      <table className="w-full text-left">
        <thead>
          <tr className="border-b border-gray-700">
            <th className="pb-4 pr-6 text-gray-400 font-medium">Provider</th>
            <th className="pb-4 px-6 text-gray-400 font-medium">GPT-4</th>
            <th className="pb-4 px-6 text-gray-400 font-medium">Claude</th>
            <th className="pb-4 px-6 text-gray-400 font-medium">Gemini</th>
            <th className="pb-4 px-6 text-gray-400 font-medium">Others</th>
            <th className="pb-4 pl-6 text-gray-400 font-medium">Total/mo</th>
          </tr>
        </thead>
        <tbody>
          {competitors.map((provider, idx) => (
            <tr 
              key={idx} 
              className={`border-b border-gray-800 ${
                provider.name === 'JustSimpleChat' 
                  ? 'bg-gradient-to-r from-purple-500/10 to-pink-500/10' 
                  : ''
              }`}
            >
              <td className="py-4 pr-6">
                <div className="flex items-center gap-2">
                  <span className="text-white font-medium">{provider.name}</span>
                  {provider.name === 'JustSimpleChat' && (
                    <span className="px-2 py-0.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-semibold">
                      BEST VALUE
                    </span>
                  )}
                </div>
              </td>
              <td className="py-4 px-6">
                {provider.gpt4 > 0 ? (
                  <span className="text-green-400">${provider.gpt4}</span>
                ) : (
                  <X className="w-4 h-4 text-gray-600" />
                )}
              </td>
              <td className="py-4 px-6">
                {provider.claude > 0 ? (
                  <span className="text-green-400">${provider.claude}</span>
                ) : (
                  <X className="w-4 h-4 text-gray-600" />
                )}
              </td>
              <td className="py-4 px-6">
                {provider.gemini > 0 ? (
                  <span className="text-green-400">${provider.gemini}</span>
                ) : (
                  <X className="w-4 h-4 text-gray-600" />
                )}
              </td>
              <td className="py-4 px-6">
                {provider.others > 0 ? (
                  <span className="text-green-400">${provider.others}</span>
                ) : (
                  <X className="w-4 h-4 text-gray-600" />
                )}
              </td>
              <td className="py-4 pl-6">
                <span className={`font-bold text-lg ${
                  provider.name === 'JustSimpleChat' 
                    ? 'text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400' 
                    : 'text-white'
                }`}>
                  ${provider.total}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

const SavingsCalculator = () => {
  const [models, setModels] = React.useState(3)
  const costPerModel = 20
  const ourPrice = 19
  const savings = (models * costPerModel) - ourPrice

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm border border-purple-500/20 p-8"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5" />
      
      <div className="relative">
        <div className="flex items-center gap-3 mb-6">
          <Calculator className="w-8 h-8 text-purple-400" />
          <h3 className="text-2xl font-bold text-white">Savings Calculator</h3>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-gray-400 mb-2">
              How many AI models do you use?
            </label>
            <input
              type="range"
              min="1"
              max="10"
              value={models}
              onChange={(e) => setModels(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-purple-500"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>1</span>
              <span className="text-lg font-bold text-white">{models} models</span>
              <span>10+</span>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-4 rounded-lg bg-gray-800/50">
              <div className="text-2xl font-bold text-gray-400">${models * costPerModel}</div>
              <div className="text-sm text-gray-500">Separate Subscriptions</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30">
              <div className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                ${ourPrice}
              </div>
              <div className="text-sm text-purple-300">JustSimpleChat</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-green-900/20 border border-green-500/30">
              <div className="text-2xl font-bold text-green-400">${savings}</div>
              <div className="text-sm text-green-300">Monthly Savings</div>
            </div>
          </div>

          <div className="text-center p-6 rounded-lg bg-gradient-to-r from-purple-900/30 to-pink-900/30 border border-purple-500/30">
            <div className="text-4xl font-bold text-white mb-2">
              ${savings * 12}
            </div>
            <div className="text-lg text-purple-300">Annual Savings</div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default function AllModelsOnePricePage() {
  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        {/* Animated background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-br from-purple-500/20 to-transparent rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-br from-pink-500/20 to-transparent rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-50 animate-pulse" />
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <DollarSign className="w-10 h-10 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-6">
              All Models.{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                One Price.
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Stop juggling multiple AI subscriptions. Get unlimited access to GPT-4, Claude, 
              Gemini, and 180+ other models for one simple price.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Sparkles className="w-5 h-5" />
                Start Saving Today
              </Link>
              <Link
                href="#calculator"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <Calculator className="w-5 h-5" />
                Calculate Your Savings
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* The Problem Section */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              The Hidden Cost of AI Subscriptions
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Most users need multiple AI models for different tasks. The costs add up quickly.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <FeatureCard
              icon={CreditCard}
              title="Multiple Subscriptions"
              description="$20 for GPT-4, $20 for Claude, $20 for Gemini... it never ends."
            />
            <FeatureCard
              icon={Receipt}
              title="Usage Limits"
              description="Hit your limit on one model? Time to switch subscriptions or pay more."
            />
            <FeatureCard
              icon={BarChart3}
              title="Tracking Nightmares"
              description="Managing multiple accounts, invoices, and usage dashboards."
            />
            <FeatureCard
              icon={Wallet}
              title="Budget Overruns"
              description="$100+ monthly for comprehensive AI access becomes the norm."
            />
          </div>

          {/* Visual Comparison */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-8"
          >
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              Real Cost Comparison
            </h3>
            <PricingComparison />
          </motion.div>
        </div>
      </section>

      {/* Our Solution Section */}
      <section className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              One Subscription. Unlimited Possibilities.
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Access every AI model you need for less than the cost of a single provider.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Check className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">180+ Models Included</h3>
                  <p className="text-gray-400">
                    From GPT-4o and Claude 3.5 to specialized models for coding, image generation, 
                    and analysis - all in one place.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Check className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">No Usage Limits</h3>
                  <p className="text-gray-400">
                    Use any model as much as you need. No monthly caps, no surprise charges, 
                    just unlimited AI access.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Check className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Smart Model Routing</h3>
                  <p className="text-gray-400">
                    Our AI automatically selects the best model for each task, saving you time 
                    and maximizing quality.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Check className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">New Models Added Weekly</h3>
                  <p className="text-gray-400">
                    As new models launch, they&apos;re automatically added to your subscription at 
                    no extra cost.
                  </p>
                </div>
              </div>
            </motion.div>

            <div id="calculator">
              <SavingsCalculator />
            </div>
          </div>
        </div>
      </section>

      {/* What's Included Section */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Everything You Get for $19/month
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Compare this to paying for individual subscriptions
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <h3 className="text-xl font-bold text-white mb-4">Premium Models</h3>
                <ul className="space-y-3">
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    GPT-4o & GPT-4 Turbo
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Claude 3.5 Sonnet & Opus
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Gemini 1.5 Pro & Flash
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    O1 Preview & O1 Mini
                  </li>
                </ul>
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <p className="text-sm text-gray-500">Individual cost: $80+/mo</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <h3 className="text-xl font-bold text-white mb-4">Specialized Models</h3>
                <ul className="space-y-3">
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    DeepSeek Coder & R1
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Mistral Large & Medium
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Perplexity Online Models
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Llama 3.1 405B & 70B
                  </li>
                </ul>
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <p className="text-sm text-gray-500">Individual cost: $60+/mo</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <h3 className="text-xl font-bold text-white mb-4">Plus Features</h3>
                <ul className="space-y-3">
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Image Generation Models
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Voice & Audio Models
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Code Analysis Tools
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    API Access Available
                  </li>
                </ul>
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <p className="text-sm text-gray-500">Additional value: $40+/mo</p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Total Value */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="mt-12 text-center"
          >
            <div className="inline-flex items-center gap-4 px-8 py-4 rounded-2xl bg-gradient-to-r from-purple-900/30 to-pink-900/30 border border-purple-500/30">
              <div>
                <p className="text-gray-400">Total Value</p>
                <p className="text-3xl font-bold text-white">$180+/mo</p>
              </div>
              <div className="text-4xl">→</div>
              <div>
                <p className="text-purple-300">Your Price</p>
                <p className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                  $19/mo
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
              Ready to Simplify Your AI Stack?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands who&apos;ve already consolidated their AI subscriptions and saved hundreds.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Sparkles className="w-5 h-5" />
                Start Your Free Trial
              </Link>
              <Link
                href="/pricing"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                View Pricing Details
              </Link>
            </div>

            <p className="mt-6 text-gray-400">
              No credit card required • Cancel anytime • 7-day free trial
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  )
}