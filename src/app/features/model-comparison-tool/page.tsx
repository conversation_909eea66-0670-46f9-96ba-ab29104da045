'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Search, BarChart3, Zap, CheckCircle, XCircle, 
  ArrowRight, Play, Target, Users, Clock, 
  TrendingUp, Award, Sparkles, Filter,
  Eye, Code, MessageSquare, Image, FileText
} from 'lucide-react'
import Link from 'next/link'

const FeatureCard = ({ icon: Icon, title, description, color = "purple" }: any) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6 hover:bg-gray-800/70 transition-all duration-300"
  >
    <div className={`absolute inset-0 bg-gradient-to-br ${
      color === 'purple' ? 'from-purple-500/5 to-pink-500/5' :
      color === 'blue' ? 'from-blue-500/5 to-cyan-500/5' :
      color === 'green' ? 'from-green-500/5 to-emerald-500/5' :
      'from-orange-500/5 to-red-500/5'
    }`} />
    <div className="relative">
      <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${
        color === 'purple' ? 'from-purple-500 to-pink-500' :
        color === 'blue' ? 'from-blue-500 to-cyan-500' :
        color === 'green' ? 'from-green-500 to-emerald-500' :
        'from-orange-500 to-red-500'
      } flex items-center justify-center mb-4`}>
        <Icon className="w-6 h-6 text-white" />
      </div>
      <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
      <p className="text-gray-400">{description}</p>
    </div>
  </motion.div>
)

const LiveComparison = () => {
  const [selectedModels, setSelectedModels] = React.useState(['gpt-4o', 'claude-3-5-sonnet'])
  const [testPrompt, setTestPrompt] = React.useState("Explain quantum computing in simple terms")
  const [isRunning, setIsRunning] = React.useState(false)

  const models = [
    { id: 'gpt-4o', name: 'GPT-4o', provider: 'OpenAI', speed: 95, quality: 94, cost: 'High' },
    { id: 'claude-3-5-sonnet', name: 'Claude 3.5 Sonnet', provider: 'Anthropic', speed: 88, quality: 96, cost: 'High' },
    { id: 'gemini-1-5-pro', name: 'Gemini 1.5 Pro', provider: 'Google', speed: 92, quality: 90, cost: 'Medium' },
    { id: 'deepseek-r1', name: 'DeepSeek R1', provider: 'DeepSeek', speed: 85, quality: 93, cost: 'Low' }
  ]

  const runComparison = () => {
    setIsRunning(true)
    setTimeout(() => setIsRunning(false), 3000)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm border border-purple-500/20 p-8"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5" />
      
      <div className="relative">
        <div className="flex items-center gap-3 mb-6">
          <BarChart3 className="w-8 h-8 text-purple-400" />
          <h3 className="text-2xl font-bold text-white">Interactive Model Comparison</h3>
        </div>

        {/* Model Selection */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-3">Select models to compare:</label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {models.map((model) => (
              <label
                key={model.id}
                className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-all ${
                  selectedModels.includes(model.id)
                    ? 'border-purple-500 bg-purple-500/10'
                    : 'border-gray-700 bg-gray-800/50 hover:bg-gray-800/70'
                }`}
              >
                <input
                  type="checkbox"
                  checked={selectedModels.includes(model.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedModels([...selectedModels, model.id])
                    } else {
                      setSelectedModels(selectedModels.filter(id => id !== model.id))
                    }
                  }}
                  className="rounded bg-gray-700 border-gray-600 text-purple-500 focus:ring-purple-500"
                />
                <div className="flex-1">
                  <div className="font-medium text-white">{model.name}</div>
                  <div className="text-sm text-gray-400">{model.provider}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Test Prompt */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2">Test prompt:</label>
          <textarea
            value={testPrompt}
            onChange={(e) => setTestPrompt(e.target.value)}
            className="w-full p-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:border-purple-500 focus:ring-1 focus:ring-purple-500 resize-none"
            rows={3}
            placeholder="Enter a prompt to test across models..."
          />
        </div>

        {/* Run Button */}
        <button
          onClick={runComparison}
          disabled={selectedModels.length < 2 || isRunning}
          className="w-full px-6 py-3 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {isRunning ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Running comparison...
            </>
          ) : (
            <>
              <Play className="w-5 h-5" />
              Compare Models
            </>
          )}
        </button>

        {/* Results Preview */}
        {selectedModels.length >= 2 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="mt-6 pt-6 border-t border-gray-700"
          >
            <h4 className="text-lg font-semibold text-white mb-4">Comparison Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {['Speed', 'Quality', 'Cost'].map((metric) => (
                <div key={metric} className="bg-gray-800/50 rounded-lg p-4">
                  <div className="text-sm text-gray-400 mb-2">{metric}</div>
                  <div className="space-y-2">
                    {selectedModels.slice(0, 2).map((modelId, idx) => {
                      const model = models.find(m => m.id === modelId)
                      const value = metric === 'Speed' ? model?.speed : 
                                   metric === 'Quality' ? model?.quality : model?.cost
                      return (
                        <div key={modelId} className="flex items-center justify-between">
                          <span className="text-sm text-gray-300">{model?.name}</span>
                          <span className={`text-sm font-medium ${
                            idx === 0 ? 'text-purple-400' : 'text-pink-400'
                          }`}>
                            {typeof value === 'number' ? `${value}%` : value}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}

const ComparisonCategories = () => {
  const categories = [
    {
      icon: MessageSquare,
      name: 'Conversational AI',
      description: 'Natural dialogue and Q&A',
      models: 12,
      color: 'blue'
    },
    {
      icon: Code,
      name: 'Code Generation',
      description: 'Programming and development',
      models: 8,
      color: 'purple'
    },
    {
      icon: FileText,
      name: 'Content Writing',
      description: 'Articles, blogs, copywriting',
      models: 15,
      color: 'green'
    },
    {
      icon: BarChart3,
      name: 'Data Analysis',
      description: 'Research and insights',
      models: 6,
      color: 'orange'
    },
    {
      icon: Image,
      name: 'Multimodal',
      description: 'Vision and image understanding',
      models: 9,
      color: 'pink'
    },
    {
      icon: Target,
      name: 'Specialized Tasks',
      description: 'Domain-specific applications',
      models: 11,
      color: 'cyan'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {categories.map((category, idx) => (
        <motion.div
          key={category.name}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: idx * 0.1 }}
          className="relative group cursor-pointer"
        >
          <div className={`absolute inset-0 bg-gradient-to-br ${
            category.color === 'blue' ? 'from-blue-500/20 to-cyan-500/20' :
            category.color === 'purple' ? 'from-purple-500/20 to-pink-500/20' :
            category.color === 'green' ? 'from-green-500/20 to-emerald-500/20' :
            category.color === 'orange' ? 'from-orange-500/20 to-red-500/20' :
            category.color === 'pink' ? 'from-pink-500/20 to-rose-500/20' :
            'from-cyan-500/20 to-blue-500/20'
          } rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300`} />
          
          <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 hover:bg-gray-800/70 transition-all duration-300">
            <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${
              category.color === 'blue' ? 'from-blue-500 to-cyan-500' :
              category.color === 'purple' ? 'from-purple-500 to-pink-500' :
              category.color === 'green' ? 'from-green-500 to-emerald-500' :
              category.color === 'orange' ? 'from-orange-500 to-red-500' :
              category.color === 'pink' ? 'from-pink-500 to-rose-500' :
              'from-cyan-500 to-blue-500'
            } flex items-center justify-center mb-4`}>
              <category.icon className="w-6 h-6 text-white" />
            </div>
            
            <h3 className="text-lg font-semibold text-white mb-2">{category.name}</h3>
            <p className="text-gray-400 mb-4">{category.description}</p>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">{category.models} models available</span>
              <ArrowRight className="w-4 h-4 text-gray-500 group-hover:text-purple-400 transition-colors" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export default function ModelComparisonToolPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        {/* Animated background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-br from-purple-500/20 to-transparent rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-br from-pink-500/20 to-transparent rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-50 animate-pulse" />
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <BarChart3 className="w-10 h-10 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-6">
              Compare AI Models{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                Side by Side
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Test, analyze, and compare 180+ AI models in real-time. Find the perfect 
              model for your specific needs with interactive benchmarks.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/compare"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Play className="w-5 h-5" />
                Try Comparison Tool
              </Link>
              <Link
                href="#features"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <Eye className="w-5 h-5" />
                See Features
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* The Problem Section */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Choosing AI Models Shouldn&apos;t Be Guesswork
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              With 180+ AI models available, finding the right one for your task is overwhelming.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <FeatureCard
              icon={Search}
              title="Too Many Options"
              description="180+ models with different strengths. Which one is best for your specific use case?"
              color="orange"
            />
            <FeatureCard
              icon={Clock}
              title="Time-Consuming Testing"
              description="Manually testing each model takes hours. You need results now."
              color="red"
            />
            <FeatureCard
              icon={Target}
              title="Unclear Performance"
              description="Marketing claims vs. real performance. What works for your actual needs?"
              color="yellow"
            />
            <FeatureCard
              icon={TrendingUp}
              title="Optimization Challenges"
              description="Even after choosing, how do you know if you&apos;re getting the best results?"
              color="blue"
            />
          </div>

          {/* Current Process Pain Points */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-8"
          >
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              The Current Model Selection Process
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-500/20 flex items-center justify-center">
                  <span className="text-2xl">😰</span>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Guess & Hope</h4>
                <p className="text-gray-400">
                  Pick a popular model and hope it works for your specific task.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-orange-500/20 flex items-center justify-center">
                  <span className="text-2xl">⏱️</span>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Trial & Error</h4>
                <p className="text-gray-400">
                  Spend days testing different models manually, one by one.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-yellow-500/20 flex items-center justify-center">
                  <span className="text-2xl">🤷</span>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Settle for &ldquo;Good Enough&rdquo;</h4>
                <p className="text-gray-400">
                  Use whatever works, even if it&apos;s not optimal for your needs.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Solution Section */}
      <section id="features" className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Intelligent Model Comparison Made Simple
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Test multiple models simultaneously with real-time results and detailed analytics.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Play className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Side-by-Side Testing</h3>
                  <p className="text-gray-400">
                    Send the same prompt to multiple models simultaneously. Compare outputs, 
                    response times, and quality metrics in real-time.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Detailed Analytics</h3>
                  <p className="text-gray-400">
                    Get comprehensive metrics: response time, token usage, cost per request, 
                    and quality scores for informed decisions.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Filter className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Smart Filtering</h3>
                  <p className="text-gray-400">
                    Filter models by task type, performance requirements, cost constraints, 
                    or provider preferences to narrow your options.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Award className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Personalized Recommendations</h3>
                  <p className="text-gray-400">
                    Based on your usage patterns and preferences, get AI-powered suggestions 
                    for the best models for each task.
                  </p>
                </div>
              </div>
            </motion.div>

            <LiveComparison />
          </div>
        </div>
      </section>

      {/* Comparison Categories */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Compare by Use Case
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Pre-built comparison templates for common AI tasks and workflows
            </p>
          </motion.div>

          <ComparisonCategories />
        </div>
      </section>

      {/* Advanced Features */}
      <section className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Advanced Comparison Features
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Professional tools for detailed model analysis and optimization
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={Zap}
              title="Performance Benchmarks"
              description="Standardized tests across speed, accuracy, and efficiency metrics with industry comparisons."
              color="blue"
            />
            <FeatureCard
              icon={Users}
              title="Team Collaboration"
              description="Share comparison results with your team. Create custom evaluation criteria and scoring systems."
              color="purple"
            />
            <FeatureCard
              icon={Clock}
              title="Historical Tracking"
              description="Monitor model performance over time. Track improvements and identify degradation patterns."
              color="green"
            />
            <FeatureCard
              icon={Target}
              title="A/B Testing Suite"
              description="Run controlled experiments comparing models on your actual workloads and datasets."
              color="orange"
            />
            <FeatureCard
              icon={BarChart3}
              title="Cost Optimization"
              description="Analyze cost-per-task across models. Find the best price-performance ratio for your budget."
              color="cyan"
            />
            <FeatureCard
              icon={CheckCircle}
              title="Quality Scoring"
              description="AI-powered quality assessment with custom criteria. Objective evaluation of output quality."
              color="pink"
            />
          </div>
        </div>
      </section>

      {/* Real Results Section */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Real Results from Real Users
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              See how teams use our comparison tool to optimize their AI workflows
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6"
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center">
                  <Code className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Development Team</h3>
                  <p className="text-gray-400">SaaS Startup</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                &ldquo;Discovered that DeepSeek R1 outperformed GPT-4 for our code review tasks 
                while costing 80% less. Saved our team $2k/month.&rdquo;
              </p>
              <div className="flex items-center gap-2 text-green-400">
                <TrendingUp className="w-4 h-4" />
                <span className="text-sm">80% cost reduction</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6"
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Marketing Agency</h3>
                  <p className="text-gray-400">Content Production</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                &ldquo;A/B tested 5 models for blog writing. Claude 3.5 had 40% higher 
                client satisfaction scores than our previous choice.&rdquo;
              </p>
              <div className="flex items-center gap-2 text-green-400">
                <Award className="w-4 h-4" />
                <span className="text-sm">40% quality improvement</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6"
            >
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Research Team</h3>
                  <p className="text-gray-400">Data Analysis</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4">
                &ldquo;Compared 8 models for data analysis. Found the perfect model mix 
                that reduced processing time by 60% with better accuracy.&rdquo;
              </p>
              <div className="flex items-center gap-2 text-green-400">
                <Zap className="w-4 h-4" />
                <span className="text-sm">60% faster processing</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
              Stop Guessing. Start Optimizing.
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Make data-driven decisions about AI models. Find the perfect match for every task.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/compare"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <BarChart3 className="w-5 h-5" />
                Try Comparison Tool
              </Link>
              <Link
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <Sparkles className="w-5 h-5" />
                Start Free Trial
              </Link>
            </div>

            <p className="mt-6 text-gray-400">
              No setup required • Compare 180+ models • Advanced analytics included
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  )
}