import Link from 'next/link'
import { 
  CheckCircle2, 
  XCircle, 
  ArrowRight, 
  Sparkles, 
  DollarSign, 
  Brain, 
  Zap,
  Trophy,
  Users,
  Shield,
  Gauge,
  MessageSquare,
  Globe,
  Code,
  TrendingUp,
  Clock,
  Star,
  ChevronRight,
  Rocket
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TOTAL_MODELS_MARKETING, TOTAL_MODELS } from '@/lib/model-stats'
import { generatePageStructuredData } from '@/lib/structured-data-enhanced'
import { cn } from '@/lib/utils'

// Metadata is exported from ./metadata.ts
export { metadata } from './metadata'

const comparisonData = [
  {
    feature: 'Access to GPT-4, GPT-4 Turbo',
    justsimple: true,
    chatgpt: true,
    highlight: false
  },
  {
    feature: 'Access to Claude 3.5 Sonnet & Opus',
    justsimple: true,
    chatgpt: false,
    highlight: true
  },
  {
    feature: 'Access to Gemini 2.0 Flash & Pro',
    justsimple: true,
    chatgpt: false,
    highlight: true
  },
  {
    feature: 'Access to Perplexity with Citations',
    justsimple: true,
    chatgpt: false,
    highlight: true
  },
  {
    feature: 'Total AI Models Available',
    justsimple: `${TOTAL_MODELS_MARKETING}+ Models`,
    chatgpt: '1 Model',
    highlight: true
  },
  {
    feature: 'Smart Model Selection',
    justsimple: true,
    chatgpt: false,
    highlight: true
  },
  {
    feature: 'Monthly Price',
    justsimple: 'From $7.99',
    chatgpt: '$20.00',
    highlight: true
  },
  {
    feature: 'Web Search & Citations',
    justsimple: true,
    chatgpt: true,
    highlight: false
  },
  {
    feature: 'Image Generation',
    justsimple: true,
    chatgpt: true,
    highlight: false
  },
  {
    feature: 'File Uploads',
    justsimple: true,
    chatgpt: true,
    highlight: false
  },
  {
    feature: 'API Access',
    justsimple: 'Coming Soon',
    chatgpt: true,
    highlight: false
  },
  {
    feature: 'Custom Instructions',
    justsimple: true,
    chatgpt: true,
    highlight: false
  }
]

const useCases = [
  {
    title: 'For Developers',
    icon: Code,
    items: [
      'Claude 3.5 for complex coding tasks',
      'DeepSeek R1 for algorithmic problems',
      'Qwen 2.5 Coder for code generation',
      'GitHub Copilot alternative built-in'
    ]
  },
  {
    title: 'For Writers',
    icon: MessageSquare,
    items: [
      'GPT-4 for creative writing',
      'Claude for long-form content',
      'Perplexity for research articles',
      'Multiple styles and tones'
    ]
  },
  {
    title: 'For Researchers',
    icon: Globe,
    items: [
      'Perplexity with real-time search',
      'Gemini for multimodal analysis',
      'Claude for document analysis',
      'Academic citation support'
    ]
  },
  {
    title: 'For Business',
    icon: TrendingUp,
    items: [
      'Multiple models for teams',
      'Cost optimization built-in',
      'One invoice, all models',
      'Priority support included'
    ]
  }
]

export default function ChatGPTAlternativePage() {
  const structuredData = generatePageStructuredData('alternative', {
    comparisonTarget: 'ChatGPT Plus',
    ourPrice: 7.99,
    theirPrice: 20.00,
    modelCount: TOTAL_MODELS_MARKETING,
    breadcrumbs: [
      { name: 'Home', url: '/' },
      { name: 'ChatGPT Alternative', url: '/chatgpt-alternative' }
    ]
  })

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="min-h-screen bg-gray-950">
        {/* Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-purple-900/20 via-gray-950 to-blue-900/20 pointer-events-none" />
        <div className="fixed inset-0 bg-[url('/grid.svg')] bg-center opacity-10 pointer-events-none" />
        
        {/* Floating Particles */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute w-96 h-96 bg-purple-600/20 rounded-full blur-3xl animate-float top-20 -left-48" />
          <div className="absolute w-96 h-96 bg-blue-600/20 rounded-full blur-3xl animate-float animation-delay-2000 bottom-20 -right-48" />
          <div className="absolute w-64 h-64 bg-green-600/20 rounded-full blur-3xl animate-float animation-delay-4000 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
        </div>

        {/* Hero Section */}
        <section className="relative pt-20 pb-16 px-4">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-xl text-purple-300 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-purple-600/30">
              <Sparkles className="w-4 h-4" />
              The Ultimate ChatGPT Alternative
              <Badge className="bg-green-600/20 text-green-300 border-green-600/30 ml-2">
                60% Cheaper
              </Badge>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
                Why Pay $20/month
              </span>
              <br />
              <span className="text-white">
                for Just One AI Model?
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              JustSimpleChat gives you <strong className="text-white">GPT-4</strong>, 
              <strong className="text-white"> Claude 3.5</strong>, 
              <strong className="text-white"> Gemini 2.0</strong>, and 
              <strong className="text-purple-400"> {TOTAL_MODELS_MARKETING}+ more AI models</strong> for 
              less than <strong className="text-green-400">half the price</strong> of ChatGPT Plus.
            </p>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center gap-6 mb-12">
              <div className="flex items-center gap-2 text-gray-400">
                <Users className="w-5 h-5 text-purple-400" />
                <span>10K+ Active Users</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400">
                <Star className="w-5 h-5 text-yellow-400" />
                <span>4.9/5 Rating</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400">
                <Shield className="w-5 h-5 text-green-400" />
                <span>SOC2 Compliant</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/signup">
                <Button 
                  size="lg" 
                  className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-6 text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
                >
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="#comparison">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="w-full sm:w-auto border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-8 py-6 text-lg rounded-xl"
                >
                  View Comparison
                </Button>
              </Link>
            </div>

            <p className="text-sm text-gray-500">
              No credit card required • 3-day free trial • Cancel anytime
            </p>
          </div>
        </section>

        {/* Quick Win Section */}
        <section className="py-12 px-4">
          <div className="max-w-4xl mx-auto">
            <Card className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border-green-600/30 backdrop-blur-xl">
              <CardContent className="p-8">
                <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-green-600/20 rounded-xl">
                      <Trophy className="w-8 h-8 text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-1">
                        Save $144/year vs ChatGPT Plus
                      </h3>
                      <p className="text-gray-400">
                        Get more models, pay less. It&apos;s that simple.
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-green-600/20 text-green-300 border-green-600/40 px-6 py-2 text-lg">
                    Limited Time Offer
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Detailed Comparison Table */}
        <section id="comparison" className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-white mb-4">
                Side-by-Side Comparison
              </h2>
              <p className="text-xl text-gray-400">
                See exactly what you get with JustSimpleChat vs ChatGPT Plus
              </p>
            </div>

            <div className="relative overflow-hidden rounded-2xl">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-blue-600/10" />
              <div className="relative bg-gray-900/80 backdrop-blur-xl border border-gray-800">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-800">
                      <th className="text-left p-6 text-gray-400 font-medium">Feature</th>
                      <th className="text-center p-6">
                        <div className="space-y-2">
                          <div className="font-bold text-xl text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
                            JustSimpleChat
                          </div>
                          <Badge className="bg-purple-600/20 text-purple-300 border-purple-600/40">
                            From $7.99/month
                          </Badge>
                        </div>
                      </th>
                      <th className="text-center p-6">
                        <div className="space-y-2">
                          <div className="font-bold text-xl text-gray-300">
                            ChatGPT Plus
                          </div>
                          <Badge variant="outline" className="border-gray-600 text-gray-400">
                            $20/month
                          </Badge>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {comparisonData.map((item, index) => (
                      <tr 
                        key={index} 
                        className={cn(
                          "border-b border-gray-800/50 transition-colors",
                          item.highlight && "bg-purple-900/10"
                        )}
                      >
                        <td className="p-6 font-medium">{item.feature}</td>
                        <td className="text-center p-6">
                          {typeof item.justsimple === 'boolean' ? (
                            item.justsimple ? (
                              <CheckCircle2 className="w-6 h-6 text-green-400 mx-auto" />
                            ) : (
                              <XCircle className="w-6 h-6 text-gray-600 mx-auto" />
                            )
                          ) : (
                            <span className={cn(
                              "font-semibold",
                              item.highlight && "text-purple-400"
                            )}>
                              {item.justsimple}
                            </span>
                          )}
                        </td>
                        <td className="text-center p-6">
                          {typeof item.chatgpt === 'boolean' ? (
                            item.chatgpt ? (
                              <CheckCircle2 className="w-6 h-6 text-green-400 mx-auto" />
                            ) : (
                              <XCircle className="w-6 h-6 text-gray-600 mx-auto" />
                            )
                          ) : (
                            <span className="text-gray-400">{item.chatgpt}</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Grid */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-bold text-center text-white mb-12">
              Why 10,000+ Users Switched from ChatGPT
            </h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="group hover:scale-105 transition-transform duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-purple-600/50">
                <CardHeader>
                  <div className="p-3 bg-gradient-to-r from-green-600 to-emerald-600 rounded-xl w-fit mb-4">
                    <DollarSign className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">60% Cheaper</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-400">
                    Get {TOTAL_MODELS_MARKETING}+ AI models for less than half the price. Save $144/year compared to ChatGPT Plus.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="group hover:scale-105 transition-transform duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-blue-600/50">
                <CardHeader>
                  <div className="p-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl w-fit mb-4">
                    <Brain className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">{TOTAL_MODELS_MARKETING}+ Models</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-400">
                    Access Claude, Gemini, Llama, Mistral, and dozens more. ChatGPT Plus only gives you GPT-4.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="group hover:scale-105 transition-transform duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-purple-600/50">
                <CardHeader>
                  <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl w-fit mb-4">
                    <Zap className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">Smart Routing</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-400">
                    AI picks the best model for each task automatically. Code? Claude. Research? Perplexity. Creative? GPT-4.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="group hover:scale-105 transition-transform duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-orange-600/50">
                <CardHeader>
                  <div className="p-3 bg-gradient-to-r from-orange-600 to-red-600 rounded-xl w-fit mb-4">
                    <Rocket className="w-8 h-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">Latest Models</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-400">
                    Get new models instantly as they launch. No waiting for OpenAI to add them months later.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Use Cases Section */}
        <section className="py-16 px-4 relative">
          <div className="absolute inset-0 bg-gradient-to-b from-purple-900/10 to-transparent pointer-events-none" />
          <div className="max-w-6xl mx-auto relative">
            <h2 className="text-4xl font-bold text-center text-white mb-4">
              Better Than ChatGPT for Every Use Case
            </h2>
            <p className="text-xl text-center text-gray-400 mb-12 max-w-3xl mx-auto">
              Our smart routing ensures you always get the best model for your specific task
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {useCases.map((useCase, index) => {
                const Icon = useCase.icon
                return (
                  <Card key={index} className="bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-gray-700 transition-all h-full">
                    <CardHeader>
                      <Icon className="w-8 h-8 text-purple-400 mb-3" />
                      <CardTitle className="text-lg">{useCase.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {useCase.items.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-2 text-sm text-gray-400">
                            <ChevronRight className="w-4 h-4 text-purple-400 mt-0.5 flex-shrink-0" />
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        </section>

        {/* Model Showcase */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <Card className="bg-gray-900/60 backdrop-blur-xl border-gray-800 overflow-hidden">
              <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-600/10 to-transparent rounded-full blur-3xl" />
              <CardHeader className="relative text-center">
                <CardTitle className="text-3xl mb-4">
                  All These Premium Models Included
                </CardTitle>
                <CardDescription className="text-lg text-gray-400">
                  ChatGPT Plus gives you 1 model. We give you {TOTAL_MODELS_MARKETING}+. Here are just a few:
                </CardDescription>
              </CardHeader>
              <CardContent className="relative">
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {[
                    { name: 'GPT-4', provider: 'OpenAI', color: 'from-green-600 to-emerald-600' },
                    { name: 'Claude 3.5', provider: 'Anthropic', color: 'from-orange-600 to-red-600' },
                    { name: 'Gemini 2.0', provider: 'Google', color: 'from-blue-600 to-cyan-600' },
                    { name: 'Perplexity', provider: 'Perplexity', color: 'from-purple-600 to-pink-600' },
                    { name: 'Llama 3.1', provider: 'Meta', color: 'from-blue-600 to-indigo-600' },
                    { name: 'Mistral Large', provider: 'Mistral', color: 'from-orange-600 to-yellow-600' },
                    { name: 'DeepSeek R1', provider: 'DeepSeek', color: 'from-purple-600 to-blue-600' },
                    { name: 'Qwen 2.5', provider: 'Alibaba', color: 'from-red-600 to-pink-600' },
                    { name: 'Command R+', provider: 'Cohere', color: 'from-green-600 to-teal-600' },
                    { name: 'Grok', provider: 'xAI', color: 'from-gray-600 to-gray-700' },
                    { name: 'And More...', provider: `${TOTAL_MODELS - 10}+ Models`, color: 'from-purple-600 to-blue-600' }
                  ].map((model, index) => (
                    <div key={index} className="relative group">
                      <div className={cn(
                        "absolute inset-0 bg-gradient-to-r rounded-lg blur opacity-20 group-hover:opacity-40 transition-opacity",
                        model.color
                      )} />
                      <div className="relative bg-gray-800/80 backdrop-blur rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-all text-center">
                        <p className="font-semibold text-white text-sm">{model.name}</p>
                        <p className="text-xs text-gray-400 mt-1">{model.provider}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-bold text-center text-white mb-12">
              What ChatGPT Users Say After Switching
            </h2>
            
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  quote: "I was paying $20/month for just GPT-4. Now I get Claude, Gemini, and 150+ more models for $8. No-brainer switch.",
                  author: "Sarah Chen",
                  role: "Software Engineer",
                  rating: 5
                },
                {
                  quote: "The smart routing is incredible. It automatically uses Claude for coding and Perplexity for research. Saves me so much time.",
                  author: "Marcus Williams",
                  role: "Content Creator",
                  rating: 5
                },
                {
                  quote: "I needed access to multiple AI models for my team. JustSimpleChat costs less than one ChatGPT subscription but gives us everything.",
                  author: "Lisa Park",
                  role: "Startup Founder",
                  rating: 5
                }
              ].map((testimonial, index) => (
                <Card key={index} className="bg-gray-900/60 backdrop-blur-xl border-gray-800">
                  <CardContent className="p-6">
                    <div className="flex gap-1 mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-gray-300 mb-4 italic">&ldquo;{testimonial.quote}&rdquo;</p>
                    <div>
                      <p className="font-semibold text-white">{testimonial.author}</p>
                      <p className="text-sm text-gray-400">{testimonial.role}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-10 blur-3xl" />
          <div className="relative max-w-4xl mx-auto text-center">
            <div className="inline-flex items-center justify-center mb-8">
              <div className="p-4 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl">
                <Rocket className="w-12 h-12 text-white" />
              </div>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Save 60% and Get {TOTAL_MODELS_MARKETING}x More AI Models?
            </h2>
            
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join 10,000+ users who switched from ChatGPT and never looked back. 
              More models, better results, half the price.
            </p>
            
            <div className="bg-gray-900/60 backdrop-blur-xl rounded-2xl p-8 border border-gray-800 max-w-md mx-auto mb-8">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">ChatGPT Plus</span>
                  <span className="text-gray-500 line-through">$20/month</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white font-semibold">JustSimpleChat Plus</span>
                  <span className="text-2xl font-bold text-green-400">$7.99/month</span>
                </div>
                <div className="pt-4 border-t border-gray-800">
                  <div className="flex items-center justify-between">
                    <span className="text-green-400 font-semibold">You Save</span>
                    <span className="text-xl font-bold text-green-400">$144/year</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/signup">
                <Button 
                  size="lg" 
                  className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-10 py-7 text-xl rounded-xl shadow-2xl hover:shadow-3xl transition-all transform hover:scale-105"
                >
                  Start 3-Day Free Trial
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="w-full sm:w-auto border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-10 py-7 text-xl rounded-xl"
                >
                  Compare All Plans
                </Button>
              </Link>
            </div>
            
            <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4 text-green-400" />
                <span>No credit card required</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4 text-green-400" />
                <span>Cancel anytime</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-4 h-4 text-green-400" />
                <span>Keep your chat history</span>
              </div>
            </div>
            
            <p className="mt-8 text-gray-500 text-sm">
              Currently using ChatGPT Plus? We&apos;ll help you migrate your conversations for free.
            </p>
          </div>
        </section>
      </div>
    </>
  )
}