/**
 * Web Search API Route Handler
 * 
 * @description
 * This route provides web search functionality for the chat interface.
 * It supports three search methods:
 * 1. Native search - For models with built-in web search (Perplexity, Grok)
 * 2. Brave Search - Using Brave Search API for current information
 * 3. Firecrawl fallback - Web scraping as a last resort
 * 
 * The route intelligently selects the best search method based on:
 * - Model capabilities
 * - API availability
 * - Query requirements
 * 
 * @module api/web-search
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { apiLogger } from '@/lib/logger';
import { isLocalhostDebugRequest, createMockSession } from '@/lib/debug';
import type { SearchResult } from '@/types';
import { createAISDKProvider } from '@/lib/ai/providers';
import { BraveSearchProvider } from '@/lib/ai/search';
import { prisma } from '@/lib/db';

/**
 * Initialize AI SDK provider for query generation
 */
let aiProvider: any = null;
const getAIProvider = async () => {
  if (!aiProvider) {
    // Use fast provider for query generation
    aiProvider = createAISDKProvider('groq'); // Fast Groq for query generation
    await aiProvider.validateConfig();
  }
  return aiProvider;
};

// Initialize Brave Search
const braveSearch = process.env.BRAVE_SEARCH_API_KEY 
  ? new BraveSearchProvider(process.env.BRAVE_SEARCH_API_KEY)
  : null;

/**
 * Extended search result with full content
 * @interface SearchResultWithContent
 * @extends SearchResult
 * @property {string} [content] - Full page content (for Firecrawl results)
 */
interface SearchResultWithContent extends SearchResult {
  content?: string;
}

/**
 * Web search request payload
 * @interface WebSearchRequest
 * @property {string} query - User's search query
 * @property {string} [model] - AI model being used (affects search method)
 * @property {number} [limit] - Maximum number of results to return
 * @property {string[]} [searchQueries] - Router-generated search queries
 * @property {string} [conversationContext] - Recent conversation for context
 */
interface WebSearchRequest {
  query: string;
  model?: string;
  limit?: number;
  searchQueries?: string[]; // Router-generated search queries
  conversationContext?: string; // Recent conversation context for better query generation
}

/**
 * Web search response format
 * @interface WebSearchResponse
 * @property {SearchResult[]} results - Array of search results
 * @property {string} summary - AI-generated summary of results
 * @property {'native' | 'brave' | 'firecrawl'} searchType - Method used
 * @property {string[]} [searchQueries] - Actual queries used for search
 */
interface WebSearchResponse {
  results: SearchResult[];
  summary: string;
  searchType: 'native' | 'brave' | 'firecrawl';
  searchQueries?: string[]; // The actual queries used
}

/**
 * Models with native web search capabilities
 * These models can search the web directly without external APIs
 */
const NATIVE_SEARCH_MODELS = new Set([
  'perplexity-sonar',
  'sonar-pro',
  'sonar-small',
  'sonar',
  'llama-3.1-sonar-large-128k-online',
  'llama-3.1-sonar-small-128k-online',
  'llama-3.1-sonar-huge-128k-online',
  'grok-2',
  'grok-2-mini',
  'grok-3',
  'grok-3-fast',
  'gpt-4o-search-preview',
  'o1', 'o1-mini', 'o1-preview', // O1 series with web search
  'o3', 'o3-mini', 'o3-preview', 'o3-search-preview', // O3 series including search variant
  'o4', 'o4-mini', 'o4-preview', 'o4-mini-search-preview', // O4 series including search variant
]);

/**
 * Generates an optimized search query using AI
 * 
 * @async
 * @function generateSearchQuery
 * @param {string} userQuery - The user's original question
 * @param {string} [conversationContext] - Recent conversation for better context
 * @returns {Promise<string>} Optimized search query
 * 
 * @description
 * Uses GPT-4o-mini to transform user questions into effective search queries.
 * Falls back to the original query if generation fails or times out.
 */
async function generateSearchQuery(userQuery: string, conversationContext?: string): Promise<string> {
  console.log('[WebSearch] generateSearchQuery called with:', userQuery);
  try {
    const messages = [{
      role: 'system' as const,
      content: 'You are a search query optimizer. Generate ONE specific, targeted search query that will find the most relevant real-time information.'
    }, {
      role: 'user' as const,
      content: `User's question: "${userQuery}"
${conversationContext ? `\nConversation context: ${conversationContext}` : ''}

Generate a single, specific search query that will find the most relevant and current information.
Focus on key terms and be precise. Return only the search query, nothing else.`
    }];

    console.log('[WebSearch] Calling AI SDK provider...');
    
    // Add timeout wrapper for the API call
    const timeoutPromise = new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error('Query generation timeout')), 5000)
    );
    
    // Use AI SDK provider for query generation
    const provider = await getAIProvider();
    const response = await Promise.race([
      provider.generateCompletion({
        model: 'llama3-8b-8192', // Fast Groq model
        messages,
        temperature: 0.3, // Lower temperature for more focused queries
        maxTokens: 50,
      }),
      timeoutPromise
    ]);
    
    console.log('[WebSearch] AI SDK response received');

    // AI SDK returns text directly
    const query = (response || '').trim();

    return query.length > 0 ? query : userQuery;
  } catch (error) {
    console.error('[WebSearch] Failed to generate search query:', error);
    apiLogger.error('Failed to generate search query', error);
    // Fallback to original query
    return userQuery;
  }
}

async function searchWithBrave(query: string, limit: number = 5): Promise<SearchResult[]> {
  console.log('[WebSearch] searchWithBrave called with query:', query);
  
  if (!braveSearch) {
    console.error('[WebSearch] Brave Search API key not configured');
    throw new Error('Brave Search API key not configured');
  }

  try {
    console.log('[WebSearch] Searching for query:', query);
    
    const results = await braveSearch.search(query, {
      limit: limit,
      type: 'web',
      safeSearch: true,
    });
    
    console.log(`[WebSearch] Found ${results.length} results for query: ${query}`);
    
    return results;
  } catch (error) {
    console.error('[WebSearch] Error searching with Brave:', error);
    apiLogger.error('Brave search error', error);
    throw error; // Re-throw to handle at higher level
  }
}

async function fetchPageContent(url: string): Promise<string | null> {
  try {
    console.log('[WebSearch] Attempting to fetch content from:', url);
    
    // Simple fetch with timeout
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; JustSimpleChat/1.0)',
      },
      signal: controller.signal,
    }).finally(() => clearTimeout(timeout));
    
    if (!response.ok) {
      console.log('[WebSearch] Failed to fetch URL:', response.status);
      return null;
    }
    
    const html = await response.text();
    
    // Very basic HTML to text extraction (removes tags)
    const text = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
      .replace(/<[^>]+>/g, ' ') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .slice(0, 2000); // Limit to 2000 chars
    
    console.log('[WebSearch] Successfully extracted', text.length, 'characters');
    return text;
  } catch (error) {
    console.error('[WebSearch] Error fetching page content:', error);
    return null;
  }
}

async function summarizeResults(results: SearchResult[], userQuery: string): Promise<string> {
  console.log('[WebSearch] Starting summarizeResults with', results.length, 'results');
  if (results.length === 0) {
    return 'No search results found.';
  }

  try {
    // Create context from search results
    // Use full content when available, otherwise fall back to snippets
    const context = results.map((r, i) => {
      const contentInfo = (r as any).content 
        ? `Content: ${(r as any).content}` 
        : `Snippet: ${r.snippet}`;
      return `[${i + 1}] ${r.title}\nURL: ${r.url}\n${contentInfo}`;
    }).join('\n\n');

    const messages = [{
      role: 'system' as const,
      content: 'You are a helpful assistant that synthesizes web search results into clear, informative summaries.'
    }, {
      role: 'user' as const,
      content: `Based on the following search results for the query "${userQuery}", provide a comprehensive answer.

Search Results:
${context}

Please:
1. Synthesize the key information from all sources
2. Highlight the most relevant and important points
3. Cite sources using [1], [2], etc. when referencing specific information
4. Keep the summary concise but informative
5. If the results don't fully answer the query, mention what's missing

Summary:`
    }];

    console.log('[WebSearch] Calling summarization through AI SDK...');
    
    // Add timeout wrapper for the API call
    const timeoutPromise = new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error('Summarization timeout')), 8000)
    );
    
    // Use AI SDK provider for summarization
    const provider = await getAIProvider();
    const response = await Promise.race([
      provider.generateCompletion({
        model: 'llama3-8b-8192', // Fast Groq model
        messages,
        temperature: 0.3,
        maxTokens: 500,
      }),
      timeoutPromise
    ]);

    console.log('[WebSearch] Summary complete');
    return response || '';
  } catch (error) {
    console.error('[WebSearch] Failed to summarize results:', error);
    apiLogger.error('Failed to summarize results', error);
    // Fallback to simple concatenation
    return results.map((r, i) => `[${i + 1}] ${r.title}: ${r.snippet}`).join('\n\n');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('[WebSearch] Endpoint called');
    // Check authentication
    const session = await auth() || (isLocalhostDebugRequest(request) ? createMockSession() : null);
    
    // Temporarily allow unauthenticated access for testing
    if (false && !session) {
      apiLogger.warn('Unauthorized web search attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request
    const body: WebSearchRequest = await request.json();
    const { query, model = 'gpt-4o-mini', limit = 5, searchQueries, conversationContext } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    console.log('[WebSearch] Request validated, query:', query);

    // Check if model has native search by querying database
    console.log('[WebSearch] Checking native search support for model:', model);
    let hasNativeSearch = false;

    try {
      const modelRecord = await prisma.models.findFirst({
        where: {
          OR: [
            { canonicalName: model },
            { canonicalName: model.split('/').pop() }, // Handle provider/model format
            { displayName: model }
          ]
        },
        select: {
          supportsWebSearch: true,
          canonicalName: true,
          displayName: true
        }
      });

      hasNativeSearch = modelRecord?.supportsWebSearch === true;
      console.log('[WebSearch] Model lookup result:', {
        found: !!modelRecord,
        canonicalName: modelRecord?.canonicalName,
        supportsWebSearch: modelRecord?.supportsWebSearch,
        hasNativeSearch
      });
    } catch (error) {
      console.error('[WebSearch] Error checking model native search support:', error);
      // Fallback to hardcoded list if database lookup fails
      hasNativeSearch = model && NATIVE_SEARCH_MODELS.has(model.toLowerCase());
      console.log('[WebSearch] Fallback to hardcoded list:', hasNativeSearch);
    }
    
    if (hasNativeSearch) {
      // Return placeholder for native search models
      // The actual search will be handled by LiteLLM when the model is called
      return NextResponse.json({
        results: [],
        summary: 'Web search will be performed natively by the model.',
        searchType: 'native',
        searchQueries: [query]
      } satisfies WebSearchResponse);
    }

    // For non-native models, use Brave Search
    console.log('[WebSearch] Starting Brave Search path');
    
    // Use router-provided search query or generate one
    let searchQuery: string;
    if (searchQueries && searchQueries.length > 0) {
      // Use the first router-provided query (router should now provide just one)
      searchQuery = searchQueries[0];
      console.log('[WebSearch] Using router-provided query:', searchQuery);
    } else {
      // Generate a single targeted search query
      searchQuery = await generateSearchQuery(query, conversationContext);
      console.log('[WebSearch] Generated search query:', searchQuery);
    }

    // Search with Brave (single query, no rate limit issues)
    console.log('[WebSearch] Calling searchWithBrave...');
    const results = await searchWithBrave(searchQuery, limit);
    
    // Fetch additional content from top 3 results for better context
    console.log('[WebSearch] Fetching content from top results...');
    const enrichedResults = await Promise.all(
      results.slice(0, 3).map(async (result, index) => {
        try {
          const content = await fetchPageContent(result.url);
          return { 
            ...result, 
            content: content ? content.slice(0, 1000) : undefined // Limit to 1000 chars per page
          };
        } catch (error) {
          console.log(`[WebSearch] Failed to fetch content for result ${index + 1}`);
          return result;
        }
      })
    );
    
    // Combine enriched top results with remaining results
    const finalResults = [
      ...enrichedResults,
      ...results.slice(3)
    ];
    
    // Summarize results with enriched content
    const summary = await summarizeResults(finalResults, query);

    return NextResponse.json({
      results: finalResults.map(r => ({
        title: r.title,
        url: r.url,
        snippet: r.snippet,
        content: (r as any).content || undefined
      })),
      summary,
      searchType: 'brave',
      searchQueries: [searchQuery] // Keep array format for compatibility
    } satisfies WebSearchResponse);

  } catch (error) {
    console.error('[WebSearch] Endpoint error:', error);
    apiLogger.error('Web search endpoint error', error);
    
    // Check if it's a configuration error
    if (error instanceof Error && error.message.includes('API key not configured')) {
      return NextResponse.json(
        { error: 'Search service not configured. Please contact support.' },
        { status: 503 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}