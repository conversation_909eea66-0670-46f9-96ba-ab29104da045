import { NextResponse } from 'next/server';
import { getModelsForPlan, getAllModelsForUI, getDefaultModelForPlan } from '@/lib/ai/models/database-service';
import { UserPlan, Model } from '@/types';
import { auth } from '@/auth';

export async function GET(request: Request) {
  try {
    // Check authentication - allow unauthenticated access for free users
    const session = await auth();
    // Get user plan - default to FREE for unauthenticated users
    const { searchParams } = new URL(request.url);
    const planParam = searchParams.get('plan') || searchParams.get('tier');
    
    // Map old tier names to new plan names for backward compatibility
    const planMapping: Record<string, UserPlan> = {
      'free': UserPlan.FREE,
      'starter': UserPlan.PLUS,
      'pro': UserPlan.ADVANCED,
      'enterprise': UserPlan.MAX,
      // Direct plan names
      'FREE': UserPlan.FREE,
      'FREEMIUM': UserPlan.FREEMIUM,
      'PLUS': UserPlan.PLUS,
      'ADVANCED': UserPlan.ADVANCED,
      'MAX': UserPlan.MAX
    };
    
    // Determine user plan: prioritize query param, then user's plan, then default to FREE
    let userPlan: UserPlan;
    if (planParam && planMapping[planParam]) {
      // Query parameter takes precedence (for testing/debugging)
      userPlan = planMapping[planParam];
    } else if (session?.user?.plan) {
      // Use authenticated user's plan
      userPlan = session.user.plan as UserPlan;
    } else {
      // Default to FREE for unauthenticated users or missing plan
      userPlan = UserPlan.FREE;
    }
    
    // Get models available for the user's plan
    let models;
    if (process.env.ENABLE_LOCALHOST_DEBUG === 'true') {
      console.log('🐛 [LOCALHOST-DEBUG] Bypassing model API route and returning mock models');
      models = [
        {
          id: 'gpt-4o-mini',
          name: 'GPT-4o Mini',
          provider: 'openai',
          description: 'Fast and efficient model for general tasks',
          capabilities: ['general-chat', 'code-generation'],
          contextWindow: 128000,
          maxOutput: 4096,
          inputCost: 0.15,
          outputCost: 0.6,
          available: true,
          deprecated: false,
          speed: 'fast',
          tier: 'starter',
          tags: [],
          recommendedFor: [],
          isAvailableForUser: true
        },
        {
          id: 'claude-3-5-sonnet-20241022',
          name: 'Claude 3.5 Sonnet',
          provider: 'anthropic',
          description: 'Advanced reasoning and analysis',
          capabilities: ['general-chat', 'code-generation', 'analysis'],
          contextWindow: 200000,
          maxOutput: 8192,
          inputCost: 3.0,
          outputCost: 15.0,
          available: true,
          deprecated: false,
          speed: 'medium',
          tier: 'pro',
          tags: [],
          recommendedFor: [],
          isAvailableForUser: true
        },
        {
          id: 'gpt-4o',
          name: 'GPT-4o',
          provider: 'openai',
          description: 'Latest multimodal model with vision capabilities',
          capabilities: ['general-chat', 'code-generation', 'vision', 'analysis'],
          contextWindow: 128000,
          maxOutput: 4096,
          inputCost: 5.0,
          outputCost: 15.0,
          available: true,
          deprecated: false,
          speed: 'medium',
          tier: 'pro',
          tags: [],
          recommendedFor: [],
          isAvailableForUser: true
        }
      ];
    } else {
      // Get ALL models with plan availability for UI display (not just user's accessible ones)
      console.log(`📱 API: Getting ALL models for UI display with plan ${userPlan}`);
      models = await getAllModelsForUI(userPlan);
      console.log(`📱 API: Got ${models.length} total models with availability info`);
    }
    
    // Group by provider
    const modelsByProvider = models.reduce((acc: Record<string, any>, model: any) => {
      if (!acc[model.provider]) {
        acc[model.provider] = {
          name: model.provider,
          models: []
        };
      }
      
      acc[model.provider].models.push({
        id: model.id,
        name: model.name,
        description: model.description,
        provider: model.provider,
        contextWindow: model.contextWindow,
        maxOutput: model.maxOutput,
        inputCost: model.inputCost,
        outputCost: model.outputCost,
        speed: model.speed,
        tier: model.tier,
        capabilities: model.capabilities || [],
        tags: model.tags || [],
        recommendedFor: model.recommendedFor || [],
        deprecated: model.deprecated || false,
        available: model.available !== false,
        planAvailability: (model as any).planAvailability,
        minimumPlan: (model as any).minimumPlan,
        // Check if model is available for the current user's plan
        isAvailableForUser: (model as any).planAvailability?.[userPlan] || false
      });
      
      return acc;
    }, {} as Record<string, any>);
    
    // Convert to array and sort
    const providers = Object.values(modelsByProvider)
      .sort((a: any, b: any) => a.name.localeCompare(b.name));
    
    // Get default model for the user's plan
    const defaultModel = await getDefaultModelForPlan(userPlan);
    
    return NextResponse.json({
      total: models.length,
      providers: providers,
      defaultModel: defaultModel?.id || 'gpt-4o-mini',
      userPlan: userPlan
    });
  } catch (error) {
    console.error('Error fetching models:', error);
    return NextResponse.json(
      { error: 'Failed to fetch models' },
      { status: 500 }
    );
  }
}