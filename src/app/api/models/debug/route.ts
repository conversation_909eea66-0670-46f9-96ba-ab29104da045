import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { UserPlan } from '@/types';

const prisma = new PrismaClient();

export async function GET(request: Request) {
  try {
    // Get plan info for tier-based access
    const freePlan = await prisma.plans.findFirst({
      where: { code: UserPlan.FREE }
    });
    
    if (!freePlan) {
      throw new Error('FREE plan not found');
    }
    
    // Check total models and plan access
    const stats = await prisma.$transaction([
      prisma.models.count({ where: { isEnabled: true } }),
      prisma.modelPlanRules.count({ where: { 
        OR: [
          { planIds: { array_contains: UserPlan.FREE } },
          { minPlanTier: { lte: freePlan.tier } }
        ],
        isEnabled: true 
      }}),
      prisma.providers.count({ where: { isActive: true } })
    ]);
    
    const [enabledModels, freePlanAccess, activeProviders] = stats;
    
    // Get sample models with plan access for FREE using raw query
    const sampleModels = await prisma.$queryRaw<Array<{
      id: string;
      canonicalName: string;
      displayName: string;
      providerId: string;
      providerName: string;
      providerSlug: string;
    }>>`
      SELECT DISTINCT m.id, m.canonicalName, m.displayName, m.providerId,
             p.name as providerName, p.slug as providerSlug
      FROM Models m
      JOIN Providers p ON m.providerId = p.id
      JOIN ModelPlanRules r ON m.id = r.modelId
      WHERE m.isEnabled = true
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${UserPlan.FREE}))
        OR (r.minPlanTier IS NOT NULL AND ${freePlan.tier} >= r.minPlanTier)
      )
      LIMIT 10
    `;
    
    // Check providers
    const providers = await prisma.providers.findMany({
      where: { isActive: true },
      select: {
        id: true,
        slug: true,
        name: true,
        _count: {
          select: { models: true }
        }
      }
    });
    
    return NextResponse.json({
      stats: {
        enabledModels,
        freePlanAccess,
        activeProviders
      },
      sampleModels: sampleModels.map((m: any) => ({
        id: m.id,
        canonicalName: m.canonicalName,
        displayName: m.displayName,
        provider: {
          id: m.providerId,
          slug: m.providerSlug,
          name: m.providerName
        }
      })),
      providers: providers
    });
  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json(
      { error: 'Debug failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}