import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { nanoid } from 'nanoid';
import { isLocalhostDebugRequest, createMockSession, logLocalhostDebug } from '@/lib/debug';

// GET /api/conversations - List user conversations
export async function GET(request: NextRequest) {
  try {
    let session = await auth();
    
    // Check for localhost debugging
    if (!session?.user?.id && isLocalhostDebugRequest(request)) {
      logLocalhostDebug('Using mock session for conversations GET');
      session = createMockSession();
    }
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const search = searchParams.get('search') || '';
    const archived = searchParams.get('archived') === 'true';
    const pinned = searchParams.get('pinned') === 'true';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      userId: session.user.id,
      deletedAt: null,
    };

    if (archived) {
      where.archivedAt = { not: null };
    } else {
      where.archivedAt = null;
    }

    if (pinned) {
      where.pinned = true;
    }

    // Search functionality
    if (search) {
      where.OR = [
        {
          title: {
            contains: search,
          },
        },
        {
          preview: {
            contains: search,
          },
        },
        {
          messages: {
            some: {
              content: {
                contains: search,
              },
            },
          },
        },
      ];
    }

    // Fetch conversations with optimized query
    const [conversations, totalCount] = await Promise.all([
      prisma.conversation.findMany({
        where,
        include: {
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
            select: {
              id: true,
              content: true,
              role: true,
              createdAt: true,
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: [
          { pinned: 'desc' },
          { lastMessageAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.conversation.count({ where }),
    ]);

    // Transform data for frontend
    const transformedConversations = conversations.map((conv: any) => {
      const lastMessage = conv.messages[0];
      const preview = conv.preview || 
        (lastMessage?.content ? 
          lastMessage.content.slice(0, 100) + (lastMessage.content.length > 100 ? '...' : '') 
          : 'No messages yet');

      return {
        id: conv.id,
        title: conv.title,
        preview,
        lastMessageAt: conv.lastMessageAt || conv.createdAt,
        messageCount: conv._count.messages,
        createdAt: conv.createdAt,
        updatedAt: conv.updatedAt,
        archived: !!conv.archivedAt,
        pinned: conv.pinned,
        tags: conv.tags as string[] || [],
        color: conv.color,
        modelPreference: conv.model,
        unread: false, // TODO: Implement unread logic based on user activity
      };
    });

    const hasMore = skip + conversations.length < totalCount;

    return NextResponse.json({
      conversations: transformedConversations,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
      },
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch conversations' },
      { status: 500 }
    );
  }
}

// POST /api/conversations - Create new conversation
export async function POST(request: NextRequest) {
  try {
    let session = await auth();
    
    // Check for localhost debugging
    if (!session?.user?.id && isLocalhostDebugRequest(request)) {
      logLocalhostDebug('Using mock session for conversations GET');
      session = createMockSession();
    }
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, preview, tags, color, projectId } = body;

    // Generate title if not provided
    const finalTitle = title || `Chat ${nanoid(6)}`;

    const conversation = await prisma.conversation.create({
      data: {
        id: nanoid(),
        title: finalTitle,
        preview: preview || 'Start a new conversation...',
        userId: session.user.id,
        tags: tags || [],
        color: color || null,
        projectId: projectId || null,
        pinned: false,
        messageCount: 0,
        tokenCount: 0,
      },
      include: {
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    // Transform for frontend
    const transformedConversation = {
      id: conversation.id,
      title: conversation.title,
      preview: conversation.preview || 'Start a new conversation...',
      lastMessageAt: conversation.lastMessageAt || conversation.createdAt,
      messageCount: conversation._count.messages,
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
      archived: !!conversation.archivedAt,
      pinned: conversation.pinned,
      tags: conversation.tags as string[] || [],
      color: conversation.color,
      modelPreference: conversation.model,
      unread: false,
    };

    return NextResponse.json(transformedConversation, { status: 201 });
  } catch (error) {
    console.error('Error creating conversation:', error);
    
    // Check if it's a foreign key constraint error (user doesn't exist)
    if (error instanceof Error && 
        (error.message.includes('Foreign key constraint') || 
         error.message.includes('User not found'))) {
      return NextResponse.json(
        { 
          error: 'Authentication required',
          code: 'USER_NOT_FOUND',
          message: 'Your session is invalid. Please log in again.'
        },
        { 
          status: 401,
          headers: {
            'Clear-Site-Data': '"cookies"'
          }
        }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create conversation' },
      { status: 500 }
    );
  }
}