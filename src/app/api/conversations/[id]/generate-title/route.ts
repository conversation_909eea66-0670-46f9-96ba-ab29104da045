import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { titleGenerator } from '@/lib/ai/title-generator-ai-sdk';
import { isLocalhostDebugRequest, createMockSession, logLocalhostDebug } from '@/lib/debug';

// POST /api/conversations/[id]/generate-title - Generate title for conversation
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  
  try {
    let session = await auth();
    
    // Check for localhost debugging
    if (!session?.user?.id && isLocalhostDebugRequest(request)) {
      logLocalhostDebug('Using mock session for title generation', { conversationId: id });
      session = createMockSession();
    }
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get conversation with messages
    const conversation = await prisma.conversation.findFirst({
      where: {
        id,
        userId: session.user.id,
        deletedAt: null,
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
          take: 4, // First 4 messages for context
          select: {
            role: true,
            content: true,
          },
        },
      },
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    if (conversation.messages.length === 0) {
      return NextResponse.json({ error: 'No messages to generate title from' }, { status: 400 });
    }

    // Generate title
    const messages = conversation.messages.map((msg: any) => ({
      role: msg.role as 'user' | 'assistant' | 'system',
      content: msg.content,
    }));

    const generatedTitle = await titleGenerator.generateTitle(messages, {
      maxLength: 60,
      style: 'auto',
      includeEmoji: true,
    });

    // Update conversation with generated title
    const updatedConversation = await prisma.conversation.update({
      where: { id },
      data: { title: generatedTitle },
      include: {
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    // Transform for frontend
    const transformedConversation = {
      id: updatedConversation.id,
      title: updatedConversation.title,
      preview: updatedConversation.preview,
      lastMessageAt: updatedConversation.lastMessageAt || updatedConversation.createdAt,
      messageCount: updatedConversation._count.messages,
      createdAt: updatedConversation.createdAt,
      updatedAt: updatedConversation.updatedAt,
      archived: !!updatedConversation.archivedAt,
      pinned: updatedConversation.pinned,
      tags: updatedConversation.tags as string[] || [],
      color: updatedConversation.color,
      modelPreference: updatedConversation.model,
    };

    return NextResponse.json({
      conversation: transformedConversation,
      generatedTitle,
    });
    
  } catch (error) {
    console.error('Error generating title:', error);
    return NextResponse.json(
      { error: 'Failed to generate title' },
      { status: 500 }
    );
  }
}

// GET /api/conversations/[id]/generate-title/suggestions - Get title suggestions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  
  try {
    let session = await auth();
    
    // Check for localhost debugging
    if (!session?.user?.id && isLocalhostDebugRequest(request)) {
      logLocalhostDebug('Using mock session for title suggestions', { conversationId: id });
      session = createMockSession();
    }
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get conversation with messages
    const conversation = await prisma.conversation.findFirst({
      where: {
        id,
        userId: session.user.id,
        deletedAt: null,
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
          take: 4,
          select: {
            role: true,
            content: true,
          },
        },
      },
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    if (conversation.messages.length === 0) {
      return NextResponse.json({ error: 'No messages to generate suggestions from' }, { status: 400 });
    }

    // Generate title suggestions
    const messages = conversation.messages.map((msg: any) => ({
      role: msg.role as 'user' | 'assistant' | 'system',
      content: msg.content,
    }));

    const suggestions = await titleGenerator.generateTitleSuggestions(messages, 3);

    return NextResponse.json({ suggestions });
    
  } catch (error) {
    console.error('Error generating title suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to generate title suggestions' },
      { status: 500 }
    );
  }
}