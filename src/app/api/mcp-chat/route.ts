/**
 * MCP-Enhanced Chat API Endpoint
 * Test endpoint for MCP image tool integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { chatWithMCPTools, modelSupportsMCPTools } from '@/lib/ai/mcp-enhanced-chat';
import { auth } from '@/auth';
import { apiLogger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    const body = await request.json();
    
    const { 
      messages, 
      model = 'gemini-2.5-pro',
      temperature = 0.7,
      maxTokens = 4096,
      enableImageTools = true 
    } = body;

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    // Check if model supports MCP tools
    if (enableImageTools && !modelSupportsMCPTools(model)) {
      return NextResponse.json(
        { 
          error: `Model ${model} does not support MCP tools. Try gemini-2.5-pro or gpt-4o.`,
          supportedModels: ['gemini-2.5-pro', 'gpt-4o', 'gpt-4o-mini']
        },
        { status: 400 }
      );
    }

    apiLogger.info('MCP chat request', {
      userId: session?.user?.id,
      model,
      messageCount: messages.length,
      enableImageTools
    });

    const response = await chatWithMCPTools(messages, {
      model,
      temperature,
      maxTokens,
      enableImageTools,
      userId: session?.user?.id
    });

    apiLogger.info('MCP chat response', {
      userId: session?.user?.id,
      model: response.model,
      hasImages: !!response.images?.length,
      imageCount: response.images?.length || 0,
      toolCallCount: response.toolCalls?.length || 0,
      usage: response.usage
    });

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    apiLogger.error('MCP chat API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'MCP Chat API',
    description: 'Enhanced chat with image generation tools via MCP',
    supportedModels: ['gemini-2.5-pro', 'gpt-4o', 'gpt-4o-mini'],
    availableTools: ['generate_image', 'edit_image', 'create_image_variations'],
    usage: {
      endpoint: '/api/mcp-chat',
      method: 'POST',
      body: {
        messages: [
          { role: 'user', content: 'Create a picture of a sunset over mountains' }
        ],
        model: 'gemini-2.5-pro',
        enableImageTools: true
      }
    }
  });
}