import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'

// PUT /api/admin/providers/[id] - Update a provider
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json()
    const { id: providerId } = await params
    
    const provider = await prisma.aIProvider.update({
      where: { id: providerId },
      data: {
        name: body.name,
        slug: body.slug,
        description: body.description,
        websiteUrl: body.websiteUrl,
        docsUrl: body.docsUrl,
        apiDocsUrl: body.apiDocsUrl,
        statusPageUrl: body.statusPageUrl,
        logoUrl: body.logoUrl,
        authType: body.auth_type,
        authConfig: body.auth_config,
        baseUrl: body.base_url,
        features: body.features,
        isActive: body.isActive,
        isVerified: body.isVerified,
        lastVerifiedAt: body.isVerified ? new Date() : undefined,
        metadata: {
          ...(body.metadata || {}),
          authType: body.auth_type,
          baseUrl: body.base_url,
          features: body.features || []
        }
      }
    })
    
    apiLogger.info('Provider updated', {
      providerId: provider.id,
      name: provider.name
    })
    
    return NextResponse.json({
      success: true,
      provider
    })
    
  } catch (error) {
    apiLogger.error('Failed to update provider', error)
    return NextResponse.json(
      { error: 'Failed to update provider' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/providers/[id] - Delete a provider
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: providerId } = await params
    
    // Check if provider has models
    const modelCount = await prisma.models.count({
      where: { providerId }
    })
    
    if (modelCount > 0) {
      return NextResponse.json(
        { error: `Cannot delete provider with ${modelCount} associated models. Please reassign or delete the models first.` },
        { status: 400 }
      )
    }
    
    // Delete the provider
    await prisma.aIProvider.delete({
      where: { id: providerId }
    })
    
    apiLogger.info('Provider deleted', {
      providerId
    })
    
    return NextResponse.json({
      success: true,
      message: 'Provider deleted successfully'
    })
    
  } catch (error) {
    apiLogger.error('Failed to delete provider', error)
    return NextResponse.json(
      { error: 'Failed to delete provider' },
      { status: 500 }
    )
  }
}