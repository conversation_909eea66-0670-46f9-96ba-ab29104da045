import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'

// POST /api/admin/router/config/export - Export complete router configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      includeCategories = true,
      includeMappings = true,
      includeModels = true,
      includeProviders = true,
      includeSystemConfig = true,
      format = 'json'
    } = body
    
    const exportData: any = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      exportedBy: 'admin', // Should get from auth context
      metadata: {}
    }
    
    // Export prompt categories
    if (includeCategories) {
      exportData.categories = await prisma.promptCategory.findMany({
        select: {
          name: true,
          displayName: true,
          description: true,
          examplePrompts: true,
          typicalRequirements: true,
          preferredModels: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        }
      })
      exportData.extendedMetadata.totalCategories = exportData.categories.length
    }
    
    // Export model mappings
    if (includeMappings) {
      exportData.modelMappings = await prisma.modelMapping.findMany({
        include: {
          categoryRelation: {
            select: {
              name: true,
              displayName: true
            }
          }
        }
      })
      exportData.extendedMetadata.totalMappings = exportData.modelMappings.length
    }
    
    // Export AI models
    if (includeModels) {
      exportData.models = await prisma.models.findMany({
        include: {
          provider: {
            select: {
              name: true,
              slug: true
            }
          }
        }
      })
      exportData.extendedMetadata.totalModels = exportData.models.length
    }
    
    // Export providers
    if (includeProviders) {
      exportData.providers = await prisma.aIProvider.findMany({
        select: {
          name: true,
          slug: true,
          description: true,
          isActive: true,
          websiteUrl: true,
          docsUrl: true,
          metadata: true,
          createdAt: true,
          updatedAt: true
        }
      })
      exportData.extendedMetadata.totalProviders = exportData.providers.length
    }
    
    // Export Thompson Sampling configuration
    if (includeSystemConfig) {
      exportData.systemConfig = await prisma.systemConfig.findMany({
        where: {
          key: {
            in: [
              'router_settings',
              'prompt_improvement_settings',
              'analytics_settings',
              'feedback_settings'
            ]
          }
        }
      })
      exportData.extendedMetadata.totalSystemConfig = exportData.systemConfig.length
    }
    
    // Calculate total size estimate
    const exportJson = JSON.stringify(exportData)
    const sizeKB = Buffer.byteLength(exportJson, 'utf8') / 1024
    
    apiLogger.info('Configuration exported', {
      categories: exportData.extendedMetadata.totalCategories,
      mappings: exportData.extendedMetadata.totalMappings,
      models: exportData.extendedMetadata.totalModels,
      providers: exportData.extendedMetadata.totalProviders,
      sizeKB: Math.round(sizeKB)
    })
    
    return NextResponse.json(exportData)
    
  } catch (error) {
    apiLogger.error('Failed to export configuration', error)
    return NextResponse.json(
      { error: 'Failed to export configuration' },
      { status: 500 }
    )
  }
}