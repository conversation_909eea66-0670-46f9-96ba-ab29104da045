import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'
import { modelTester } from '@/lib/ai/model-tester'

// POST /api/admin/models/[id]/test - Test a model connection
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const modelId = params.id
    const { testPrompt } = body
    
    // Get model with provider info
    const model = await prisma.models.findUnique({
      where: { id: modelId },
      include: {
        provider: true,
        versions: {
          where: { deprecatedDate: null },
          include: {
            endpoints: {
              where: { isEnabled: true },
              take: 1
            }
          },
          take: 1
        }
      }
    })
    
    if (!model) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      )
    }
    
    if (!model.isEnabled) {
      return NextResponse.json({
        success: false,
        error: 'Model is disabled',
        details: {
          reason: model.disabledReason,
          disabledAt: model.disabledAt
        }
      })
    }
    
    // Test the model through AI SDK
    const testResult = await modelTester.testModel(
      model.canonicalName,
      testPrompt || 'Hello, please respond with "Test successful" to confirm you\'re working correctly.'
    )
    
    // Log test attempt
    await prisma.$executeRaw`
      INSERT INTO model_test_logs (
        model_id,
        canonical_name,
        test_prompt,
        success,
        latency_ms,
        error,
        response,
        tested_at,
        tested_by
      ) VALUES (
        ${modelId},
        ${model.canonicalName},
        ${testPrompt || 'Default test prompt'},
        ${testResult.success},
        ${testResult.latency || null},
        ${testResult.error || null},
        ${testResult.response ? JSON.stringify(testResult.response) : null},
        NOW(),
        'admin'
      )
    `
    
    // Update model health status if test failed
    if (!testResult.success) {
      await prisma.models.update({
        where: { id: modelId },
        data: {
          metadata: {
            ...model.extendedMetadata,
            lastTestFailed: true,
            lastTestError: testResult.error,
            lastTestAt: new Date().toISOString()
          }
        }
      })
    }
    
    apiLogger.info('Model test completed', {
      modelId,
      canonicalName: model.canonicalName,
      success: testResult.success,
      latency: testResult.latency
    })
    
    return NextResponse.json({
      success: testResult.success,
      modelId,
      canonicalName: model.canonicalName,
      latency: testResult.latency,
      response: testResult.response,
      error: testResult.error,
      details: {
        provider: model.provider.name,
        endpoint: model.versions[0]?.endpoints[0]?.url,
        contextWindow: model.versions[0]?.endpoints[0]?.maxContextTokensIn,
        maxOutputTokens: model.versions[0]?.endpoints[0]?.maxTokensOut
      }
    })
    
  } catch (error) {
    apiLogger.error('Failed to test model', error)
    return NextResponse.json(
      { 
        error: 'Failed to test model',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}