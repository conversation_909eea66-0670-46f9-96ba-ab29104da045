import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'
// litellmIntegration removed - using database-first architecture

// POST /api/admin/models/[id]/toggle - Enable/disable a model
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json()
    const { id: modelId } = await params
    const { isEnabled, disabledReason } = body
    
    // Get model with provider info
    const model = await prisma.models.findUnique({
      where: { id: modelId },
      // Removed provider and versions relations - using database-first architecture
    })
    
    if (!model) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      )
    }
    
    // Update model status
    const updatedModel = await prisma.models.update({
      where: { id: modelId },
      data: {
        isEnabled,
        disabledReason: isEnabled ? null : (disabledReason || 'Manually disabled'),
        disabledAt: isEnabled ? null : new Date(),
        disabledBy: isEnabled ? null : 'admin' // Should get from auth context
      }
    })
    
    // LiteLLM sync removed - using database-first architecture
    
    apiLogger.info('Model toggled', {
      modelId: updatedModel.id,
      canonicalName: updatedModel.canonicalName,
      isEnabled: updatedModel.isEnabled
    })
    
    return NextResponse.json({
      success: true,
      modelId: updatedModel.id,
      isEnabled: updatedModel.isEnabled
    })
    
  } catch (error) {
    apiLogger.error('Failed to toggle model', error)
    return NextResponse.json(
      { error: 'Failed to toggle model' },
      { status: 500 }
    )
  }
}