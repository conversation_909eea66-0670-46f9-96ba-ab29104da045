import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { apiLogger } from '@/lib/logger'
import { UserPlan } from '@/types'
// litellmIntegration removed - using database-first architecture

// PUT /api/admin/models/[id] - Update a model
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json()
    const { id: modelId } = await params
    
    // Get existing model
    const existingModel = await prisma.models.findUnique({
      where: { id: modelId }
    })
    
    if (!existingModel) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      )
    }
    
    // If provider is being changed, look up the new provider
    let newProviderId = existingModel.providerId
    if (body.providerId && body.providerId !== existingModel.providerId) {
      // Verify the new provider exists in our Providers table
      const providerExists = await prisma.providers.findUnique({
        where: { id: body.providerId }
      })
      if (!providerExists) {
        return NextResponse.json(
          { error: 'Provider not found' },
          { status: 404 }
        )
      }
      newProviderId = body.providerId
    }
    
    // Start a transaction
    const result = await prisma.$transaction(async (tx: any) => {
      // Update model
      const model = await tx.models.update({
        where: { id: modelId },
        data: {
          providerId: newProviderId,
          canonicalName: body.canonicalName,
          displayName: body.displayName,
          description: body.description,
          family: body.family,
          generation: body.generation,
          modelType: body.modelType,
          isEnabled: body.isEnabled,
          disabledReason: body.disabledReason,
          disabledAt: body.isEnabled ? null : new Date(),
          disabledBy: body.isEnabled ? null : 'admin', // Should get from auth
          defaultPriority: body.routerConfig?.priority,
          metadata: body.metadata || {}
        }
      })
      
      // Update endpoint if exists
      const latestVersion = await tx.modelVersion.findFirst({
        where: { 
          modelId: modelId,
          deprecatedDate: null
        },
        orderBy: { releaseDate: 'desc' }
      })
      
      if (latestVersion) {
        const endpoint = await tx.aIEndpoint.findFirst({
          where: { 
            versionId: latestVersion.id,
            isEnabled: true
          }
        })
        
        if (endpoint) {
          await tx.aIEndpoint.update({
            where: { id: endpoint.id },
            data: {
              url: body.litellmConfig?.api_base || endpoint.url,
              maxContextTokensIn: body.contextWindow || endpoint.maxContextTokensIn,
              maxTokensOut: body.maxOutputTokens || endpoint.maxOutput,
              optimalContextTokens: body.optimalContextTokens,
              supportsStreaming: body.capabilities?.streaming ?? endpoint.supportsStreaming,
              supportsFunctions: body.capabilities?.functionCalling ?? endpoint.supportsFunctionCalling,
              supportsTools: body.capabilities?.toolUse ?? endpoint.supportsTools,
              supportsVision: body.capabilities?.vision ?? endpoint.supportsVision,
              inputCostUSD: body.pricing?.inputCostPer1M ? body.pricing.inputCostPer1M / 1000 : endpoint.inputCostUSD,
              outputCostUSD: body.pricing?.outputCostPer1M ? body.pricing.outputCostPer1M / 1000 : endpoint.outputCostUSD,
              priority: body.routerConfig?.priority || endpoint.priority,
              routingGroup: body.routerConfig?.routingGroup
            }
          })
        }
      }
      
      // Update plan access using ModelPlanRules
      if (body.planAccess) {
        // Delete existing rules for this model
        await tx.modelPlanRules.deleteMany({
          where: { modelId: modelId }
        })
        
        // Create new rules based on plan access
        const enabledPlans = Object.entries(body.planAccess)
          .filter(([_, isAvailable]) => isAvailable)
          .map(([userPlan]) => userPlan as UserPlan);
        
        if (enabledPlans.length > 0) {
          // Create a single rule with all enabled plans
          await tx.modelPlanRules.create({
            data: {
              modelId: modelId,
              name: `Access rule for ${existingModel.canonicalName}`,
              description: 'Admin-configured plan access',
              planIds: JSON.stringify(enabledPlans),
              minPlanTier: null, // Not using tier-based access for manual rules
              isEnabled: true,
              createdBy: 'admin',
              priority: 100 // Default priority
            }
          })
        }
      }
      
      return model
    })
    
    // LiteLLM sync removed - using database-first architecture
    
    apiLogger.info('Model updated', {
      modelId: result.id,
      canonicalName: result.canonicalName
    })
    
    return NextResponse.json({
      success: true,
      modelId: result.id
    })
    
  } catch (error) {
    apiLogger.error('Failed to update model', error)
    return NextResponse.json(
      { error: 'Failed to update model' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/models/[id] - Delete a model
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: modelId } = await params
    
    // Get model details before deletion
    const model = await prisma.models.findUnique({
      where: { id: modelId }
    })
    
    if (!model) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      )
    }
    
    // Delete from database (cascades to related tables)
    await prisma.models.delete({
      where: { id: modelId }
    })
    
    // LiteLLM removal not needed - using database-first architecture
    
    apiLogger.info('Model deleted', {
      modelId: modelId,
      canonicalName: model.canonicalName
    })
    
    return NextResponse.json({
      success: true,
      message: 'Model deleted successfully'
    })
    
  } catch (error) {
    apiLogger.error('Failed to delete model', error)
    return NextResponse.json(
      { error: 'Failed to delete model' },
      { status: 500 }
    )
  }
}