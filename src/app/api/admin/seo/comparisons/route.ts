import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateModelComparison } from '@/lib/seo/ai-content-generator'
import { Model, ModelCapability, UserPlan } from '@/types'

// Helper function to convert database model to app model (same as in database-service.ts)
function convertDbModelToAppModel(dbModel: any): Model {
  const metadata = dbModel.extendedMetadata;
  
  // Handle capabilities field - ensure it's always an array
  let capabilities: string[] = [ModelCapability.GENERAL_CHAT];
  
  if (metadata?.capabilities) {
    if (Array.isArray(metadata.capabilities)) {
      capabilities = metadata.capabilities.filter((cap: any) => typeof cap === 'string' && cap.length > 0);
    } else if (typeof metadata.capabilities === 'object' && metadata.capabilities !== null) {
      capabilities = [];
      const capabilityMap: Record<string, string> = {
        'vision': ModelCapability.VISION,
        'toolUse': ModelCapability.TOOL_USE,
        'functionCalling': ModelCapability.FUNCTION_CALLING,
        'webSearch': ModelCapability.WEB_SEARCH,
        'codeExecution': ModelCapability.CODE_GENERATION,
        'codeGeneration': ModelCapability.CODE_GENERATION,
        'multimodal': ModelCapability.VISION,
        'reasoning': ModelCapability.REASONING,
        'analysis': ModelCapability.ANALYSIS,
        'creative_writing': ModelCapability.CREATIVE_WRITING,
        'translation': ModelCapability.TRANSLATION,
        'summarization': ModelCapability.SUMMARIZATION,
        'math': ModelCapability.MATH,
        'long_context': ModelCapability.LONG_CONTEXT,
        'fast_response': ModelCapability.FAST_RESPONSE,
        'thinking_mode': ModelCapability.THINKING_MODE,
        'audio_processing': ModelCapability.AUDIO_PROCESSING
      };
      
      for (const [key, value] of Object.entries(metadata.capabilities)) {
        if (value === true && capabilityMap[key]) {
          capabilities.push(capabilityMap[key]);
        }
      }
      
      if (capabilities.length === 0) {
        capabilities = [ModelCapability.GENERAL_CHAT];
      }
    } else if (typeof metadata.capabilities === 'string') {
      capabilities = [metadata.capabilities];
    }
  }
  
  if (capabilities.length === 0) {
    capabilities = [ModelCapability.GENERAL_CHAT];
  } else {
    capabilities = [...new Set(capabilities)];
  }
  
  // Calculate pricing per million tokens
  const pricing = metadata?.pricing || { input: 0, output: 0, blended: 0 };
  const inputPricePerMillion = pricing.input ? pricing.input * 1000000 : 0;
  const outputPricePerMillion = pricing.output ? pricing.output * 1000000 : 0;
  
  return {
    id: dbModel.canonicalName,
    name: dbModel.displayName,
    canonicalName: dbModel.canonicalName,
    provider: dbModel.provider?.name || 'Unknown',
    description: dbModel.description || '',
    capabilities: capabilities,
    contextWindow: metadata?.contextWindow || 4096,
    contextLength: metadata?.contextWindow || 4096,
    maxOutput: metadata?.maxOutput || 2048,
    inputCost: pricing.input || 0.01,
    outputCost: pricing.output || 0.03,
    inputPricePerMillion: inputPricePerMillion,
    outputPricePerMillion: outputPricePerMillion,
    avgLatency: metadata?.avgLatency || 1000,
    processingSpeed: metadata?.avgLatency || 1000,
    knowledgeCutoff: metadata?.knowledgeCutoff || '2024-01',
    slug: dbModel.canonicalName.toLowerCase().replace(/[^a-z0-9]/g, '-'),
    available: dbModel.isEnabled,
    speed: metadata?.speed || 'medium',
    tier: metadata?.tier || 'starter',
    tags: metadata?.tags || [],
    recommendedFor: metadata?.recommendedFor || [],
    deprecated: metadata?.deprecated || false,
    family: dbModel.family
  } as Model;
}

// GET /api/admin/seo/comparisons - List cached comparisons
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const staleOnly = searchParams.get('stale') === 'true'
    
    const skip = (page - 1) * limit
    
    const where = staleOnly ? {
      OR: [
        { isStale: true },
        { updatedAt: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } }
      ]
    } : {}
    
    const [comparisons, total] = await Promise.all([
      prisma.modelComparison.findMany({
        where,
        include: {
          model1: { select: { displayName: true, canonicalName: true } },
          model2: { select: { displayName: true, canonicalName: true } }
        },
        orderBy: { updatedAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.modelComparison.count({ where })
    ])
    
    return NextResponse.json({
      comparisons,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Error fetching comparisons:', error)
    return NextResponse.json(
      { error: 'Failed to fetch comparisons' },
      { status: 500 }
    )
  }
}

// Rate limiting for SEO generation
const generationTimes = new Map<string, number>()
const RATE_LIMIT_MS = 60000 // 1 minute between generations per IP

// POST /api/admin/seo/comparisons - Generate specific comparison
export async function POST(request: NextRequest) {
  try {
    // Simple rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const now = Date.now()
    const lastGeneration = generationTimes.get(clientIP) || 0
    
    if (now - lastGeneration < RATE_LIMIT_MS) {
      const waitTime = Math.ceil((RATE_LIMIT_MS - (now - lastGeneration)) / 1000)
      return NextResponse.json(
        { error: `Rate limited. Please wait ${waitTime} seconds before generating another comparison.` },
        { status: 429 }
      )
    }
    
    generationTimes.set(clientIP, now)
    
    const { model1Id, model2Id, forceRegenerate = false } = await request.json()
    
    if (!model1Id || !model2Id) {
      return NextResponse.json(
        { error: 'model1Id and model2Id are required' },
        { status: 400 }
      )
    }
    
    // Get the models with full relations (provider, metadata, etc.)
    const [dbModel1, dbModel2] = await Promise.all([
      prisma.models.findUnique({ 
        where: { id: model1Id },
        include: {
          provider: true
        }
      }),
      prisma.models.findUnique({ 
        where: { id: model2Id },
        include: {
          provider: true
        }
      })
    ])
    
    if (!dbModel1 || !dbModel2) {
      return NextResponse.json(
        { error: 'One or both models not found' },
        { status: 404 }
      )
    }
    
    // Convert to Model type using the same logic as the router
    const modelData1 = convertDbModelToAppModel(dbModel1)
    const modelData2 = convertDbModelToAppModel(dbModel2)
    
    // Generate the comparison
    const comparison = await generateModelComparison(modelData1, modelData2, forceRegenerate)
    
    return NextResponse.json({
      success: true,
      comparison,
      message: `Generated comparison for ${dbModel1.displayName} vs ${dbModel2.displayName}`
    })
    
  } catch (error) {
    console.error('Error generating comparison:', error)
    return NextResponse.json(
      { error: 'Failed to generate comparison', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/seo/comparisons?id=xxx - Delete specific comparison
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: 'Comparison ID is required' },
        { status: 400 }
      )
    }
    
    await prisma.modelComparison.delete({
      where: { id }
    })
    
    return NextResponse.json({
      success: true,
      message: 'Comparison deleted successfully'
    })
    
  } catch (error) {
    console.error('Error deleting comparison:', error)
    return NextResponse.json(
      { error: 'Failed to delete comparison' },
      { status: 500 }
    )
  }
}