import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { generateUseCaseContent } from '@/lib/seo/ai-content-generator'
import { getAllModels } from '@/lib/models'
import { Markdown } from '@/components/Markdown'
import { ModelRecommendations } from '@/components/use-cases/ModelRecommendations'
import { UseCaseTemplate } from '@/components/use-cases/UseCaseTemplate'
import { FAQ } from '@/components/seo/FAQ'
import { generatePageStructuredData } from '@/lib/structured-data-enhanced'
import type { Model } from '@/types'
import { industryMap, type IndustryMapType, type IndustryData, type TaskData } from '@/data/industry-map'

interface PageProps {
  params: Promise<{
    industry: string
    task: string
  }>
}

export async function generateStaticParams() {
  const params: Array<{ industry: string; task: string }> = []
  
  for (const [industryKey, industry] of Object.entries(industryMap)) {
    for (const taskKey of Object.keys(industry.tasks)) {
      params.push({
        industry: industryKey,
        task: taskKey
      })
    }
  }
  
  return params
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { industry: industryKey, task: taskKey } = await params
  
  const industry = industryMap[industryKey as keyof typeof industryMap]
  const task = industry?.tasks[taskKey as keyof typeof industry.tasks]
  
  if (!industry || !task) {
    return {
      title: 'Use Case Not Found',
      robots: 'noindex'
    }
  }
  
  // Type assertions to help TypeScript understand these are defined
  const validIndustry = industry as NonNullable<typeof industry>
  const validTask = task as NonNullable<typeof task>
  
  const title = `${validTask.name} for ${validIndustry.name} | AI Tools & Best Models 2025`
  const description = `Complete guide to using AI for ${validTask.name.toLowerCase()}. Best models, prompts, and workflows for ${validIndustry.name.toLowerCase()}. Compare GPT-4, Claude, Gemini & more.`
  
  return {
    title,
    description,
    keywords: [
      `AI for ${validTask.name.toLowerCase()}`,
      `${validIndustry.name.toLowerCase()} AI tools`,
      `best AI model for ${validTask.name.toLowerCase()}`,
      'AI workflow automation',
      'ChatGPT alternative',
      'Claude vs GPT-4',
      `${validTask.name.toLowerCase()} automation`
    ],
    alternates: {
      canonical: `/use-cases/${industryKey}/${taskKey}`
    },
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime: new Date().toISOString(),
      authors: ['JustSimpleChat Team'],
      tags: [`${industry.name}`, `${task.name}`, 'AI Tools']
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description
    }
  }
}

export default async function UseCasePage({ params }: PageProps) {
  const { industry: industryKey, task: taskKey } = await params
  
  const industry = industryMap[industryKey as keyof typeof industryMap]
  const task = industry?.tasks[taskKey as keyof typeof industry.tasks]
  
  if (!industry || !task) {
    notFound()
  }
  
  // Type assertions to help TypeScript understand these are defined
  const validIndustry = industry as NonNullable<typeof industry>
  const validTask = task as NonNullable<typeof task>
  
  const allModels = await getAllModels()
  const relevantModels = allModels.filter(model => 
    validTask.models.some((modelName: string) => 
      model.canonicalName.toLowerCase().includes(modelName.toLowerCase()) ||
      model.name.toLowerCase().includes(modelName.toLowerCase())
    )
  )
  
  // Serialize models to remove any function references
  const serializedModels = JSON.parse(JSON.stringify(relevantModels))
  
  const useCaseContent = await generateUseCaseContent(
    validIndustry.name,
    validTask.name,
    serializedModels
  )
  
  // Serialize industry data for client components (remove icon function)
  const serializedIndustry = {
    name: validIndustry.name,
    color: validIndustry.color
  }
  
  // Generate structured data
  const structuredData = generatePageStructuredData('use-case', {
    industry: validIndustry.name,
    task: validTask.name,
    models: serializedModels,
    breadcrumbs: [
      { name: 'Home', url: '/' },
      { name: 'Use Cases', url: '/use-cases' },
      { name: validIndustry.name, url: `/use-cases/${industryKey}` },
      { name: validTask.name, url: `/use-cases/${industryKey}/${taskKey}` }
    ]
  })
  
  const faqItems = [
    {
      question: `What's the best AI model for ${validTask.name.toLowerCase()}?`,
      answer: `For ${validTask.name.toLowerCase()}, we recommend ${serializedModels[0]?.name || 'Claude 3.5 Sonnet'} as it excels at ${validTask.description.toLowerCase()}. However, different models work better for different aspects - try our comparison tool to see which works best for your specific needs.`
    },
    {
      question: `How much does it cost to use AI for ${validTask.name.toLowerCase()}?`,
      answer: `With JustSimpleChat, you get access to all premium models including GPT-4, Claude, and Gemini starting from just $7.99/month. This is significantly cheaper than paying for individual subscriptions to each AI service.`
    },
    {
      question: `Can AI replace human ${validIndustry.name.toLowerCase()} in ${validTask.name.toLowerCase()}?`,
      answer: `AI is best used as a powerful assistant that enhances human capabilities rather than replacing them. It can handle routine tasks, provide suggestions, and speed up workflows, but human expertise and judgment remain essential for quality results.`
    },
    {
      question: `How do I get started with AI for ${validTask.name.toLowerCase()}?`,
      answer: `Start with our free trial to test different models with your specific use case. We provide ready-made prompts and workflows to help you get results immediately. Our smart routing will automatically suggest the best model for each task.`
    }
  ]
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <UseCaseTemplate
        industry={serializedIndustry}
        task={validTask}
        models={serializedModels}
        content={useCaseContent}
        faqItems={faqItems}
        industryKey={industryKey}
        taskKey={taskKey}
      />
    </>
  )
}