import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { generateModelComparison } from '@/lib/seo/ai-content-generator'
import { getModelBySlug, getAllModels } from '@/lib/models'
import { ModelCapability } from '@/types'
import { Markdown } from '@/components/Markdown'
import { ComparisonTable } from '@/components/comparison/ComparisonTable'
import { ModelTestWidget } from '@/components/comparison/ModelTestWidget'
import { FAQ } from '@/components/seo/FAQ'
import { generatePageStructuredData } from '@/lib/structured-data-enhanced'
import { 
  Sparkles, 
  Zap, 
  Trophy, 
  ArrowRight, 
  CheckCircle2, 
  XCircle,
  Brain,
  Clock,
  DollarSign,
  Shield,
  Cpu,
  MessageSquare,
  BarChart3,
  TrendingUp,
  Star,
  Users,
  Gauge,
  Code,
  Globe,
  FileText,
  Lightbulb,
  Rocket
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { Model } from '@/types'

interface PageProps {
  params: Promise<{
    model1: string
    model2: string
  }>
}

// Runtime generation only - no static prerendering
// This ensures AI content generation happens at request time, not build time

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { model1: model1Slug, model2: model2Slug } = await params
  
  const model1 = await getModelBySlug(model1Slug)
  const model2 = await getModelBySlug(model2Slug)
  
  if (!model1 || !model2) {
    return {
      title: 'Model Comparison Not Found',
      robots: 'noindex'
    }
  }
  
  const comparisonData = await generateModelComparison(model1, model2)
  
  return {
    title: comparisonData.title || `${model1.name} vs ${model2.name}: Complete AI Model Comparison 2025`,
    description: comparisonData.metaDescription || `Detailed comparison of ${model1.name} and ${model2.name}. Performance, pricing, capabilities, and use cases. Find the best AI model for your needs.`,
    keywords: comparisonData.keywords,
    alternates: {
      canonical: `/compare/${model1Slug}/vs/${model2Slug}`
    },
    openGraph: {
      title: comparisonData.title,
      description: comparisonData.metaDescription,
      type: 'article',
      publishedTime: new Date().toISOString(),
      authors: ['JustSimpleChat Team'],
      tags: [...comparisonData.keywords, 'AI comparison', 'LLM comparison']
    },
    twitter: {
      card: 'summary_large_image',
      title: comparisonData.title,
      description: comparisonData.metaDescription
    }
  }
}

// Feature comparison categories
const getModelFeatures = (model: Model) => ({
  performance: {
    speed: model.processingSpeed || 'Standard',
    contextWindow: model.contextLength || 'N/A',
    reasoningScore: model.capabilities?.includes(ModelCapability.REASONING) ? 'High' : 'Standard'
  },
  capabilities: {
    vision: model.capabilities?.includes(ModelCapability.VISION) || false,
    functionCalling: model.capabilities?.includes(ModelCapability.FUNCTION_CALLING) || false,
    webSearch: model.capabilities?.includes(ModelCapability.WEB_SEARCH) || false,
    codeGeneration: model.capabilities?.includes(ModelCapability.CODE) || false
  },
  pricing: {
    inputCost: model.inputPricePerMillion,
    outputCost: model.outputPricePerMillion,
    tier: model.tier
  }
})

export default async function ModelComparisonPage({ params }: PageProps) {
  const { model1: model1Slug, model2: model2Slug } = await params
  
  const model1 = await getModelBySlug(model1Slug)
  const model2 = await getModelBySlug(model2Slug)
  
  if (!model1 || !model2) {
    notFound()
  }
  
  const comparisonData = await generateModelComparison(model1, model2)
  const model1Features = getModelFeatures(model1)
  const model2Features = getModelFeatures(model2)
  
  // Use structured data from AI-generated content instead of regex parsing
  const overallWinner = comparisonData.overallWinner
  const modelChoiceGuide = comparisonData.modelChoiceGuide
  
  // Generate structured data for this comparison
  const structuredData = generatePageStructuredData('model-comparison', {
    model1,
    model2,
    faqItems: comparisonData.faqItems,
    breadcrumbs: [
      { name: 'Home', url: '/' },
      { name: 'Compare', url: '/compare' },
      { name: `${model1.name} vs ${model2.name}`, url: `/compare/${model1Slug}/vs/${model2Slug}` }
    ]
  })
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="min-h-screen bg-gray-950">
        {/* Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-purple-900/20 via-gray-950 to-blue-900/20 pointer-events-none" />
        <div className="fixed inset-0 bg-[url('/grid.svg')] bg-center opacity-10 pointer-events-none" />
        
        {/* Floating Particles */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute w-96 h-96 bg-purple-600/20 rounded-full blur-3xl animate-float top-20 -left-48" />
          <div className="absolute w-96 h-96 bg-blue-600/20 rounded-full blur-3xl animate-float animation-delay-2000 bottom-20 -right-48" />
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Hero Section with Gradient Border */}
          <header className="text-center mb-16 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 blur-3xl opacity-20 -z-10" />
            
            {/* VS Badge */}
            <div className="inline-flex items-center justify-center mb-8 relative">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full blur-xl opacity-50" />
                <div className="relative bg-gray-900 rounded-full p-4 border border-gray-800">
                  <Zap className="w-8 h-8 text-purple-400" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 mb-6">
              {model1.name} <span className="text-gray-500">vs</span> {model2.name}
            </h1>
            
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
              Comprehensive AI model comparison powered by real-world testing and community feedback
            </p>
            
            {/* Quick Stats */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <Badge variant="outline" className="px-4 py-2 border-purple-600/50 bg-purple-600/10">
                <Users className="w-4 h-4 mr-2" />
                Used by 10K+ developers
              </Badge>
              <Badge variant="outline" className="px-4 py-2 border-blue-600/50 bg-blue-600/10">
                <Star className="w-4 h-4 mr-2" />
                4.9/5 accuracy rating
              </Badge>
              <Badge variant="outline" className="px-4 py-2 border-green-600/50 bg-green-600/10">
                <Clock className="w-4 h-4 mr-2" />
                Updated {new Date().toLocaleDateString()}
              </Badge>
            </div>
          </header>
          
          {/* Winner Card (if applicable) */}
          <section className="mb-12">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity" />
              <Card className="relative bg-gray-900/80 backdrop-blur-xl border-gray-800">
                <CardHeader className="text-center pb-6">
                  <div className="inline-flex items-center gap-2 text-yellow-500 mb-4">
                    <Trophy className="w-6 h-6" />
                    <span className="text-sm font-semibold uppercase tracking-wider">Overall Winner</span>
                    <Trophy className="w-6 h-6" />
                  </div>
                  <CardTitle className="text-3xl">
                    {overallWinner ? `Overall Winner: ${overallWinner.name}` : `${model1.name} vs ${model2.name}: Performance Analysis`}
                  </CardTitle>
                  {overallWinner && (
                    <p className="text-gray-400 mt-2">
                      Composite Score: {overallWinner.compositeScore}/100 • {overallWinner.reasoning}
                    </p>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-purple-600/20 flex items-center justify-center">
                          <CheckCircle2 className="w-5 h-5 text-purple-400" />
                        </div>
                        Choose {model1.name} if you need:
                      </h3>
                      <ul className="space-y-2 text-sm text-gray-300">
                        {modelChoiceGuide?.model1 ? (
                          <>
                            {modelChoiceGuide.model1.chooseIfNeeds.map((need, index) => (
                              <li key={index} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                                {need}
                              </li>
                            ))}
                            {modelChoiceGuide.model1.keyStrengths.map((strength, index) => (
                              <li key={`strength-${index}`} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                                {strength}
                              </li>
                            ))}
                            <li className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                              {modelChoiceGuide.model1.pricing}
                            </li>
                          </>
                        ) : (
                          <>
                            {model1Features.capabilities.vision && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                                Vision and image understanding
                              </li>
                            )}
                            {model1Features.capabilities.codeGeneration && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                                Advanced code generation
                              </li>
                            )}
                            {model1Features.capabilities.functionCalling && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                                Function calling and tool use
                              </li>
                            )}
                            {typeof model1Features.performance.contextWindow === 'number' && model1Features.performance.contextWindow > 100000 && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                                Large context processing ({model1Features.performance.contextWindow.toLocaleString()} tokens)
                              </li>
                            )}
                            <li className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                              ${model1Features.pricing.inputCost}/${model1Features.pricing.outputCost} per million tokens
                            </li>
                          </>
                        )}
                      </ul>
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center">
                          <CheckCircle2 className="w-5 h-5 text-blue-400" />
                        </div>
                        Choose {model2.name} if you need:
                      </h3>
                      <ul className="space-y-2 text-sm text-gray-300">
                        {modelChoiceGuide?.model2 ? (
                          <>
                            {modelChoiceGuide.model2.chooseIfNeeds.map((need, index) => (
                              <li key={index} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                                {need}
                              </li>
                            ))}
                            {modelChoiceGuide.model2.keyStrengths.map((strength, index) => (
                              <li key={`strength-${index}`} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                                {strength}
                              </li>
                            ))}
                            <li className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                              {modelChoiceGuide.model2.pricing}
                            </li>
                          </>
                        ) : (
                          <>
                            {model2Features.capabilities.vision && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                                Vision and image understanding
                              </li>
                            )}
                            {model2Features.capabilities.codeGeneration && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                                Advanced code generation
                              </li>
                            )}
                            {model2Features.capabilities.functionCalling && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                                Function calling and tool use
                              </li>
                            )}
                            {typeof model2Features.performance.contextWindow === 'number' && model2Features.performance.contextWindow > 100000 && (
                              <li className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                                Large context processing ({model2Features.performance.contextWindow.toLocaleString()} tokens)
                              </li>
                            )}
                            <li className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
                              ${model2Features.pricing.inputCost}/${model2Features.pricing.outputCost} per million tokens
                            </li>
                          </>
                        )}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>
          
          {/* Visual Comparison Grid */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-white mb-8 text-center">
              Side-by-Side Comparison
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              {/* Model 1 Card */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/20 to-purple-900/20 rounded-2xl blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
                <Card className="relative h-full bg-gray-900/80 backdrop-blur-xl border-purple-600/20 hover:border-purple-600/40 transition-all">
                  <CardHeader>
                    <CardTitle className="text-2xl flex items-center justify-between">
                      <span>{model1.name}</span>
                      <Badge className="bg-purple-600/20 text-purple-300 border-purple-600/40">
                        {model1.provider}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Performance Metrics */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                        <Gauge className="w-4 h-4" />
                        Performance
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300">Speed</span>
                          <Badge variant="outline" className="text-xs">
                            {model1Features.performance.speed}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300">Context</span>
                          <span className="text-sm font-mono text-purple-400">
                            {model1Features.performance.contextWindow?.toLocaleString() || 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Capabilities */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        Capabilities
                      </h4>
                      <div className="grid grid-cols-2 gap-2">
                        {Object.entries(model1Features.capabilities).map(([key, value]) => (
                          <div key={key} className="flex items-center gap-2">
                            {value ? (
                              <CheckCircle2 className="w-4 h-4 text-green-400" />
                            ) : (
                              <XCircle className="w-4 h-4 text-gray-600" />
                            )}
                            <span className={cn(
                              "text-xs capitalize",
                              value ? "text-gray-200" : "text-gray-600"
                            )}>
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Pricing */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Pricing
                      </h4>
                      <div className="bg-purple-900/20 rounded-lg p-3 border border-purple-600/20">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-400">Input</span>
                          <span className="text-sm font-mono text-purple-300">
                            ${model1Features.pricing.inputCost} per 1M tokens
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-400">Output</span>
                          <span className="text-sm font-mono text-purple-300">
                            ${model1Features.pricing.outputCost} per 1M tokens
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Model 2 Card */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-blue-900/20 rounded-2xl blur-xl opacity-50 group-hover:opacity-70 transition-opacity" />
                <Card className="relative h-full bg-gray-900/80 backdrop-blur-xl border-blue-600/20 hover:border-blue-600/40 transition-all">
                  <CardHeader>
                    <CardTitle className="text-2xl flex items-center justify-between">
                      <span>{model2.name}</span>
                      <Badge className="bg-blue-600/20 text-blue-300 border-blue-600/40">
                        {model2.provider}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Performance Metrics */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                        <Gauge className="w-4 h-4" />
                        Performance
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300">Speed</span>
                          <Badge variant="outline" className="text-xs">
                            {model2Features.performance.speed}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300">Context</span>
                          <span className="text-sm font-mono text-blue-400">
                            {model2Features.performance.contextWindow?.toLocaleString() || 'N/A'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Capabilities */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                        <Brain className="w-4 h-4" />
                        Capabilities
                      </h4>
                      <div className="grid grid-cols-2 gap-2">
                        {Object.entries(model2Features.capabilities).map(([key, value]) => (
                          <div key={key} className="flex items-center gap-2">
                            {value ? (
                              <CheckCircle2 className="w-4 h-4 text-green-400" />
                            ) : (
                              <XCircle className="w-4 h-4 text-gray-600" />
                            )}
                            <span className={cn(
                              "text-xs capitalize",
                              value ? "text-gray-200" : "text-gray-600"
                            )}>
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Pricing */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Pricing
                      </h4>
                      <div className="bg-blue-900/20 rounded-lg p-3 border border-blue-600/20">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs text-gray-400">Input</span>
                          <span className="text-sm font-mono text-blue-300">
                            ${model2Features.pricing.inputCost} per 1M tokens
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-400">Output</span>
                          <span className="text-sm font-mono text-blue-300">
                            ${model2Features.pricing.outputCost} per 1M tokens
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </section>
          
          {/* Interactive Test Widget */}
          <section className="mb-16">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-blue-600/10 rounded-2xl blur-xl" />
              <Card className="relative bg-gray-900/60 backdrop-blur-xl border-gray-800 overflow-hidden">
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-600/10 to-transparent rounded-full blur-3xl" />
                <CardHeader className="relative">
                  <CardTitle className="text-2xl flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                      <MessageSquare className="w-6 h-6 text-white" />
                    </div>
                    Try Both Models Live
                  </CardTitle>
                  <p className="text-gray-400 mt-2">
                    Test {model1.name} and {model2.name} with your own prompts in real-time
                  </p>
                </CardHeader>
                <CardContent className="relative">
                  <ModelTestWidget models={[model1, model2]} />
                </CardContent>
              </Card>
            </div>
          </section>
          
          {/* AI-Generated Deep Analysis */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-white mb-8 flex items-center gap-3">
              <Lightbulb className="w-8 h-8 text-yellow-400" />
              In-Depth Analysis
            </h2>
            
            <div className="prose prose-invert prose-lg max-w-none">
              <div className="bg-gray-900/60 backdrop-blur-xl rounded-2xl p-8 border border-gray-800">
                <Markdown content={comparisonData.content} />
              </div>
            </div>
          </section>
          
          {/* FAQ Section with Gradient Cards */}
          {comparisonData.faqItems.length > 0 && (
            <section className="mb-16">
              <h2 className="text-3xl font-bold text-white mb-8 text-center">
                Frequently Asked Questions
              </h2>
              <div className="max-w-3xl mx-auto">
                <FAQ items={comparisonData.faqItems} />
              </div>
            </section>
          )}
          
          {/* CTA Section - Ultra Premium */}
          <section className="relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-10 blur-3xl" />
            <div className="relative bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-3xl p-1">
              <div className="bg-gray-900 rounded-3xl p-12 text-center">
                <div className="inline-flex items-center justify-center mb-6">
                  <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl">
                    <Rocket className="w-10 h-10 text-white" />
                  </div>
                </div>
                
                <h2 className="text-4xl font-bold text-white mb-4">
                  Why Choose When You Can Have Both?
                </h2>
                
                <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                  Access {model1.name}, {model2.name}, and 150+ more cutting-edge AI models with one subscription. 
                  Save 60% compared to individual plans.
                </p>
                
                <div className="flex flex-wrap items-center justify-center gap-6 mb-8">
                  <div className="flex items-center gap-2 text-green-400">
                    <CheckCircle2 className="w-5 h-5" />
                    <span>All Premium Models</span>
                  </div>
                  <div className="flex items-center gap-2 text-green-400">
                    <CheckCircle2 className="w-5 h-5" />
                    <span>No Rate Limits</span>
                  </div>
                  <div className="flex items-center gap-2 text-green-400">
                    <CheckCircle2 className="w-5 h-5" />
                    <span>3-Day Free Trial</span>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-6 text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
                  >
                    Start Free Trial
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-8 py-6 text-lg rounded-xl"
                  >
                    View All Models
                  </Button>
                </div>
                
                <p className="text-sm text-gray-500 mt-6">
                  No credit card required • Cancel anytime • Instant access
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </>
  )
}