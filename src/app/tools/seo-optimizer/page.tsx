'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  Target, TrendingUp, Search, FileText, BarChart3, 
  CheckCircle, AlertCircle, Lightbulb, Zap, 
  Eye, Clock, Users, Award, Sparkles, Upload,
  Download, RefreshCw, Settings, ArrowRight
} from 'lucide-react'

const OptimizationCard = ({ 
  title, 
  description, 
  score, 
  recommendations, 
  status,
  icon: Icon 
}: any) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6"
  >
    <div className="flex items-start justify-between mb-4">
      <div className="flex items-center gap-3">
        <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${
          score >= 80 ? 'from-green-500 to-emerald-500' :
          score >= 60 ? 'from-yellow-500 to-orange-500' :
          'from-red-500 to-pink-500'
        } flex items-center justify-center`}>
          <Icon className="w-5 h-5 text-white" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">{title}</h3>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
      </div>
      <div className="text-right">
        <div className={`text-2xl font-bold ${
          score >= 80 ? 'text-green-400' :
          score >= 60 ? 'text-yellow-400' :
          'text-red-400'
        }`}>
          {score}%
        </div>
        <div className="text-xs text-gray-500">Score</div>
      </div>
    </div>

    {recommendations.length > 0 && (
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-300">Recommendations:</h4>
        {recommendations.map((rec: string, idx: number) => (
          <div key={idx} className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 rounded-full bg-purple-400 mt-2 flex-shrink-0" />
            <span className="text-sm text-gray-400">{rec}</span>
          </div>
        ))}
      </div>
    )}
  </motion.div>
)

const SEOOptimizer = () => {
  const [content, setContent] = React.useState('')
  const [targetKeyword, setTargetKeyword] = React.useState('')
  const [isAnalyzing, setIsAnalyzing] = React.useState(false)
  const [results, setResults] = React.useState<any>(null)

  const sampleContent = `Artificial Intelligence is transforming how we work and live. From chatbots to image recognition, AI tools are becoming more accessible and powerful every day. Modern AI systems can understand natural language, generate creative content, and solve complex problems that once required human expertise.

  The rise of large language models like GPT-4 and Claude has democratized access to AI capabilities. These models can write code, create marketing copy, analyze data, and even engage in philosophical discussions. As AI continues to evolve, we&apos;re seeing new applications emerge across industries from healthcare to finance.

  For businesses looking to leverage AI, the key is understanding which tools fit their specific needs. Whether you&apos;re automating customer service or generating content at scale, there&apos;s likely an AI solution that can help streamline your workflows and improve efficiency.`

  const analyzeContent = async () => {
    setIsAnalyzing(true)
    
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    setResults({
      overallScore: 72,
      readability: {
        score: 85,
        recommendations: [
          'Excellent sentence variety and flow',
          'Good use of transition words',
          'Consider adding more subheadings for better structure'
        ]
      },
      seoOptimization: {
        score: 68,
        recommendations: [
          `Increase "${targetKeyword}" density to 1-2%`,
          'Add more semantic keywords related to AI',
          'Include target keyword in first paragraph',
          'Optimize meta description length to 150-160 characters'
        ]
      },
      keywordUsage: {
        score: 60,
        recommendations: [
          'Target keyword appears only once',
          'Add related keywords: machine learning, automation, artificial intelligence',
          'Include long-tail variations of your target keyword'
        ]
      },
      contentStructure: {
        score: 75,
        recommendations: [
          'Add H2 and H3 subheadings',
          'Include bullet points or numbered lists',
          'Break up longer paragraphs for better readability'
        ]
      },
      engagement: {
        score: 70,
        recommendations: [
          'Add a compelling call-to-action',
          'Include relevant statistics or data points',
          'Consider adding questions to engage readers'
        ]
      }
    })
    
    setIsAnalyzing(false)
  }

  const optimizedSuggestions = [
    "Add compelling meta title with target keyword",
    "Include relevant internal and external links",
    "Optimize images with descriptive alt text",
    "Add FAQ section to capture long-tail keywords",
    "Include location-based keywords if relevant"
  ]

  return (
    <div className="space-y-8">
      {/* Content Input */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6"
      >
        <div className="flex items-center gap-3 mb-4">
          <FileText className="w-6 h-6 text-purple-400" />
          <h3 className="text-xl font-semibold text-white">Content Analysis</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-gray-400 mb-2">Target Keyword</label>
            <input
              type="text"
              value={targetKeyword}
              onChange={(e) => setTargetKeyword(e.target.value)}
              className="w-full p-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
              placeholder="e.g., AI tools, artificial intelligence, machine learning"
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-gray-400">Content to Optimize</label>
              <button
                onClick={() => setContent(sampleContent)}
                className="text-xs text-purple-400 hover:text-purple-300"
              >
                Use Sample Content
              </button>
            </div>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full p-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:border-purple-500 focus:ring-1 focus:ring-purple-500 resize-none"
              rows={8}
              placeholder="Paste your content here for AI-powered SEO analysis..."
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{content.length} characters</span>
              <span>{content.split(' ').filter(word => word.length > 0).length} words</span>
            </div>
          </div>

          <button
            onClick={analyzeContent}
            disabled={!content.trim() || !targetKeyword.trim() || isAnalyzing}
            className="w-full px-6 py-3 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isAnalyzing ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Analyzing with AI...
              </>
            ) : (
              <>
                <Zap className="w-5 h-5" />
                Analyze & Optimize
              </>
            )}
          </button>
        </div>
      </motion.div>

      {/* Results */}
      {results && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          {/* Overall Score */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Overall SEO Score</h3>
              <div className="text-right">
                <div className={`text-4xl font-bold ${
                  results.overallScore >= 80 ? 'text-green-400' :
                  results.overallScore >= 60 ? 'text-yellow-400' :
                  'text-red-400'
                }`}>
                  {results.overallScore}%
                </div>
                <div className="text-sm text-gray-400">
                  {results.overallScore >= 80 ? 'Excellent' :
                   results.overallScore >= 60 ? 'Good' : 'Needs Work'}
                </div>
              </div>
            </div>
            
            <div className="w-full bg-gray-700 rounded-full h-3">
              <div 
                className={`h-3 rounded-full bg-gradient-to-r ${
                  results.overallScore >= 80 ? 'from-green-500 to-emerald-500' :
                  results.overallScore >= 60 ? 'from-yellow-500 to-orange-500' :
                  'from-red-500 to-pink-500'
                }`}
                style={{ width: `${results.overallScore}%` }}
              />
            </div>
          </div>

          {/* Detailed Analysis */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <OptimizationCard
              title="Readability"
              description="Content clarity and flow"
              score={results.readability.score}
              recommendations={results.readability.recommendations}
              icon={Eye}
            />
            
            <OptimizationCard
              title="SEO Optimization"
              description="Search engine friendliness"
              score={results.seoOptimization.score}
              recommendations={results.seoOptimization.recommendations}
              icon={Search}
            />
            
            <OptimizationCard
              title="Keyword Usage"
              description="Target keyword optimization"
              score={results.keywordUsage.score}
              recommendations={results.keywordUsage.recommendations}
              icon={Target}
            />
            
            <OptimizationCard
              title="Content Structure"
              description="Organization and formatting"
              score={results.contentStructure.score}
              recommendations={results.contentStructure.recommendations}
              icon={BarChart3}
            />
          </div>

          {/* AI Suggestions */}
          <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm rounded-xl border border-purple-500/20 p-6">
            <div className="flex items-center gap-3 mb-4">
              <Lightbulb className="w-6 h-6 text-yellow-400" />
              <h3 className="text-xl font-semibold text-white">AI-Powered Suggestions</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {optimizedSuggestions.map((suggestion, idx) => (
                <div key={idx} className="flex items-start gap-3 p-3 rounded-lg bg-gray-800/50">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300">{suggestion}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button className="flex-1 px-6 py-3 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 flex items-center justify-center gap-2">
              <Download className="w-5 h-5" />
              Download Report
            </button>
            <button className="flex-1 px-6 py-3 rounded-lg bg-gray-700 text-white font-semibold hover:bg-gray-600 transition-all duration-200 flex items-center justify-center gap-2">
              <RefreshCw className="w-5 h-5" />
              Re-analyze
            </button>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default function SEOOptimizerPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        {/* Animated background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-br from-purple-500/20 to-transparent rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-br from-pink-500/20 to-transparent rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-50 animate-pulse" />
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <TrendingUp className="w-10 h-10 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-6">
              AI-Powered{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                SEO Optimizer
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Optimize your content for search engines using advanced AI analysis. 
              Get actionable insights to improve rankings and drive more organic traffic.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#optimizer"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Zap className="w-5 h-5" />
                Start Optimizing
              </a>
              <a
                href="#features"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <Eye className="w-5 h-5" />
                See Features
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Comprehensive SEO Analysis
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Our AI analyzes multiple factors to give you a complete picture of your content&apos;s SEO potential
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <Search className="w-12 h-12 text-blue-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Keyword Optimization</h3>
                <p className="text-gray-400">
                  Analyze keyword density, placement, and semantic relevance to improve search rankings.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <Eye className="w-12 h-12 text-purple-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Readability Analysis</h3>
                <p className="text-gray-400">
                  Ensure your content is easy to read and understand for both users and search engines.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <BarChart3 className="w-12 h-12 text-green-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Content Structure</h3>
                <p className="text-gray-400">
                  Optimize headings, paragraphs, and formatting for better user experience and SEO.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <Target className="w-12 h-12 text-orange-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">Competitive Analysis</h3>
                <p className="text-gray-400">
                  Compare your content against top-ranking pages to identify optimization opportunities.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/20 to-rose-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <Users className="w-12 h-12 text-pink-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">User Engagement</h3>
                <p className="text-gray-400">
                  Analyze factors that influence user engagement and time spent on page.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <Lightbulb className="w-12 h-12 text-yellow-400 mb-4" />
                <h3 className="text-xl font-bold text-white mb-2">AI Suggestions</h3>
                <p className="text-gray-400">
                  Get personalized recommendations powered by advanced AI models for maximum impact.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Optimizer Tool */}
      <section id="optimizer" className="relative py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Optimize Your Content Now
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Paste your content below and get instant AI-powered SEO recommendations
            </p>
          </motion.div>

          <SEOOptimizer />
        </div>
      </section>

      {/* Benefits Section */}
      <section className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Why Use Our AI SEO Optimizer?
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Leverage the power of advanced AI models to optimize your content like a pro
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Clock className="w-8 h-8 text-blue-400 mx-auto mb-4" />
              <div className="text-2xl font-bold text-white mb-2">5x Faster</div>
              <div className="text-gray-400">Than manual SEO analysis</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-4" />
              <div className="text-2xl font-bold text-white mb-2">40%+</div>
              <div className="text-gray-400">Average ranking improvement</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Award className="w-8 h-8 text-purple-400 mx-auto mb-4" />
              <div className="text-2xl font-bold text-white mb-2">95%</div>
              <div className="text-gray-400">Accuracy rate</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Users className="w-8 h-8 text-orange-400 mx-auto mb-4" />
              <div className="text-2xl font-bold text-white mb-2">10K+</div>
              <div className="text-gray-400">Content pieces optimized</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
              Ready to Dominate Search Results?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Start optimizing your content with AI-powered insights and watch your rankings soar.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#optimizer"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <TrendingUp className="w-5 h-5" />
                Start Optimizing Now
              </a>
              <a
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <Sparkles className="w-5 h-5" />
                Get Full Access
              </a>
            </div>

            <p className="mt-6 text-gray-400">
              Free analysis • Instant results • Powered by advanced AI
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  )
}