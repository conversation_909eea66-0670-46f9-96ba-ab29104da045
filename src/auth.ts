/**
 * NextAuth Instance Export
 * 
 * @description
 * Creates and exports the NextAuth instance with the configured options.
 * This file provides the main authentication functions used throughout
 * the application.
 * 
 * @module auth
 * 
 * @exports
 * - handlers: Route handlers for NextAuth API routes
 * - auth: Function to get the current session
 * - signIn: Function to programmatically sign in users
 * - signOut: Function to programmatically sign out users
 */

import NextAuth from "next-auth"
import { authOptions } from "@/lib/auth/index"

// Debug environment
console.log('[Auth] Environment Check:', {
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NODE_ENV: process.env.NODE_ENV,
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 'Set' : 'Not set',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET ? 'Set' : 'Not set',
});

export const { handlers, auth, signIn, signOut } = NextAuth(authOptions)