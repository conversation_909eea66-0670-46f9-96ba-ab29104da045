import { 
  Code, 
  PenTool, 
  BookOpen, 
  TrendingUp,
  Building,
  Heart,
  Briefcase,
  Palette,
  Calculator,
  Globe,
  Users,
  Mail
} from 'lucide-react'

// Define types for industry map structure
type TaskData = {
  name: string;
  description: string;
  models: string[];
}

type IndustryData = {
  name: string;
  icon: any;
  color: string;
  tasks: Record<string, TaskData>;
}

type IndustryMapType = Record<string, IndustryData>;

// Define industry categories and their tasks
export const industryMap: IndustryMapType = {
  developers: {
    name: 'Developers',
    icon: Code,
    color: 'from-blue-600 to-cyan-600',
    tasks: {
      'code-review': {
        name: 'Code Review',
        description: 'AI-powered code analysis and suggestions',
        models: ['claude-3-5-sonnet', 'deepseek-r1', 'qwen-coder']
      },
      'debugging': {
        name: 'Debugging',
        description: 'Find and fix bugs in your code',
        models: ['claude-3-5-sonnet', 'gpt-4', 'deepseek-r1']
      },
      'api-documentation': {
        name: 'API Documentation',
        description: 'Generate comprehensive API docs',
        models: ['gpt-4', 'claude-3-5-sonnet', 'mistral-large']
      },
      'test-generation': {
        name: 'Test Generation',
        description: 'Create unit and integration tests',
        models: ['claude-3-5-sonnet', 'qwen-coder', 'deepseek-r1']
      }
    }
  },
  marketers: {
    name: 'Marketers',
    icon: TrendingUp,
    color: 'from-purple-600 to-pink-600',
    tasks: {
      'content-creation': {
        name: 'Content Creation',
        description: 'Create engaging marketing content',
        models: ['gpt-4', 'claude-3-5-sonnet', 'gemini-2-flash']
      },
      'seo-optimization': {
        name: 'SEO Optimization',
        description: 'Optimize content for search engines',
        models: ['perplexity-sonar', 'gpt-4', 'claude-3-5-sonnet']
      },
      'ad-copy': {
        name: 'Ad Copy',
        description: 'Write compelling advertisements',
        models: ['gpt-4', 'claude-3-haiku', 'mistral-large']
      },
      'email-campaigns': {
        name: 'Email Campaigns',
        description: 'Design effective email marketing',
        models: ['gpt-4', 'claude-3-5-sonnet', 'gemini-2-flash']
      }
    }
  },
  students: {
    name: 'Students',
    icon: BookOpen,
    color: 'from-green-600 to-emerald-600',
    tasks: {
      'essay-writing': {
        name: 'Essay Writing',
        description: 'Research and write academic essays',
        models: ['gpt-4', 'claude-3-5-sonnet', 'perplexity-sonar']
      },
      'research-assistance': {
        name: 'Research Assistance',
        description: 'Find and analyze academic sources',
        models: ['perplexity-sonar', 'gpt-4', 'claude-3-5-sonnet']
      },
      'problem-solving': {
        name: 'Problem Solving',
        description: 'Solve math and science problems',
        models: ['gpt-4', 'claude-3-5-sonnet', 'qwen-math']
      },
      'study-guides': {
        name: 'Study Guides',
        description: 'Create comprehensive study materials',
        models: ['gpt-4', 'claude-3-5-sonnet', 'gemini-2-flash']
      }
    }
  },
  businesses: {
    name: 'Businesses',
    icon: Building,
    color: 'from-orange-600 to-red-600',
    tasks: {
      'customer-support': {
        name: 'Customer Support',
        description: 'Automate customer service responses',
        models: ['gpt-4', 'claude-3-5-sonnet', 'mistral-large']
      },
      'data-analysis': {
        name: 'Data Analysis',
        description: 'Analyze business data and trends',
        models: ['gpt-4', 'claude-3-5-sonnet', 'deepseek-r1']
      },
      'report-generation': {
        name: 'Report Generation',
        description: 'Create professional business reports',
        models: ['gpt-4', 'claude-3-5-sonnet', 'gemini-2-flash']
      },
      'meeting-summaries': {
        name: 'Meeting Summaries',
        description: 'Summarize meetings and action items',
        models: ['claude-3-5-sonnet', 'gpt-4', 'mistral-large']
      }
    }
  },
  healthcare: {
    name: 'Healthcare',
    icon: Heart,
    color: 'from-red-600 to-pink-600',
    tasks: {
      'medical-documentation': {
        name: 'Medical Documentation',
        description: 'Assist with medical record documentation',
        models: ['claude-3-5-sonnet', 'gpt-4', 'mistral-large']
      },
      'research-literature': {
        name: 'Research Literature',
        description: 'Review medical literature and studies',
        models: ['perplexity-sonar', 'claude-3-5-sonnet', 'gpt-4']
      },
      'patient-education': {
        name: 'Patient Education',
        description: 'Create patient education materials',
        models: ['gpt-4', 'claude-3-5-sonnet', 'gemini-2-flash']
      }
    }
  },
  legal: {
    name: 'Legal',
    icon: Briefcase,
    color: 'from-gray-600 to-gray-800',
    tasks: {
      'contract-analysis': {
        name: 'Contract Analysis',
        description: 'Review and analyze legal contracts',
        models: ['claude-3-5-sonnet', 'gpt-4', 'mistral-large']
      },
      'legal-research': {
        name: 'Legal Research',
        description: 'Research case law and precedents',
        models: ['perplexity-sonar', 'claude-3-5-sonnet', 'gpt-4']
      },
      'document-drafting': {
        name: 'Document Drafting',
        description: 'Draft legal documents and briefs',
        models: ['claude-3-5-sonnet', 'gpt-4', 'mistral-large']
      }
    }
  }
};

export type { IndustryMapType, IndustryData, TaskData };