/**
 * Real-time Monitoring and Logging Service for AI Provider Performance
 * 
 * @description
 * Comprehensive monitoring system that tracks provider performance, costs, quality,
 * and availability in real-time. Provides alerting, analytics, and integration
 * with external monitoring systems.
 * 
 * Features:
 * - Real-time performance metrics collection
 * - Provider cost tracking and optimization
 * - Quality scoring and trend analysis
 * - Automated alerting and notifications
 * - Request tracing and correlation
 * - Dashboard data aggregation
 * - Integration with external monitoring (DataDog, New Relic, etc.)
 * - Custom metric definitions
 * - Historical data retention and analysis
 * 
 * @module lib/ai/monitoring-service
 */

import { EventEmitter } from 'events';
import { apiLogger } from '@/lib/logger';
import { HealthMonitor, HealthMetrics } from './health-monitor';
import { GenerationOptions, StreamChunk } from './providers/types';

/**
 * Performance metrics for a single request
 */
export interface RequestMetrics {
  requestId: string;
  provider: string;
  model: string;
  userId?: string;
  sessionId?: string;
  
  // Timing metrics
  startTime: number;
  endTime: number;
  duration: number;
  timeToFirstToken?: number;
  timeToLastToken?: number;
  
  // Token metrics
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  tokensPerSecond?: number;
  
  // Cost metrics
  inputCost: number;
  outputCost: number;
  totalCost: number;
  
  // Quality metrics
  success: boolean;
  errorType?: string;
  errorMessage?: string;
  qualityScore?: number;
  
  // Request characteristics
  requestType: 'completion' | 'streaming';
  temperature?: number;
  maxTokens?: number;
  
  // Context
  userTier?: string;
  priority?: string;
  region?: string;
  
  // Metadata
  configVersion?: string;
  timestamp: Date;
}

/**
 * Aggregated metrics for a provider over a time period
 */
export interface ProviderAggregatedMetrics {
  provider: string;
  model?: string;
  period: {
    start: Date;
    end: Date;
    duration: number; // milliseconds
  };
  
  // Request metrics
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  successRate: number;
  
  // Performance metrics
  averageLatency: number;
  medianLatency: number;
  p95Latency: number;
  p99Latency: number;
  averageTokensPerSecond: number;
  
  // Token metrics
  totalInputTokens: number;
  totalOutputTokens: number;
  totalTokens: number;
  averageTokensPerRequest: number;
  
  // Cost metrics
  totalCost: number;
  averageCostPerRequest: number;
  averageCostPerToken: number;
  costPerSuccessfulRequest: number;
  
  // Quality metrics
  averageQualityScore: number;
  errorBreakdown: { [errorType: string]: number };
  
  // Trends
  trends: {
    latency: number; // percentage change from previous period
    successRate: number;
    cost: number;
    quality: number;
  };
}

/**
 * Alert configuration
 */
export interface AlertConfig {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  provider?: string;
  model?: string;
  
  // Conditions
  conditions: {
    metric: 'success_rate' | 'latency' | 'cost' | 'error_rate' | 'tokens_per_second' | 'quality_score';
    operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'ne';
    threshold: number;
    windowMinutes: number;
    minSamples?: number;
  }[];
  
  // Actions
  actions: {
    type: 'webhook' | 'email' | 'slack' | 'pagerduty' | 'log';
    config: any;
  }[];
  
  // Alert management
  cooldownMinutes: number;
  maxAlertsPerHour: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
}

/**
 * Alert instance
 */
export interface Alert {
  id: string;
  configId: string;
  provider: string;
  model?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  metrics: any;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

/**
 * Dashboard metrics for real-time display
 */
export interface DashboardMetrics {
  timestamp: Date;
  overview: {
    totalRequests: number;
    totalProviders: number;
    averageLatency: number;
    overallSuccessRate: number;
    totalCost: number;
    activeAlerts: number;
  };
  providers: {
    [providerId: string]: {
      status: 'healthy' | 'degraded' | 'unhealthy' | 'offline';
      requestsPerMinute: number;
      successRate: number;
      averageLatency: number;
      cost: number;
      lastUpdate: Date;
    };
  };
  trends: {
    requestVolume: Array<{ timestamp: Date; value: number }>;
    successRate: Array<{ timestamp: Date; value: number }>;
    latency: Array<{ timestamp: Date; value: number }>;
    cost: Array<{ timestamp: Date; value: number }>;
  };
}

/**
 * Monitoring configuration
 */
export interface MonitoringConfig {
  // Data retention
  retentionPolicy: {
    rawMetrics: number; // days
    aggregatedMetrics: number; // days
    alerts: number; // days
  };
  
  // Aggregation settings
  aggregationIntervals: number[]; // minutes
  
  // Performance settings
  bufferSize: number;
  flushIntervalMs: number;
  
  // External integrations
  integrations: {
    datadog?: {
      enabled: boolean;
      apiKey: string;
      tags: string[];
    };
    newrelic?: {
      enabled: boolean;
      licenseKey: string;
    };
    prometheus?: {
      enabled: boolean;
      endpoint: string;
    };
    webhook?: {
      enabled: boolean;
      url: string;
      headers: { [key: string]: string };
    };
  };
  
  // Custom metrics
  customMetrics: {
    [name: string]: {
      type: 'counter' | 'gauge' | 'histogram';
      description: string;
      labels: string[];
    };
  };
}

/**
 * Request tracing information
 */
export interface RequestTrace {
  traceId: string;
  requestId: string;
  parentSpanId?: string;
  spans: TraceSpan[];
  startTime: number;
  endTime?: number;
  duration?: number;
  status: 'pending' | 'completed' | 'failed';
  tags: { [key: string]: string };
}

export interface TraceSpan {
  spanId: string;
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  tags: { [key: string]: string };
  logs: Array<{
    timestamp: number;
    message: string;
    level: 'debug' | 'info' | 'warn' | 'error';
  }>;
  parentSpanId?: string;
}

/**
 * Main Monitoring Service
 */
export class MonitoringService extends EventEmitter {
  private config: MonitoringConfig;
  private healthMonitor?: HealthMonitor;
  
  // Data storage
  private metricsBuffer: RequestMetrics[] = [];
  private aggregatedMetrics: Map<string, ProviderAggregatedMetrics[]> = new Map();
  private alerts: Map<string, Alert> = new Map();
  private alertConfigs: Map<string, AlertConfig> = new Map();
  private traces: Map<string, RequestTrace> = new Map();
  
  // Timers and intervals
  private flushTimer?: NodeJS.Timeout;
  private aggregationTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  
  // State
  private isStarted = false;
  private lastDashboardUpdate = 0;
  private dashboardCache?: DashboardMetrics;

  constructor(config: Partial<MonitoringConfig> = {}, healthMonitor?: HealthMonitor) {
    super();
    
    this.config = {
      retentionPolicy: {
        rawMetrics: 7,
        aggregatedMetrics: 30,
        alerts: 90
      },
      aggregationIntervals: [1, 5, 15, 60], // 1min, 5min, 15min, 1hour
      bufferSize: 1000,
      flushIntervalMs: 30000, // 30 seconds
      integrations: {},
      customMetrics: {},
      ...config
    };
    
    this.healthMonitor = healthMonitor;
    
    this.setupDefaultAlerts();
  }

  /**
   * Start the monitoring service
   */
  start(): void {
    if (this.isStarted) {
      return;
    }
    
    this.isStarted = true;
    
    // Start periodic tasks
    this.startFlushTimer();
    this.startAggregationTimer();
    this.startCleanupTimer();
    
    // Initialize integrations
    this.initializeIntegrations();
    
    apiLogger.info('Monitoring service started', {
      bufferSize: this.config.bufferSize,
      flushInterval: this.config.flushIntervalMs,
      integrations: Object.keys(this.config.integrations).filter(k => 
        this.config.integrations[k as keyof typeof this.config.integrations]?.enabled
      )
    });
    
    this.emit('started');
  }

  /**
   * Stop the monitoring service
   */
  stop(): void {
    if (!this.isStarted) {
      return;
    }
    
    this.isStarted = false;
    
    // Clear timers
    if (this.flushTimer) clearInterval(this.flushTimer);
    if (this.aggregationTimer) clearInterval(this.aggregationTimer);
    if (this.cleanupTimer) clearInterval(this.cleanupTimer);
    
    // Flush remaining metrics
    this.flushMetrics();
    
    apiLogger.info('Monitoring service stopped');
    this.emit('stopped');
  }

  /**
   * Record metrics for a completed request
   */
  recordRequest(metrics: RequestMetrics): void {
    // Add to buffer
    this.metricsBuffer.push(metrics);
    
    // Emit real-time event
    this.emit('requestCompleted', metrics);
    
    // Check for immediate alerts
    this.checkAlerts(metrics);
    
    // Send to external integrations
    this.sendToIntegrations(metrics);
    
    // Flush if buffer is full
    if (this.metricsBuffer.length >= this.config.bufferSize) {
      this.flushMetrics();
    }
    
    // Update health monitor
    if (this.healthMonitor) {
      this.healthMonitor.recordRequest(
        metrics.provider,
        metrics.success,
        metrics.duration
      );
    }
  }

  /**
   * Start tracing a request
   */
  startTrace(traceId: string, requestId: string, tags: { [key: string]: string } = {}): RequestTrace {
    const trace: RequestTrace = {
      traceId,
      requestId,
      spans: [],
      startTime: Date.now(),
      status: 'pending',
      tags
    };
    
    this.traces.set(traceId, trace);
    return trace;
  }

  /**
   * Add span to trace
   */
  addSpan(traceId: string, span: Omit<TraceSpan, 'spanId'>): string {
    const trace = this.traces.get(traceId);
    if (!trace) {
      throw new Error(`Trace not found: ${traceId}`);
    }
    
    const spanId = `span_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullSpan: TraceSpan = {
      ...span,
      spanId
    };
    
    trace.spans.push(fullSpan);
    return spanId;
  }

  /**
   * Complete a trace
   */
  completeTrace(traceId: string, status: 'completed' | 'failed'): void {
    const trace = this.traces.get(traceId);
    if (!trace) {
      return;
    }
    
    trace.endTime = Date.now();
    trace.duration = trace.endTime - trace.startTime;
    trace.status = status;
    
    this.emit('traceCompleted', trace);
  }

  /**
   * Get aggregated metrics for a provider and time period
   */
  getAggregatedMetrics(
    provider: string,
    model?: string,
    period?: { start: Date; end: Date }
  ): ProviderAggregatedMetrics[] {
    const key = model ? `${provider}:${model}` : provider;
    const metrics = this.aggregatedMetrics.get(key) || [];
    
    if (!period) {
      return metrics;
    }
    
    return metrics.filter(m => 
      m.period.start >= period.start && m.period.end <= period.end
    );
  }

  /**
   * Get current dashboard metrics
   */
  getDashboardMetrics(): DashboardMetrics {
    const now = Date.now();
    
    // Return cached data if recent
    if (this.dashboardCache && (now - this.lastDashboardUpdate) < 30000) {
      return this.dashboardCache;
    }
    
    this.dashboardCache = this.calculateDashboardMetrics();
    this.lastDashboardUpdate = now;
    
    return this.dashboardCache;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
  }

  /**
   * Add alert configuration
   */
  addAlertConfig(config: Omit<AlertConfig, 'createdAt' | 'updatedAt' | 'triggerCount'>): void {
    const fullConfig: AlertConfig = {
      ...config,
      createdAt: new Date(),
      updatedAt: new Date(),
      triggerCount: 0
    };
    
    this.alertConfigs.set(config.id, fullConfig);
    
    apiLogger.info('Alert configuration added', {
      alertId: config.id,
      name: config.name,
      severity: config.severity
    });
  }

  /**
   * Remove alert configuration
   */
  removeAlertConfig(alertId: string): boolean {
    const removed = this.alertConfigs.delete(alertId);
    
    if (removed) {
      apiLogger.info('Alert configuration removed', { alertId });
    }
    
    return removed;
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.resolved) {
      return false;
    }
    
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = new Date();
    
    this.emit('alertAcknowledged', alert);
    
    apiLogger.info('Alert acknowledged', {
      alertId,
      acknowledgedBy,
      severity: alert.severity
    });
    
    return true;
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.resolved) {
      return false;
    }
    
    alert.resolved = true;
    alert.resolvedAt = new Date();
    
    this.emit('alertResolved', alert);
    
    apiLogger.info('Alert resolved', {
      alertId,
      severity: alert.severity,
      duration: alert.resolvedAt.getTime() - alert.timestamp.getTime()
    });
    
    return true;
  }

  /**
   * Get request traces
   */
  getTraces(filters?: {
    provider?: string;
    status?: 'pending' | 'completed' | 'failed';
    minDuration?: number;
    maxDuration?: number;
  }): RequestTrace[] {
    let traces = Array.from(this.traces.values());
    
    if (filters) {
      if (filters.provider) {
        traces = traces.filter(t => t.tags.provider === filters.provider);
      }
      if (filters.status) {
        traces = traces.filter(t => t.status === filters.status);
      }
      if (filters.minDuration) {
        traces = traces.filter(t => (t.duration || 0) >= filters.minDuration!);
      }
      if (filters.maxDuration) {
        traces = traces.filter(t => (t.duration || 0) <= filters.maxDuration!);
      }
    }
    
    return traces.sort((a, b) => b.startTime - a.startTime);
  }

  /**
   * Get performance summary for a time period
   */
  getPerformanceSummary(period: { start: Date; end: Date }): {
    totalRequests: number;
    successRate: number;
    averageLatency: number;
    totalCost: number;
    topProviders: Array<{ provider: string; requests: number; successRate: number }>;
    errorBreakdown: { [errorType: string]: number };
  } {
    const startTime = period.start.getTime();
    const endTime = period.end.getTime();
    
    // Filter metrics in time period
    const filteredMetrics = this.metricsBuffer.filter(m => {
      const metricTime = m.timestamp.getTime();
      return metricTime >= startTime && metricTime <= endTime;
    });
    
    if (filteredMetrics.length === 0) {
      return {
        totalRequests: 0,
        successRate: 0,
        averageLatency: 0,
        totalCost: 0,
        topProviders: [],
        errorBreakdown: {}
      };
    }
    
    // Calculate summary
    const totalRequests = filteredMetrics.length;
    const successfulRequests = filteredMetrics.filter(m => m.success).length;
    const successRate = successfulRequests / totalRequests;
    const averageLatency = filteredMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests;
    const totalCost = filteredMetrics.reduce((sum, m) => sum + m.totalCost, 0);
    
    // Provider breakdown
    const providerStats = new Map<string, { requests: number; successful: number }>();
    filteredMetrics.forEach(m => {
      const existing = providerStats.get(m.provider) || { requests: 0, successful: 0 };
      existing.requests++;
      if (m.success) existing.successful++;
      providerStats.set(m.provider, existing);
    });
    
    const topProviders = Array.from(providerStats.entries())
      .map(([provider, stats]) => ({
        provider,
        requests: stats.requests,
        successRate: stats.successful / stats.requests
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 5);
    
    // Error breakdown
    const errorBreakdown: { [errorType: string]: number } = {};
    filteredMetrics.forEach(m => {
      if (!m.success && m.errorType) {
        errorBreakdown[m.errorType] = (errorBreakdown[m.errorType] || 0) + 1;
      }
    });
    
    return {
      totalRequests,
      successRate,
      averageLatency,
      totalCost,
      topProviders,
      errorBreakdown
    };
  }

  /**
   * Private helper methods
   */
  
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushMetrics();
    }, this.config.flushIntervalMs);
  }

  private startAggregationTimer(): void {
    // Run aggregation every minute
    this.aggregationTimer = setInterval(() => {
      this.aggregateMetrics();
    }, 60000);
  }

  private startCleanupTimer(): void {
    // Run cleanup every hour
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldData();
    }, 3600000);
  }

  private flushMetrics(): void {
    if (this.metricsBuffer.length === 0) {
      return;
    }
    
    const metrics = [...this.metricsBuffer];
    this.metricsBuffer = [];
    
    // Process metrics (save to database, send to external systems, etc.)
    this.processMetrics(metrics);
    
    this.emit('metricsFlushed', { count: metrics.length });
  }

  private processMetrics(metrics: RequestMetrics[]): void {
    // In a real implementation, this would save to database
    // For now, we'll just log the metrics
    apiLogger.debug('Processing metrics batch', {
      count: metrics.length,
      providers: [...new Set(metrics.map(m => m.provider))],
      timeRange: {
        start: Math.min(...metrics.map(m => m.timestamp.getTime())),
        end: Math.max(...metrics.map(m => m.timestamp.getTime()))
      }
    });
  }

  private aggregateMetrics(): void {
    // Aggregate metrics for each interval
    for (const intervalMinutes of this.config.aggregationIntervals) {
      this.aggregateForInterval(intervalMinutes);
    }
  }

  private aggregateForInterval(intervalMinutes: number): void {
    const now = new Date();
    const intervalStart = new Date(now.getTime() - intervalMinutes * 60 * 1000);
    
    // Group metrics by provider
    const providerMetrics = new Map<string, RequestMetrics[]>();
    
    this.metricsBuffer.forEach(metric => {
      if (metric.timestamp >= intervalStart) {
        const key = `${metric.provider}:${metric.model}`;
        const existing = providerMetrics.get(key) || [];
        existing.push(metric);
        providerMetrics.set(key, existing);
      }
    });
    
    // Calculate aggregated metrics for each provider
    providerMetrics.forEach((metrics, key) => {
      const [provider, model] = key.split(':');
      const aggregated = this.calculateAggregatedMetrics(provider, model, metrics, intervalStart, now);
      
      // Store aggregated metrics
      const existing = this.aggregatedMetrics.get(key) || [];
      existing.push(aggregated);
      
      // Keep only recent aggregations
      const cutoff = new Date(now.getTime() - this.config.retentionPolicy.aggregatedMetrics * 24 * 60 * 60 * 1000);
      const filtered = existing.filter(m => m.period.start >= cutoff);
      
      this.aggregatedMetrics.set(key, filtered);
    });
  }

  private calculateAggregatedMetrics(
    provider: string,
    model: string,
    metrics: RequestMetrics[],
    start: Date,
    end: Date
  ): ProviderAggregatedMetrics {
    const successfulMetrics = metrics.filter(m => m.success);
    const latencies = metrics.map(m => m.duration).sort((a, b) => a - b);
    
    return {
      provider,
      model,
      period: {
        start,
        end,
        duration: end.getTime() - start.getTime()
      },
      totalRequests: metrics.length,
      successfulRequests: successfulMetrics.length,
      failedRequests: metrics.length - successfulMetrics.length,
      successRate: metrics.length > 0 ? successfulMetrics.length / metrics.length : 0,
      averageLatency: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
      medianLatency: latencies.length > 0 ? latencies[Math.floor(latencies.length / 2)] : 0,
      p95Latency: latencies.length > 0 ? latencies[Math.floor(latencies.length * 0.95)] : 0,
      p99Latency: latencies.length > 0 ? latencies[Math.floor(latencies.length * 0.99)] : 0,
      averageTokensPerSecond: successfulMetrics.length > 0 ? 
        successfulMetrics.reduce((sum, m) => sum + (m.tokensPerSecond || 0), 0) / successfulMetrics.length : 0,
      totalInputTokens: metrics.reduce((sum, m) => sum + m.inputTokens, 0),
      totalOutputTokens: metrics.reduce((sum, m) => sum + m.outputTokens, 0),
      totalTokens: metrics.reduce((sum, m) => sum + m.totalTokens, 0),
      averageTokensPerRequest: metrics.length > 0 ? 
        metrics.reduce((sum, m) => sum + m.totalTokens, 0) / metrics.length : 0,
      totalCost: metrics.reduce((sum, m) => sum + m.totalCost, 0),
      averageCostPerRequest: metrics.length > 0 ? 
        metrics.reduce((sum, m) => sum + m.totalCost, 0) / metrics.length : 0,
      averageCostPerToken: metrics.reduce((sum, m) => sum + m.totalTokens, 0) > 0 ?
        metrics.reduce((sum, m) => sum + m.totalCost, 0) / metrics.reduce((sum, m) => sum + m.totalTokens, 0) : 0,
      costPerSuccessfulRequest: successfulMetrics.length > 0 ?
        successfulMetrics.reduce((sum, m) => sum + m.totalCost, 0) / successfulMetrics.length : 0,
      averageQualityScore: metrics.length > 0 ? 
        metrics.reduce((sum, m) => sum + (m.qualityScore || 0), 0) / metrics.length : 0,
      errorBreakdown: this.calculateErrorBreakdown(metrics),
      trends: {
        latency: 0, // Would calculate from previous period
        successRate: 0,
        cost: 0,
        quality: 0
      }
    };
  }

  private calculateErrorBreakdown(metrics: RequestMetrics[]): { [errorType: string]: number } {
    const breakdown: { [errorType: string]: number } = {};
    
    metrics.forEach(metric => {
      if (!metric.success && metric.errorType) {
        breakdown[metric.errorType] = (breakdown[metric.errorType] || 0) + 1;
      }
    });
    
    return breakdown;
  }

  private checkAlerts(metrics: RequestMetrics): void {
    // Check each alert configuration
    this.alertConfigs.forEach((config, configId) => {
      if (!config.enabled) return;
      
      // Filter by provider/model if specified
      if (config.provider && config.provider !== metrics.provider) return;
      if (config.model && config.model !== metrics.model) return;
      
      // Check if alert should trigger
      if (this.shouldTriggerAlert(config, metrics)) {
        this.triggerAlert(config, metrics);
      }
    });
  }

  private shouldTriggerAlert(config: AlertConfig, metrics: RequestMetrics): boolean {
    // Simplified alert logic - in real implementation would check historical data
    return config.conditions.some(condition => {
      const value = this.getMetricValue(metrics, condition.metric);
      return this.evaluateCondition(value, condition.operator, condition.threshold);
    });
  }

  private getMetricValue(metrics: RequestMetrics, metric: string): number {
    switch (metric) {
      case 'success_rate': return metrics.success ? 1 : 0;
      case 'latency': return metrics.duration;
      case 'cost': return metrics.totalCost;
      case 'error_rate': return metrics.success ? 0 : 1;
      case 'tokens_per_second': return metrics.tokensPerSecond || 0;
      case 'quality_score': return metrics.qualityScore || 0;
      default: return 0;
    }
  }

  private evaluateCondition(value: number, operator: string, threshold: number): boolean {
    switch (operator) {
      case 'gt': return value > threshold;
      case 'gte': return value >= threshold;
      case 'lt': return value < threshold;
      case 'lte': return value <= threshold;
      case 'eq': return value === threshold;
      case 'ne': return value !== threshold;
      default: return false;
    }
  }

  private triggerAlert(config: AlertConfig, metrics: RequestMetrics): void {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const alert: Alert = {
      id: alertId,
      configId: config.id,
      provider: metrics.provider,
      model: metrics.model,
      severity: config.severity,
      title: `${config.name} - ${metrics.provider}`,
      message: `Alert triggered for ${config.name}`,
      metrics: metrics,
      timestamp: new Date(),
      resolved: false
    };
    
    this.alerts.set(alertId, alert);
    
    // Update config trigger count
    config.triggerCount++;
    config.lastTriggered = new Date();
    
    this.emit('alertTriggered', alert);
    
    // Execute alert actions
    this.executeAlertActions(config, alert);
    
    apiLogger.warn('Alert triggered', {
      alertId,
      configId: config.id,
      provider: metrics.provider,
      severity: config.severity
    });
  }

  private executeAlertActions(config: AlertConfig, alert: Alert): void {
    config.actions.forEach(action => {
      try {
        switch (action.type) {
          case 'log':
            apiLogger.error('ALERT', { alert, config: action.config });
            break;
          case 'webhook':
            this.sendWebhookAlert(action.config, alert);
            break;
          // Add other action types as needed
        }
      } catch (error) {
        apiLogger.error('Failed to execute alert action', { 
          alertId: alert.id, 
          actionType: action.type, 
          error 
        });
      }
    });
  }

  private sendWebhookAlert(config: any, alert: Alert): void {
    // Webhook implementation would go here
    apiLogger.debug('Webhook alert sent', { url: config.url, alertId: alert.id });
  }

  private calculateDashboardMetrics(): DashboardMetrics {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    // Get recent metrics
    const recentMetrics = this.metricsBuffer.filter(m => m.timestamp >= oneHourAgo);
    
    const overview = {
      totalRequests: recentMetrics.length,
      totalProviders: new Set(recentMetrics.map(m => m.provider)).size,
      averageLatency: recentMetrics.length > 0 ? 
        recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length : 0,
      overallSuccessRate: recentMetrics.length > 0 ?
        recentMetrics.filter(m => m.success).length / recentMetrics.length : 0,
      totalCost: recentMetrics.reduce((sum, m) => sum + m.totalCost, 0),
      activeAlerts: this.getActiveAlerts().length
    };
    
    // Provider breakdown
    const providerStats = new Map<string, any>();
    recentMetrics.forEach(m => {
      const existing = providerStats.get(m.provider) || {
        requests: 0,
        successful: 0,
        totalLatency: 0,
        totalCost: 0
      };
      
      existing.requests++;
      if (m.success) existing.successful++;
      existing.totalLatency += m.duration;
      existing.totalCost += m.totalCost;
      
      providerStats.set(m.provider, existing);
    });
    
    const providers: DashboardMetrics['providers'] = {};
    providerStats.forEach((stats, provider) => {
      providers[provider] = {
        status: this.getProviderStatus(provider),
        requestsPerMinute: stats.requests / 60,
        successRate: stats.requests > 0 ? stats.successful / stats.requests : 0,
        averageLatency: stats.requests > 0 ? stats.totalLatency / stats.requests : 0,
        cost: stats.totalCost,
        lastUpdate: now
      };
    });
    
    return {
      timestamp: now,
      overview,
      providers,
      trends: {
        requestVolume: [],
        successRate: [],
        latency: [],
        cost: []
      }
    };
  }

  private getProviderStatus(provider: string): 'healthy' | 'degraded' | 'unhealthy' | 'offline' {
    if (this.healthMonitor) {
      const metrics = this.healthMonitor.getMetrics(provider);
      if (metrics) {
        return metrics.status as 'healthy' | 'degraded' | 'unhealthy' | 'offline';
      }
    }
    return 'healthy';
  }

  private cleanupOldData(): void {
    const now = new Date();
    
    // Clean up raw metrics
    const rawCutoff = new Date(now.getTime() - this.config.retentionPolicy.rawMetrics * 24 * 60 * 60 * 1000);
    this.metricsBuffer = this.metricsBuffer.filter(m => m.timestamp >= rawCutoff);
    
    // Clean up alerts
    const alertCutoff = new Date(now.getTime() - this.config.retentionPolicy.alerts * 24 * 60 * 60 * 1000);
    this.alerts.forEach((alert, alertId) => {
      if (alert.timestamp < alertCutoff) {
        this.alerts.delete(alertId);
      }
    });
    
    // Clean up traces
    const traceCutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours
    this.traces.forEach((trace, traceId) => {
      if (trace.startTime < traceCutoff.getTime()) {
        this.traces.delete(traceId);
      }
    });
    
    apiLogger.debug('Cleanup completed', {
      metricsCount: this.metricsBuffer.length,
      alertsCount: this.alerts.size,
      tracesCount: this.traces.size
    });
  }

  private setupDefaultAlerts(): void {
    const defaultAlerts: Omit<AlertConfig, 'createdAt' | 'updatedAt' | 'triggerCount'>[] = [
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        description: 'Alert when error rate exceeds 10%',
        enabled: true,
        conditions: [{
          metric: 'error_rate',
          operator: 'gt',
          threshold: 0.1,
          windowMinutes: 5,
          minSamples: 10
        }],
        actions: [{
          type: 'log',
          config: {}
        }],
        cooldownMinutes: 15,
        maxAlertsPerHour: 4,
        severity: 'high'
      },
      {
        id: 'high_latency',
        name: 'High Latency',
        description: 'Alert when latency exceeds 10 seconds',
        enabled: true,
        conditions: [{
          metric: 'latency',
          operator: 'gt',
          threshold: 10000,
          windowMinutes: 5
        }],
        actions: [{
          type: 'log',
          config: {}
        }],
        cooldownMinutes: 10,
        maxAlertsPerHour: 6,
        severity: 'medium'
      }
    ];
    
    defaultAlerts.forEach(alert => this.addAlertConfig(alert));
  }

  private initializeIntegrations(): void {
    // Initialize external monitoring integrations
    if (this.config.integrations.datadog?.enabled) {
      this.initializeDatadog();
    }
    
    if (this.config.integrations.prometheus?.enabled) {
      this.initializePrometheus();
    }
    
    // Add other integrations...
  }

  private initializeDatadog(): void {
    // DataDog integration implementation
    apiLogger.info('DataDog integration initialized');
  }

  private initializePrometheus(): void {
    // Prometheus integration implementation
    apiLogger.info('Prometheus integration initialized');
  }

  private sendToIntegrations(metrics: RequestMetrics): void {
    // Send metrics to external systems
    if (this.config.integrations.webhook?.enabled) {
      // Send to webhook
    }
    
    // Add other integration sends...
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stop();
    this.removeAllListeners();
    
    // Clear all data
    this.metricsBuffer = [];
    this.aggregatedMetrics.clear();
    this.alerts.clear();
    this.alertConfigs.clear();
    this.traces.clear();
  }
}

// Singleton instance
let monitoringServiceInstance: MonitoringService | null = null;

export function getMonitoringService(
  config?: Partial<MonitoringConfig>,
  healthMonitor?: HealthMonitor
): MonitoringService {
  if (!monitoringServiceInstance && (config || healthMonitor)) {
    monitoringServiceInstance = new MonitoringService(config, healthMonitor);
  }
  
  if (!monitoringServiceInstance) {
    throw new Error('MonitoringService not initialized. Call with config or healthMonitor first.');
  }
  
  return monitoringServiceInstance;
}