import { AIProvider, GenerationOptions, StreamChunk, ModelInfo } from './providers/types';
import GoogleProvider from './providers/google';
import OpenAIProvider from './providers/openai';
import AnthropicProvider from './providers/anthropic';
import MistralProvider from './providers/mistral';
import DeepSeekProvider from './providers/deepseek';
import TogetherProvider from './providers/together';
import QwenProvider from './providers/qwen';
import XAIProvider from './providers/xai';
import CohereProvider from './providers/cohere';
import PerplexityProvider from './providers/perplexity';
import GroqProvider from './providers/groq';
import { apiLogger } from '@/lib/logger';
import { recoveryService } from './recovery-service';

interface FallbackProviderConfig {
  providers: AIProvider[];
  healthCheckInterval?: number;
  maxConsecutiveFailures?: number;
}

export class FallbackProvider extends AIProvider {
  public readonly providerName = 'FallbackProvider';
  private providers: AIProvider[];
  private providerHealth: Map<string, boolean> = new Map();
  private failureCounts: Map<string, number> = new Map();
  private lastHealthCheck: number = 0;
  private readonly healthCheckInterval: number;
  private readonly maxConsecutiveFailures: number;

  constructor(config: FallbackProviderConfig) {
    super({});
    this.providers = config.providers;
    this.healthCheckInterval = config.healthCheckInterval || 60000; // 1 minute
    this.maxConsecutiveFailures = config.maxConsecutiveFailures || 3;
    
    // Initialize all providers as healthy
    this.providers.forEach(provider => {
      this.providerHealth.set(provider.constructor.name, true);
      this.failureCounts.set(provider.constructor.name, 0);
    });
  }

  async generateCompletion(options: GenerationOptions): Promise<string> {
    await this.checkProvidersHealth();
    
    // Try the specific provider for this model first
    const modelToProvider = this.getProviderForModel(options.model);
    
    if (modelToProvider) {
      try {
        apiLogger.info(`Routing ${options.model} to ${modelToProvider.constructor.name}`);
        const result = await recoveryService.executeWithRetry(
          () => modelToProvider.generateCompletion(options),
          { maxRetries: 2, initialDelay: 500 }
        );
        
        // Reset failure count on success
        this.failureCounts.set(modelToProvider.constructor.name, 0);
        
        return result;
      } catch (error) {
        this.recordFailure(modelToProvider.constructor.name);
        apiLogger.warn(`Provider ${modelToProvider.constructor.name} failed for ${options.model}:`, error as Record<string, any>);
        // Fall through to general fallback logic
      }
    }
    
    // Fallback: try to map unsupported models
    const mappedModel = this.mapUnsupportedModel(options.model);
    if (mappedModel !== options.model) {
      apiLogger.info(`Model ${options.model} not available, using ${mappedModel} as fallback`);
      options = { ...options, model: mappedModel };
    }
    
    for (const provider of this.getHealthyProviders()) {
      try {
        const result = await recoveryService.executeWithRetry(
          () => provider.generateCompletion(options),
          { maxRetries: 2, initialDelay: 500 }
        );
        
        // Reset failure count on success
        this.failureCounts.set(provider.constructor.name, 0);
        
        return result;
      } catch (error) {
        apiLogger.error(`Provider ${provider.constructor.name} failed:`, error);
        this.recordFailure(provider.constructor.name);
        
        // Continue to next provider
        continue;
      }
    }
    
    throw new Error('All providers failed. Please try again later.');
  }

  async *generateStream(options: GenerationOptions): AsyncGenerator<StreamChunk> {
    await this.checkProvidersHealth();
    
    // Debug logging to trace routing
    apiLogger.info(`[FallbackProvider] generateStream called for model: ${options.model}`);
    apiLogger.info(`[FallbackProvider] Available providers: ${this.providers.map(p => p.providerName).join(', ')}`);
    
    // Determine which provider should handle this model
    const modelToProvider = this.getProviderForModel(options.model);
    
    if (modelToProvider) {
      try {
        apiLogger.info(`[FallbackProvider] Routing ${options.model} to ${modelToProvider.constructor.name}`);
        for await (const chunk of modelToProvider.generateStream(options)) {
          yield chunk;
        }
        return;
      } catch (error) {
        apiLogger.warn(`[FallbackProvider] Provider ${modelToProvider.constructor.name} failed for ${options.model}:`, error as Record<string, any>);
        // Fall through to general fallback logic
      }
    } else {
      apiLogger.warn(`[FallbackProvider] No specific provider found for ${options.model}, using fallback logic`);
    }
    
    // Fallback: try to map unsupported models to available ones
    const mappedModel = this.mapUnsupportedModel(options.model);
    if (mappedModel !== options.model) {
      apiLogger.info(`Model ${options.model} not available, using ${mappedModel} as fallback`);
      options = { ...options, model: mappedModel };
    }
    
    for (const provider of this.getHealthyProviders()) {
      try {
        yield* recoveryService.executeStreamWithRetry(
          () => provider.generateStream(options),
          { maxRetries: 2, initialDelay: 500 }
        );
        
        // Reset failure count on success
        this.failureCounts.set(provider.constructor.name, 0);
        
        return;
      } catch (error) {
        apiLogger.error(`Provider ${provider.constructor.name} stream failed:`, error);
        this.recordFailure(provider.constructor.name);
        
        // Continue to next provider
        continue;
      }
    }
    
    throw new Error('All providers failed to stream. Please try again later.');
  }

  async listModels(): Promise<ModelInfo[]> {
    const allModels: ModelInfo[] = [];
    
    for (const provider of this.getHealthyProviders()) {
      try {
        const models = await provider.listModels();
        allModels.push(...models);
      } catch (error) {
        apiLogger.error(`Failed to list models from ${provider.constructor.name}:`, error);
      }
    }
    
    return allModels;
  }

  async validateConfig(): Promise<boolean> {
    const results = await Promise.all(
      this.providers.map(provider => provider.validateConfig())
    );
    
    return results.some(result => result === true);
  }

  private async checkProvidersHealth(): Promise<void> {
    const now = Date.now();
    
    // Only check health if interval has passed
    if (now - this.lastHealthCheck < this.healthCheckInterval) {
      return;
    }
    
    this.lastHealthCheck = now;
    
    apiLogger.info('Checking provider health...');
    
    await Promise.all(
      this.providers.map(async provider => {
        const providerName = provider.constructor.name;
        const isHealthy = await recoveryService.checkProviderHealth(
          providerName,
          () => provider.validateConfig()
        );
        
        this.providerHealth.set(providerName, isHealthy);
        
        if (isHealthy) {
          // Reset failure count if provider is healthy
          this.failureCounts.set(providerName, 0);
        }
        
        apiLogger.info(`Provider ${providerName} health: ${isHealthy ? 'healthy' : 'unhealthy'}`);
      })
    );
  }

  private getHealthyProviders(): AIProvider[] {
    return this.providers.filter(provider => {
      const providerName = provider.constructor.name;
      const isHealthy = this.providerHealth.get(providerName) ?? true;
      const failureCount = this.failureCounts.get(providerName) ?? 0;
      
      return isHealthy && failureCount < this.maxConsecutiveFailures;
    });
  }

  private recordFailure(providerName: string): void {
    const currentCount = this.failureCounts.get(providerName) ?? 0;
    const newCount = currentCount + 1;
    
    this.failureCounts.set(providerName, newCount);
    
    if (newCount >= this.maxConsecutiveFailures) {
      apiLogger.warn(`Provider ${providerName} marked as unhealthy after ${newCount} consecutive failures`);
      this.providerHealth.set(providerName, false);
    }
  }

  private getProviderForModel(modelId: string): AIProvider | null {
    apiLogger.info(`[FallbackProvider] getProviderForModel called for: ${modelId}`);
    
    // Map models to their correct providers using providerName instead of constructor.name
    if (modelId.startsWith('gpt-') || modelId.startsWith('o1-') || modelId.includes('chatgpt')) {
      const provider = this.providers.find(p => p.providerName === 'OpenAIProvider' || p.providerName === 'OpenAIEnhancedProvider');
      apiLogger.info(`[FallbackProvider] OpenAI model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('claude-') || modelId.includes('anthropic')) {
      const provider = this.providers.find(p => p.providerName === 'AnthropicProvider');
      apiLogger.info(`[FallbackProvider] Anthropic model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('gemini-') || modelId.startsWith('gemma-') || modelId.includes('google')) {
      const provider = this.providers.find(p => p.providerName === 'GoogleProvider');
      apiLogger.info(`[FallbackProvider] Google model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('grok-') || modelId.includes('xai')) {
      const provider = this.providers.find(p => p.providerName === 'XAIProvider');
      apiLogger.info(`[FallbackProvider] xAI/Grok model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('deepseek-') || modelId.includes('deepseek')) {
      const provider = this.providers.find(p => p.providerName === 'DeepSeekProvider');
      apiLogger.info(`[FallbackProvider] DeepSeek model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('mistral-') || modelId.startsWith('mixtral-') || modelId.includes('mistral')) {
      const provider = this.providers.find(p => p.providerName === 'MistralProvider');
      apiLogger.info(`[FallbackProvider] Mistral model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('llama-') || modelId.includes('meta')) {
      const provider = this.providers.find(p => p.providerName === 'TogetherProvider');
      apiLogger.info(`[FallbackProvider] Llama/Meta model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('qwen-') || modelId.includes('alibaba')) {
      const provider = this.providers.find(p => p.providerName === 'QwenProvider' || p.providerName === 'qwen');
      apiLogger.info(`[FallbackProvider] Qwen/Alibaba model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.includes('command-') || modelId.includes('cohere')) {
      const provider = this.providers.find(p => p.providerName === 'CohereProvider');
      apiLogger.info(`[FallbackProvider] Cohere model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.includes('sonar') || modelId.includes('perplexity')) {
      const provider = this.providers.find(p => p.providerName === 'PerplexityProvider');
      apiLogger.info(`[FallbackProvider] Perplexity model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('nova-')) {
      const provider = this.providers.find(p => p.providerName === 'AmazonNovaProvider');
      apiLogger.info(`[FallbackProvider] Amazon Nova model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.includes('groq') || 
        (modelId.includes('llama') && modelId.includes('versatile')) ||
        (modelId.includes('llama') && modelId.includes('instant')) ||
        modelId === 'mixtral-8x7b-32768' ||
        modelId === 'gemma2-9b-it') {
      const provider = this.providers.find(p => p.providerName === 'GroqProvider');
      apiLogger.info(`[FallbackProvider] Groq hardware-accelerated model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.includes('ministral') || modelId.includes('codestral')) {
      const provider = this.providers.find(p => p.providerName === 'MistralProvider');
      apiLogger.info(`[FallbackProvider] Mistral specialized model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.startsWith('Qwen/') || modelId.includes('qwq')) {
      const provider = this.providers.find(p => p.providerName === 'QwenProvider');
      apiLogger.info(`[FallbackProvider] Qwen model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.includes('o3-') || modelId.includes('o4-')) {
      const provider = this.providers.find(p => p.providerName === 'OpenAIProvider');
      apiLogger.info(`[FallbackProvider] OpenAI O-series model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    if (modelId.includes('dall-e') || modelId.includes('whisper') || modelId.includes('tts-') || modelId.includes('text-embedding') || modelId.includes('text-moderation')) {
      const provider = this.providers.find(p => p.providerName === 'OpenAIProvider');
      apiLogger.info(`[FallbackProvider] OpenAI specialized model detected, provider found: ${provider ? provider.providerName : 'none'}`);
      return provider || null;
    }
    
    // Check for OpenRouter models (they often have prefixes like openrouter/)
    if (modelId.includes('openrouter/') || modelId.includes('/')) {
      // Try to find the appropriate OpenRouter-based provider
      const modelLower = modelId.toLowerCase();
      
      if (modelLower.includes('deepseek')) {
        const provider = this.providers.find(p => p.providerName === 'DeepSeekProvider' && p.constructor.name.includes('OpenRouter'));
        if (provider) {
          apiLogger.info(`[FallbackProvider] OpenRouter DeepSeek model detected, provider found: ${provider.providerName}`);
          return provider;
        }
      }
      
      if (modelLower.includes('perplexity') || modelLower.includes('sonar')) {
        const provider = this.providers.find(p => p.providerName === 'PerplexityProvider' && p.constructor.name.includes('OpenRouter'));
        if (provider) {
          apiLogger.info(`[FallbackProvider] OpenRouter Perplexity model detected, provider found: ${provider.providerName}`);
          return provider;
        }
      }
      
      if (modelLower.includes('qwen') || modelLower.includes('qwq')) {
        const provider = this.providers.find(p => p.providerName === 'QwenProvider' && p.constructor.name.includes('OpenRouter'));
        if (provider) {
          apiLogger.info(`[FallbackProvider] OpenRouter Qwen model detected, provider found: ${provider.providerName}`);
          return provider;
        }
      }
      
      if (modelLower.includes('llama') || modelLower.includes('mixtral')) {
        const provider = this.providers.find(p => p.providerName === 'GroqProvider' && p.constructor.name.includes('OpenRouter'));
        if (provider) {
          apiLogger.info(`[FallbackProvider] OpenRouter Llama/Mixtral model detected, provider found: ${provider.providerName}`);
          return provider;
        }
      }
      
      // Fallback to base OpenRouter provider
      const provider = this.providers.find(p => p.providerName === 'OpenRouterProvider');
      if (provider) {
        apiLogger.info(`[FallbackProvider] Generic OpenRouter model detected, using base OpenRouter provider`);
        return provider;
      }
    }
    
    // Default to Google provider for unrecognized models (most universal)
    apiLogger.warn(`[FallbackProvider] No specific provider mapping for ${modelId}, defaulting to GoogleProvider`);
    return this.providers.find(p => p.providerName === 'GoogleProvider') || null;
  }

  private mapUnsupportedModel(modelId: string): string {
    // Only map truly unsupported models, prefer keeping original model names
    const modelMappings: Record<string, string> = {
      // Fix common model name variations
      'gemini-2.0-flash': 'gemini-2.0-flash-exp',
      'gemini-2.5-flash-preview': 'gemini-2.0-flash-exp',
      'gemini-2.5-pro-preview': 'gemini-1.5-pro',
      'gemini-2.5-flash-preview-05-20': 'gemini-2.0-flash-exp',
      
      // Map deprecated DeepSeek models
      'deepseek-v3-0324': 'deepseek-v3',
      
      // Ultimate fallbacks for completely unknown models
      'unknown-model': 'gemini-2.0-flash-exp',
    };
    
    return modelMappings[modelId] || modelId;
  }
  
  private applySpecialParameters(options: GenerationOptions): GenerationOptions {
    const enhancedOptions = { ...options };
    
    // Apply special parameters based on model
    switch (options.model) {
      // O-series models support reasoning_effort
      case 'o3-pro':
      case 'o3':
      case 'o3-mini':
      case 'o4-mini':
      case 'o1':
      case 'o1-mini':
        if (!enhancedOptions.metadata) enhancedOptions.metadata = {};
        enhancedOptions.metadata.reasoning_effort = options.metadata?.reasoning_effort || 'medium';
        break;
        
      // Gemini 2.5 models support thinking mode
      case 'gemini-2.5-pro-deep-think':
        if (!enhancedOptions.metadata) enhancedOptions.metadata = {};
        enhancedOptions.metadata['tools.think'] = 'on';
        break;
        
      case 'gemini-2.5-pro-preview':
      case 'gemini-2.5-flash-preview-05-20':
      case 'gemini-2.5-flash-preview':
        if (!enhancedOptions.metadata) enhancedOptions.metadata = {};
        enhancedOptions.metadata['tools.think'] = options.metadata?.['tools.think'] || 'off';
        break;
        
      // Claude models with special thinking modes
      case 'claude-opus-4':
        if (!enhancedOptions.metadata) enhancedOptions.metadata = {};
        enhancedOptions.metadata.extended_thinking = options.metadata?.extended_thinking || true;
        break;
        
      case 'claude-3.7-sonnet':
        if (!enhancedOptions.metadata) enhancedOptions.metadata = {};
        enhancedOptions.metadata.thinking_budget = options.metadata?.thinking_budget || 50;
        break;
        
      // Qwen models with think_mode
      case 'qwq-32b':
      case 'qwen-3-8b':
        if (!enhancedOptions.metadata) enhancedOptions.metadata = {};
        enhancedOptions.metadata.think_mode = true;
        break;
    }
    
    return enhancedOptions;
  }
}

// Create a fallback provider with all available providers
export const createFallbackProvider = (): FallbackProvider => {
  const providers: AIProvider[] = [];
  
  // Add providers in order of preference/reliability
  try {
    providers.push(new GoogleProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize Google provider:', error as Record<string, any>);
  }
  
  try {
    providers.push(new OpenAIProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize OpenAI provider:', error as Record<string, any>);
  }
  
  try {
    providers.push(new AnthropicProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize Anthropic provider:', error as Record<string, any>);
  }
  
  try {
    providers.push(new DeepSeekProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize DeepSeek provider:', error as Record<string, any>);
  }
  
  // Add xAI provider if API key exists
  if (process.env.XAI_API_KEY) {
    try {
      providers.push(new XAIProvider());
      apiLogger.info('✅ XAIProvider initialized successfully');
    } catch (error) {
      apiLogger.warn('Failed to initialize xAI provider:', error as Record<string, any>);
    }
  } else {
    apiLogger.warn('XAI_API_KEY not set, Grok models unavailable');
  }
  
  try {
    providers.push(new TogetherProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize Together provider:', error as Record<string, any>);
  }
  
  try {
    providers.push(new QwenProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize Qwen/Alibaba provider:', error as Record<string, any>);
  }
  
  try {
    providers.push(new MistralProvider());
  } catch (error) {
    apiLogger.warn('Failed to initialize Mistral provider:', error as Record<string, any>);
  }
  
  // OpenAI Enhanced provider is not available in the new AI SDK structure
  // Regular OpenAI provider handles all OpenAI models
  
  // Add Cohere provider if API key exists
  if (process.env.COHERE_API_KEY) {
    try {
      providers.push(new CohereProvider({ apiKey: process.env.COHERE_API_KEY }));
      apiLogger.info('✅ CohereProvider initialized successfully');
    } catch (error) {
      apiLogger.warn('Failed to initialize Cohere provider:', error as Record<string, any>);
    }
  } else {
    apiLogger.warn('COHERE_API_KEY not set, Cohere models unavailable');
  }
  
  // Add Perplexity provider if API key exists
  if (process.env.PERPLEXITY_API_KEY) {
    try {
      providers.push(new PerplexityProvider({ apiKey: process.env.PERPLEXITY_API_KEY }));
      apiLogger.info('✅ PerplexityProvider initialized successfully');
    } catch (error) {
      apiLogger.warn('Failed to initialize Perplexity provider:', error as Record<string, any>);
    }
  } else {
    apiLogger.warn('PERPLEXITY_API_KEY not set, Perplexity models unavailable');
  }
  
  // Add Groq provider if API key exists
  if (process.env.GROQ_API_KEY) {
    try {
      providers.push(new GroqProvider());
      apiLogger.info('✅ GroqProvider initialized successfully');
    } catch (error) {
      apiLogger.warn('Failed to initialize Groq provider:', error as Record<string, any>);
    }
  } else {
    apiLogger.warn('GROQ_API_KEY not set, Groq hardware-accelerated models unavailable');
  }
  
  // Amazon Nova provider is not available in the new AI SDK structure
  // Nova models are accessed through Amazon Bedrock
  
  // Add OpenRouter-based providers if API key exists
  if (process.env.OPENROUTER_API_KEY) {
    try {
      const { 
        DeepSeekProvider: OpenRouterDeepSeek, 
        GroqProvider: OpenRouterGroq,
        PerplexityProvider: OpenRouterPerplexity, 
        QwenProvider: OpenRouterQwen 
      } = require('./providers/openrouter');
      
      providers.push(new OpenRouterDeepSeek());
      providers.push(new OpenRouterGroq());
      providers.push(new OpenRouterPerplexity());
      providers.push(new OpenRouterQwen());
      
      apiLogger.info('✅ OpenRouter-based providers initialized successfully');
    } catch (error) {
      apiLogger.warn('Failed to initialize OpenRouter providers:', error as Record<string, any>);
    }
  }
  
  if (providers.length === 0) {
    throw new Error('No AI providers could be initialized. Please check your API keys.');
  }
  
  apiLogger.info(`[FallbackProvider] Initialized with ${providers.length} providers: ${providers.map(p => p.constructor.name).join(', ')}`);
  
  return new FallbackProvider({
    providers,
    healthCheckInterval: 30000, // 30 seconds
    maxConsecutiveFailures: 3
  });
};