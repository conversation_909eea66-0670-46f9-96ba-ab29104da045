/**
 * Circuit Breaker Implementation
 * 
 * @description
 * Circuit breaker pattern implementation for provider resilience.
 * Prevents cascading failures by temporarily stopping requests to failing services.
 * 
 * @module lib/ai/circuit-breaker
 */

import { apiLogger } from '@/lib/logger';

/**
 * Circuit breaker states
 */
export type CircuitBreakerState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

/**
 * Circuit breaker configuration
 */
export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
}

/**
 * Circuit breaker state information
 */
export interface CircuitBreakerStateInfo {
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  lastFailureTime: number;
  nextAttemptTime: number;
}

/**
 * Circuit breaker error
 */
export class CircuitBreakerError extends Error {
  constructor(
    message: string,
    public state: CircuitBreakerState,
    public nextAttemptTime: number
  ) {
    super(message);
    this.name = 'CircuitBreakerError';
  }
}

/**
 * Circuit Breaker implementation
 */
export class CircuitBreaker {
  private states = new Map<string, CircuitBreakerStateInfo>();
  private config: CircuitBreakerConfig;

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 10000, // 10 seconds
      halfOpenMaxCalls: 3,
      ...config
    };
  }

  /**
   * Execute operation with circuit breaker protection
   */
  async execute<T>(
    key: string,
    operation: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>
  ): Promise<T> {
    const effectiveConfig = { ...this.config, ...config };
    const state = this.getOrCreateState(key);

    // Check if circuit is open
    if (state.state === 'OPEN') {
      if (Date.now() < state.nextAttemptTime) {
        throw new CircuitBreakerError(
          'Circuit breaker is OPEN',
          'OPEN',
          state.nextAttemptTime
        );
      } else {
        // Transition to half-open
        state.state = 'HALF_OPEN';
        state.successCount = 0;
        apiLogger.info(`Circuit breaker ${key} transitioning to HALF_OPEN`);
      }
    }

    try {
      const result = await operation();
      this.onSuccess(key, effectiveConfig);
      return result;
    } catch (error) {
      this.onFailure(key, effectiveConfig);
      throw error;
    }
  }

  /**
   * Get state for a specific key
   */
  getState(key: string): CircuitBreakerStateInfo {
    return this.getOrCreateState(key);
  }

  /**
   * Get all states
   */
  getAllStates(): Map<string, CircuitBreakerStateInfo> {
    return new Map(this.states);
  }

  /**
   * Reset circuit breaker for a specific key
   */
  reset(key: string): void {
    const state = this.states.get(key);
    if (state) {
      state.state = 'CLOSED';
      state.failureCount = 0;
      state.successCount = 0;
      state.lastFailureTime = 0;
      state.nextAttemptTime = 0;
      
      apiLogger.info(`Circuit breaker ${key} reset to CLOSED`);
    }
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    this.states.forEach((_, key) => this.reset(key));
  }

  /**
   * Get metrics for monitoring
   */
  getMetrics(): Map<string, CircuitBreakerStateInfo> {
    return this.getAllStates();
  }

  private getOrCreateState(key: string): CircuitBreakerStateInfo {
    let state = this.states.get(key);
    if (!state) {
      state = {
        state: 'CLOSED',
        failureCount: 0,
        successCount: 0,
        lastFailureTime: 0,
        nextAttemptTime: 0
      };
      this.states.set(key, state);
    }
    return state;
  }

  private onSuccess(key: string, config: CircuitBreakerConfig): void {
    const state = this.getOrCreateState(key);
    state.successCount++;

    if (state.state === 'HALF_OPEN') {
      if (state.successCount >= config.halfOpenMaxCalls) {
        state.state = 'CLOSED';
        state.failureCount = 0;
        apiLogger.info(`Circuit breaker ${key} transitioning to CLOSED`);
      }
    } else if (state.state === 'CLOSED') {
      // Reset failure count on success
      state.failureCount = 0;
    }
  }

  private onFailure(key: string, config: CircuitBreakerConfig): void {
    const state = this.getOrCreateState(key);
    state.failureCount++;
    state.lastFailureTime = Date.now();

    if (state.state === 'CLOSED' && state.failureCount >= config.failureThreshold) {
      state.state = 'OPEN';
      state.nextAttemptTime = Date.now() + config.recoveryTimeout;
      apiLogger.warn(`Circuit breaker ${key} transitioning to OPEN`, {
        failureCount: state.failureCount,
        threshold: config.failureThreshold
      });
    } else if (state.state === 'HALF_OPEN') {
      state.state = 'OPEN';
      state.nextAttemptTime = Date.now() + config.recoveryTimeout;
      apiLogger.warn(`Circuit breaker ${key} returning to OPEN from HALF_OPEN`);
    }
  }
}