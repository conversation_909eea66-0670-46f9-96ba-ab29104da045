/**
 * MCP-Enhanced Chat Handler
 * Integrates image generation tools with LLMs via function calling
 */

import { GoogleGenerativeAI, SchemaType } from '@google/generative-ai';
import { getMCPImageToolsForOpenAI, executeMCPImageTool, isImageTool } from './mcp-image-tools';
import { apiLogger } from '@/lib/logger';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || '');

export interface MCPChatOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  enableImageTools?: boolean;
  userId?: string;
}

export interface MCPChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  toolCalls?: Array<{
    id: string;
    type: 'function';
    function: {
      name: string;
      arguments: string;
    };
  }>;
  toolCallId?: string;
  name?: string;
}

export interface MCPChatResponse {
  message: string;
  toolCalls?: Array<{
    id: string;
    name: string;
    arguments: Record<string, any>;
    result?: any;
  }>;
  images?: Array<{
    type: 'url' | 'base64';
    url?: string;
    data?: string;
  }>;
  usage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  model: string;
}

/**
 * Enhanced chat with MCP image tools support
 */
export async function chatWithMCPTools(
  messages: MCPChatMessage[],
  options: MCPChatOptions = {}
): Promise<MCPChatResponse> {
  const {
    model = 'gemini-2.5-pro',
    temperature = 0.7,
    maxTokens = 4096,
    enableImageTools = true,
    userId
  } = options;

  try {
    apiLogger.info('Starting MCP-enhanced chat', {
      model,
      messageCount: messages.length,
      enableImageTools,
      userId
    });

    // For Gemini models, use function calling
    if (model.includes('gemini')) {
      return await handleGeminiWithTools(messages, {
        model,
        temperature,
        maxTokens,
        enableImageTools,
        userId
      });
    }

    // For OpenAI models, use their function calling
    if (model.includes('gpt') || model.includes('o1') || model.includes('o3')) {
      return await handleOpenAIWithTools(messages, {
        model,
        temperature,
        maxTokens,
        enableImageTools,
        userId
      });
    }

    // Fallback: regular chat without tools
    return await handleRegularChat(messages, options);

  } catch (error) {
    apiLogger.error('MCP chat failed', {
      model,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle Gemini models with function calling
 */
async function handleGeminiWithTools(
  messages: MCPChatMessage[],
  options: MCPChatOptions
): Promise<MCPChatResponse> {
  const { model = 'gemini-2.5-pro', enableImageTools, userId } = options;

  const geminiModel = genAI.getGenerativeModel({ 
    model: model.replace('gemini/', '').replace('google/', ''),
    generationConfig: {
      temperature: options.temperature,
      maxOutputTokens: options.maxTokens,
    }
  });

  // Convert messages to Gemini format
  const geminiMessages = messages.map(msg => ({
    role: msg.role === 'assistant' ? 'model' : 'user',
    parts: [{ text: msg.content }]
  }));

  // Define tools if image tools are enabled
  const tools = enableImageTools ? [{
    functionDeclarations: getMCPImageToolsForOpenAI().map(tool => ({
      name: tool.function.name,
      description: tool.function.description,
      parameters: {
        ...tool.function.parameters,
        type: SchemaType.OBJECT
      }
    }))
  }] : undefined;

  // Start chat with tools
  const chat = geminiModel.startChat({
    history: geminiMessages.slice(0, -1),
    tools
  });

  // Send the latest message
  const latestMessage = geminiMessages[geminiMessages.length - 1];
  const result = await chat.sendMessage(latestMessage.parts[0].text);

  let response = result.response;
  let finalText = response.text();
  const toolCalls: any[] = [];
  const images: any[] = [];

  // Handle function calls
  const functionCalls = response.functionCalls();
  if (functionCalls && functionCalls.length > 0) {
    apiLogger.info('Processing function calls', {
      count: functionCalls.length,
      functions: functionCalls.map(call => call.name)
    });

    for (const call of functionCalls) {
      if (isImageTool(call.name)) {
        try {
          const toolResult = await executeMCPImageTool({
            name: call.name,
            arguments: call.args
          }, userId);

          toolCalls.push({
            id: `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            name: call.name,
            arguments: call.args,
            result: toolResult
          });

          if (toolResult.success && toolResult.data?.images) {
            images.push(...toolResult.data.images);
            finalText += `\n\n🎨 I've generated ${toolResult.data.images.length} image(s) for you using ${toolResult.data.model}.`;
          }
        } catch (error) {
          apiLogger.error('Tool execution failed', {
            toolName: call.name,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }
  }

  return {
    message: finalText,
    toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
    images: images.length > 0 ? images : undefined,
    usage: {
      inputTokens: result.response.usageMetadata?.promptTokenCount || 0,
      outputTokens: result.response.usageMetadata?.candidatesTokenCount || 0,
      totalTokens: result.response.usageMetadata?.totalTokenCount || 0
    },
    model
  };
}

/**
 * Handle OpenAI models with function calling
 */
async function handleOpenAIWithTools(
  messages: MCPChatMessage[],
  options: MCPChatOptions
): Promise<MCPChatResponse> {
  // Import OpenAI dynamically
  const OpenAI = (await import('openai')).default;
  
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.LITELLM_BASE_URL || undefined
  });

  const { model = 'gpt-4o', enableImageTools, userId } = options;

  // Convert messages to OpenAI format
  const openaiMessages = messages.map(msg => ({
    role: msg.role as any,
    content: msg.content,
    tool_calls: msg.toolCalls,
    tool_call_id: msg.toolCallId,
    name: msg.name
  }));

  // Prepare tools
  const tools = enableImageTools ? getMCPImageToolsForOpenAI() : undefined;

  const completion = await openai.chat.completions.create({
    model,
    messages: openaiMessages,
    tools,
    tool_choice: 'auto',
    temperature: options.temperature,
    max_tokens: options.maxTokens
  });

  const responseMessage = completion.choices[0].message;
  let finalText = responseMessage.content || '';
  const toolCalls: any[] = [];
  const images: any[] = [];

  // Handle tool calls
  if (responseMessage.tool_calls) {
    for (const toolCall of responseMessage.tool_calls) {
      if (isImageTool(toolCall.function.name)) {
        try {
          const args = JSON.parse(toolCall.function.arguments);
          const toolResult = await executeMCPImageTool({
            name: toolCall.function.name,
            arguments: args
          }, userId);

          toolCalls.push({
            id: toolCall.id,
            name: toolCall.function.name,
            arguments: args,
            result: toolResult
          });

          if (toolResult.success && toolResult.data?.images) {
            images.push(...toolResult.data.images);
            finalText += `\n\n🎨 I've generated ${toolResult.data.images.length} image(s) for you using ${toolResult.data.model}.`;
          }
        } catch (error) {
          apiLogger.error('Tool execution failed', {
            toolName: toolCall.function.name,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }
  }

  return {
    message: finalText,
    toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
    images: images.length > 0 ? images : undefined,
    usage: {
      inputTokens: completion.usage?.prompt_tokens || 0,
      outputTokens: completion.usage?.completion_tokens || 0,
      totalTokens: completion.usage?.total_tokens || 0
    },
    model
  };
}

/**
 * Fallback regular chat without tools
 */
async function handleRegularChat(
  messages: MCPChatMessage[],
  options: MCPChatOptions
): Promise<MCPChatResponse> {
  // Basic implementation for models without function calling
  const lastMessage = messages[messages.length - 1];
  
  return {
    message: "I can help with text responses, but this model doesn't support image generation tools. Try using Gemini 2.5 Pro or GPT-4 for image generation capabilities.",
    usage: {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0
    },
    model: options.model || 'unknown'
  };
}

/**
 * Utility function to check if a model supports MCP tools
 */
export function modelSupportsMCPTools(modelId: string): boolean {
  return modelId.includes('gemini-2.5') || 
         modelId.includes('gpt-4') || 
         modelId.includes('o1') || 
         modelId.includes('o3');
}