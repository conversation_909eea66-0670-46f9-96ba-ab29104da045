/**
 * Cost Optimization and Quality-Based Routing Algorithms
 * 
 * @description
 * Smart routing system that optimizes provider selection based on cost,
 * quality, and performance metrics. Uses real data to make intelligent
 * routing decisions.
 * 
 * @module lib/ai/routing-optimizer
 */

import { apiLogger } from '@/lib/logger';
import { HealthMonitor, HealthMetrics } from './health-monitor';
import { GenerationOptions, ModelInfo } from './providers/types';

/**
 * Provider cost information
 */
export interface ProviderCost {
  provider: string;
  model: string;
  inputCostPer1K: number;  // USD per 1K input tokens
  outputCostPer1K: number; // USD per 1K output tokens
  lastUpdated: Date;
}

/**
 * Quality metrics for a provider/model combination
 */
export interface QualityMetrics {
  provider: string;
  model: string;
  
  // Performance metrics
  averageLatency: number;
  p95Latency: number;
  successRate: number;
  
  // Quality scores (0-1)
  responseQuality: number;    // Based on user feedback/ratings
  accuracyScore: number;      // Task-specific accuracy
  coherenceScore: number;     // Response coherence
  safetyScore: number;        // Content safety score
  
  // Aggregate quality score
  overallQuality: number;
  
  // Sample size and confidence
  totalSamples: number;
  confidenceLevel: number;
  
  lastUpdated: Date;
}

/**
 * Routing decision weights
 */
export interface RoutingWeights {
  cost: number;      // 0-1, weight for cost considerations
  quality: number;   // 0-1, weight for quality metrics  
  speed: number;     // 0-1, weight for latency/speed
  reliability: number; // 0-1, weight for success rate/uptime
}

/**
 * Routing context with user preferences
 */
export interface RoutingContext {
  // Request details
  estimatedInputTokens: number;
  estimatedOutputTokens: number;
  maxLatencyMs?: number;
  
  // User preferences
  userTier: 'free' | 'premium' | 'enterprise';
  budgetConstraints?: {
    maxCostPerRequest?: number;
    maxCostPerToken?: number;
  };
  qualityRequirements?: {
    minQualityScore?: number;
    minSuccessRate?: number;
  };
  
  // Task characteristics  
  taskType?: 'chat' | 'completion' | 'analysis' | 'creative' | 'code';
  criticalityLevel?: 'low' | 'medium' | 'high' | 'critical';
  
  // Context
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

/**
 * Routing decision with scoring breakdown
 */
export interface RoutingDecision {
  selectedProvider: string;
  selectedModel: string;
  
  // Scoring breakdown
  scores: {
    cost: number;
    quality: number;
    speed: number;
    reliability: number;
    overall: number;
  };
  
  // Estimates
  estimatedCost: number;
  estimatedLatency: number;
  estimatedQuality: number;
  
  // Alternative options
  alternatives: Array<{
    provider: string;
    model: string;
    score: number;
    costDelta: number;
    qualityDelta: number;
  }>;
  
  // Decision reasoning
  reasoning: string[];
  optimizationStrategy: 'cost' | 'quality' | 'speed' | 'balanced';
}

/**
 * Provider performance tracking
 */
interface ProviderPerformance {
  provider: string;
  model: string;
  
  // Recent performance (rolling window)
  recentLatency: number[];
  recentSuccessRate: number;
  recentCosts: number[];
  recentQualityScores: number[];
  
  // Trends
  latencyTrend: 'improving' | 'stable' | 'degrading';
  costTrend: 'improving' | 'stable' | 'degrading';
  qualityTrend: 'improving' | 'stable' | 'degrading';
  
  lastUpdated: Date;
}

/**
 * Main Routing Optimizer
 */
export class RoutingOptimizer {
  private healthMonitor?: HealthMonitor;
  private providerCosts: Map<string, ProviderCost> = new Map();
  private qualityMetrics: Map<string, QualityMetrics> = new Map();
  private performance: Map<string, ProviderPerformance> = new Map();
  
  // Default routing weights for different user tiers
  private defaultWeights: { [tier: string]: RoutingWeights } = {
    free: {
      cost: 0.7,      // Free users prioritize cost
      quality: 0.15,
      speed: 0.1,
      reliability: 0.05
    },
    premium: {
      cost: 0.3,      // Premium users balance cost/quality
      quality: 0.4,
      speed: 0.2,
      reliability: 0.1
    },
    enterprise: {
      cost: 0.1,      // Enterprise prioritizes quality/reliability
      quality: 0.4,
      speed: 0.2,
      reliability: 0.3
    }
  };

  constructor(healthMonitor?: HealthMonitor) {
    this.healthMonitor = healthMonitor;
    this.loadDefaultCosts();
    this.loadDefaultQualityMetrics();
  }

  /**
   * Make an optimized routing decision
   */
  async optimizeRouting(
    availableProviders: Array<{ provider: string; model: string }>,
    context: RoutingContext,
    customWeights?: Partial<RoutingWeights>
  ): Promise<RoutingDecision> {
    const weights = this.getRoutingWeights(context.userTier, customWeights);
    const reasoning: string[] = [];
    
    // Score each provider option
    const scoredOptions = await Promise.all(
      availableProviders.map(async option => {
        const scores = await this.scoreProvider(option, context, weights);
        return { ...option, ...scores };
      })
    );
    
    // Filter out options that don't meet requirements
    const viableOptions = this.filterViableOptions(scoredOptions, context);
    reasoning.push(`Filtered to ${viableOptions.length} viable options from ${scoredOptions.length} total`);
    
    if (viableOptions.length === 0) {
      throw new Error('No viable provider options meet the specified requirements');
    }
    
    // Sort by overall score
    viableOptions.sort((a, b) => b.overallScore - a.overallScore);
    
    const selected = viableOptions[0];
    const alternatives = viableOptions.slice(1, 4).map(alt => ({
      provider: alt.provider,
      model: alt.model,
      score: alt.overallScore,
      costDelta: alt.estimatedCost - selected.estimatedCost,
      qualityDelta: alt.qualityScore - selected.qualityScore
    }));
    
    // Determine optimization strategy
    const strategy = this.determineOptimizationStrategy(weights);
    reasoning.push(`Selected ${selected.provider}/${selected.model} using ${strategy} strategy`);
    reasoning.push(`Score breakdown: Cost=${selected.costScore.toFixed(2)}, Quality=${selected.qualityScore.toFixed(2)}, Speed=${selected.speedScore.toFixed(2)}, Reliability=${selected.reliabilityScore.toFixed(2)}`);
    
    return {
      selectedProvider: selected.provider,
      selectedModel: selected.model,
      scores: {
        cost: selected.costScore,
        quality: selected.qualityScore,
        speed: selected.speedScore,
        reliability: selected.reliabilityScore,
        overall: selected.overallScore
      },
      estimatedCost: selected.estimatedCost,
      estimatedLatency: selected.estimatedLatency,
      estimatedQuality: selected.estimatedQuality,
      alternatives,
      reasoning,
      optimizationStrategy: strategy
    };
  }

  /**
   * Update cost information for a provider/model
   */
  updateProviderCost(cost: ProviderCost): void {
    const key = `${cost.provider}:${cost.model}`;
    this.providerCosts.set(key, cost);
    
    apiLogger.debug('Provider cost updated', {
      provider: cost.provider,
      model: cost.model,
      inputCost: cost.inputCostPer1K,
      outputCost: cost.outputCostPer1K
    });
  }

  /**
   * Update quality metrics for a provider/model
   */
  updateQualityMetrics(metrics: QualityMetrics): void {
    const key = `${metrics.provider}:${metrics.model}`;
    this.qualityMetrics.set(key, metrics);
    
    apiLogger.debug('Quality metrics updated', {
      provider: metrics.provider,
      model: metrics.model,
      overallQuality: metrics.overallQuality,
      samples: metrics.totalSamples
    });
  }

  /**
   * Record performance data from a completed request
   */
  recordPerformance(
    provider: string,
    model: string,
    latency: number,
    success: boolean,
    cost: number,
    qualityScore?: number
  ): void {
    const key = `${provider}:${model}`;
    let perf = this.performance.get(key);
    
    if (!perf) {
      perf = {
        provider,
        model,
        recentLatency: [],
        recentSuccessRate: 1,
        recentCosts: [],
        recentQualityScores: [],
        latencyTrend: 'stable',
        costTrend: 'stable',
        qualityTrend: 'stable',
        lastUpdated: new Date()
      };
    }
    
    // Update rolling windows (keep last 100 samples)
    perf.recentLatency.push(latency);
    if (perf.recentLatency.length > 100) {
      perf.recentLatency.shift();
    }
    
    perf.recentCosts.push(cost);
    if (perf.recentCosts.length > 100) {
      perf.recentCosts.shift();
    }
    
    if (qualityScore !== undefined) {
      perf.recentQualityScores.push(qualityScore);
      if (perf.recentQualityScores.length > 100) {
        perf.recentQualityScores.shift();
      }
    }
    
    // Update trends
    perf.latencyTrend = this.calculateTrend(perf.recentLatency);
    perf.costTrend = this.calculateTrend(perf.recentCosts);
    if (perf.recentQualityScores.length > 0) {
      perf.qualityTrend = this.calculateTrend(perf.recentQualityScores);
    }
    
    perf.lastUpdated = new Date();
    this.performance.set(key, perf);
  }

  /**
   * Get cost optimization recommendations
   */
  getCostOptimizationRecommendations(
    currentProvider: string,
    currentModel: string,
    context: RoutingContext
  ): Array<{
    provider: string;
    model: string;
    costSavings: number;
    qualityDelta: number;
    recommendation: string;
  }> {
    const currentKey = `${currentProvider}:${currentModel}`;
    const currentCost = this.estimateCost(currentProvider, currentModel, context);
    const currentQuality = this.getQualityScore(currentProvider, currentModel);
    
    const recommendations: Array<{
      provider: string;
      model: string;
      costSavings: number;
      qualityDelta: number;
      recommendation: string;
    }> = [];
    
    // Check all alternatives
    this.providerCosts.forEach((cost, key) => {
      if (key === currentKey) return;
      
      const [provider, model] = key.split(':');
      const altCost = this.estimateCost(provider, model, context);
      const altQuality = this.getQualityScore(provider, model);
      
      const costSavings = currentCost - altCost;
      const qualityDelta = altQuality - currentQuality;
      
      if (costSavings > 0) {
        let recommendation = '';
        
        if (qualityDelta >= -0.05) {
          recommendation = 'Excellent alternative - lower cost with similar quality';
        } else if (qualityDelta >= -0.15) {
          recommendation = 'Good alternative - significant cost savings with minor quality reduction';
        } else {
          recommendation = 'Cost savings available but with noticeable quality reduction';
        }
        
        recommendations.push({
          provider,
          model,
          costSavings,
          qualityDelta,
          recommendation
        });
      }
    });
    
    return recommendations.sort((a, b) => b.costSavings - a.costSavings);
  }

  /**
   * Private helper methods
   */
  
  private async scoreProvider(
    option: { provider: string; model: string },
    context: RoutingContext,
    weights: RoutingWeights
  ): Promise<{
    provider: string;
    model: string;
    costScore: number;
    qualityScore: number;
    speedScore: number;
    reliabilityScore: number;
    overallScore: number;
    estimatedCost: number;
    estimatedLatency: number;
    estimatedQuality: number;
  }> {
    // Calculate individual scores (0-1, higher is better)
    const costScore = this.calculateCostScore(option.provider, option.model, context);
    const qualityScore = this.getQualityScore(option.provider, option.model);
    const speedScore = this.calculateSpeedScore(option.provider, option.model);
    const reliabilityScore = this.calculateReliabilityScore(option.provider, option.model);
    
    // Calculate weighted overall score
    const overallScore = 
      (costScore * weights.cost) +
      (qualityScore * weights.quality) +
      (speedScore * weights.speed) +
      (reliabilityScore * weights.reliability);
    
    return {
      provider: option.provider,
      model: option.model,
      costScore,
      qualityScore,
      speedScore,
      reliabilityScore,
      overallScore,
      estimatedCost: this.estimateCost(option.provider, option.model, context),
      estimatedLatency: this.estimateLatency(option.provider, option.model),
      estimatedQuality: qualityScore
    };
  }

  private calculateCostScore(provider: string, model: string, context: RoutingContext): number {
    const cost = this.estimateCost(provider, model, context);
    
    // Get cost range across all providers for normalization
    const allCosts: number[] = [];
    this.providerCosts.forEach((costInfo, key) => {
      const [p, m] = key.split(':');
      allCosts.push(this.estimateCost(p, m, context));
    });
    
    if (allCosts.length === 0) return 0.5;
    
    const minCost = Math.min(...allCosts);
    const maxCost = Math.max(...allCosts);
    
    if (maxCost === minCost) return 1.0;
    
    // Lower cost = higher score (inverted scale)
    return 1.0 - ((cost - minCost) / (maxCost - minCost));
  }

  private getQualityScore(provider: string, model: string): number {
    const key = `${provider}:${model}`;
    const metrics = this.qualityMetrics.get(key);
    
    if (!metrics) {
      return 0.5; // Default neutral score
    }
    
    return metrics.overallQuality;
  }

  private calculateSpeedScore(provider: string, model: string): number {
    const latency = this.estimateLatency(provider, model);
    
    // Get latency range for normalization
    const allLatencies: number[] = [];
    this.performance.forEach((perf) => {
      if (perf.recentLatency.length > 0) {
        const avgLatency = perf.recentLatency.reduce((a, b) => a + b, 0) / perf.recentLatency.length;
        allLatencies.push(avgLatency);
      }
    });
    
    // Use health monitor data if available
    if (this.healthMonitor) {
      const metrics = this.healthMonitor.getMetrics(provider);
      if (metrics) {
        allLatencies.push(metrics.averageLatency);
      }
    }
    
    if (allLatencies.length === 0) return 0.5;
    
    const minLatency = Math.min(...allLatencies);
    const maxLatency = Math.max(...allLatencies);
    
    if (maxLatency === minLatency) return 1.0;
    
    // Lower latency = higher score (inverted scale)
    return 1.0 - ((latency - minLatency) / (maxLatency - minLatency));
  }

  private calculateReliabilityScore(provider: string, model: string): number {
    // Use health monitor if available
    if (this.healthMonitor) {
      const metrics = this.healthMonitor.getMetrics(provider);
      if (metrics) {
        return metrics.successRate;
      }
    }
    
    // Fallback to performance tracking
    const key = `${provider}:${model}`;
    const perf = this.performance.get(key);
    
    if (perf) {
      return perf.recentSuccessRate;
    }
    
    return 0.8; // Default assumption
  }

  private estimateCost(provider: string, model: string, context: RoutingContext): number {
    const key = `${provider}:${model}`;
    const cost = this.providerCosts.get(key);
    
    if (!cost) {
      return 0.001; // Default estimate
    }
    
    const inputCost = (context.estimatedInputTokens / 1000) * cost.inputCostPer1K;
    const outputCost = (context.estimatedOutputTokens / 1000) * cost.outputCostPer1K;
    
    return inputCost + outputCost;
  }

  private estimateLatency(provider: string, model: string): number {
    // Use performance tracking if available
    const key = `${provider}:${model}`;
    const perf = this.performance.get(key);
    
    if (perf && perf.recentLatency.length > 0) {
      return perf.recentLatency.reduce((a, b) => a + b, 0) / perf.recentLatency.length;
    }
    
    // Use health monitor if available
    if (this.healthMonitor) {
      const metrics = this.healthMonitor.getMetrics(provider);
      if (metrics) {
        return metrics.averageLatency;
      }
    }
    
    // Default estimates based on provider characteristics
    const defaultLatencies: { [key: string]: number } = {
      'groq': 500,     // Fast inference
      'openai': 2000,  // Balanced
      'anthropic': 3000, // Thoughtful
      'together': 1500,  // Good balance
      'perplexity': 2500 // Research-focused
    };
    
    return defaultLatencies[provider] || 2000;
  }

  private filterViableOptions(
    options: Array<any>,
    context: RoutingContext
  ): Array<any> {
    return options.filter(option => {
      // Budget constraints
      if (context.budgetConstraints?.maxCostPerRequest && 
          option.estimatedCost > context.budgetConstraints.maxCostPerRequest) {
        return false;
      }
      
      // Quality requirements
      if (context.qualityRequirements?.minQualityScore &&
          option.qualityScore < context.qualityRequirements.minQualityScore) {
        return false;
      }
      
      if (context.qualityRequirements?.minSuccessRate &&
          option.reliabilityScore < context.qualityRequirements.minSuccessRate) {
        return false;
      }
      
      // Latency requirements
      if (context.maxLatencyMs &&
          option.estimatedLatency > context.maxLatencyMs) {
        return false;
      }
      
      return true;
    });
  }

  private getRoutingWeights(userTier: string, customWeights?: Partial<RoutingWeights>): RoutingWeights {
    const defaultWeights = this.defaultWeights[userTier] || this.defaultWeights.premium;
    
    if (!customWeights) {
      return defaultWeights;
    }
    
    return {
      cost: customWeights.cost ?? defaultWeights.cost,
      quality: customWeights.quality ?? defaultWeights.quality,
      speed: customWeights.speed ?? defaultWeights.speed,
      reliability: customWeights.reliability ?? defaultWeights.reliability
    };
  }

  private determineOptimizationStrategy(weights: RoutingWeights): 'cost' | 'quality' | 'speed' | 'balanced' {
    const maxWeight = Math.max(weights.cost, weights.quality, weights.speed, weights.reliability);
    
    if (weights.cost === maxWeight && weights.cost > 0.5) return 'cost';
    if (weights.quality === maxWeight && weights.quality > 0.4) return 'quality';
    if (weights.speed === maxWeight && weights.speed > 0.4) return 'speed';
    
    return 'balanced';
  }

  private calculateTrend(values: number[]): 'improving' | 'stable' | 'degrading' {
    if (values.length < 10) return 'stable';
    
    const recent = values.slice(-10);
    const older = values.slice(-20, -10);
    
    if (older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (change > 0.1) return 'degrading'; // 10% worse (higher latency/cost is worse)
    if (change < -0.1) return 'improving'; // 10% better
    
    return 'stable';
  }

  private loadDefaultCosts(): void {
    // Load realistic cost data for major providers
    const defaultCosts: ProviderCost[] = [
      // OpenAI
      { provider: 'openai', model: 'gpt-4o', inputCostPer1K: 0.0025, outputCostPer1K: 0.01, lastUpdated: new Date() },
      { provider: 'openai', model: 'gpt-4o-mini', inputCostPer1K: 0.000150, outputCostPer1K: 0.0006, lastUpdated: new Date() },
      { provider: 'openai', model: 'gpt-3.5-turbo', inputCostPer1K: 0.0005, outputCostPer1K: 0.0015, lastUpdated: new Date() },
      
      // Anthropic
      { provider: 'anthropic', model: 'claude-3-5-sonnet-20241022', inputCostPer1K: 0.003, outputCostPer1K: 0.015, lastUpdated: new Date() },
      { provider: 'anthropic', model: 'claude-3-haiku-20240307', inputCostPer1K: 0.00025, outputCostPer1K: 0.00125, lastUpdated: new Date() },
      
      // Groq (very fast, competitive pricing)
      { provider: 'groq', model: 'llama3-8b-8192', inputCostPer1K: 0.00005, outputCostPer1K: 0.00008, lastUpdated: new Date() },
      { provider: 'groq', model: 'llama3-70b-8192', inputCostPer1K: 0.00059, outputCostPer1K: 0.00079, lastUpdated: new Date() },
      
      // DeepSeek (very competitive)
      { provider: 'deepseek', model: 'deepseek-chat', inputCostPer1K: 0.00014, outputCostPer1K: 0.00028, lastUpdated: new Date() },
      
      // Mistral
      { provider: 'mistral', model: 'mistral-small-latest', inputCostPer1K: 0.0002, outputCostPer1K: 0.0006, lastUpdated: new Date() },
      { provider: 'mistral', model: 'mistral-large-latest', inputCostPer1K: 0.002, outputCostPer1K: 0.006, lastUpdated: new Date() }
    ];
    
    defaultCosts.forEach(cost => this.updateProviderCost(cost));
  }

  private loadDefaultQualityMetrics(): void {
    // Load baseline quality metrics
    const defaultQuality: QualityMetrics[] = [
      {
        provider: 'openai', model: 'gpt-4o',
        averageLatency: 2000, p95Latency: 5000, successRate: 0.98,
        responseQuality: 0.92, accuracyScore: 0.89, coherenceScore: 0.94, safetyScore: 0.96,
        overallQuality: 0.9, totalSamples: 10000, confidenceLevel: 0.95, lastUpdated: new Date()
      },
      {
        provider: 'anthropic', model: 'claude-3-5-sonnet-20241022',
        averageLatency: 3000, p95Latency: 6000, successRate: 0.97,
        responseQuality: 0.94, accuracyScore: 0.91, coherenceScore: 0.96, safetyScore: 0.98,
        overallQuality: 0.92, totalSamples: 8000, confidenceLevel: 0.94, lastUpdated: new Date()
      },
      {
        provider: 'groq', model: 'llama3-70b-8192',
        averageLatency: 500, p95Latency: 1000, successRate: 0.96,
        responseQuality: 0.85, accuracyScore: 0.82, coherenceScore: 0.87, safetyScore: 0.93,
        overallQuality: 0.84, totalSamples: 5000, confidenceLevel: 0.92, lastUpdated: new Date()
      }
    ];
    
    defaultQuality.forEach(quality => this.updateQualityMetrics(quality));
  }
}