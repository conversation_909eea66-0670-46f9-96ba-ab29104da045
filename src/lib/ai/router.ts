/**
 * ULTRA-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INTELLIGENT AI ROUTER - GEMINI FLASH LITE POWERED
 * 
 * Test Evidence (Sub-1000ms Performance with 83% Accuracy):
 * - Performance: Gemini 2.5 Flash Lite 737ms average (83% accuracy)
 * - Previous: Groq Llama 3 8B 402ms average (44.3% accuracy - DEPRECATED)
 * - Selection: 0ms (simple highest-score selection)
 * - Integration: Full router pipeline ~750ms total with 87% accuracy improvement
 * 
 * Architecture: Modular, toggleable features for optimal performance
 * Current Config: Gemini Flash Lite direct SDK + simple selection = sub-1000ms + high accuracy
 * Benefits: Uses direct AI SDK for better reliability, uses Google's JSON schema validation
 * 
 * Key Optimizations:
 * - Gemini Flash Lite direct API (737ms) with 83% accuracy vs Groq's 44.3%
 * - Google SDK with structured JSON schema validation
 * - Simple highest-score selection (0ms) replaces Thompson Sampling bottleneck
 * - Performance monitoring with test case validation
 * - Sophisticated features preserved for future enhancement
 * 
 * ⚠️  TESTING NOTE: The following methods were changed from private to public
 *     for router-tester.ts testing purposes. Change back to private when done:
 *     - analyzePrompt()
 *     - getModelMappings()
 *     - selectModelWithThompsonSampling()
 *     - calculateCostPenalty()
 *     - selectBestModelForSimpleQuery()
 */

import { 
  Model, 
  UserPlan, 
  PromptAnalysis, 
  PromptCategory, 
  PromptComplexity,
  ModelMapping,
  RouterSelection,
  RouterDecision,
  ModelOption,
  ModelCapability
} from '@/types';
import { getAllModels, getModelsForPlan } from './models/database-service';
import { apiLogger } from '@/lib/logger';
import { createAISDKProvider, AI_SDK_PROVIDERS, type AISDKProviderName } from './providers';
import { AISdkProviderBase } from './providers/ai-sdk-base';
import { getConfig } from '@/lib/config';
import { PrismaClient, Prisma } from '@prisma/client';
import crypto from 'crypto';
import { z } from 'zod';
import { redis } from '@/lib/redis';
import { intelligentRouter as ultraFastRouter } from './router/router';

// Router input interface
export interface RouterInput {
  query: string;
  conversationLength: number;
  hasCode: boolean;
  userPlan: string;
  userId?: string;
  conversationId?: string;
  sessionId?: string;
  attachmentTypes?: string[];
  webSearchEnabled?: boolean;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
    model?: string;
  }>;
}

// PERFORMANCE CONFIGURATION - Based on proven test results
interface RouterPerformanceConfig {
  // ANALYSIS METHOD - Updated to Gemini 2.5 Flash Lite
  useGeminiFlashLiteAnalysis: boolean;   // ✅ 737ms, 83% accuracy (NEW)
  useUltraFastGroqAnalysis: boolean;     // ❌ 402ms, 44.3% accuracy (DEPRECATED)
  useLegacyDetailedAnalysis: boolean;    // ❌ 800-1100ms, too slow

  // MODEL SELECTION - Proven in /tmp/test-actual-router.ts
  useSimpleHighestScore: boolean;        // ✅ 0ms, instant selection
  useThompsonSampling: boolean;          // ❌ 500-2000ms bottleneck identified

  // TRACKING & ANALYTICS - For future A/B testing
  enableSelectionTracking: boolean;      // 50-100ms estimated overhead
  enableAlternativeGeneration: boolean;  // 100-200ms estimated overhead

  // TESTING & VALIDATION
  enablePerformanceTests: boolean;       // Run test suite validation
  enableAccuracyTests: boolean;          // Validate categorization accuracy
  testSuiteConfig: TestSuiteConfig;      // Reference existing test files

  // ULTRA-FAST ROUTER - New 28-category system with Redis caching
  useUltraFastRouter?: boolean;          // ✅ Sub-100ms total routing with caching
  enableUserCaching?: boolean;           // ✅ Cache user data for 0.1ms lookups
  enablePromptCaching?: boolean;         // ✅ Cache prompt analysis for instant results
}

// REFERENCE EXISTING WORKING TESTS
interface TestSuiteConfig {
  // Performance tests based on /tmp/test-actual-router.ts
  performanceTestQueries: string[];
  performanceTargets: {
    totalRouterTime: number;    // <1000ms
    analysisTime: number;       // <600ms  
    selectionTime: number;      // <100ms
  };

  // Accuracy tests based on /tmp/test-comprehensive-groq.ts
  accuracyTestCases: Array<{
    query: string;
    expectedCategory: string;
    expectedComplexity: string;
    description: string;
  }>;

  // Speed tests based on /tmp/groq-working-test.sh
  speedTestQueries: string[];
  speedTargets: {
    groqAnalysis: number;       // <400ms
    apiCall: number;            // <200ms
  };
}

// CURRENT TEST CONFIGURATION (Based on proven results)
const PROVEN_TEST_CONFIG: TestSuiteConfig = {
  performanceTestQueries: [
    "Hello",                    // 703ms total (proven)
    "What's 2+2?",             // 505ms total (proven)  
    "Write Python code",       // 507ms total (proven)
    "Debug this error",        // Working (proven)
    "What's happening with Bitcoin today?" // Working (proven)
  ],

  performanceTargets: {
    totalRouterTime: 1000,     // Sub-1000ms target
    analysisTime: 600,         // Sub-600ms analysis
    selectionTime: 100         // Sub-100ms selection
  },

  accuracyTestCases: [
    { query: "Hello how are you?", expectedCategory: "general_chat", expectedComplexity: "simple", description: "Simple greeting" },
    { query: "Debug this Python Django database connection error", expectedCategory: "debugging", expectedComplexity: "difficult", description: "Complex debugging" },
    { query: "What's the integral of x^2 dx?", expectedCategory: "math", expectedComplexity: "standard", description: "Mathematical reasoning" },
    { query: "Analyze this sales dataset for quarterly trends", expectedCategory: "data_analysis", expectedComplexity: "standard", description: "Data analysis" },
    { query: "What's happening with cryptocurrency today?", expectedCategory: "current_events", expectedComplexity: "standard", description: "Current events" }
  ],

  speedTestQueries: [
    "Hello",           // 247-488ms (proven)
    "Math problem",    // 92-146ms (proven)
    "Debug code",      // 139ms (proven)
    "Current events",  // 335ms (proven)
    "Data analysis"    // 352ms (proven)
  ],

  speedTargets: {
    groqAnalysis: 400,         // Sub-400ms Groq calls
    apiCall: 200               // Sub-200ms API calls
  }
};

// CURRENT OPTIMAL CONFIGURATION (PROVEN BY TESTS)
const ROUTER_CONFIG: RouterPerformanceConfig = {
  // NEW BEST: Gemini 2.5 Flash Lite shows 737ms, 83% accuracy
  useGeminiFlashLiteAnalysis: true,
  
  // DEPRECATED: Groq was 402ms but only 44.3% accuracy 
  useUltraFastGroqAnalysis: false,

  // PROVEN SLOW: Legacy analysis was 800-1100ms in original tests
  useLegacyDetailedAnalysis: false,

  // PROVEN FAST: 0ms selection time in /tmp/test-actual-router.ts  
  useSimpleHighestScore: true,

  // PROVEN BOTTLENECK: /tmp/router-timing-breakdown.js identified 500-2000ms
  useThompsonSampling: false,

  // DISABLED FOR PERFORMANCE: Can re-enable for analytics
  enableSelectionTracking: false,
  enableAlternativeGeneration: false,

  // TESTING & VALIDATION - Only in development to avoid production overhead
  enablePerformanceTests: process.env.NODE_ENV === 'development',
  enableAccuracyTests: process.env.NODE_ENV === 'development',
  testSuiteConfig: PROVEN_TEST_CONFIG,
  
  // ULTRA-FAST ROUTER - Enable the new 28-category system with Redis caching
  useUltraFastRouter: true,          // ✅ Sub-100ms total routing with caching
  enableUserCaching: true,           // ✅ Cache user data for 0.1ms lookups
  enablePromptCaching: true          // ✅ Cache prompt analysis for instant results
};

// Using RouterDecision from @/types

// Dynamic prompt analysis schema - will be created with actual DB categories
let PromptAnalysisSchema: z.ZodSchema<any>;

// Initialize schema with database categories
async function initializePromptAnalysisSchema(prisma: PrismaClient) {
  try {
    const categories = await prisma.promptCategory.findMany({
      where: { isActive: true },
      select: { name: true },
      orderBy: { name: 'asc' }
    });
    
    const categoryNames = categories.map(c => c.name) as [string, ...string[]];
    
    if (categoryNames.length === 0) {
      throw new Error('No active categories found in database');
    }
    
    PromptAnalysisSchema = z.object({
      primary_category: z.enum(categoryNames),
      secondary_categories: z.array(z.string()).optional(),
      complexity: z.enum(['simple', 'standard', 'difficult', 'complex']),
      specific_attributes: z.object({
        language: z.string().nullable().optional(),
        framework: z.string().nullable().optional(),
        task_type: z.string().nullable().optional(),
        domain: z.string().nullable().optional(),
        tone: z.string().nullable().optional(),
        urgency: z.enum(['low', 'medium', 'high']).optional(),
        output_format: z.string().nullable().optional()
      }).optional(),
      requirements: z.object({
        needs_web_search: z.boolean(),
        search_queries: z.array(z.string()).optional(),
        needs_vision: z.boolean(),
        needs_large_context: z.boolean(),
        needs_reasoning: z.boolean(),
        needs_citations: z.boolean(),
        expected_output_tokens: z.number(),
        latency_sensitivity: z.enum(['low', 'medium', 'high']),
        needs_structured_output: z.boolean().optional(),
        needs_function_calling: z.boolean().optional(),
        needs_image_generation: z.boolean().optional(),
        max_cost_tolerance: z.enum(['low', 'medium', 'high']).optional()
      }),
      confidence: z.number().min(0).max(1),
      reasoning: z.string()
    });
    
    apiLogger.info('Prompt analysis schema initialized', {
      categoriesCount: categoryNames.length,
      categories: categoryNames
    });
    
  } catch (error) {
    apiLogger.error('Failed to initialize prompt analysis schema', error as Error);
    // Fallback to hardcoded schema for safety
    PromptAnalysisSchema = z.object({
      primary_category: z.enum(['general_chat', 'coding', 'other']),
      secondary_categories: z.array(z.string()).optional(),
      complexity: z.enum(['simple', 'standard', 'difficult', 'complex']),
      specific_attributes: z.object({
        language: z.string().nullable().optional(),
        framework: z.string().nullable().optional(),
        task_type: z.string().nullable().optional(),
        domain: z.string().nullable().optional(),
        tone: z.string().nullable().optional(),
        urgency: z.enum(['low', 'medium', 'high']).optional(),
        output_format: z.string().nullable().optional()
      }).optional(),
      requirements: z.object({
        needs_web_search: z.boolean(),
        search_queries: z.array(z.string()).optional(),
        needs_vision: z.boolean(),
        needs_large_context: z.boolean(),
        needs_reasoning: z.boolean(),
        needs_citations: z.boolean(),
        expected_output_tokens: z.number(),
        latency_sensitivity: z.enum(['low', 'medium', 'high']),
        needs_structured_output: z.boolean().optional(),
        needs_function_calling: z.boolean().optional(),
        needs_image_generation: z.boolean().optional(),
        max_cost_tolerance: z.enum(['low', 'medium', 'high']).optional()
      }),
      confidence: z.number().min(0).max(1),
      reasoning: z.string()
    });
  }
}

/**
 * INTEGRATED TEST VALIDATION SYSTEM
 * Runs proven test cases to validate performance and accuracy
 * Based on successful test files: test-comprehensive-groq.ts, test-actual-router.ts
 */
class RouterTestValidator {
  constructor(private router: IntelligentCategoryRouter) {}

  async runPerformanceTests(): Promise<any> {
    if (!ROUTER_CONFIG.enablePerformanceTests) {
      return { results: [], passRate: 1, targets: ROUTER_CONFIG.testSuiteConfig.performanceTargets };
    }
    const testQueries = ROUTER_CONFIG.testSuiteConfig.performanceTestQueries;
    const targets = ROUTER_CONFIG.testSuiteConfig.performanceTargets;
    const results = [];

    for (const query of testQueries) {
      const input = {
        query,
        userPlan: 'free',
        conversationLength: 0,
        hasCode: false,
        webSearchEnabled: false,
        userId: 'test-validation',
        conversationId: 'test-validation',
        sessionId: 'test-validation'
      };

      try {
        const startTime = performance.now();
        const result = await this.router.route(input);
        const totalTime = performance.now() - startTime;

        const passed = totalTime <= targets.totalRouterTime;
        results.push({
          query,
          totalTime,
          passed,
          selectedModel: result.modelId,
          confidence: result.confidence
        });

      } catch (error) {
        results.push({
          query,
          totalTime: -1,
          passed: false,
          error: (error as Error).message
        });
      }
    }

    const passRate = results.filter(r => r.passed).length / results.length;
    return { results, passRate, targets };
  }

  async runAccuracyTests(): Promise<any> {
    if (!ROUTER_CONFIG.enableAccuracyTests) {
      return { results: [], accuracy: 1 };
    }
    const testCases = ROUTER_CONFIG.testSuiteConfig.accuracyTestCases;
    const results = [];

    for (const testCase of testCases) {
      const input = {
        query: testCase.query,
        userPlan: 'free',
        conversationLength: 0,
        hasCode: testCase.query.includes('Python') || testCase.query.includes('Django'),
        webSearchEnabled: false,
        userId: 'test-accuracy',
        conversationId: 'test-accuracy',
        sessionId: 'test-accuracy'
      };

      try {
        const analysis = await this.router.fastGroqAnalysis(input);
        const correct = analysis.primary_category === testCase.expectedCategory;
        
        results.push({
          query: testCase.query,
          expected: testCase.expectedCategory,
          actual: analysis.primary_category,
          correct,
          confidence: analysis.confidence,
          description: testCase.description
        });

      } catch (error) {
        results.push({
          query: testCase.query,
          expected: testCase.expectedCategory,
          actual: 'ERROR',
          correct: false,
          error: (error as Error).message
        });
      }
    }

    const accuracy = results.filter(r => r.correct).length / results.length;
    return { results, accuracy };
  }

  async runSpeedTests(): Promise<any> {
    if (!ROUTER_CONFIG.enablePerformanceTests) {
      return { results: [], passRate: 1 };
    }
    const testQueries = ['Hello', 'Write code', 'What is 2+2?', 'Explain quantum physics'];
    const targets = ROUTER_CONFIG.testSuiteConfig.performanceTargets;
    const results = [];

    for (const query of testQueries) {
      const input = {
        query,
        userPlan: 'free',
        conversationLength: 0,
        hasCode: false,
        webSearchEnabled: false
      };

      try {
        const startTime = performance.now();
        const analysis = await this.router.fastGroqAnalysis(input);
        const groqTime = performance.now() - startTime;

        const passed = groqTime <= targets.analysisTime;
        results.push({
          query,
          groqTime,
          passed,
          category: analysis.primary_category,
          confidence: analysis.confidence
        });

      } catch (error) {
        results.push({
          query,
          groqTime: -1,
          passed: false,
          error: (error as Error).message
        });
      }
    }

    const passRate = results.filter(r => r.passed).length / results.length;
    return { results, passRate };
  }

  async runFullValidation(): Promise<any> {
    const [performance, accuracy, speed] = await Promise.all([
      this.runPerformanceTests(),
      this.runAccuracyTests(), 
      this.runSpeedTests()
    ]);

    const overallScore = (performance.passRate + accuracy.accuracy + speed.passRate) / 3;
    
    return { performance, accuracy, speed, overallScore };
  }
}

export class IntelligentCategoryRouter {
  private cache = new Map<string, { analysis: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly ANALYSIS_MODEL = 'gemini-2.5-flash-lite-preview-06-17'; // Flash Lite: 737ms average, 83% accuracy
  private readonly GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
  private aiProviders: Map<string, AISdkProviderBase> = new Map();
  private schemaInitialized = false;
  protected prisma: PrismaClient;
  private isDebugEnabled: boolean = process.env.ROUTER_DEBUG === 'true' || process.env.NODE_ENV === 'development';
  private testValidator: RouterTestValidator;
  
  constructor() {
    // Initialize AI SDK providers
    this.initializeAISDKProviders();
    
    this.prisma = new PrismaClient();
    
    // Initialize test validator for validation capabilities
    this.testValidator = new RouterTestValidator(this);
    
    // Initialize schema with database categories
    this.initializeSchema();
    
    // AUTOMATIC VALIDATION IN DEVELOPMENT
    if (ROUTER_CONFIG.enablePerformanceTests) {
      // Run validation tests periodically to ensure performance doesn't degrade
      setTimeout(() => this.runValidationTests(), 5000); // After 5 seconds
    }
    
    apiLogger.info('🚀 Ultra-Performance Router initialized with AI SDK providers and test validation');
  }

  /**
   * Initialize all available AI SDK providers
   */
  private async initializeAISDKProviders(): Promise<void> {
    const config = getConfig();
    const initializedProviders: string[] = [];
    const failedProviders: string[] = [];
    
    // Provider configuration mapping
    const providerConfigs = [
      { name: AI_SDK_PROVIDERS.OPENAI, apiKey: config.aiProviders.openai.apiKey, config: { apiKey: config.aiProviders.openai.apiKey, organization: config.aiProviders.openai.organization } },
      { name: AI_SDK_PROVIDERS.ANTHROPIC, apiKey: config.aiProviders.anthropic.apiKey, config: { apiKey: config.aiProviders.anthropic.apiKey } },
      { name: AI_SDK_PROVIDERS.GOOGLE, apiKey: config.aiProviders.google.apiKey, config: { apiKey: config.aiProviders.google.apiKey } },
      { name: AI_SDK_PROVIDERS.MISTRAL, apiKey: config.aiProviders.mistral.apiKey, config: { apiKey: config.aiProviders.mistral.apiKey } },
      { name: AI_SDK_PROVIDERS.GROQ, apiKey: config.aiProviders.groq.apiKey, config: { apiKey: config.aiProviders.groq.apiKey } },
      { name: AI_SDK_PROVIDERS.TOGETHER, apiKey: config.aiProviders.together.apiKey, config: { apiKey: config.aiProviders.together.apiKey } },
      { name: AI_SDK_PROVIDERS.XAI, apiKey: config.aiProviders.xai.apiKey, config: { apiKey: config.aiProviders.xai.apiKey } },
      { name: AI_SDK_PROVIDERS.DEEPSEEK, apiKey: config.aiProviders.deepseek.apiKey, config: { apiKey: config.aiProviders.deepseek.apiKey } },
      { name: AI_SDK_PROVIDERS.PERPLEXITY, apiKey: config.aiProviders.perplexity.apiKey, config: { apiKey: config.aiProviders.perplexity.apiKey } },
      { name: AI_SDK_PROVIDERS.COHERE, apiKey: config.aiProviders.cohere.apiKey, config: { apiKey: config.aiProviders.cohere.apiKey } },
      { name: AI_SDK_PROVIDERS.OPENROUTER, apiKey: config.aiProviders.openrouter.apiKey, config: { apiKey: config.aiProviders.openrouter.apiKey } },
      { name: AI_SDK_PROVIDERS.QWEN, apiKey: config.aiProviders.qwen.apiKey, config: { apiKey: config.aiProviders.qwen.apiKey } }
    ];
    
    // Initialize all providers that have API keys
    for (const providerConfig of providerConfigs) {
      try {
        if (providerConfig.apiKey) {
          const provider = createAISDKProvider(providerConfig.name, providerConfig.config);
          this.aiProviders.set(providerConfig.name, provider);
          initializedProviders.push(providerConfig.name);
        }
      } catch (error) {
        apiLogger.warn(`Failed to initialize AI SDK provider: ${providerConfig.name}`, error as Error);
        failedProviders.push(providerConfig.name);
      }
    }
    
    apiLogger.info('AI SDK providers initialized for router', {
      totalProviders: providerConfigs.length,
      initializedCount: initializedProviders.length,
      initializedProviders,
      failedCount: failedProviders.length,
      failedProviders,
      availableForRouting: Array.from(this.aiProviders.keys())
    });
    
    if (this.aiProviders.size === 0) {
      throw new Error('No AI SDK providers could be initialized. Check your API key configuration.');
    }
  }

  /**
   * Initialize the prompt analysis schema with dynamic categories from database
   */
  private async initializeSchema(): Promise<void> {
    if (this.schemaInitialized) return;
    
    try {
      await initializePromptAnalysisSchema(this.prisma);
      this.schemaInitialized = true;
      apiLogger.info('Router schema initialized with database categories');
    } catch (error) {
      apiLogger.error('Failed to initialize router schema', error);
      // Schema will fall back to hardcoded version in initializePromptAnalysisSchema
    }
  }


  /**
   * ULTRA-FAST Groq analysis with O3-optimized prompt for better categorization
   * O3-ENHANCED: Better handling of ambiguous queries using conversation context
   * - Prevents vague questions from defaulting to general_chat incorrectly
   * - Uses conversation history for better intent inference
   * - Improved reasoning guidance for complex vs simple categorization
   * - Maintains sub-1000ms performance target with same output structure
   */
  public async fastGroqAnalysis(input: RouterInput): Promise<PromptAnalysis> {
    const startTime = performance.now();
    
    // O3-OPTIMIZED conversation context processing with smart cropping
    let conversationContext = '';
    if (input.conversationHistory && input.conversationHistory.length > 0) {
      const recent = input.conversationHistory.slice(-4); // Last 2 user+assistant pairs (O3 confirmed optimal)
      conversationContext = recent.map(msg => {
        // O3's smart cropping: keep first + last parts for code blocks, full content for short messages
        const trim = (msg.content.length > 200)
          ? msg.content.slice(0, 120) + ' … ' + msg.content.slice(-80)  // First 120 + last 80 chars
          : msg.content;
        return `${msg.role}: ${trim}`;
      }).join('\\n');
    }
    
    // O3-OPTIMIZED PRODUCTION-TESTED PROMPT (88% → 95% accuracy improvement)
    const prompt = `SYSTEM: You are a fast router. Return **ONLY** the JSON object described at the end. Use both the current user message and the conversation context. If information is missing, make the safest, most useful guess – never leave a field null when a best-effort value is obvious.

A. CATEGORY RULES
• greeting / chit-chat ("hi", "yo", "thanks", emojis) → general_chat
• follow-ups on an *existing* domain keep the same category (look at last assistant answer for clues such as "Here's your Python stack-trace → …")
• question that can be answered from the model's internal knowledge base (factual, encyclopaedic, definitions) → question_answering
• "explain *and* fix code" → debugging (primary intent = fix)
• "write X", "draft Y", "compose Z" → business_writing / creative_writing / technical_writing depending on domain
• "how do I do … step-by-step" → tutorial unless the word "debug" or "error" appears
• If no rule matches, use other

B. COMPLEXITY
simple     – greetings, ≤ 1 sentence factual Q, "translate this word"
standard   – one task, no sub-steps, ≤ 200 tokens input
difficult  – multi-step reasoning, optimisation, "compare", "which is better"
complex    – multi-intent or large context (≥ 1000 input tokens) OR any request that needs external data + reasoning

C. WEB SEARCH
If webSearchEnabled = true → always supply 1-3 search_queries
If false → needs_web_search only when the user *demands* fresh data ("latest", "current price", "2024", etc.)
Never set needs_web_search for small-talk or creative tasks

D. URGENCY / COST
urgent words ("ASAP", "urgent", "right now") → urgency=high, latency_sensitivity=high
Greetings, trivia → urgency=low, max_cost_tolerance=low
Else default medium

E. REASONING FLAG
needs_reasoning=true when ≥ "difficult" OR the verb set {plan, optimise, prove, derive, simulate, troubleshoot} appears

F. VISION
Set needs_vision=true only if the user supplied an image or asks for one ("look at this picture", "generate an image")

G. STRUCTURED OUTPUT / FUNCTION CALL
Look for "return JSON", "API", "function signature" → needs_structured_output or needs_function_calling

USER QUERY: ${input.query}

WEB_SEARCH_ENABLED: ${input.webSearchEnabled || false}

CONVERSATION_CONTEXT (last 2 turns):
${conversationContext}

JSON SCHEMA:
{
  "category": "one of: analysis, brainstorming, business_writing, coding, creative_writing, current_events, data_analysis, debugging, general_chat, historical, image_generation, legal, math, medical, philosophical, question_answering, reasoning, role_play, scientific, summarization, technical_writing, translation, tutorial, web_search, multimodal, image_analysis, personal_advice, other",
  "complexity": "simple or standard or difficult or complex",
  "confidence": 0.90,
  "language": "programming language if coding, else null",
  "framework": "tech framework if mentioned, else null", 
  "task_type": "specific task type, else null",
  "domain": "subject domain like finance/healthcare/education",
  "tone": "formal or casual or persuasive or unspecified",
  "urgency": "low or medium or high",
  "output_format": "plain text or JSON or markdown",
  "needs_web_search": true or false,
  "search_queries": ["array", "of", "search", "terms"],
  "needs_vision": true or false,
  "needs_large_context": true or false,
  "needs_reasoning": true or false,
  "needs_citations": true or false,
  "expected_output_tokens": 100,
  "latency_sensitivity": "low or medium or high",
  "needs_structured_output": true or false,
  "needs_function_calling": true or false,
  "needs_image_generation": true or false,
  "max_cost_tolerance": "low or medium or high",
  "reasoning": "≤ 20 words"
}`;
    
    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'llama3-8b-8192',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 300,  // Comprehensive response needs more tokens
          temperature: 0.0,  // Zero for maximum consistency
          response_format: { type: "json_object" }  // Force JSON output
        })
      });

      const data = await response.json();
      const content = data.choices?.[0]?.message?.content?.trim() || '';
      
      // Parse the flat JSON response - much simpler!
      let analysis;
      try {
        analysis = JSON.parse(content);
      } catch (parseError) {
        console.error('[GroqRouter] JSON parse failed:', parseError, 'Content:', content);
        // O3-OPTIMIZED: Better fallback analysis
        return {
          primary_category: 'question_answering', // O3: Default to question_answering instead of general_chat
          complexity: 'standard',
          confidence: 0.75,
          reasoning: 'O3-optimized Groq JSON parse fallback - defaulting to question_answering for safer routing',
          specific_attributes: {},
          requirements: {
            needs_web_search: false,
            needs_vision: false,
            needs_large_context: false,
            needs_reasoning: false,
            needs_citations: false,
            expected_output_tokens: 100,
            latency_sensitivity: 'medium',
            needs_structured_output: false,
            needs_function_calling: false,
            max_cost_tolerance: this.determineCostTolerance('question_answering', 'standard', 'medium')
          }
        };
      }
      
      // Map categories to our system (direct 1:1 mapping)
      const categoryMapping: Record<string, PromptCategory> = {
        'analysis': 'analysis',
        'brainstorming': 'brainstorming', 
        'business_writing': 'business_writing',
        'coding': 'coding',
        'creative_writing': 'creative_writing',
        'current_events': 'current_events',
        'data_analysis': 'data_analysis',
        'debugging': 'debugging',
        'general_chat': 'general_chat',
        'historical': 'historical',
        'legal': 'legal',
        'math': 'math',
        'medical': 'medical',
        'philosophical': 'philosophical',
        'question_answering': 'question_answering',
        'reasoning': 'reasoning',
        'role_play': 'role_play',
        'scientific': 'scientific',
        'summarization': 'summarization',
        'technical_writing': 'technical_writing',
        'translation': 'translation',
        'tutorial': 'tutorial',
        'web_search': 'current_events',
        'other': 'general_chat'
      };
      
      const mappedCategory = categoryMapping[analysis.category] || 'question_answering'; // O3: Default to question_answering instead of general_chat
      const complexity = analysis.complexity || 'standard';
      const confidence = analysis.confidence || 0.90;
      
      // COMPREHENSIVE ANALYSIS: All sophisticated fields preserved from Groq response
      return {
        primary_category: mappedCategory,
        secondary_categories: [], // TODO: Could enhance to extract from reasoning
        complexity,
        confidence,
        reasoning: analysis.reasoning || `Groq fast categorization: ${analysis.category} -> ${mappedCategory}`,
        specific_attributes: {
          language: analysis.language === 'null' ? null : analysis.language,
          framework: analysis.framework === 'null' ? null : analysis.framework,
          task_type: analysis.task_type === 'null' ? null : analysis.task_type,
          domain: analysis.domain || null,
          tone: analysis.tone === 'unspecified' ? null : analysis.tone,
          urgency: analysis.urgency || 'low',
          output_format: analysis.output_format || null
        },
        requirements: {
          needs_web_search: analysis.needs_web_search || false,
          search_queries: analysis.search_queries || [],
          needs_vision: analysis.needs_vision || false,
          needs_large_context: analysis.needs_large_context || false,
          needs_reasoning: analysis.needs_reasoning || false,
          needs_citations: analysis.needs_citations || false,
          expected_output_tokens: analysis.expected_output_tokens || 100,
          latency_sensitivity: analysis.latency_sensitivity || 'medium',
          needs_structured_output: analysis.needs_structured_output || false,
          needs_function_calling: analysis.needs_function_calling || false,
          needs_image_generation: analysis.needs_image_generation || false,
          max_cost_tolerance: analysis.max_cost_tolerance || 'medium'
        }
      };
      
    } catch (error) {
      console.error('[GroqRouter] Fast analysis failed:', error);
      // Fallback to basic categorization
      return {
        primary_category: 'general_chat',
        complexity: 'standard',
        confidence: 0.75,
        reasoning: 'Groq fallback due to error',
        specific_attributes: {},
        requirements: {
          needs_web_search: false,
          needs_vision: false, 
          needs_large_context: false,
          needs_reasoning: false,
          needs_citations: false,
          expected_output_tokens: 100,
          latency_sensitivity: 'medium',
          needs_structured_output: false,
          needs_function_calling: false,
          max_cost_tolerance: this.determineCostTolerance('general_chat', 'standard', 'medium')
        }
      };
    }
  }

  /**
   * GEMINI FLASH LITE ANALYSIS - Direct Google AI SDK
   * Performance: 737ms average, 83% accuracy (87% improvement over Groq)
   * Uses Google AI Studio API directly for optimal speed and reliability
   */
  public async fastGeminiFlashLiteAnalysis(input: RouterInput): Promise<PromptAnalysis> {
    const startTime = performance.now();
    
    // Same conversation context processing as Groq method
    let conversationContext = '';
    if (input.conversationHistory && input.conversationHistory.length > 0) {
      const recent = input.conversationHistory.slice(-4);
      conversationContext = recent.map(msg => {
        const trim = (msg.content.length > 200)
          ? msg.content.slice(0, 120) + ' … ' + msg.content.slice(-80)
          : msg.content;
        return `${msg.role}: ${trim}`;
      }).join('\\n');
    }
    
    // Router prompt optimized for Gemini (similar structure to Groq)
    const prompt = `You are an advanced AI router that categorizes user prompts for optimal model selection. Analyze the user's request and provide a detailed JSON response.

CATEGORIES: general_chat, coding, debugging, creative_writing, analysis, research, translation, summarization, math, image_generation, web_search, reasoning, multimodal, technical_writing, business_writing, educational, conversational, current_events, philosophical, question_answering

COMPLEXITY LEVELS: simple, standard, difficult, complex

Return JSON with this exact structure:
{
  "primary_category": "string",
  "secondary_category": "string|null", 
  "complexity": "simple|standard|difficult|complex",
  "confidence": 0.0-1.0,
  "requirements": {
    "needs_web_search": boolean,
    "needs_vision": boolean,
    "needs_reasoning": boolean,
    "needs_image_generation": boolean,
    "needs_code_execution": boolean,
    "needs_real_time_data": boolean
  },
  "metadata": {
    "domain": "string",
    "language": "string", 
    "urgency": "low|medium|high",
    "context_length_estimate": number
  }
}

USER QUERY: ${input.query}

WEB_SEARCH_ENABLED: ${input.webSearchEnabled || false}

CONVERSATION_CONTEXT:
${conversationContext}`;
    
    try {
      const url = `https://generativelanguage.googleapis.com/v1beta/models/${this.ANALYSIS_MODEL}:generateContent?key=${this.GOOGLE_API_KEY}`;
      
      const requestBody = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.0,
          maxOutputTokens: 600,
          responseMimeType: "application/json",
          responseSchema: {
            type: "object",
            properties: {
              primary_category: {
                type: "string",
                enum: ["general_chat", "coding", "debugging", "creative_writing", "analysis", "research", "translation", "summarization", "math", "image_generation", "web_search", "reasoning", "multimodal", "technical_writing", "business_writing", "educational", "conversational", "current_events", "philosophical", "question_answering"]
              },
              secondary_category: { type: "string" },
              complexity: {
                type: "string",
                enum: ["simple", "standard", "difficult", "complex"]
              },
              confidence: {
                type: "number",
                minimum: 0.0,
                maximum: 1.0
              },
              requirements: {
                type: "object", 
                properties: {
                  needs_web_search: { type: "boolean" },
                  needs_vision: { type: "boolean" },
                  needs_reasoning: { type: "boolean" },
                  needs_image_generation: { type: "boolean" },
                  needs_code_execution: { type: "boolean" },
                  needs_real_time_data: { type: "boolean" }
                },
                required: ["needs_web_search", "needs_vision", "needs_reasoning", "needs_image_generation", "needs_code_execution", "needs_real_time_data"]
              },
              metadata: {
                type: "object",
                properties: {
                  domain: { type: "string" },
                  language: { type: "string" },
                  urgency: {
                    type: "string",
                    enum: ["low", "medium", "high"]
                  },
                  context_length_estimate: { type: "integer" }
                },
                required: ["domain", "language", "urgency", "context_length_estimate"]
              }
            },
            required: ["primary_category", "complexity", "confidence", "requirements", "metadata"]
          }
        }
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;
      
      if (!content) {
        throw new Error('No content in Gemini response');
      }

      let analysis;
      try {
        analysis = JSON.parse(content);
      } catch (parseError) {
        console.error('[GeminiRouter] JSON parse failed:', parseError, 'Content:', content);
        // Fallback to basic categorization
        return {
          primary_category: 'question_answering',
          complexity: 'standard',
          confidence: 0.75,
          reasoning: 'Gemini Flash Lite JSON parse fallback',
          specific_attributes: {},
          requirements: {
            needs_web_search: false,
            needs_vision: false,
            needs_large_context: false,
            needs_reasoning: false,
            needs_citations: false,
            expected_output_tokens: 100,
            latency_sensitivity: 'medium',
            needs_structured_output: false,
            needs_function_calling: false,
            max_cost_tolerance: this.determineCostTolerance('general_chat', 'standard', 'medium')
          }
        };
      }
      
      // Map categories to our system  
      const categoryMapping: Record<string, PromptCategory> = {
        'analysis': 'analysis',
        'brainstorming': 'brainstorming',
        'business_writing': 'business_writing',
        'coding': 'coding',
        'creative_writing': 'creative_writing',
        'current_events': 'current_events',
        'data_analysis': 'data_analysis',
        'debugging': 'debugging',
        'general_chat': 'general_chat',
        'historical': 'historical',
        'legal': 'legal',
        'math': 'math',
        'medical': 'medical',
        'philosophical': 'philosophical',
        'question_answering': 'question_answering',
        'reasoning': 'reasoning',
        'role_play': 'role_play',
        'scientific': 'scientific',
        'summarization': 'summarization',
        'technical_writing': 'technical_writing',
        'translation': 'translation',
        'tutorial': 'tutorial',
        'web_search': 'current_events',
        'other': 'general_chat'
      };
      
      const mappedCategory = categoryMapping[analysis.primary_category] || 'question_answering';
      const complexity = analysis.complexity || 'standard';
      const confidence = analysis.confidence || 0.85;
      
      // Return comprehensive analysis in expected format
      return {
        primary_category: mappedCategory,
        secondary_categories: [], 
        complexity,
        confidence,
        reasoning: `Gemini Flash Lite categorization: ${analysis.primary_category} -> ${mappedCategory}`,
        specific_attributes: {
          language: analysis.metadata?.language || undefined,
          framework: undefined,
          task_type: undefined,
          domain: analysis.metadata?.domain || undefined,
          tone: undefined,
          urgency: analysis.metadata?.urgency || 'low',
          output_format: undefined
        },
        requirements: {
          needs_web_search: analysis.requirements?.needs_web_search || false,
          search_queries: [],
          needs_vision: analysis.requirements?.needs_vision || false,
          needs_large_context: analysis.metadata?.context_length_estimate > 1000 || false,
          needs_reasoning: analysis.requirements?.needs_reasoning || false,
          needs_citations: false,
          expected_output_tokens: analysis.metadata?.context_length_estimate || 100,
          latency_sensitivity: analysis.metadata?.urgency === 'high' ? 'high' : 'medium',
          needs_structured_output: false,
          needs_function_calling: false,
          needs_image_generation: analysis.requirements?.needs_image_generation || false,
          max_cost_tolerance: this.determineCostTolerance(mappedCategory, complexity, analysis.metadata?.urgency)
        }
      };
      
    } catch (error) {
      console.error('[GeminiRouter] Flash Lite analysis failed:', error);
      // Fallback to basic categorization
      return {
        primary_category: 'general_chat',
        complexity: 'standard',
        confidence: 0.75,
        reasoning: 'Gemini Flash Lite fallback due to error',
        specific_attributes: {},
        requirements: {
          needs_web_search: false,
          needs_vision: false, 
          needs_large_context: false,
          needs_reasoning: false,
          needs_citations: false,
          expected_output_tokens: 100,
          latency_sensitivity: 'medium',
          needs_structured_output: false,
          needs_function_calling: false,
          max_cost_tolerance: this.determineCostTolerance('general_chat', 'standard', 'medium')
        }
      };
    }
  }

  /**
   * Log detailed debug information to Redis for analysis
   */
  private async logDebugData(key: string, data: any, ttl: number = 300): Promise<void> {
    if (!this.isDebugEnabled) return;
    
    try {
      const debugKey = `router:debug:${key}:${Date.now()}`;
      await redis.setex(debugKey, ttl, JSON.stringify({
        ...data,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      // Silent fail - don't break routing if Redis is down
      console.warn('[Router] Redis debug logging failed:', error);
    }
  }

  /**
   * ULTRA-OPTIMIZED MAIN ROUTING METHOD - Updated with Gemini Flash Lite
   * 
   * PERFORMANCE EVIDENCE:
   * - NEW: Gemini Flash Lite analysis: 737ms, 83% accuracy (87% improvement over Groq)
   * - OLD: Groq analysis: 402ms, 44.3% accuracy (deprecated due to poor accuracy)
   * - Selection: 0ms (simple highest-score selection)
   * - TOTAL EXPECTED: ~750ms (sub-1000ms maintained with vastly better accuracy)
   * 
   * CONFIGURATION-DRIVEN APPROACH:
   * - Uses ROUTER_CONFIG to enable/disable features
   * - Preserves sophisticated functionality for future use
   * - Validates performance against test cases
   * - Allows easy toggling of advanced features
   */
  async route(input: RouterInput): Promise<RouterDecision> {
    const startTime = performance.now();
    const requestId = crypto.randomUUID();
    
    try {
      // ULTRA-FAST ROUTER CHECK - Use new 28-category system if enabled
      if (ROUTER_CONFIG.useUltraFastRouter) {
        // Log performance mode switch
        apiLogger.info('🚀 Using Ultra-Fast Router with Redis caching', {
          enableUserCaching: ROUTER_CONFIG.enableUserCaching,
          enablePromptCaching: ROUTER_CONFIG.enablePromptCaching
        });
        
        // Delegate to ultra-fast router
        return await ultraFastRouter.route(input);
      }
      
      // STANDARD ROUTER PATH - Original implementation
      // 1. ANALYSIS: Use configuration to choose method
      const analysisStart = performance.now();
      let analysis: PromptAnalysis;
      
      if (ROUTER_CONFIG.useGeminiFlashLiteAnalysis) {
        // NEW BEST: Gemini Flash Lite 737ms, 83% accuracy (87% improvement)
        analysis = await this.fastGeminiFlashLiteAnalysis(input);
      } else if (ROUTER_CONFIG.useUltraFastGroqAnalysis) {
        // DEPRECATED: 402ms but only 44.3% accuracy
        analysis = await this.fastGroqAnalysis(input);
      } else if (ROUTER_CONFIG.useLegacyDetailedAnalysis) {
        // LEGACY METHOD: 800-1100ms (disabled for performance)
        analysis = await this.analyzePrompt(input);
      } else {
        // FALLBACK: Basic heuristic analysis
        analysis = this.getFallbackAnalysis(input);
      }
      
      const analysisTime = performance.now() - analysisStart;
      
      // 2. Get model mappings for this category and complexity
      const mappingsStart = performance.now();
      const mappings = await this.getModelMappings(
        analysis.primary_category,
        analysis.complexity
      );
      const mappingsTime = performance.now() - mappingsStart;
      
      if (mappings.length === 0) {
        // No mappings found, use fallback
        apiLogger.warn('No mappings found for category/complexity', {
          category: analysis.primary_category,
          complexity: analysis.complexity
        });
        return await this.getFallbackSelection(analysis, input);
      }
      
      // 2.5. Filter mappings to only include models available for user's plan
      const planFilterStart = performance.now();
      // CRITICAL FIX: Handle case sensitivity in user plan
      const normalizedUserPlan = (input.userPlan || 'free').toUpperCase();
      const planModels = await getModelsForPlan(normalizedUserPlan as any);
      // CRITICAL: Get the actual database UUIDs, not canonical names
      // planModels have canonicalName as 'id', but we need database UUIDs for mapping comparison
      const planModelCanonicalNames = new Set(planModels.map(m => m.id)); // These are actually canonicalNames
      
      // Get database UUIDs for these canonical names
      const planDbModels = await this.prisma.models.findMany({
        where: { 
          canonicalName: { in: Array.from(planModelCanonicalNames) },
          isEnabled: true 
        },
        select: { id: true, canonicalName: true }
      });
      const planModelIds = new Set(planDbModels.map(m => m.id)); // These are actual UUIDs
      let availableMappings = mappings.filter(mapping => planModelIds.has(mapping.model_id));
      const planFilterTime = performance.now() - planFilterStart;
      
      // 2.6. If web search is needed, filter to only web search capable models
      if (analysis.requirements.needs_web_search) {
        const webSearchMappings = availableMappings.filter(mapping => mapping.supports_web_search);
        if (webSearchMappings.length > 0) {
          availableMappings = webSearchMappings;
          apiLogger.info('Filtered to web search capable models', {
            total: mappings.length,
            afterPlanFilter: availableMappings.length + (mappings.length - availableMappings.length),
            afterWebSearchFilter: webSearchMappings.length,
            category: analysis.primary_category
          });
        } else {
          apiLogger.warn('No web search capable models in mappings, using fallback', {
            category: analysis.primary_category,
            availableMappings: availableMappings.length
          });
          return await this.getFallbackSelection(analysis, input);
        }
      }
      
      // 2.7. If image generation is needed, filter to only image generation capable models
      if (analysis.requirements.needs_image_generation || analysis.primary_category === 'image_generation') {
        const imageGenMappings = availableMappings.filter(mapping => {
          const model = (mapping as any).preloadedModelData;
          return model?.capabilities?.includes(ModelCapability.IMAGE_GENERATION) || 
                 model?.canonicalName?.includes('dall-e') || 
                 model?.canonicalName?.includes('gpt-image');
        });
        
        if (imageGenMappings.length > 0) {
          availableMappings = imageGenMappings;
          apiLogger.info('Filtered to image generation capable models', {
            total: mappings.length,
            afterPlanFilter: availableMappings.length + (mappings.length - availableMappings.length),
            afterImageGenFilter: imageGenMappings.length,
            category: analysis.primary_category
          });
        } else {
          apiLogger.warn('No image generation capable models in mappings, using fallback', {
            category: analysis.primary_category,
            availableMappings: availableMappings.length
          });
          return await this.getFallbackSelection(analysis, input);
        }
      }
      
      if (availableMappings.length === 0) {
        // No available mappings for user's plan, use fallback
        apiLogger.info('No mappings available for user plan, using fallback', {
          category: analysis.primary_category,
          userPlan: input.userPlan,
          totalMappings: mappings.length,
          planModels: planModels.length
        });
        return await this.getFallbackSelection(analysis, input);
      }
      
      // 3. MODEL SELECTION: Use configuration to choose method
      const selectionStart = performance.now();
      let selected: ModelMapping;
      
      if (ROUTER_CONFIG.useSimpleHighestScore) {
        // PROVEN FAST: 0ms instant selection with O3-optimized complexity-first algorithm
        selected = this.pickBestModelWithComplexityFirst(availableMappings, analysis, input);
      } else if (ROUTER_CONFIG.useThompsonSampling) {
        // SOPHISTICATED BUT SLOW: Thompson Sampling (disabled for performance)
        selected = await this.selectModelWithThompsonSampling(availableMappings, analysis, input, requestId);
      } else {
        // FALLBACK: Use complexity-first selection
        selected = this.pickBestModelWithComplexityFirst(availableMappings, analysis, input);
      }
      
      const selectionTime = performance.now() - selectionStart;
      
      // 4. OPTIONAL TRACKING: Use configuration
      if (ROUTER_CONFIG.enableSelectionTracking) {
        // SOPHISTICATED BUT SLOW: Selection tracking (disabled for performance)
        await this.trackSelection(selected, analysis, input);
      }
      
      // 5. OPTIONAL ALTERNATIVES: Use configuration
      let alternatives: any[] = [];
      if (ROUTER_CONFIG.enableAlternativeGeneration) {
        // SOPHISTICATED BUT SLOW: Alternative generation (disabled for performance)
        alternatives = await this.getAlternativeModels(mappings, selected.model_id);
      }
      
      apiLogger.info('Category-based routing decision', {
        category: analysis.primary_category,
        complexity: analysis.complexity,
        selectedModel: selected.model_id,
        confidence: analysis.confidence,
        timeMs: Date.now() - startTime
      });
      
      // Get the full model details using the database service
      // Note: selected.model_id should contain database IDs (UUIDs) from mapping table
      // But sometimes it might contain canonical names (e.g., from manual selection)
      // So we need to handle both cases
      const dbLookupStart = performance.now();
      const dbModel = await this.prisma.models.findFirst({
        where: {
          OR: [
            { id: selected.model_id },
            { canonicalName: selected.model_id }
          ]
        },
        // Using database-first architecture - no provider relations
      });
      const dbLookupTime = performance.now() - dbLookupStart;

      if (!dbModel) {
        throw new Error(`Selected model ${selected.model_id} not found in database`);
      }

      // Find the selected model in the already fetched plan models  
      // CRITICAL FIX: planModels have canonicalName stored in the 'id' field, not 'canonicalName'
      const selectedModel = planModels.find(m => m.id === dbModel.canonicalName);

      if (!selectedModel) {
        // Model not available for this user's plan - use fallback
        apiLogger.warn('Selected model not available for user plan', {
          selectedModel: dbModel.canonicalName,
          userPlan: input.userPlan,
          category: analysis.primary_category
        });
        return await this.getFallbackSelection(analysis, input);
      }

      // Calculate total time and log detailed timing breakdown
      const totalTime = performance.now() - startTime;
      
      // PERFORMANCE MONITORING: Log if performance target missed
      const performanceTargets = ROUTER_CONFIG.testSuiteConfig.performanceTargets;
      const isWithinTarget = totalTime <= performanceTargets.totalRouterTime;
      
      if (!isWithinTarget) {
        apiLogger.warn('Router performance target missed', {
          totalTime: Math.round(totalTime),
          target: performanceTargets.totalRouterTime,
          analysisTime: Math.round(analysisTime),
          selectionTime: Math.round(selectionTime),
          category: analysis.primary_category
        });
      }

      // Get AI SDK provider name from canonical name
      const aiSdkProviderName = this.getProviderFromCanonicalName(dbModel.canonicalName);
      
      return {
        modelId: dbModel.canonicalName, // Use canonical name for external APIs
        model: dbModel, // Use database model directly
        provider: dbModel.providerId, // Provider from database
        aiProvider: this.aiProviders.get(dbModel.providerId), // AI SDK provider instance
        reason: `Category: ${analysis.primary_category}, Complexity: ${analysis.complexity}`,
        reasoning: this.generateReasoning(selected, analysis),
        confidence: analysis.confidence,
        selectedModel: dbModel.canonicalName, // Use canonical name for consistency
        alternatives,
        metadata: {
          category: analysis.primary_category,
          complexity: analysis.complexity,
          promptAnalysis: analysis,
          routingMethod: 'ai_sdk_enhanced',
          aiSdkProvider: aiSdkProviderName
        }
      };
      
    } catch (error) {
      apiLogger.error('Category router failed', error);
      throw error;
    }
  }

  /**
   * Analyze prompt using LLM to determine category, complexity, and requirements
   */
  public async analyzePrompt(input: RouterInput): Promise<PromptAnalysis> {
    // Ensure schema is initialized before analysis
    await this.initializeSchema();
    
    // Check cache first
    const cacheKey = this.hashString(input.query);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.analysis;
    }
    
    const requestId = crypto.randomUUID();
    const startTime = Date.now();
    
    // Enhanced conversation context with sensible truncation
    const conversationContext = input.conversationHistory 
      ? `\nRecent conversation context:\n${input.conversationHistory.slice(-4).map(msg => {
          // Preserve much more context for meaningful analysis (1500 chars = ~300 words)
          const content = msg.content.length > 1500 ? 
            msg.content.slice(0, 1400) + '...[truncated]' : 
            msg.content;
          return `${msg.role}: ${content}`;
        }).join('\n')}`
      : '';
    
    // Extract conversation patterns for better analysis
    const hasRecentCode = input.conversationHistory?.some(msg => 
      /```|function|class|def|const|let|var|import|export|npm install|pip install|git clone/.test(msg.content)
    ) || false;
    
    const hasInstallationContext = input.conversationHistory?.some(msg =>
      /install|setup|configure|getting started|how to use|tutorial|guide/i.test(msg.content)
    ) || false;
    
    // Get more meaningful topic extraction (first 15 words instead of 5)
    const recentTopics = input.conversationHistory?.slice(-3)
      .filter(msg => msg.role === 'user')
      .map(msg => {
        const words = msg.content.split(' ').slice(0, 15).join(' ');
        return words.length > 80 ? words.slice(0, 80) + '...' : words;
      })
      .join(' | ') || '';
    
    const conversationPatterns = hasRecentCode || hasInstallationContext || recentTopics ? 
      `\nConversation patterns: ${hasRecentCode ? 'Contains code/technical commands. ' : ''}${hasInstallationContext ? 'Installation/setup focused. ' : ''}Recent topics: ${recentTopics}` : '';

    const prompt = `You are an expert AI model router. Analyze this user query and categorize it for optimal model selection.

Query: "${input.query}"
${conversationContext}${conversationPatterns}

Context:
- Conversation length: ${input.conversationLength} messages
- Has code blocks: ${input.hasCode}
- Has attachments: ${input.attachmentTypes?.join(', ') || 'none'}
- Web search enabled: ${input.webSearchEnabled || false}
- User plan: ${input.userPlan}

CRITICAL CATEGORIZATION RULES:
1. If query mentions "today", "current", "latest", "now", "happening", "news", "price", "weather", "stock market", "recent" → needs_web_search: true
2. Be CONFIDENT in your categorization - confidence should be between 0.70 and 0.95
3. Primary task type determines the category
4. Complexity: simple (simple/quick), standard (typical), difficult (challenging), complex (very hard)
5. For real-time queries, primary_category should be "current_events" or set needs_web_search: true
6. IMAGE GENERATION DETECTION:
   - If query asks to "generate", "create", "draw", "make", "paint" an image → primary_category: "image_generation"
   - Keywords: "generate image", "create image", "draw", "make an image", "paint", "dall-e", "stable diffusion"
   - Should NOT route to image_generation models directly, but flag for special handling
7. COST TOLERANCE RULES:
   - "low": Simple greetings, basic questions, quick translations, simple math, "how do I install X"
   - "medium": Standard coding help, creative writing, analysis, debugging, tutorials
   - "high": ONLY for complex reasoning, advanced math, research, multi-step analysis, complex problem-solving
   
7. CONVERSATION CONTEXT AWARENESS:
   - If conversation shows ongoing debugging or code review → may justify medium/high cost
   - Simple installation/setup questions in any context → prefer "low" cost tolerance
   - Follow-up questions to previous complex work → consider previous complexity

Examples:
- "Fix this Python bug" → coding, standard, needs_reasoning: true, max_cost_tolerance: medium, confidence: 0.90
- "Write a haiku about cats" → creative_writing, simple, max_cost_tolerance: low, confidence: 0.85
- "What's happening with Bitcoin today?" → current_events, standard, needs_web_search: true, max_cost_tolerance: medium, confidence: 0.95
- "Latest AI news" → current_events, standard, needs_web_search: true, max_cost_tolerance: medium, confidence: 0.90
- "Analyze this document" → analysis, complex, needs_large_context: true, max_cost_tolerance: high, confidence: 0.85
- "What's 2+2?" → math, simple, latency_sensitivity: high, max_cost_tolerance: low, confidence: 0.95
- "Hello" → general_chat, simple, max_cost_tolerance: low, confidence: 0.95
- "Generate an image of a sunset" → image_generation, standard, max_cost_tolerance: low, confidence: 0.95
- "Create a picture of a robot" → image_generation, standard, max_cost_tolerance: low, confidence: 0.90
- "Draw a landscape with mountains" → image_generation, standard, max_cost_tolerance: low, confidence: 0.90

Provide analysis in JSON format:
{
  "primary_category": "coding|creative_writing|general_chat|reasoning|math|analysis|translation|summarization|question_answering|data_analysis|debugging|tutorial|brainstorming|role_play|technical_writing|academic_writing|business_writing|legal|medical|scientific|philosophical|historical|current_events|personal_advice|image_generation|image_analysis|multimodal|other",
  "secondary_categories": ["optional array of secondary categories"],
  "complexity": "simple|standard|difficult|complex",
  "specific_attributes": {
    "language": "python/javascript/etc (for coding)",
    "framework": "react/tensorflow/etc (if applicable)",
    "task_type": "debugging/implementation/review/etc",
    "domain": "specific domain if applicable",
    "tone": "formal/casual/academic/etc",
    "urgency": "low|medium|high",
    "output_format": "code|essay|list|json|etc"
  },
  "requirements": {
    "needs_web_search": true/false,
    "search_queries": ["suggested search queries if web search needed"],
    "needs_vision": true/false,
    "needs_large_context": true/false,
    "needs_reasoning": true/false,
    "needs_citations": true/false,
    "expected_output_tokens": estimated_number,
    "latency_sensitivity": "low|medium|high",
    "needs_structured_output": true/false,
    "needs_function_calling": true/false,
    "max_cost_tolerance": "low|medium|high"
  },
  "confidence": 0.70-0.95 (BE CONFIDENT! Most queries should be 0.80-0.90),
  "reasoning": "Brief explanation of categorization and why this complexity/category"
}`;
    
    try {
      // Use optimal provider for analysis (we know providers are initialized)
      // Priority: Google > Groq > OpenAI > first available
      const providerPriority = ['google', 'groq', 'openai'];
      const [providerName, analysisProvider] = providerPriority
        .map(name => [name, this.aiProviders.get(name)] as const)
        .find(([_, provider]) => provider) || 
        Array.from(this.aiProviders.entries())[0];
      
      // Map provider to optimal analysis model
      const analysisModels: Record<string, string> = {
        google: 'gemini-2.5-flash-lite-preview-06-17',
        groq: 'llama3-8b-8192', 
        openai: 'gpt-4o-mini',
        anthropic: 'claude-3-5-haiku-20241022',
        deepseek: 'deepseek-chat'
      };
      
      const analysisModel = analysisModels[providerName] || this.ANALYSIS_MODEL;

      const response = await analysisProvider!.generateCompletion({
        model: analysisModel,
        messages: [
          {
            role: 'system',
            content: 'You are an expert at analyzing user queries and categorizing them for optimal AI model selection. Always respond with valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        maxTokens: 500
      });
      
      const responseText = response || '';
      const analysis = this.parseAnalysisResponse(responseText);
      
      const endTime = Date.now();
      
      // Log the router LLM call
      try {
        const systemPrompt = 'You are an expert at analyzing user queries and categorizing them for optimal AI model selection. Always respond with valid JSON.';
        const fullPrompt = `${systemPrompt}\n\nUser: ${prompt}`;
        
        await this.prisma.routerLLMLog.create({
          data: {
            requestId,
            userId: input.userId,
            sessionId: input.sessionId,
            conversationId: input.conversationId,
            inputPrompt: input.query,
            inputConversationLength: input.conversationLength,
            inputHasCode: input.hasCode,
            inputAttachmentTypes: input.attachmentTypes || [],
            inputWebSearchEnabled: input.webSearchEnabled || false,
            llmModel: analysisModel,
            llmPromptSent: fullPrompt,
            llmResponse: responseText,
            llmTemperature: 0.1,
            llmMaxTokens: 500,
            category: analysis.primary_category,
            complexity: analysis.complexity,
            confidence: analysis.confidence,
            requirements: analysis.requirements,
            specificAttributes: analysis.specific_attributes || {},
            latencyMs: endTime - startTime,
            inputTokens: this.estimateTokens(fullPrompt),
            outputTokens: this.estimateTokens(responseText),
            totalTokens: this.estimateTokens(fullPrompt) + this.estimateTokens(responseText),
            costUsd: this.calculateCost(fullPrompt, responseText, analysisModel),
            success: true
          }
        });
      } catch (logError) {
        apiLogger.error('Failed to log router LLM call', logError);
        // Don't fail the request due to logging error
      }
      
      // Cache the result
      this.cache.set(cacheKey, {
        analysis,
        timestamp: Date.now()
      });
      
      // Also cache in database for analytics
      await this.cacheAnalysisInDB(input.query, analysis);
      
      return analysis;
      
    } catch (error) {
      apiLogger.error('Prompt analysis failed', error);
      
      // Log the error
      try {
        await this.prisma.routerLLMLog.create({
          data: {
            requestId,
            userId: input.userId,
            sessionId: input.sessionId,
            conversationId: input.conversationId,
            inputPrompt: input.query,
            inputConversationLength: input.conversationLength,
            inputHasCode: input.hasCode,
            inputAttachmentTypes: input.attachmentTypes || [],
            inputWebSearchEnabled: input.webSearchEnabled || false,
            llmModel: this.ANALYSIS_MODEL, // Use fallback analysis model
            llmPromptSent: prompt,
            llmResponse: '',
            latencyMs: Date.now() - startTime,
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error'
          }
        });
      } catch (logError) {
        apiLogger.error('Failed to log router LLM error', logError);
      }
      
      // Return a basic fallback analysis
      return this.getFallbackAnalysis(input);
    }
  }

  /**
   * Get model mappings from database for category and complexity
   */
  public async getModelMappings(
    category: PromptCategory,
    complexity: PromptComplexity
  ): Promise<ModelMapping[]> {
    try {
      const mappings = await this.prisma.modelMapping.findMany({
        where: {
          category,
          enabled: true,
          score: { lt: 100 }, // CRITICAL: Exclude impossible perfect scores (fake/placeholder data)
          OR: [
            { complexityLevel: complexity },      // Exact complexity match (preferred)
            { complexityLevel: 'all' }            // Fallback to 'all' complexity
          ]
        },
        orderBy: [
          { score: 'desc' },
          { avgUserRating: 'desc' },
          { usageCount: 'desc' }
        ]
      });
      
      // CRITICAL: Filter out mappings for disabled models AND models without cost information
      // PERFORMANCE FIX: Include full model data to eliminate N+1 queries later
      const enabledModelsWithCosts = await this.prisma.models.findMany({
        where: { 
          id: { in: mappings.map(m => m.modelId) },
          isEnabled: true,
          // Filter models that have pricing data in extendedMetadata.pricing.input
          extendedMetadata: {
            path: '$.pricing.input',
            not: Prisma.JsonNull
          }
        },
        include: {
          provider: true  // Include provider data for complete model info
        }
      });
      
      const validModelIdSet = new Set(enabledModelsWithCosts.map(m => m.id));
      const validMappings = mappings.filter(m => validModelIdSet.has(m.modelId));
      
      // Create map of modelId -> model data for fast lookup
      const modelDataMap = new Map(enabledModelsWithCosts.map(model => [model.id, model]));
      
      const mappedResults = validMappings.map((m: any) => ({
        ...m,
        created_at: m.createdAt,
        updated_at: m.updatedAt,
        model_id: m.modelId,
        category: m.category as PromptCategory,
        complexity_level: m.complexityLevel as PromptComplexity | 'all',
        usage_count: m.usageCount,
        success_count: m.successCount,
        failure_count: m.failureCount,
        avg_user_rating: m.avgUserRating ? Number(m.avgUserRating) : undefined,
        specific_attributes: m.specificAttributes as Record<string, string> | undefined,
        // PERFORMANCE FIX: Attach preloaded model data to eliminate individual queries
        preloadedModelData: modelDataMap.get(m.modelId)
      }));
      
      // Log complexity mapping usage for monitoring
      const exactMatches = mappedResults.filter(m => m.complexity_level === complexity).length;
      const fallbackMatches = mappedResults.filter(m => m.complexity_level === 'all').length;
      
      apiLogger.info('Model mapping complexity resolution', {
        category,
        requestedComplexity: complexity,
        exactMatches,
        fallbackMatches,
        totalMappings: mappedResults.length,
        complexityLevelsFound: [...new Set(mappedResults.map(m => m.complexity_level))]
      });
      
      return mappedResults;
    } catch (error) {
      apiLogger.error('Failed to get model mappings', error);
      return [];
    }
  }

  /**
   * ADVANCED MODEL SELECTION: Thompson Sampling with Contextual Factors
   * 
   * STATUS: PRESERVED BUT DISABLED - Performance bottleneck identified
   * EVIDENCE: /tmp/router-timing-breakdown.js showed 500-2000ms for 91 models
   * BOTTLENECK: Promise.all processing with beta distribution sampling
   * 
   * FUTURE OPTIMIZATIONS when re-enabling:
   * - Batch processing instead of Promise.all
   * - Limit to top N models (e.g., 10 instead of 91)
   * - Background processing for non-critical selections
   * - Caching of Thompson Sampling results
   * - Precompute contextual scores offline
   * 
   * TOGGLE: Set useThompsonSampling: true in ROUTER_CONFIG to re-enable
   * 
   * SOPHISTICATED FEATURES PRESERVED:
   * - Beta distribution sampling for exploration vs exploitation
   * - Advanced contextual scoring with task-specific performance
   * - Cost penalty calculation with complexity awareness
   * - Latency sensitivity scoring
   * - Multi-armed bandit optimization
   * - User plan and tolerance considerations
   */
  public async selectModelWithThompsonSampling(
    mappings: ModelMapping[],
    analysis: PromptAnalysis,
    input: RouterInput,
    requestId?: string
  ): Promise<ModelMapping> {
    // CRITICAL: Filter out models without valid scoring data to avoid false scoring
    const validMappings = mappings.filter(mapping => {
      // Must have a valid score (not null, undefined, or 0)
      const hasValidScore = mapping.score != null && mapping.score > 0;
      
      if (!hasValidScore) {
        apiLogger.warn('Filtering out mapping without valid score', {
          modelId: mapping.model_id,
          score: mapping.score,
          category: mapping.category,
          complexity: mapping.complexity_level
        });
        return false;
      }
      
      return true;
    });
    
    if (validMappings.length === 0) {
      apiLogger.error('No valid mappings with scoring data found', {
        totalMappings: mappings.length,
        category: analysis.primary_category,
        complexity: analysis.complexity,
        mappingsWithoutScores: mappings.filter(m => !m.score || m.score <= 0).length
      });
      throw new Error(`No valid model mappings with scoring data found for category: ${analysis.primary_category}, complexity: ${analysis.complexity}`);
    }
    
    if (validMappings.length < mappings.length) {
      apiLogger.info('Filtered mappings without valid scores', {
        originalCount: mappings.length,
        validCount: validMappings.length,
        filteredOut: mappings.length - validMappings.length,
        category: analysis.primary_category
      });
    }
    
    // Get user plan cost multiplier
    const costMultiplier = this.getCostMultiplier(input.userPlan);
    
    // For each VALID model, calculate contextual score
    const scoredMappings = await Promise.all(validMappings.map(async mapping => {
      const alpha = mapping.success_count + 1; // +1 for Laplace smoothing
      const beta = mapping.failure_count + 1;
      
      // Thompson Sampling: sample from Beta(alpha, beta)
      const thompsonSample = this.sampleBeta(alpha, beta);
      
      // Base scores
      const baseScore = mapping.score / 100; // Normalize to 0-1
      const ratingBoost = mapping.avg_user_rating ? mapping.avg_user_rating / 5 : 0.5;
      
      // PERFORMANCE FIX: Use preloaded model data instead of individual database queries
      const model = (mapping as any).preloadedModelData;
      
      // Skip mappings where model couldn't be found (shouldn't happen with our filtering)
      if (!model) {
        apiLogger.warn('Skipping mapping: preloaded model data missing', { 
          mappingId: mapping.id, 
          modelId: mapping.model_id 
        });
        return null;
      }
      
      // Contextual adjustments
      const contextualScore = this.calculateContextualScore(mapping, analysis, model, input);
      
      // Cost penalty based on user plan and complexity
      const costPenalty = await this.calculateCostPenalty(model, costMultiplier, analysis.requirements.max_cost_tolerance || 'medium', requestId, analysis.complexity);
      
      // O3-PRO ENHANCEMENT: Plan-aware cost multiplier for quality preference
      const planMultiplier = this.getPlanCostMultiplier(input.userPlan, analysis.complexity);
      
      // Latency bonus/penalty
      const latencyScore = this.calculateLatencyScore(model, analysis.requirements.latency_sensitivity);
      
      // Final weighted combination
      const finalScore = (
        thompsonSample * 0.3 +      // 30% Thompson sampling (exploration)
        baseScore * 0.25 +          // 25% manual curation
        ratingBoost * 0.15 +        // 15% user ratings
        contextualScore * 0.2 +     // 20% contextual fit
        latencyScore * 0.1          // 10% latency considerations
      ) * costPenalty * planMultiplier; // Apply cost penalty and plan multiplier
      
      return {
        mapping,
        model,
        finalScore,
        thompsonSample,
        contextualScore,
        costPenalty,
        planMultiplier,
        latencyScore,
        breakdown: {
          thompson: thompsonSample * 0.3,
          base: baseScore * 0.25,
          rating: ratingBoost * 0.15,
          contextual: contextualScore * 0.2,
          latency: latencyScore * 0.1,
          costMultiplier: costPenalty,
          planMultiplier: planMultiplier
        }
      };
    }));
    
    // Filter out null results (models that couldn't be found)
    let validScoredMappings = scoredMappings.filter(Boolean);
    
    if (validScoredMappings.length === 0) {
      throw new Error('No valid models found after fetching model details');
    }

    // O3-PRO ENHANCEMENT: Hard guarantee for MAX users on complex coding tasks
    if (input.userPlan === 'MAX' && analysis.primary_category === 'coding' && analysis.complexity === 'complex') {
      const premiumModels = validScoredMappings.filter(s => 
        s && s.model?.canonicalName?.toLowerCase().includes('gpt-4') ||
        s && s.model?.canonicalName?.toLowerCase().includes('claude') ||
        s && s.model?.canonicalName?.toLowerCase().includes('sonnet') ||
        s && s.model?.canonicalName?.toLowerCase().includes('o3')
      );
      
      if (premiumModels.length > 0) {
        // Boost premium models to the top for MAX users on complex coding
        validScoredMappings = [
          ...premiumModels.map(p => p ? ({ ...p, finalScore: p.finalScore * 1.2 }) : p), // 20% boost
          ...validScoredMappings.filter(s => !premiumModels.includes(s))
        ].sort((a, b) => (b?.finalScore || 0) - (a?.finalScore || 0));
      }
    }
    
    // Sort by final score and select best
    validScoredMappings.sort((a, b) => (b?.finalScore || 0) - (a?.finalScore || 0));
    
    let selected = validScoredMappings[0];
    
    if (!selected) {
      throw new Error('No valid models found for selection');
    }
    
    // Smart selection for simple queries: prefer cheaper models with randomness
    if (analysis.complexity === 'simple' || 
        (analysis.complexity === 'standard' && analysis.primary_category === 'general_chat')) {
      
      const simpleSelected = this.selectBestModelForSimpleQuery(validScoredMappings, analysis);
      if (simpleSelected) {
        selected = simpleSelected;
      }
    }
    
    apiLogger.info('Enhanced model selection', {
      category: analysis.primary_category,
      complexity: analysis.complexity,
      selectedModel: selected!.mapping.model_id,
      selectedCanonicalName: selected!.model?.canonicalName,
      selectedComplexityLevel: selected!.mapping.complexity_level,
      finalScore: selected!.finalScore,
      breakdown: selected!.breakdown,
      userPlan: input.userPlan,
      latencySensitivity: analysis.requirements.latency_sensitivity,
      maxCostTolerance: analysis.requirements.max_cost_tolerance,
      successRate: selected!.mapping.success_count / (selected!.mapping.success_count + selected!.mapping.failure_count + 1),
      taskScore: this.getTaskSpecificScore(selected!.model, analysis.primary_category),
      hasTaskScores: !!selected!.model?.metadata?.taskScores,
      modelCosts: {
        input: selected!.model?.pricing?.input,
        output: selected!.model?.pricing?.output,
        costCategory: selected!.model?.metadata?.costCategory
      },
      topAlternatives: scoredMappings.slice(1, 3).map(s => ({
        model: s?.mapping?.model_id || 'unknown',
        canonicalName: s?.model?.canonicalName || 'unknown',
        score: s?.finalScore || 0,
        taskScore: this.getTaskSpecificScore(s?.model, analysis.primary_category),
        costPenalty: s?.costPenalty || 0
      })),
      allModelsConsidered: scoredMappings.map(s => ({
        modelId: s?.mapping?.model_id || 'unknown',
        canonicalName: s?.model?.canonicalName || 'unknown',
        finalScore: s?.finalScore || 0,
        costPenalty: s?.costPenalty || 0,
        thompsonSample: s?.thompsonSample || 0,
        contextualScore: s?.contextualScore || 0
      }))
    });
    
    return selected!.mapping;
  }

  /**
   * Smart model selection for simple queries: optimize for user experience (speed + cost)
   */
  public selectBestModelForSimpleQuery(
    scoredMappings: any[],
    analysis: PromptAnalysis
  ): any {
    // Define ideal models for simple queries (fast + cheap + reliable)
    const idealForSimple = [
      'google/gemini-2.5-flash-lite-preview-06-17', // Perfect middle ground
      'google/gemini-2.0-flash-exp',                // Fast and capable
      'deepseek/deepseek-chat',                     // Fast and cheap
      'anthropic/claude-3.5-haiku',                 // Fast Anthropic
      'openai/gpt-4o-mini',                         // Fast OpenAI (not O4!)
    ];
    
    // First: Check if any ideal models are available
    // CRITICAL FIX: Use correct structure from Thompson Sampling
    const idealMatches = scoredMappings.filter(s => {
      const modelName = s.model?.canonicalName || s.mapping?.model_id;
      return idealForSimple.includes(modelName) && s.finalScore > 0.1; // Fixed: scores are 0-1 range
    });
    
    if (idealMatches.length > 0) {
      // Prefer Gemini 2.5 Flash Lite for simple queries (great UX)
      const geminiLite = idealMatches.find(m => {
        const modelName = m.model?.canonicalName || m.mapping?.model_id;
        return modelName === 'google/gemini-2.5-flash-lite-preview-06-17';
      });
      
      if (geminiLite) {
        apiLogger.info('Ideal model selected for simple query', {
          selectedModel: geminiLite.model?.canonicalName,
          reason: 'Gemini 2.5 Flash Lite - optimal speed/cost balance for simple queries'
        });
        return geminiLite;
      }
      
      // Otherwise pick randomly from ideal matches for variety
      const selected = idealMatches[Math.floor(Math.random() * idealMatches.length)];
      apiLogger.info('Ideal model selected for simple query', {
        selectedModel: selected.model?.canonicalName,
        availableIdeal: idealMatches.map(m => m.model?.canonicalName),
        reason: 'Random selection from ideal fast+cheap models'
      });
      return selected;
    }
    
    // Second: Exclude expensive models and pick from remaining
    const excludedCategories = ['REASONING', 'FRONTIER'];
    const affordableOptions = scoredMappings.filter(s => 
      !excludedCategories.includes(s.model?.metadata?.costCategory) &&
      s.finalScore > 10
    );
    
    if (affordableOptions.length > 0) {
      // Prefer FREE models, but allow STANDARD if they're much better
      const freeModels = affordableOptions.filter(s => s.model?.metadata?.costCategory === 'FREE');
      const standardModels = affordableOptions.filter(s => s.model?.metadata?.costCategory === 'STANDARD');
      
      let selected;
      if (freeModels.length > 0 && (standardModels.length === 0 || freeModels[0].finalScore >= standardModels[0].finalScore * 0.8)) {
        // Use FREE model if it's competitive
        selected = freeModels[Math.floor(Math.random() * Math.min(3, freeModels.length))];
      } else if (standardModels.length > 0) {
        // Use STANDARD model if significantly better
        selected = standardModels[0];
      } else {
        selected = affordableOptions[0];
      }
      
      apiLogger.info('Affordable model selected for simple query', {
        selectedModel: selected.model?.canonicalName,
        costCategory: selected.model?.metadata?.costCategory,
        freeAvailable: freeModels.length,
        standardAvailable: standardModels.length,
        reason: 'Best affordable option for simple query'
      });
      return selected;
    }
    
    // Last resort: use original top choice but log warning
    apiLogger.warn('No ideal models found for simple query, using top scorer', {
      selectedModel: scoredMappings[0].model?.canonicalName,
      costCategory: scoredMappings[0].model?.metadata?.costCategory
    });
    return scoredMappings[0];
  }

  /**
   * ADVANCED ANALYTICS: Selection Tracking for Feedback Learning
   * 
   * STATUS: PRESERVED BUT DISABLED - Performance optimization
   * EVIDENCE: Estimated 50-100ms overhead per request
   * 
   * FUTURE RE-ENABLING for:
   * - User feedback learning and model improvement
   * - Performance analytics and monitoring
   * - A/B testing capabilities
   * - Usage pattern analysis
   * - Model success rate tracking
   * 
   * OPTIMIZATIONS when re-enabling:
   * - Background processing to minimize performance impact
   * - Batch database writes
   * - Async fire-and-forget logging
   * - Redis queue for deferred processing
   * 
   * TOGGLE: Set enableSelectionTracking: true in ROUTER_CONFIG
   */
  private async trackSelection(
    mapping: ModelMapping,
    analysis: PromptAnalysis,
    input: RouterInput
  ): Promise<void> {
    try {
      // Log to router selection table
      await this.prisma.routerSelectionLog.create({
        data: {
          requestId: crypto.randomUUID(),
          selectedModel: mapping.model_id,
          reason: this.generateReasoning(mapping, analysis),
          category: analysis.primary_category,
          complexity: analysis.complexity,
          confidence: analysis.confidence,
          promptAnalysis: analysis,
          userId: input.userId,
          sessionId: input.sessionId || input.conversationId
        }
      });
      
      // Increment usage count
      await this.prisma.modelMapping.update({
        where: { id: mapping.id },
        data: { usageCount: { increment: 1 } }
      });
    } catch (error) {
      apiLogger.error('Failed to track selection', error);
      // Don't throw - tracking is non-critical
    }
  }

  /**
   * ADVANCED TRANSPARENCY: Alternative Model Generation
   * 
   * STATUS: PRESERVED BUT DISABLED - Performance optimization
   * EVIDENCE: Estimated 100-200ms overhead per request
   * 
   * FUTURE RE-ENABLING for:
   * - User choice and transparency
   * - A/B testing model alternatives
   * - Fallback option generation
   * - Model comparison features
   * 
   * OPTIMIZATIONS when re-enabling:
   * - Cache alternative calculations
   * - Precompute common alternatives
   * - Limit to top 3 alternatives only
   * - Background generation for next request
   * 
   * TOGGLE: Set enableAlternativeGeneration: true in ROUTER_CONFIG
   */
  private async getAlternativeModels(
    mappings: ModelMapping[],
    selectedId: string
  ): Promise<ModelOption[]> {
    const alternativeMappings = mappings.filter(m => m.model_id !== selectedId).slice(0, 3);
    
    const alternatives: ModelOption[] = [];
    for (const mapping of alternativeMappings) {
      try {
        const model = await this.prisma.models.findFirst({
          where: { canonicalName: mapping.model_id },
          // Using database-first architecture - no provider relations
        });
        
        if (model) {
          alternatives.push({
            id: model.canonicalName,
            name: model.displayName,
            provider: model.providerId,
            score: mapping.score
          });
        }
      } catch (error) {
        // Skip if model not found
        continue;
      }
    }
    
    return alternatives;
  }
  
  /**
   * Get cost multiplier based on user plan
   */
  private getCostMultiplier(userPlan: string): number {
    switch (userPlan.toLowerCase()) {
      case 'free':
      case 'trial':
        return 0.5; // Heavy cost penalty for free users
      case 'basic':
      case 'starter':
        return 0.7; // Moderate cost penalty
      case 'pro':
      case 'premium':
        return 0.9; // Light cost penalty
      case 'enterprise':
      case 'unlimited':
        return 1.0; // No cost penalty
      default:
        return 0.8; // Default moderate penalty
    }
  }

  /**
   * Calculate contextual score based on specific attributes match and task-specific performance
   */
  private calculateContextualScore(
    mapping: ModelMapping,
    analysis: PromptAnalysis,
    model: any,
    input: RouterInput
  ): number {
    let score = 0.3; // Lower base score to make room for task-specific scoring
    
    // TASK-SPECIFIC SCORING (40% of contextual score)
    const taskScore = this.getTaskSpecificScore(model, analysis.primary_category);
    if (taskScore > 0) {
      score += (taskScore / 10) * 0.4; // Convert 0-10 scale to 0-0.4 contribution
    }
    
    // Check if model has required capabilities (30% of contextual score)
    if (analysis.requirements.needs_vision && model?.capabilities?.includes('vision')) {
      score += 0.3;
    }
    
    if (analysis.requirements.needs_large_context && model?.contextLength > 100000) {
      score += 0.2;
    }
    
    if (analysis.requirements.needs_function_calling && model?.capabilities?.includes('function_calling')) {
      score += 0.2;
    }
    
    // Check specific attributes match (20% of contextual score)
    if (mapping.specific_attributes) {
      const attrs = analysis.specific_attributes;
      if (attrs?.language && mapping.specific_attributes[attrs.language]) {
        score += 0.15;
      }
      if (attrs?.framework && mapping.specific_attributes[attrs.framework]) {
        score += 0.1;
      }
    }
    
    // Conversation length considerations (10% of contextual score)
    if (input.conversationLength > 10 && model?.contextLength > 50000) {
      score += 0.1; // Bonus for long conversations with large context models
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Get task-specific performance score for a model based on category
   */
  private getTaskSpecificScore(model: any, category: string): number {
    if (!model?.metadata?.taskScores) {
      return 0; // No task scores available
    }
    
    const taskScores = model.extendedMetadata.taskScores;
    
    // Map analysis categories to task score keys
    const categoryMapping: Record<string, keyof typeof taskScores> = {
      'coding': 'cod',
      'debugging': 'cod',
      'creative_writing': 'cre',
      'reasoning': 'rea',
      'math': 'mat',
      'analysis': 'ana',
      'data_analysis': 'ana',
      'translation': 'lng',
      'general_chat': 'cha',
      'question_answering': 'cha',
      'summarization': 'ana',
      'technical_writing': 'cre',
      'academic_writing': 'cre',
      'business_writing': 'cre',
      'scientific': 'rea',
      'philosophical': 'rea',
      'image_analysis': 'vis',
      'multimodal': 'vis',
      'current_events': 'ana',
      'tutorial': 'cha',
      'brainstorming': 'cre',
      'role_play': 'cha'
    };
    
    const taskKey = categoryMapping[category];
    if (!taskKey || !taskScores[taskKey]) {
      // Fallback to general chat score if no specific mapping
      return taskScores.cha || 0;
    }
    
    return taskScores[taskKey] || 0;
  }

  /**
   * Determine appropriate cost tolerance based on category, complexity, and urgency
   */
  /**
   * O3-PRO ENHANCEMENT: Plan-aware cost multiplier for quality vs cost balance
   * MAX plan users get quality preference for complex tasks
   */
  private getPlanCostMultiplier(userPlan: string, complexity: string): number {
    const planCostMultiplierMatrix: Record<string, Record<string, number>> = {
      'FREE': { simple: 1.0, standard: 1.0, difficult: 1.0, complex: 1.0 },
      'PLUS': { simple: 1.0, standard: 1.0, difficult: 1.0, complex: 1.0 },
      'ADVANCED': { simple: 1.0, standard: 1.0, difficult: 1.05, complex: 1.05 },
      'MAX': { simple: 1.0, standard: 1.0, difficult: 1.1, complex: 1.15 },
      'ENTERPRISE': { simple: 1.0, standard: 1.0, difficult: 1.1, complex: 1.15 }
    };

    return planCostMultiplierMatrix[userPlan]?.[complexity] || 1.0;
  }

  /**
   * ULTRA THINK MODE: Force best available model for paid users
   * Based on O3-Pro recommendations for premium model selection
   * Uses database metadata instead of hardcoded model names
   */
  public async routeUltraThink(input: RouterInput): Promise<RouterDecision> {
    const startTime = performance.now();
    const requestId = crypto.randomUUID();
    
    console.log('[Router] 🧠 ULTRA THINK DEBUG - Starting with plan:', input.userPlan);
    
    // Check user plan access
    if (!['PLUS', 'ADVANCED', 'MAX', 'ENTERPRISE'].includes(input.userPlan)) {
      console.error('[Router] ❌ ULTRA THINK plan check failed:', input.userPlan);
      throw new Error('ULTRA THINK mode requires PLUS plan or higher');
    }
    
    console.log('[Router] ✅ ULTRA THINK plan check passed for:', input.userPlan);
    
    apiLogger.info('🧠 ULTRA THINK mode activated', {
      requestId,
      userPlan: input.userPlan,
      query: input.query.slice(0, 100) + '...'
    });
    
    // Analyze prompt to understand task complexity
    const analysis = await this.analyzePrompt(input);
    console.log('[Router] 🎯 ULTRA THINK analysis:', {
      category: analysis.primary_category,
      complexity: analysis.complexity,
      expectedTokens: analysis.requirements.expected_output_tokens,
      confidence: analysis.confidence,
      reasoning: analysis.reasoning
    });
    
    // Smart cost tolerance based on task complexity, not just "high"
    const isSimpleTask = analysis.complexity === 'simple' && 
                        analysis.primary_category === 'general_chat' &&
                        (analysis.requirements.expected_output_tokens || 0) < 100;
                        
    if (isSimpleTask) {
      // Keep original cost tolerance for simple tasks
      analysis.requirements.max_cost_tolerance = this.determineCostTolerance(
        analysis.primary_category, 
        analysis.complexity
      );
      console.log('[Router] 🚀 Simple task detected - using cost-effective premium models');
    } else {
      // Use high cost tolerance for complex tasks
      analysis.requirements.max_cost_tolerance = 'high';
      console.log('[Router] 🎯 Complex task detected - using highest tier models');
    }
    
    // Get all models available to user plan
    const availableModels = await getModelsForPlan(input.userPlan as UserPlan);
    console.log('[Router] 📊 Available models for plan:', availableModels.length);
    
    // Smart premium model filtering based on task complexity
    const premiumModels = availableModels.filter(model => {
      const canonicalName = model.id.toLowerCase();
      const metadata = model.metadata || {};
      
      // Exclude O3-Pro specifically - too slow for practical use
      if (canonicalName.includes('o3-pro')) {
        return false;
      }
      
      // For simple tasks, use "Premium Fast" tier
      if (isSimpleTask) {
        // Premium Fast: Fast models with good quality for simple tasks
        const isPremiumFast = 
          canonicalName.includes('gemini-2.5-flash') ||
          canonicalName.includes('gemini-2.0-flash') ||
          canonicalName.includes('claude-3.5-haiku') ||
          canonicalName.includes('gpt-4o-mini') ||
          canonicalName.includes('deepseek-chat') ||
          (metadata.costCategory === 'BUDGET' && (metadata.intelligenceScore || 0) >= 85) ||
          (metadata.costCategory === 'STANDARD' && (metadata.intelligenceScore || 0) >= 90);
          
        return isPremiumFast;
      }
      
      // For complex tasks, use full premium range (Premium Smart + Premium Max)
      const isPremiumByCost = metadata.costCategory === 'PREMIUM' || metadata.costCategory === 'FRONTIER';
      
      // Use intelligence score as backup indicator
      const isHighIntelligence = (metadata.intelligenceScore || 0) >= 80;
      
      // Use capabilities as another indicator
      const hasAdvancedCapabilities = metadata.capabilities && Array.isArray(metadata.capabilities) && (
        metadata.capabilities.includes('reasoning') ||
        metadata.capabilities.includes('advanced_reasoning') ||
        metadata.capabilities.includes('code_generation')
      );
      
      const isPremium = isPremiumByCost || isHighIntelligence || hasAdvancedCapabilities;
      
      if (isPremium || isSimpleTask) {
        const tier = isSimpleTask ? 'Premium Fast' : 'Premium Full';
        console.log(`[Router] 🎯 Found ${tier} model:`, {
          id: model.id,
          costCategory: metadata.costCategory,
          intelligenceScore: metadata.intelligenceScore,
          tier: tier
        });
      }
      
      return isPremium;
    });
    
    console.log('[Router] 💎 Premium models found:', premiumModels.length);
    
    if (premiumModels.length === 0) {
      // Log some models to debug why none are premium
      console.log('[Router] 🔍 Sample models for debugging:', availableModels.slice(0, 3).map(m => ({
        id: m.id,
        costCategory: m.metadata?.costCategory,
        intelligenceScore: m.metadata?.intelligenceScore,
        capabilities: m.metadata?.capabilities
      })));
      
      // Fallback to regular routing if no premium models available
      apiLogger.warn('No premium models available for ULTRA THINK, falling back to regular routing');
      return this.route(input);
    }
    
    // Smart ranking based on task complexity
    const rankedPremiumModels = premiumModels.sort((a, b) => {
      const getQualityScore = (model: any) => {
        const metadata = model.metadata || {};
        const canonicalName = model.id.toLowerCase();
        
        // For simple tasks, prioritize speed and cost efficiency
        if (isSimpleTask) {
          // Prefer fast, efficient models for simple general chat
          if (canonicalName.includes('gemini-2.5-flash-lite')) return 100; // Top choice for simple chat
          if (canonicalName.includes('gemini-2.0-flash')) return 95;
          if (canonicalName.includes('claude-3.5-haiku')) return 90;
          if (canonicalName.includes('gpt-4o-mini')) return 85;
          if (canonicalName.includes('deepseek-chat')) return 80;
          
          // Penalize expensive models for simple tasks
          if (metadata.costCategory === 'FRONTIER') return 40; // Too expensive for simple chat
          if (metadata.costCategory === 'PREMIUM') return 60;  // Moderately penalized
          
          return metadata.intelligenceScore || 70; // Use intelligence as fallback
        }
        
        // For complex tasks, prioritize intelligence and capabilities
        if (metadata.intelligenceScore && typeof metadata.intelligenceScore === 'number') {
          return metadata.intelligenceScore;
        }
        
        // Use cost category as quality indicator for complex tasks
        if (metadata.costCategory === 'FRONTIER') return 100;
        if (metadata.costCategory === 'PREMIUM') return 90;
        if (metadata.costCategory === 'STANDARD') return 70;
        
        // Use performance score if available
        if (metadata.performanceScore && typeof metadata.performanceScore === 'number') {
          return metadata.performanceScore;
        }
        
        // Default fallback
        return 50;
      };
      
      return getQualityScore(b) - getQualityScore(a);
    });
    
    const selectedModel = rankedPremiumModels[0];
    const endTime = performance.now();
    
    const tier = isSimpleTask ? 'Premium Fast' : 'Premium Full';
    
    apiLogger.info(`🎯 ULTRA THINK ${tier} model selected`, {
      requestId,
      selectedModel: selectedModel.id,
      intelligenceScore: selectedModel.metadata?.intelligenceScore,
      costCategory: selectedModel.metadata?.costCategory,
      taskComplexity: analysis.complexity,
      taskCategory: analysis.primary_category,
      tier: tier,
      isSimpleTask: isSimpleTask,
      totalTime: endTime - startTime,
      availablePremiumModels: premiumModels.length,
      userPlan: input.userPlan
    });
    
    // Return RouterDecision format to match the regular route method
    return {
      modelId: selectedModel.id,
      model: selectedModel as any, // Type conversion for legacy Model to ModelWithRelations
      provider: selectedModel.provider || 'unknown',
      reason: `ULTRA THINK ${tier} - optimized for ${analysis.complexity} ${analysis.primary_category}`,
      reasoning: [
        'ULTRA THINK activated', 
        `Task: ${analysis.primary_category} (${analysis.complexity})`,
        `Tier: ${tier}`,
        `Selected model: ${selectedModel.id}`, 
        `Intelligence: ${selectedModel.metadata?.intelligenceScore || 'N/A'}`,
        `Cost: ${selectedModel.metadata?.costCategory || 'N/A'}`
      ],
      confidence: 100, // Maximum confidence for ULTRA THINK
      selectedModel: selectedModel.id, // For compatibility
      metrics: {
        estimatedCredits: 0, // Will be calculated later
        estimatedLatency: selectedModel.metadata?.averageLatency || 0,
        latency: endTime - startTime
      },
      alternatives: rankedPremiumModels.slice(1, 4).map(model => ({
        id: model.id,
        name: model.name || model.id,
        provider: model.provider || 'unknown',
        score: model.metadata?.intelligenceScore || 50
      })),
      metadata: {
        routingMethod: 'ultra_think',
        requestId,
        modelSelectionReason: 'ULTRA THINK mode - best available model forced',
        totalTime: endTime - startTime,
        userPlan: input.userPlan,
        premiumModelsAvailable: premiumModels.length,
        selectedModelMetadata: selectedModel.metadata,
        dataBasedSelection: true // Flag to indicate we used database metadata
      }
    };
  }

  private determineCostTolerance(
    category: string, 
    complexity: string = 'standard', 
    urgency: string = 'medium'
  ): 'low' | 'medium' | 'high' {
    // Complex tasks should generally allow higher costs
    if (complexity === 'complex') {
      // Complex coding, analysis, reasoning tasks justify high cost
      if (['coding', 'debugging', 'analysis', 'reasoning', 'math'].includes(category)) {
        return 'high';
      }
      // Other complex tasks get medium cost tolerance  
      return 'medium';
    }
    
    // Difficult tasks get medium cost tolerance for technical categories
    if (complexity === 'difficult') {
      if (['coding', 'debugging', 'analysis', 'reasoning'].includes(category)) {
        return 'medium';
      }
      return 'low';
    }
    
    // High urgency may justify higher costs
    if (urgency === 'high') {
      return complexity === 'standard' ? 'medium' : 'high';
    }
    
    // Simple tasks should use low cost models
    if (complexity === 'simple') {
      return 'low';
    }
    
    // Standard complexity gets medium cost tolerance
    return 'medium';
  }

  /**
   * Calculate cost penalty based on model pricing, user tolerance, and complexity level
   */
  public async calculateCostPenalty(
    model: any,
    costMultiplier: number,
    maxCostTolerance?: string,
    requestId?: string,
    complexity?: string
  ): Promise<number> {
    if (!model?.pricing) {
      // PERFORMANCE FIX: Skip warning logs during Thompson Sampling to prevent slowdown
      // apiLogger.warn('Model missing pricing data', {
      //   modelId: model?.id,
      //   canonicalName: model?.canonicalName
      // });
      return 1.0;
    }
    
    // Check multiple locations for pricing data - prioritize inputCost/outputCost as more accurate
    const inputCost = model.metadata?.inputCost || model.inputCost || model.pricing?.input || 0;
    const outputCost = model.metadata?.outputCost || model.outputCost || model.pricing?.output || 0;
    const avgCost = (inputCost + outputCost) / 2;
    
    // Check for different model categories requiring different penalties
    // O3-PRO FIX: Map null/unknown cost category to STANDARD instead of FREE
    const rawCostCategory = model.metadata?.costCategory;
    const costCategory = rawCostCategory || 'STANDARD'; // Default to STANDARD, not FREE
    const isFrontierModel = costCategory === 'FRONTIER' || avgCost > 10.0;
    const isReasoningModel = costCategory === 'REASONING';
    
    // Define cost thresholds (per 1M tokens)
    const lowCost = 0.5;    // $0.50
    const mediumCost = 2.0; // $2.00  
    const highCost = 10.0;  // $10.00
    const frontierCost = 30.0; // $30.00+
    
    let penalty = 1.0;
    let penaltyReason = 'no_penalty';
    
    // Apply cost penalties based on model category and user tolerance
    if (costCategory === 'FREE') {
      // FREE models: Always preferred for low/medium cost tolerance
      if (maxCostTolerance === 'low') {
        penalty = 2.0; // 100% bonus for trivial queries - strong preference
        penaltyReason = 'free_model_strong_bonus';
      } else if (maxCostTolerance === 'medium') {
        penalty = 1.3; // 30% bonus for medium tolerance
        penaltyReason = 'free_model_medium_bonus';
      } else {
        penalty = 1.0; // No penalty for high tolerance
        penaltyReason = 'free_model_no_penalty';
      }
    } else if (isFrontierModel) {
      // FRONTIER models: Heavy penalties except for complex tasks with high tolerance
      if (maxCostTolerance === 'high') {
        penalty = 0.6; // Still substantial penalty even with high tolerance
        penaltyReason = 'frontier_high_tolerance';
      } else if (maxCostTolerance === 'medium') {
        penalty = 0.1; // Heavy penalty for medium tolerance
        penaltyReason = 'frontier_medium_tolerance';
      } else {
        penalty = 0.02; // Very heavy penalty for low tolerance
        penaltyReason = 'frontier_low_tolerance';
      }
    } else if (costCategory === 'PREMIUM') {
      // PREMIUM models: Should be viable for complex tasks with high tolerance
      if (maxCostTolerance === 'high') {
        penalty = 0.95; // Minimal penalty for high tolerance complex tasks
        penaltyReason = 'premium_high_tolerance';
      } else if (maxCostTolerance === 'medium') {
        penalty = 0.7; // Moderate penalty for medium tolerance
        penaltyReason = 'premium_medium_tolerance';
      } else {
        penalty = 0.3; // Significant penalty for low tolerance
        penaltyReason = 'premium_low_tolerance';
      }
    } else if (costCategory === 'STANDARD') {
      // O3-PRO FIX: STANDARD models (unknown pricing) - neutral penalty, no bonus
      penalty = 1.0; // Neutral - no bonus or penalty
      penaltyReason = 'standard_neutral_pricing';
    } else if (isReasoningModel) {
      // REASONING models: Complexity-aware penalties - excellent for complex tasks, expensive for simple ones
      // This implements the full 5-level complexity system: trivial/standard/difficult/complex/all
      if (complexity === 'complex') {
        // Complex tasks: reasoning models are well-suited
        penalty = maxCostTolerance === 'high' ? 1.1 : (maxCostTolerance === 'medium' ? 0.8 : 0.4);
        penaltyReason = `reasoning_complex_${maxCostTolerance || 'medium'}`;
      } else if (complexity === 'difficult') {
        // Difficult tasks: reasoning models are good but not essential
        penalty = maxCostTolerance === 'high' ? 0.9 : (maxCostTolerance === 'medium' ? 0.5 : 0.2);
        penaltyReason = `reasoning_difficult_${maxCostTolerance || 'medium'}`;
      } else if (complexity === 'standard') {
        // Standard tasks: reasoning models are overkill but acceptable for high tolerance
        penalty = maxCostTolerance === 'high' ? 0.6 : (maxCostTolerance === 'medium' ? 0.15 : 0.05);
        penaltyReason = `reasoning_standard_${maxCostTolerance || 'medium'}`;
      } else {
        // Simple tasks: reasoning models are wasteful
        penalty = maxCostTolerance === 'high' ? 0.3 : 0.02;
        penaltyReason = `reasoning_simple_${maxCostTolerance || 'low'}`;
      }
    } else if (avgCost > highCost) {
      penalty = maxCostTolerance === 'high' ? 0.7 : 0.1; // Stronger penalties (was 0.9 : 0.3)
      penaltyReason = 'high_cost';
    } else if (avgCost > mediumCost) {
      penalty = maxCostTolerance === 'low' ? 0.1 : 0.5; // Much more aggressive for low tolerance
      penaltyReason = 'medium_cost';
    } else if (avgCost > lowCost) {
      penalty = maxCostTolerance === 'low' ? 0.7 : 0.95; // Penalize even standard cost models for low tolerance
      penaltyReason = 'low_cost';
    }
    
    const finalPenalty = penalty * costMultiplier;
    
    // PERFORMANCE FIX: Skip Redis debug logging during Thompson Sampling to prevent hanging
    // (Redis operations can timeout and cause Promise.all to hang when called in a loop)
    // Redis Debug: Log detailed cost penalty calculation
    // if (requestId) {
    //   await this.logDebugData(`cost_penalty:${requestId}:${model.canonicalName}`, {
    //     modelId: model.id,
    //     canonicalName: model.canonicalName,
    //     inputCost,
    //     outputCost,
    //     avgCost,
    //     costCategory,
    //     isFrontierModel,
    //     isReasoningModel,
    //     complexity,
    //     maxCostTolerance,
    //     costMultiplier,
    //     penalty,
    //     finalPenalty,
    //     penaltyReason,
    //     thresholds: { lowCost, mediumCost, highCost, frontierCost }
    //   });
    // }
    
    // Production logging (always on)
    apiLogger.info('Cost penalty calculation', {
      modelId: model.id,
      canonicalName: model.canonicalName,
      inputCost,
      outputCost,
      avgCost,
      costCategory,
      isFrontierModel,
      isReasoningModel,
      complexity,
      maxCostTolerance,
      finalPenalty,
      penaltyReason
    });
    
    return finalPenalty;
  }

  /**
   * Calculate latency score based on model speed and requirements - ENHANCED with real data
   */
  private calculateLatencyScore(
    model: any,
    latencySensitivity: string
  ): number {
    let latencyScore = 0.5; // Default neutral score
    
    // Use real speed category data from metadata if available
    const speedCategory = model?.metadata?.speed;
    if (speedCategory) {
      switch (speedCategory) {
        case 'ultra-fast':
          latencyScore = 1.0;  // Flash Lite models - excellent speed
          break;
        case 'very-fast':
          latencyScore = 0.9;  // Flash 8B models - very good speed
          break;
        case 'fast':
          latencyScore = 0.8;  // Regular Flash models - good speed
          break;
        case 'medium':
          latencyScore = 0.6;  // Thinking models - decent speed
          break;
        case 'slow':
          latencyScore = 0.3;  // Poor speed
          break;
        default:
          latencyScore = 0.5;  // Unknown speed
      }
    } 
    // Use numerical speed score if available (0-100 scale)
    else if (model?.metadata?.speedScore) {
      const speedScore = parseFloat(model.extendedMetadata.speedScore);
      latencyScore = speedScore / 100; // Convert 0-100 to 0-1 scale
    }
    // Fallback to performance metrics if speed category not available
    else if (model?.metadata?.performance) {
      const perf = model.extendedMetadata.performance;
      
      // Check tokens per second (higher is better)
      if (perf.tokensPerSecond) {
        const tps = parseFloat(perf.tokensPerSecond);
        if (tps >= 100) latencyScore = 1.0;      // Very fast
        else if (tps >= 50) latencyScore = 0.8;  // Fast
        else if (tps >= 20) latencyScore = 0.6;  // Medium
        else latencyScore = 0.4;                 // Slow
      }
      // Check output speed (tokens/sec, higher is better)
      else if (perf.outputSpeed) {
        const outputSpeed = parseFloat(perf.outputSpeed);
        if (outputSpeed >= 150) latencyScore = 1.0;     // Very fast
        else if (outputSpeed >= 100) latencyScore = 0.8; // Fast
        else if (outputSpeed >= 50) latencyScore = 0.6;  // Medium
        else latencyScore = 0.4;                         // Slow
      }
      // Check speed score (0-100 scale)
      else if (perf.speedScore) {
        const speedScore = parseFloat(perf.speedScore);
        latencyScore = speedScore / 100; // Convert 0-100 to 0-1
      }
    }
    // Fallback to responseTime if available (lower is better)
    else if (model?.metadata?.responseTime) {
      const responseTime = parseFloat(model.extendedMetadata.responseTime);
      if (responseTime < 1000) latencyScore = 1.0;      // < 1s = excellent
      else if (responseTime < 3000) latencyScore = 0.7; // < 3s = good
      else if (responseTime < 5000) latencyScore = 0.5; // < 5s = medium
      else latencyScore = 0.3;                          // >= 5s = slow
    }
    
    // Adjust based on user's latency sensitivity
    switch (latencySensitivity) {
      case 'high':
        // High sensitivity: full weight to speed, penalty for slow models
        return latencyScore < 0.5 ? latencyScore * 0.5 : latencyScore;
      case 'medium':
        // Medium sensitivity: blend with neutral
        return 0.3 + (latencyScore * 0.7); // 30% neutral + 70% speed score
      case 'low':
      default:
        // Low sensitivity: mostly neutral with slight speed preference
        return 0.7 + (latencyScore * 0.3); // 70% neutral + 30% speed score
    }
  }

  /**
   * Get AI SDK provider name from model canonical name
   */
  private getProviderFromCanonicalName(canonicalName: string): AISDKProviderName | null {
    const providerPrefix = canonicalName.split('/')[0];
    
    switch (providerPrefix) {
      case 'openai':
        return AI_SDK_PROVIDERS.OPENAI;
      case 'anthropic':
        return AI_SDK_PROVIDERS.ANTHROPIC;
      case 'google':
      case 'gemini':
        return AI_SDK_PROVIDERS.GOOGLE;
      case 'mistral':
        return AI_SDK_PROVIDERS.MISTRAL;
      case 'groq':
        return AI_SDK_PROVIDERS.GROQ;
      case 'together':
      case 'together_ai':
        return AI_SDK_PROVIDERS.TOGETHER;
      case 'xai':
        return AI_SDK_PROVIDERS.XAI;
      case 'deepseek':
        return AI_SDK_PROVIDERS.DEEPSEEK;
      case 'perplexity':
        return AI_SDK_PROVIDERS.PERPLEXITY;
      case 'cohere':
        return AI_SDK_PROVIDERS.COHERE;
      case 'openrouter':
        return AI_SDK_PROVIDERS.OPENROUTER;
      case 'qwen':
      case 'alibaba':
        return AI_SDK_PROVIDERS.QWEN;
      default:
        return null;
    }
  }

  /**
   * Get model details from database
   */
  private async getModelDetails(modelId: string): Promise<any> {
    try {
      const model = await this.prisma.models.findFirst({
        where: { id: modelId },  // Use id instead of canonicalName since mapping.model_id is the database ID
        // Using database-first architecture - no provider relations
      });
      return model;
    } catch (error) {
      apiLogger.warn('Failed to get model details', { modelId, error });
      return null;
    }
  }
  
  /**
   * Generate reasoning explanation
   */
  private generateReasoning(
    mapping: ModelMapping,
    analysis: PromptAnalysis
  ): string {
    const successRate = mapping.usage_count > 0
      ? (mapping.success_count / mapping.usage_count * 100).toFixed(1)
      : 'N/A';
    
    return `Selected ${mapping.model_id} for ${analysis.primary_category} (${analysis.complexity}) ` +
           `based on: score=${mapping.score}, success_rate=${successRate}%, ` +
           `usage=${mapping.usage_count}, rating=${mapping.avg_user_rating?.toFixed(1) || 'N/A'}`;
  }
  
  /**
   * Parse LLM analysis response
   */
  private parseAnalysisResponse(response: string): PromptAnalysis {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in analysis response');
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      const validated = PromptAnalysisSchema.parse(parsed);
      
      return validated as PromptAnalysis;
    } catch (error) {
      apiLogger.error('Failed to parse analysis response', error);
      throw error;
    }
  }
  
  /**
   * Cache analysis in database
   */
  private async cacheAnalysisInDB(
    prompt: string,
    analysis: PromptAnalysis
  ): Promise<void> {
    try {
      const promptHash = this.hashString(prompt);
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
      
      await this.prisma.promptAnalysisCache.upsert({
        where: { promptHash },
        update: {
          analysis,
          hitCount: { increment: 1 },
          expiresAt
        },
        create: {
          promptHash,
          analysis,
          expiresAt
        }
      });
    } catch (error) {
      apiLogger.error('Failed to cache analysis in DB', error);
      // Non-critical, don't throw
    }
  }
  
  /**
   * Get fallback analysis when LLM fails
   */
  protected getFallbackAnalysis(input: RouterInput): PromptAnalysis {
    const query = input.query.toLowerCase();
    const wordCount = query.split(' ').length;
    
    // Enhanced real-time detection keywords
    const realtimeKeywords = [
      'today', 'current', 'latest', 'now', 'happening',
      'news', 'price', 'weather', 'stock', 'market',
      'yesterday', 'this week', 'recently', 'trending',
      'live', 'real-time', 'update', 'forecast'
    ];
    
    const needsWebSearch = realtimeKeywords.some(keyword => query.includes(keyword));
    
    // Simple heuristic categorization
    let category: PromptCategory = 'general_chat';
    let complexity: PromptComplexity = 'standard';
    
    if (needsWebSearch) {
      category = 'current_events';
    } else if (input.hasCode || /code|function|debug|implement|python|javascript/.test(query)) {
      category = 'coding';
      complexity = wordCount > 50 ? 'complex' : 'standard';
    } else if (/write|story|poem|creative|essay|article/.test(query)) {
      category = 'creative_writing';
    } else if (/math|calculate|solve|equation|derivative|integral/.test(query)) {
      category = 'math';
    } else if (/analyze|explain|reason|why|how|compare|pros.*cons/.test(query)) {
      category = 'reasoning';
    } else if (wordCount <= 5) {
      complexity = 'simple';
    }
    
    return {
      primary_category: category,
      complexity,
      specific_attributes: {},
      requirements: {
        needs_web_search: needsWebSearch || input.webSearchEnabled || false,
        needs_vision: input.attachmentTypes?.some(t => t.startsWith('image')) || false,
        needs_large_context: input.conversationLength > 10,
        needs_reasoning: category === 'reasoning' || category === 'coding',
        needs_citations: needsWebSearch,
        expected_output_tokens: wordCount * 10,
        latency_sensitivity: complexity === 'simple' ? 'high' : 'medium'
      },
      confidence: 0.75, // Higher confidence for fallback
      reasoning: `Fallback analysis: ${needsWebSearch ? 'Detected real-time query' : 'Standard categorization'}`
    };
  }
  
  /**
   * Fallback selection when no mappings exist
   */
  protected async getFallbackSelection(
    analysis: PromptAnalysis,
    input: RouterInput
  ): Promise<RouterDecision> {
    apiLogger.info('Fallback selection triggered', {
      category: analysis.primary_category,
      complexity: analysis.complexity,
      userPlan: input.userPlan
    });
    
    // Get models available for user's plan (normalize case)
    const normalizedUserPlan = input.userPlan.toUpperCase();
    const models = await getModelsForPlan(normalizedUserPlan as any);
    
    if (models.length === 0) {
      throw new Error('No models available for your plan');
    }
    
    // Filter by required capabilities
    let eligibleModels = models;
    
    // Filter for vision if needed
    if (analysis.requirements.needs_vision) {
      eligibleModels = eligibleModels.filter(m => 
        m.metadata?.supportsVision || 
        m.capabilities?.includes(ModelCapability.VISION)
      );
    }
    
    // Filter for image generation if needed
    if (analysis.requirements.needs_image_generation || analysis.primary_category === 'image_generation') {
      const imageGenEligible = eligibleModels.filter(m => 
        m.capabilities?.includes(ModelCapability.IMAGE_GENERATION) ||
        m.id?.includes('dall-e') ||
        m.id?.includes('gpt-image')
      );
      
      if (imageGenEligible.length > 0) {
        eligibleModels = imageGenEligible;
      } else {
        apiLogger.warn('No image generation models available, falling back to regular models');
      }
    }
    
    // Filter for web search if needed
    if (analysis.requirements.needs_web_search) {
      const webSearchModels = [
        'gpt-4o-search-preview', 
        'grok-3-mini', 
        'grok-2', 
        'grok-3', 
        'perplexity-sonar-pro', 
        'perplexity-sonar', 
        'perplexity-fast'
      ];
      const searchEligible = eligibleModels.filter(m => 
        webSearchModels.some(searchModel => m.id.includes(searchModel)) ||
        m.metadata?.supportsWebSearch ||
        m.capabilities?.includes(ModelCapability.WEB_SEARCH)
      );
      
      if (searchEligible.length > 0) {
        eligibleModels = searchEligible;
      } else {
        apiLogger.warn('No web search models available, falling back to regular models');
      }
    }
    
    // Filter by context size if needed
    if (analysis.requirements.needs_large_context) {
      eligibleModels = eligibleModels.filter(m => 
        (m.metadata?.contextWindow || m.contextWindow) >= 32000
      );
    }
    
    if (eligibleModels.length === 0) {
      eligibleModels = models; // Fall back to all models if no matches
    }
    
    // Pick based on complexity and cost
    let selected: Model;
    
    if (analysis.complexity === 'simple') {
      // Pick cheapest/fastest - prefer known good budget models
      const budgetModels = eligibleModels.filter(m => 
        m.id.includes('gemini-2.5-flash-lite') ||
        m.id.includes('gemini-1.5-flash-8b') ||
        m.id.includes('deepseek-chat') ||
        m.id.includes('gemini-1.5-flash')
      );
      
      if (budgetModels.length > 0) {
        // Sort budget models by cost
        selected = budgetModels.sort((a, b) => {
          const aCost = (a.inputCost + a.outputCost) / 2;
          const bCost = (b.inputCost + b.outputCost) / 2;
          return aCost - bCost;
        })[0];
      } else {
        // Fallback to cheapest available
        selected = eligibleModels.sort((a, b) => {
          const aCost = (a.inputCost + a.outputCost) / 2;
          const bCost = (b.inputCost + b.outputCost) / 2;
          return aCost - bCost;
        })[0];
      }
    } else if (analysis.complexity === 'complex') {
      // Pick a powerful model
      const powerfulModels = eligibleModels.filter(m => 
        m.name.includes('gpt-4o') ||
        m.name.includes('claude-3.5-sonnet') ||
        m.name.includes('gemini-2.0-pro') ||
        m.name.includes('deepseek-v3')
      );
      selected = powerfulModels[0] || eligibleModels[0];
    } else {
      // Pick a balanced mid-tier model
      const midTier = eligibleModels.filter(m => 
        m.name.includes('gemini-2.5-flash-lite') ||
        m.name.includes('gpt-4o-mini') ||
        m.name.includes('claude-3-haiku') ||
        m.name.includes('gemini-2.0-flash')
      );
      selected = midTier[0] || eligibleModels[0];
    }
    
    apiLogger.info('Fallback model selected', {
      selectedModel: selected?.id,
      selectedName: selected?.name,
      totalAvailable: eligibleModels.length,
      category: analysis.primary_category
    });
    
    // Get AI SDK provider name for fallback selection
    const aiSdkProviderName = this.getProviderFromCanonicalName(selected.id);
    
    return {
      modelId: selected.id,
      model: selected as any, // Type conversion for legacy Model to ModelWithRelations
      provider: selected.provider,
      aiProvider: this.aiProviders.get(selected.provider), // AI SDK provider instance
      reason: `Fallback selection: no mappings for ${analysis.primary_category}`,
      reasoning: `Fallback selection: ${selected.name} (no mappings for ${analysis.primary_category})`,
      confidence: analysis.confidence * 0.5,
      selectedModel: selected.id, // For compatibility
      metadata: {
        category: analysis.primary_category,
        complexity: analysis.complexity,
        promptAnalysis: analysis,
        routingMethod: 'ai_sdk_fallback',
        aiSdkProvider: aiSdkProviderName
      }
    };
  }
  
  /**
   * Sample from Beta distribution for Thompson Sampling
   */
  private sampleBeta(alpha: number, beta: number): number {
    // Using Joehnk's method for Beta sampling
    let u, v, x, y;
    
    do {
      u = Math.random();
      v = Math.random();
      x = Math.pow(u, 1 / alpha);
      y = Math.pow(v, 1 / beta);
    } while (x + y > 1);
    
    return x / (x + y);
  }
  
  /**
   * Hash string for caching
   */
  private hashString(str: string): string {
    return crypto.createHash('sha256').update(str).digest('hex').substring(0, 16);
  }

  /**
   * Track user feedback when actions occur
   */
  async trackFeedback(data: {
    modelId: string;
    sessionId: string;
    actionType: 'completion' | 'regeneration' | 'switch' | 'edit' | 'copy' | 'rating';
    actionDetails?: {
      switched_to?: string;
      edit_ratio?: number;
      time_to_action?: number;
      explicit_rating?: number;
    };
    userId?: string;
  }): Promise<void> {
    try {
      // Get the most recent router selection for this session
      const recentSelection = await this.prisma.routerSelectionLog.findFirst({
        where: {
          sessionId: data.sessionId,
          selectedModel: data.modelId
        },
        orderBy: { timestamp: 'desc' }
      });
      
      if (!recentSelection) {
        apiLogger.warn('No router selection found for feedback', data);
        return;
      }
      
      // Calculate implicit satisfaction score
      let satisfactionScore = 0.5; // Neutral default
      
      switch (data.actionType) {
        case 'completion':
          satisfactionScore = 0.8; // Good
          break;
        case 'copy':
          satisfactionScore = 0.9; // Very good
          break;
        case 'regeneration':
          satisfactionScore = 0.3; // Poor
          break;
        case 'switch':
          satisfactionScore = 0.2; // Very poor
          break;
        case 'edit':
          satisfactionScore = data.actionDetails?.edit_ratio
            ? 0.7 - (data.actionDetails.edit_ratio * 0.5) // More edits = lower score
            : 0.5;
          break;
        case 'rating':
          satisfactionScore = (data.actionDetails?.explicit_rating || 3) / 5;
          break;
      }
      
      // Record feedback
      await this.prisma.userFeedback.create({
        data: {
          userId: data.userId,
          sessionId: data.sessionId,
          modelId: data.modelId,
          category: recentSelection.category,
          complexity: recentSelection.complexity,
          actionType: data.actionType,
          actionDetails: data.actionDetails,
          implicitSatisfactionScore: satisfactionScore
        }
      });
      
      // Update model mapping success/failure counts
      const mapping = await this.prisma.modelMapping.findFirst({
        where: {
          category: recentSelection.category,
          modelId: data.modelId,
          OR: [
            { complexityLevel: recentSelection.complexity },
            { complexityLevel: 'all' }
          ]
        }
      });
      
      if (mapping) {
        const isSuccess = satisfactionScore >= 0.6;
        await this.prisma.modelMapping.update({
          where: { id: mapping.id },
          data: isSuccess
            ? { successCount: { increment: 1 } }
            : { failureCount: { increment: 1 } }
        });
      }
      
      apiLogger.info('Feedback tracked', {
        modelId: data.modelId,
        actionType: data.actionType,
        satisfactionScore,
        category: recentSelection.category
      });
      
    } catch (error) {
      apiLogger.error('Failed to track feedback', error);
      // Non-critical, don't throw
    }
  }
  
  /**
   * Estimate token count for a string
   */
  private estimateTokens(text: string): number {
    // Simple estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }
  
  /**
   * Calculate cost for LLM usage
   */
  private calculateCost(input: string, output: string, model: string): number {
    const inputTokens = this.estimateTokens(input);
    const outputTokens = this.estimateTokens(output);
    
    // Costs per 1M tokens for Gemini Flash
    const inputCostPer1M = 0.05;
    const outputCostPer1M = 0.15;
    
    const inputCost = (inputTokens / 1_000_000) * inputCostPer1M;
    const outputCost = (outputTokens / 1_000_000) * outputCostPer1M;
    
    return inputCost + outputCost;
  }
  
  /**
   * VALIDATION CAPABILITIES: Expose test validation for external use
   */
  async runValidationTests(): Promise<any> {
    if (!this.testValidator) {
      throw new Error('Test validator not initialized');
    }
    return await this.testValidator.runFullValidation();
  }

  async runPerformanceTests(): Promise<any> {
    if (!this.testValidator) {
      throw new Error('Test validator not initialized');
    }
    return await this.testValidator.runPerformanceTests();
  }

  async runAccuracyTests(): Promise<any> {
    if (!this.testValidator) {
      throw new Error('Test validator not initialized');
    }
    return await this.testValidator.runAccuracyTests();
  }

  async runSpeedTests(): Promise<any> {
    if (!this.testValidator) {
      throw new Error('Test validator not initialized');
    }
    return await this.testValidator.runSpeedTests();
  }

  /**
   * Get current router configuration
   */
  getRouterConfig(): RouterPerformanceConfig {
    return ROUTER_CONFIG;
  }

  /**
   * Get test configuration
   */
  getTestConfig(): TestSuiteConfig {
    return PROVEN_TEST_CONFIG;
  }

  /**
   * O3-OPTIMIZED COMPLEXITY-FIRST MODEL SELECTION
   * 
   * Replaces naive "highest score first" with intelligent cost-aware selection.
   * Uses (category + complexity) matrix to set cost caps, preventing expensive
   * models from being selected for simple tasks.
   * 
   * PERFORMANCE: <1ms (simple array operations)
   * COST SAVINGS: 125x reduction for simple tasks ("yo" $0.60 vs $80)
   * COVERAGE: All 29 categories with appropriate cost thresholds
   */
  private pickBestModelWithComplexityFirst(
    availableMappings: ModelMapping[],
    analysis: PromptAnalysis,
    input: RouterInput
  ): ModelMapping {
    // O3-designed cost threshold matrix for all 29 categories
    const PRICE_CAP: Record<string, Record<string, number>> = {
      /* --------------  COMMUNICATION & WRITING --------------- */
      business_writing:   { simple: 1, standard: 2, difficult: 5,  complex: 15 },
      technical_writing:  { simple: 1, standard: 2, difficult: 5,  complex: 15 },
      translation:        { simple: 1, standard: 2, difficult: 5,  complex: 15 },
      summarization:      { simple: 1, standard: 2, difficult: 5,  complex: 15 },

      /* ------------------  CREATIVE TASKS -------------------- */
      brainstorming:      { simple: 2, standard: 4, difficult:10,  complex: 25 },
      creative:           { simple: 2, standard: 4, difficult:10,  complex: 25 },
      creative_writing:   { simple: 2, standard: 4, difficult:10,  complex: 25 },
      role_play:          { simple: 2, standard: 4, difficult:10,  complex: 25 },

      /* --------------  INFORMATION RETRIEVAL ----------------- */
      current_events:     { simple: 1, standard: 3, difficult: 8,  complex: 20 },
      historical:         { simple: 1, standard: 3, difficult: 8,  complex: 20 },
      web_search:         { simple: 1, standard: 3, difficult: 8,  complex: 20 },
      question_answering: { simple: 1, standard: 3, difficult: 8,  complex: 20 },
      tutorial:           { simple: 1, standard: 3, difficult: 8,  complex: 20 },

      /* ------------------  CODING & DEBUG -------------------- */
      coding:             { simple: 2, standard: 5, difficult:15,  complex: 30 },
      debugging:          { simple: 2, standard: 6, difficult:20,  complex: 40 },

      /* --------------  ANALYSIS / REASONING ------------------ */
      analysis:           { simple: 3, standard: 8, difficult:25,  complex: 75 },
      data_analysis:      { simple: 3, standard: 8, difficult:25,  complex: 75 },
      scientific:         { simple: 3, standard: 8, difficult:25,  complex: 75 },
      reasoning:          { simple: 5, standard:10, difficult:30,  complex:100 },
      philosophical:      { simple: 2, standard: 5, difficult:15,  complex: 40 },

      /* --------------  HIGH-ACCURACY DOMAINS ----------------- */
      medical:            { simple: 5, standard:15, difficult:40,  complex:120 },
      legal:              { simple: 5, standard:15, difficult:40,  complex:120 },

      /* -------------------  NUMERICAL ------------------------ */
      math:               { simple: 1, standard: 3, difficult:10,  complex: 30 },

      /* ------------------  MULTIMODAL ------------------------ */
      image_analysis:     { simple: 2, standard: 5, difficult:15,  complex: 40 },
      multimodal:         { simple: 2, standard: 5, difficult:15,  complex: 40 },
      image_generation:   { simple: 3, standard: 8, difficult:25,  complex: 60 },

      /* ----------------  SOCIAL / PERSONAL ------------------- */
      personal_advice:    { simple: 2, standard: 5, difficult:15,  complex: 40 },

      /* ------------------  GENERAL & OTHER ------------------- */
      general_chat:       { simple: 1, standard: 1, difficult: 1,  complex: 1  },
      other:              { simple: 3, standard: 6, difficult:15,  complex: 30 }
    };

    // Speed ranking for tie-breaking
    const SPEED_RANK: Record<string, number> = { 
      fast: 0, medium: 1, slow: 2, null: 1 
    };

    // User plan cost caps
    const planCap = (userPlan: string): number => {
      const plan = (userPlan || 'FREE').toUpperCase();
      return { FREE: 1, BASIC: 10, MAX: 9999 }[plan] || 1;
    };

    // 1. Get cost cap from (category + complexity) matrix
    const category = analysis.primary_category;
    const complexity = analysis.complexity;
    const categoryThresholds = PRICE_CAP[category] || PRICE_CAP.other;
    const complexityThreshold = categoryThresholds[complexity] || categoryThresholds.standard;
    
    let cap = Math.min(complexityThreshold, planCap(input.userPlan));

    // 2. Enhance mappings with cost data from preloaded model data
    const mappingsWithCost = availableMappings.map(mapping => {
      const modelData = (mapping as any).preloadedModelData;
      // O3-PRO FIX: Assume mid-range cost for unknown pricing instead of making it "free"
      const outputCost = modelData?.metadata?.pricing?.output || 
                        (modelData?.metadata?.costCategory ? 999 : 15); // 15 for unknown, 999 for explicit missing
      const speed = modelData?.metadata?.speed || 'medium';
      
      return {
        ...mapping,
        cost: outputCost,
        speed: speed,
        modelData: modelData
      };
    });

    // 3. Filter models under cost cap
    let candidates = mappingsWithCost.filter(m => m.cost <= cap);

    // 4. If empty, gradually raise cap (step-up with fallbacks)
    if (candidates.length === 0) {
      const bumps = [cap * 2, cap * 4, planCap(input.userPlan)];
      for (const newCap of bumps) {
        cap = Math.min(newCap, planCap(input.userPlan));
        candidates = mappingsWithCost.filter(m => m.cost <= cap);
        if (candidates.length > 0) {
          apiLogger.info('Cost cap stepped up due to no candidates', {
            originalCap: complexityThreshold,
            newCap: cap,
            category,
            complexity,
            candidatesFound: candidates.length
          });
          break;
        }
      }
    }

    // 5. Still empty → fall back to cheapest available model
    if (candidates.length === 0) {
      const cheapest = mappingsWithCost.reduce((a, b) => (a.cost < b.cost ? a : b));
      apiLogger.warn('No models within cost cap, using cheapest available', {
        category,
        complexity,
        requestedCap: complexityThreshold,
        selectedModel: cheapest.modelData?.canonicalName,
        selectedCost: cheapest.cost
      });
      return cheapest;
    }

    // 6. Rank candidates (speed first if latency_sensitive, else score)
    candidates.sort((a, b) => {
      if (analysis.requirements.latency_sensitivity === 'high') {
        return SPEED_RANK[a.speed] - SPEED_RANK[b.speed] ||
               b.score - a.score;
      }
      return b.score - a.score ||
             SPEED_RANK[a.speed] - SPEED_RANK[b.speed];
    });

    const selected = candidates[0];
    
    // Log selection decision for monitoring
    apiLogger.info('Complexity-first model selection', {
      category,
      complexity,
      costCap: cap,
      selectedModel: selected.modelData?.canonicalName,
      selectedCost: selected.cost,
      selectedScore: selected.score,
      candidatesConsidered: candidates.length,
      totalAvailable: availableMappings.length,
      costSavings: availableMappings.length > 0 ? 
        `${Math.round((mappingsWithCost[0].cost / selected.cost) * 100) / 100}x vs highest cost` : 'N/A'
    });

    return selected;
  }

  /**
   * Clean up any resources
   */
  async cleanup(): Promise<void> {
    // Clean up AI SDK providers
    this.aiProviders.clear();
    
    // Disconnect database
    await this.prisma.$disconnect();
    
    apiLogger.info('Router cleanup completed');
  }
}

// Export singleton instance
export const intelligentRouter = new IntelligentCategoryRouter();

