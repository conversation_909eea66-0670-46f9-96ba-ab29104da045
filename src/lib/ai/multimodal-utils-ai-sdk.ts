/**
 * Utilities for handling multimodal inputs (images, PDFs, etc.) using AI SDK
 * Migrated from LiteLLM to AI SDK in Phase 7.2
 */

import { Attachment, AttachmentType } from '@/types';
import { apiLogger } from '@/lib/logger';

/**
 * Models that support native file/image inputs (AI SDK format)
 * Updated for AI SDK provider compatibility
 */
export const MULTIMODAL_MODELS = {
  // OpenAI models with vision support
  openai: [
    'gpt-4o',
    'gpt-4o-mini', 
    'gpt-4o-2024-08-06',
    'gpt-4o-2024-11-20',
    'gpt-4o-mini-2024-07-18',
    'gpt-4-turbo',
    'gpt-4-turbo-2024-04-09',
    'gpt-4-vision-preview',
    'gpt-4.5',
    'gpt-4.5-mini',
  ],
  
  // Anthropic models with vision support
  anthropic: [
    'claude-3-opus',
    'claude-3-sonnet', 
    'claude-3-haiku',
    'claude-3.5-sonnet',
    'claude-3.5-haiku',
    'claude-3-5-sonnet-20241022',
    'claude-3-5-haiku-20241022',
    'claude-3.7-sonnet',
    'claude-4-opus',
    'claude-4-sonnet',
  ],
  
  // Google models with vision support
  google: [
    'gemini-1.5-pro',
    'gemini-1.5-pro-latest',
    'gemini-1.5-flash',
    'gemini-1.5-flash-latest',
    'gemini-2.0-flash',
    'gemini-2.5-pro',
    'gemini-2.5-flash',
    'gemini-2.5-flash-preview-05-20',
  ],
  
  // xAI models with vision support
  xai: [
    'grok-2-vision-1212',
    'grok-vision-beta',
  ],
  
  // Together AI models with vision support
  together: [
    'meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo',
    'meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo',
    'meta-llama/Llama-Vision-Free',
  ],
  
  // OpenRouter models with vision support
  openrouter: [
    'openai/gpt-4o',
    'openai/gpt-4o-mini',
    'anthropic/claude-3.5-sonnet',
    'google/gemini-flash-1.5',
  ]
};

/**
 * Get all multimodal models as a flat array
 */
export function getAllMultimodalModels(): string[] {
  return Object.values(MULTIMODAL_MODELS).flat();
}

/**
 * Check if a model supports multimodal inputs
 */
export function supportsMultimodal(modelId: string): boolean {
  // Normalize model ID by removing provider prefix for comparison
  const normalizedModel = modelId.includes('/') ? modelId.split('/').slice(1).join('/') : modelId;
  
  const allModels = getAllMultimodalModels();
  
  // Check exact match
  if (allModels.includes(normalizedModel) || allModels.includes(modelId)) {
    return true;
  }
  
  // Check partial matches for known patterns
  const multimodalPatterns = [
    'gpt-4o',
    'gpt-4-turbo', 
    'gpt-4-vision',
    'claude-3',
    'claude-4',
    'gemini-1.5',
    'gemini-2.0',
    'gemini-2.5',
    'grok-2-vision',
    'grok-vision',
    'llama-3.2',
    'vision'
  ];
  
  return multimodalPatterns.some(pattern => 
    normalizedModel.toLowerCase().includes(pattern.toLowerCase()) ||
    modelId.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * Check if a model supports web search natively
 */
export function supportsWebSearchNative(modelId: string): boolean {
  const webSearchPatterns = [
    'grok-2',
    'grok-3', 
    'perplexity',
    'sonar',
    'search-preview',
    'gemini' // Google models have web search capabilities
  ];
  
  return webSearchPatterns.some(pattern => 
    modelId.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * Convert attachment to AI SDK message content format
 */
export async function formatAttachmentForAISDK(
  attachment: Attachment,
  modelId: string
): Promise<any> {
  try {
    switch (attachment.type) {
      case AttachmentType.IMAGE:
        // AI SDK image format
        return {
          type: 'image',
          image: attachment.url.startsWith('http') 
            ? new URL(attachment.url) // URL format for AI SDK
            : attachment.url // Base64 format
        };
        
      case AttachmentType.DOCUMENT:
        // For models that support document input
        if (supportsDocumentInput(modelId)) {
          return {
            type: 'file',
            data: attachment.url,
            mimeType: attachment.mimeType || 'application/pdf'
          };
        } else {
          // Fallback: convert to text if possible
          return {
            type: 'text',
            text: `[Document: ${attachment.name || 'Attached file'}]`
          };
        }
        
      case AttachmentType.AUDIO:
        // Only certain models support audio
        if (supportsAudioInput(modelId)) {
          return {
            type: 'audio',
            data: attachment.url,
            mimeType: attachment.mimeType || 'audio/mpeg'
          };
        } else {
          return {
            type: 'text', 
            text: `[Audio file: ${attachment.name || 'audio'}]`
          };
        }
        
      default:
        return {
          type: 'text',
          text: `[Unsupported attachment: ${attachment.name || 'file'}]`
        };
    }
  } catch (error) {
    apiLogger.error('Failed to format attachment for AI SDK', {
      error: error instanceof Error ? error.message : 'Unknown error',
      attachmentType: attachment.type,
      modelId
    });
    
    return {
      type: 'text',
      text: `[Error processing attachment: ${attachment.name || 'file'}]`
    };
  }
}

/**
 * Check if model supports document input
 */
function supportsDocumentInput(modelId: string): boolean {
  const documentSupportPatterns = [
    'claude-3',
    'claude-4',
    'gemini-1.5',
    'gemini-2.0', 
    'gemini-2.5',
    'gpt-4o'
  ];
  
  return documentSupportPatterns.some(pattern => 
    modelId.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * Check if model supports audio input
 */
function supportsAudioInput(modelId: string): boolean {
  const audioSupportPatterns = [
    'gemini-1.5',
    'gemini-2.0',
    'gemini-2.5',
    'gpt-4o'
  ];
  
  return audioSupportPatterns.some(pattern => 
    modelId.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * Prepare multimodal content for AI SDK message
 */
export async function prepareMultimodalContent(
  textContent: string,
  attachments?: Attachment[],
  modelId?: string
): Promise<any> {
  if (!attachments || attachments.length === 0) {
    return textContent; // Simple text content
  }
  
  if (!modelId || !supportsMultimodal(modelId)) {
    // Model doesn't support multimodal - append attachment descriptions to text
    const attachmentDescriptions = attachments.map(att => 
      `[Attachment: ${att.name || att.type}]`
    ).join(' ');
    
    return `${textContent} ${attachmentDescriptions}`;
  }
  
  // Create multimodal content array for AI SDK
  const content: any[] = [];
  
  // Add text part
  if (textContent.trim()) {
    content.push({
      type: 'text',
      text: textContent
    });
  }
  
  // Add attachment parts
  for (const attachment of attachments) {
    const attachmentPart = await formatAttachmentForAISDK(attachment, modelId);
    content.push(attachmentPart);
  }
  
  return content;
}

/**
 * Prepare web search options for AI SDK
 */
export function prepareWebSearchOptions(
  webSearchEnabled: boolean,
  modelId?: string
): any {
  if (!webSearchEnabled || !modelId) {
    return {};
  }
  
  // Check if model supports native web search
  if (supportsWebSearchNative(modelId)) {
    return {
      webSearch: true,
      searchRecency: 'week',
      returnSources: true
    };
  }
  
  // For models without native web search, this will be handled by external search
  return {};
}

/**
 * Get recommended models for specific attachment types
 */
export function getRecommendedModelsForAttachments(attachments: Attachment[]): string[] {
  if (!attachments || attachments.length === 0) {
    return [];
  }
  
  const hasImages = attachments.some(att => att.type === AttachmentType.IMAGE);
  const hasDocuments = attachments.some(att => 
    att.type === AttachmentType.DOCUMENT
  );
  const hasAudio = attachments.some(att => att.type === AttachmentType.AUDIO);
  
  const recommendations: string[] = [];
  
  if (hasImages) {
    recommendations.push(
      'openai/gpt-4o',
      'anthropic/claude-3.5-sonnet',
      'google/gemini-1.5-pro'
    );
  }
  
  if (hasDocuments) {
    recommendations.push(
      'anthropic/claude-3.5-sonnet',
      'google/gemini-1.5-pro',
      'openai/gpt-4o'
    );
  }
  
  if (hasAudio) {
    recommendations.push(
      'google/gemini-1.5-pro',
      'openai/gpt-4o'
    );
  }
  
  return [...new Set(recommendations)]; // Remove duplicates
}