/**
 * Configuration Service for AI Provider Management
 * 
 * @description
 * High-level service for managing AI provider configurations, routing rules,
 * and optimization strategies. Provides presets, templates, and integration
 * with the dynamic configuration system.
 * 
 * @module lib/ai/config-service
 */

import { 
  DynamicConfigManager, 
  RoutingRule, 
  ProviderConfig, 
  GlobalRoutingConfig,
  RoutingContext,
  RoutingDecision 
} from './dynamic-config';
import { HealthMonitor } from './health-monitor';
import { apiLogger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

/**
 * Configuration preset types
 */
export type ConfigPreset = 
  | 'cost_optimized'
  | 'performance_first' 
  | 'balanced'
  | 'reliability_focused'
  | 'development'
  | 'production'
  | 'enterprise';

/**
 * Configuration template
 */
export interface ConfigTemplate {
  id: string;
  name: string;
  description: string;
  preset: ConfigPreset;
  globalConfig: Partial<GlobalRoutingConfig>;
  providerConfigs: Partial<ProviderConfig>[];
  rules: Omit<RoutingRule, 'id' | 'createdAt' | 'updatedAt'>[];
  tags: string[];
}

/**
 * Configuration backup
 */
export interface ConfigBackup {
  id: string;
  name: string;
  description?: string;
  timestamp: Date;
  version: string;
  config: any;
  createdBy?: string;
  automatic: boolean;
}

/**
 * Configuration test result
 */
export interface ConfigTestResult {
  success: boolean;
  testCount: number;
  passedCount: number;
  failedCount: number;
  results: Array<{
    testCase: string;
    context: RoutingContext;
    decision: RoutingDecision;
    expected: any;
    actual: any;
    passed: boolean;
    errors: string[];
  }>;
  performance: {
    averageDecisionTime: number;
    maxDecisionTime: number;
    minDecisionTime: number;
  };
}

/**
 * Configuration analytics
 */
export interface ConfigAnalytics {
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalDecisions: number;
    providerUsage: { [providerId: string]: number };
    ruleApplications: { [ruleId: string]: number };
    averageDecisionTime: number;
    errorRate: number;
    costSavings?: number;
    performanceImprovement?: number;
  };
  trends: {
    [metric: string]: Array<{
      timestamp: Date;
      value: number;
    }>;
  };
}

/**
 * Main Configuration Service
 */
export class ConfigService {
  private configManager: DynamicConfigManager;
  private healthMonitor: HealthMonitor;
  private backups: Map<string, ConfigBackup> = new Map();
  private templates: Map<string, ConfigTemplate> = new Map();
  private autoBackupEnabled = true;
  private autoBackupInterval = 24 * 60 * 60 * 1000; // 24 hours
  private autoBackupTimer?: NodeJS.Timeout;

  constructor(healthMonitor: HealthMonitor) {
    this.healthMonitor = healthMonitor;
    this.configManager = new DynamicConfigManager(undefined, healthMonitor);
    
    this.setupEventHandlers();
    this.loadBuiltinTemplates();
    this.startAutoBackup();
  }

  /**
   * Initialize with default configurations
   */
  async initialize(): Promise<void> {
    try {
      // Load configuration from database if available
      await this.loadConfigurationFromDatabase();
      
      // If no configuration exists, apply default preset
      const currentConfig = this.configManager.getConfiguration();
      if (currentConfig.providers.length === 0) {
        await this.applyPreset('balanced');
      }
      
      apiLogger.info('Configuration service initialized', {
        rules: currentConfig.rules.length,
        providers: currentConfig.providers.length,
        version: currentConfig.version
      });
      
    } catch (error) {
      apiLogger.error('Failed to initialize configuration service', error);
      
      // Fallback to safe defaults
      await this.applyPreset('development');
    }
  }

  /**
   * Apply a configuration preset
   */
  async applyPreset(preset: ConfigPreset): Promise<void> {
    const template = this.getPresetTemplate(preset);
    await this.applyTemplate(template);
    
    apiLogger.info('Configuration preset applied', { preset });
  }

  /**
   * Apply a configuration template
   */
  async applyTemplate(template: ConfigTemplate): Promise<void> {
    // Create backup before applying
    if (this.autoBackupEnabled) {
      await this.createBackup(`Before applying template: ${template.name}`, false);
    }
    
    // Apply global configuration
    if (template.globalConfig) {
      this.configManager.updateGlobalConfig(template.globalConfig);
    }
    
    // Apply provider configurations
    for (const providerConfig of template.providerConfigs) {
      if (providerConfig.id) {
        const fullConfig = this.mergeProviderConfig(providerConfig as ProviderConfig);
        this.configManager.updateProvider(fullConfig);
      }
    }
    
    // Apply routing rules
    for (const rule of template.rules) {
      const ruleId = `${template.id}_${rule.name.replace(/\s+/g, '_').toLowerCase()}`;
      this.configManager.addRule({
        ...rule,
        id: ruleId,
        tags: [...(rule.tags || []), template.preset]
      });
    }
    
    apiLogger.info('Configuration template applied', {
      templateId: template.id,
      preset: template.preset
    });
  }

  /**
   * Make a routing decision
   */
  async makeRoutingDecision(context: RoutingContext): Promise<RoutingDecision> {
    const startTime = Date.now();
    
    try {
      const decision = await this.configManager.makeRoutingDecision(context);
      
      // Log the decision for analytics
      await this.logRoutingDecision(context, decision, Date.now() - startTime);
      
      return decision;
      
    } catch (error) {
      apiLogger.error('Routing decision failed', { context, error });
      throw error;
    }
  }

  /**
   * Test configuration with sample scenarios
   */
  async testConfiguration(testCases?: RoutingContext[]): Promise<ConfigTestResult> {
    const defaultTestCases = this.getDefaultTestCases();
    const cases = testCases || defaultTestCases;
    
    const results: ConfigTestResult['results'] = [];
    const timings: number[] = [];
    
    for (const testCase of cases) {
      const startTime = Date.now();
      
      try {
        const decision = await this.configManager.makeRoutingDecision(testCase);
        const duration = Date.now() - startTime;
        timings.push(duration);
        
        // Validate decision
        const validation = this.validateDecision(decision, testCase);
        
        results.push({
          testCase: `Test case for ${testCase.model}`,
          context: testCase,
          decision,
          expected: validation.expected,
          actual: validation.actual,
          passed: validation.passed,
          errors: validation.errors
        });
        
      } catch (error) {
        results.push({
          testCase: `Test case for ${testCase.model}`,
          context: testCase,
          decision: {} as RoutingDecision,
          expected: 'Valid decision',
          actual: (error as Error).message,
          passed: false,
          errors: [(error as Error).message]
        });
      }
    }
    
    const passedCount = results.filter(r => r.passed).length;
    
    return {
      success: passedCount === results.length,
      testCount: results.length,
      passedCount,
      failedCount: results.length - passedCount,
      results,
      performance: {
        averageDecisionTime: timings.reduce((a, b) => a + b, 0) / timings.length,
        maxDecisionTime: Math.max(...timings),
        minDecisionTime: Math.min(...timings)
      }
    };
  }

  /**
   * Create configuration backup
   */
  async createBackup(name: string, automatic = false): Promise<string> {
    const config = this.configManager.getConfiguration();
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const backup: ConfigBackup = {
      id: backupId,
      name,
      timestamp: new Date(),
      version: config.version,
      config: this.configManager.exportConfiguration(),
      automatic
    };
    
    this.backups.set(backupId, backup);
    
    // Persist to database
    try {
      await this.saveBackupToDatabase(backup);
    } catch (error) {
      apiLogger.error('Failed to save backup to database', error);
    }
    
    apiLogger.info('Configuration backup created', { 
      backupId, 
      name, 
      automatic 
    });
    
    return backupId;
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupId: string): Promise<void> {
    const backup = this.backups.get(backupId);
    if (!backup) {
      throw new Error(`Backup not found: ${backupId}`);
    }
    
    // Create backup of current state before restoring
    await this.createBackup(`Before restore from ${backup.name}`, false);
    
    // Apply the backup configuration
    await this.configManager.loadConfiguration('file', { config: backup.config });
    
    apiLogger.info('Configuration restored from backup', {
      backupId,
      backupName: backup.name,
      version: backup.version
    });
  }

  /**
   * Get configuration analytics
   */
  async getAnalytics(period: { start: Date; end: Date }): Promise<ConfigAnalytics> {
    try {
      // Query routing decisions from database
      // Note: routingDecision table may not exist in current schema
      // Using RouterLLMLog as alternative for routing analytics
      const decisions = await prisma.routerLLMLog.findMany({
        where: {
          timestamp: {
            gte: period.start,
            lte: period.end
          }
        }
      });
      
      // Calculate metrics
      const providerUsage: { [key: string]: number } = {};
      const ruleApplications: { [key: string]: number } = {};
      let totalDecisionTime = 0;
      let errorCount = 0;
      
      for (const decision of decisions) {
        // Provider usage (use llmModel as provider indicator)
        const provider = decision.llmModel.split('/')[0] || decision.llmModel;
        providerUsage[provider] = (providerUsage[provider] || 0) + 1;
        
        // Rule applications (use specificAttributes for rules data)
        if (decision.specificAttributes && (decision.specificAttributes as any).appliedRules) {
          for (const ruleId of (decision.specificAttributes as any).appliedRules as string[]) {
            ruleApplications[ruleId] = (ruleApplications[ruleId] || 0) + 1;
          }
        }
        
        // Decision time
        totalDecisionTime += decision.latencyMs || 0;
        
        // Errors
        if (decision.errorMessage) {
          errorCount++;
        }
      }
      
      return {
        period,
        metrics: {
          totalDecisions: decisions.length,
          providerUsage,
          ruleApplications,
          averageDecisionTime: decisions.length > 0 ? totalDecisionTime / decisions.length : 0,
          errorRate: decisions.length > 0 ? errorCount / decisions.length : 0
        },
        trends: await this.calculateTrends(period)
      };
      
    } catch (error) {
      apiLogger.error('Failed to get analytics', error);
      throw error;
    }
  }

  /**
   * Get available templates
   */
  getTemplates(): ConfigTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get backups
   */
  getBackups(): ConfigBackup[] {
    return Array.from(this.backups.values()).sort((a, b) => 
      b.timestamp.getTime() - a.timestamp.getTime()
    );
  }

  /**
   * Get current configuration
   */
  getCurrentConfiguration(): ReturnType<DynamicConfigManager['getConfiguration']> {
    return this.configManager.getConfiguration();
  }

  /**
   * Add custom routing rule
   */
  addRoutingRule(rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'>): void {
    this.configManager.addRule(rule);
  }

  /**
   * Update provider configuration
   */
  updateProviderConfig(config: ProviderConfig): void {
    this.configManager.updateProvider(config);
  }

  /**
   * Private helper methods
   */
  
  private setupEventHandlers(): void {
    this.configManager.on('configChanged', (event) => {
      apiLogger.debug('Configuration changed', event);
    });
  }

  private loadBuiltinTemplates(): void {
    const templates = this.createBuiltinTemplates();
    for (const template of templates) {
      this.templates.set(template.id, template);
    }
  }

  private createBuiltinTemplates(): ConfigTemplate[] {
    return [
      {
        id: 'cost_optimized',
        name: 'Cost Optimized',
        description: 'Minimize costs while maintaining acceptable quality',
        preset: 'cost_optimized',
        globalConfig: {
          defaultStrategy: 'cost_optimized',
          costOptimization: {
            enabled: true,
            maxCostIncreasePercent: 10,
            costTrackingWindowMs: 3600000
          }
        },
        providerConfigs: [],
        rules: [
          {
            name: 'Prefer Low Cost Providers',
            description: 'Route to providers with lowest cost per token',
            priority: 100,
            enabled: true,
            conditions: [],
            actions: {
              routingStrategy: 'cheapest',
              maxCostPerToken: 0.001
            }
          }
        ],
        tags: ['cost', 'optimization']
      },
      {
        id: 'performance_first',
        name: 'Performance First',
        description: 'Optimize for speed and reliability',
        preset: 'performance_first',
        globalConfig: {
          defaultStrategy: 'quality_first',
          qualityOptimization: {
            enabled: true,
            minQualityThreshold: 0.95,
            qualityMetrics: ['latency', 'success_rate']
          }
        },
        providerConfigs: [],
        rules: [
          {
            name: 'Prefer Fast Providers',
            description: 'Route to providers with lowest latency',
            priority: 100,
            enabled: true,
            conditions: [],
            actions: {
              routingStrategy: 'fastest'
            }
          }
        ],
        tags: ['performance', 'speed']
      },
      {
        id: 'balanced',
        name: 'Balanced',
        description: 'Balance cost, performance, and reliability',
        preset: 'balanced',
        globalConfig: {
          defaultStrategy: 'weighted',
          loadBalancing: {
            algorithm: 'weighted_round_robin',
            healthCheckWeight: 0.4,
            performanceWeight: 0.6
          }
        },
        providerConfigs: [],
        rules: [],
        tags: ['balanced', 'default']
      }
      // Add more templates...
    ];
  }

  private getPresetTemplate(preset: ConfigPreset): ConfigTemplate {
    const template = this.templates.get(preset);
    if (!template) {
      throw new Error(`Unknown preset: ${preset}`);
    }
    return template;
  }

  private mergeProviderConfig(partial: Partial<ProviderConfig>): ProviderConfig {
    return {
      id: partial.id!,
      name: partial.name!,
      enabled: partial.enabled ?? true,
      priority: partial.priority ?? 50,
      healthWeight: partial.healthWeight ?? 0.3,
      costWeight: partial.costWeight ?? 0.3,
      qualityWeight: partial.qualityWeight ?? 0.4,
      timeoutMs: partial.timeoutMs ?? 30000,
      retryConfig: {
        maxRetries: 3,
        baseDelayMs: 1000,
        maxDelayMs: 10000,
        backoffFactor: 2,
        ...partial.retryConfig
      },
      costLimits: {
        maxCostPerRequest: 0.1,
        ...partial.costLimits
      },
      qualityRequirements: {
        minSuccessRate: 0.9,
        maxLatencyMs: 5000,
        ...partial.qualityRequirements
      },
      ...partial
    };
  }

  private getDefaultTestCases(): RoutingContext[] {
    return [
      {
        requestId: 'test-1',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date(),
        userTier: 'premium'
      },
      {
        requestId: 'test-2',
        model: 'claude-3-sonnet',
        messages: [{ role: 'user', content: 'Complex analysis task' }],
        timestamp: new Date(),
        priorityLevel: 'high'
      },
      {
        requestId: 'test-3',
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Simple question' }],
        timestamp: new Date(),
        userTier: 'free'
      }
    ];
  }

  private validateDecision(decision: RoutingDecision, context: RoutingContext): {
    passed: boolean;
    expected: any;
    actual: any;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (!decision.selectedProvider) {
      errors.push('No provider selected');
    }
    
    if (!decision.selectedModel) {
      errors.push('No model selected');
    }
    
    if (decision.metrics.finalScore < 0 || decision.metrics.finalScore > 10) {
      errors.push('Invalid final score');
    }
    
    return {
      passed: errors.length === 0,
      expected: 'Valid routing decision',
      actual: errors.length === 0 ? 'Valid' : errors.join(', '),
      errors
    };
  }

  private async logRoutingDecision(
    context: RoutingContext,
    decision: RoutingDecision,
    decisionTimeMs: number
  ): Promise<void> {
    try {
      // Note: routingDecision table may not exist in current schema
      // Using RouterLLMLog as alternative for routing decision logging
      await prisma.routerLLMLog.create({
        data: {
          requestId: context.requestId,
          llmModel: decision.selectedModel || 'gemini/gemini-2.5-flash',
          llmPromptSent: 'routing-decision',
          llmResponse: 'routing-completed',
          latencyMs: decisionTimeMs,
          success: true,
          specificAttributes: {
            selectedProvider: decision.selectedProvider,
            selectedModel: decision.selectedModel,
            configVersion: decision.configVersion,
            reasoning: Array.isArray(decision.reasoning) ? decision.reasoning.join('; ') : (decision.reasoning || '')
          }
        } as any
      });
    } catch (error) {
      apiLogger.error('Failed to log routing decision', error);
    }
  }

  private async loadConfigurationFromDatabase(): Promise<void> {
    // Implementation would load from database
    // For now, this is a placeholder
  }

  private async saveBackupToDatabase(backup: ConfigBackup): Promise<void> {
    // Implementation would save to database
    // For now, this is a placeholder
  }

  private async calculateTrends(period: { start: Date; end: Date }): Promise<ConfigAnalytics['trends']> {
    // Implementation would calculate trends from historical data
    return {};
  }

  private startAutoBackup(): void {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
    }
    
    if (this.autoBackupEnabled) {
      this.autoBackupTimer = setInterval(async () => {
        try {
          await this.createBackup('Automatic backup', true);
        } catch (error) {
          apiLogger.error('Auto backup failed', error);
        }
      }, this.autoBackupInterval);
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
    }
    this.configManager.destroy();
  }
}

// Singleton instance
let configServiceInstance: ConfigService | null = null;

export function getConfigService(healthMonitor?: HealthMonitor): ConfigService {
  if (!configServiceInstance && healthMonitor) {
    configServiceInstance = new ConfigService(healthMonitor);
  }
  
  if (!configServiceInstance) {
    throw new Error('ConfigService not initialized. Call with HealthMonitor first.');
  }
  
  return configServiceInstance;
}