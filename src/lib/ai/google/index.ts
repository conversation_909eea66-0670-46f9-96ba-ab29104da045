/**
 * Google AI Integration
 * Main entry point for Google Generative AI direct integration
 */

export * from './client';
export * from './models';
export { generateImagen4 } from './imagen4';
// Vertex AI enabled
export { 
  chatWithGeminiVertex, 
  generateImageWithImagen3, 
  isVertexAIConfigured, 
  getAvailableVertexModels,
  VERTEX_GEMINI_MODELS 
} from './vertex-ai';

import { isGoogleModel } from './models';
import { chatWithGoogle, streamChatWithGoogle } from './client';
import { chatWithGeminiVertex, isVertexAIConfigured } from './vertex-ai';
import { apiLogger } from '@/lib/logger';

export interface GoogleAIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: Array<{
    type: 'url' | 'base64';
    data: string;
    mimeType?: string;
  }>;
}

export interface GoogleAIOptions {
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  systemPrompt?: string;
  stream?: boolean;
  tools?: any[];
  responseFormat?: 'text' | 'json';
}

/**
 * Main function to handle Google AI requests
 * Automatically routes to the appropriate service
 */
export async function handleGoogleAI(
  messages: GoogleAIMessage[],
  options: GoogleAIOptions
) {
  const { model, stream = false } = options;

  // Check if this is a Google model
  if (!isGoogleModel(model)) {
    throw new Error(`Not a Google model: ${model}`);
  }

  apiLogger.info('Handling Google AI request', {
    model,
    messageCount: messages.length,
    stream,
    hasTools: !!options.tools
  });

  try {
    if (stream) {
      return streamChatWithGoogle(messages, {
        model,
        temperature: options.temperature,
        maxOutputTokens: options.maxTokens,
        topP: options.topP,
        topK: options.topK,
        systemInstruction: options.systemPrompt,
        tools: options.tools,
        responseFormat: options.responseFormat
      });
    } else {
      return await chatWithGoogle(messages, {
        model,
        temperature: options.temperature,
        maxOutputTokens: options.maxTokens,
        topP: options.topP,
        topK: options.topK,
        systemInstruction: options.systemPrompt,
        tools: options.tools,
        responseFormat: options.responseFormat
      });
    }
  } catch (error) {
    apiLogger.error('Google AI request failed', {
      model,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Check if Google AI should handle this model
 */
export function shouldUseGoogleAI(modelId: string): boolean {
  return isGoogleModel(modelId) && !!process.env.GOOGLE_API_KEY;
}