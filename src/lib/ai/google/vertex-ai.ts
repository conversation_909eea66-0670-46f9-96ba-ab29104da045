/**
 * Vertex AI Gemini Integration
 * Direct integration with Gemini Pro models through Vertex AI
 */

import { VertexAI, HarmCategory, HarmBlockThreshold } from '@google-cloud/vertexai';
import { apiLogger } from '@/lib/logger';

// Initialize Vertex AI
const vertexAI = new VertexAI({
  project: process.env.GCLOUD_PROJECT || 'yorkshire3d',
  location: 'us-central1',
});

// Safety settings for Gemini
const safetySettings = [
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
];

export interface GeminiChatOptions {
  model?: string;
  temperature?: number;
  maxOutputTokens?: number;
  topP?: number;
  topK?: number;
}

export interface GeminiImageOptions {
  prompt: string;
  aspectRatio?: string;
  numberOfImages?: number;
  negativePrompt?: string;
  guidanceScale?: number;
  seed?: number;
}

/**
 * Available Gemini models through Vertex AI
 */
export const VERTEX_GEMINI_MODELS = {
  // Text models
  'gemini-1.5-pro': 'gemini-1.5-pro-002',
  'gemini-1.5-pro-latest': 'gemini-1.5-pro-002',
  'gemini-1.5-flash': 'gemini-1.5-flash-002',
  'gemini-1.5-flash-latest': 'gemini-1.5-flash-002',
  'gemini-2.0-flash-thinking': 'gemini-2.0-flash-thinking-latest',
  'gemini-2.0-flash-exp': 'gemini-2.0-flash-exp',
  
  // Vision models (multimodal)
  'gemini-1.5-pro-vision': 'gemini-1.5-pro-002',
  'gemini-1.5-flash-vision': 'gemini-1.5-flash-002',
  
  // Image generation models
  'imagen-3': 'imagegeneration@006',
  'imagen-3-fast': 'imagen-3.0-fast-generate-001',
  'imagen-3-quality': 'imagen-3.0-generate-001',
};

/**
 * Chat with Gemini Pro through Vertex AI
 */
export async function chatWithGeminiVertex(
  messages: Array<{ role: string; content: string }>,
  options: GeminiChatOptions = {}
): Promise<{
  content: string;
  usage: {
    promptTokens: number;
    candidatesTokens: number;
    totalTokens: number;
  };
}> {
  const {
    model = 'gemini-1.5-pro',
    temperature = 0.7,
    maxOutputTokens = 8192,
    topP = 0.95,
    topK = 40,
  } = options;

  try {
    const modelName = VERTEX_GEMINI_MODELS[model as keyof typeof VERTEX_GEMINI_MODELS] || model;
    const generativeModel = vertexAI.preview.getGenerativeModel({
      model: modelName,
      generationConfig: {
        temperature,
        maxOutputTokens,
        topP,
        topK,
      },
      safetySettings,
    });

    // Convert messages to Vertex AI format
    const contents = messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }],
    }));

    // Generate content
    const result = await generativeModel.generateContent({
      contents,
    });

    const response = result.response;
    const text = response.candidates?.[0]?.content?.parts?.[0]?.text || '';
    
    return {
      content: text,
      usage: {
        promptTokens: response.usageMetadata?.promptTokenCount || 0,
        candidatesTokens: response.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.usageMetadata?.totalTokenCount || 0,
      },
    };
  } catch (error) {
    apiLogger.error('Vertex AI Gemini chat failed', {
      model,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
}

/**
 * Generate images with Imagen 3 through Vertex AI
 */
export async function generateImageWithImagen3(
  options: GeminiImageOptions
): Promise<{
  images: Array<{
    type: 'base64';
    data: string;
  }>;
  metadata: {
    model: string;
    aspectRatio: string;
  };
}> {
  const {
    prompt,
    aspectRatio = '1:1',
    numberOfImages = 1,
    negativePrompt,
    guidanceScale = 7.5,
    seed,
  } = options;

  try {
    const imageModel = vertexAI.preview.getGenerativeModel({
      model: 'imagen-3.0-generate-001',
    });

    // Prepare the prompt
    const fullPrompt = negativePrompt 
      ? `${prompt}. Avoid: ${negativePrompt}`
      : prompt;

    const request = {
      contents: [{
        role: 'user',
        parts: [{
          text: fullPrompt,
        }],
      }],
      generationConfig: {
        maxOutputTokens: 8192,
        temperature: 1,
        candidateCount: numberOfImages,
      },
    };

    const result = await imageModel.generateContent(request);
    
    // Extract images from response
    const images = [];
    for (const candidate of result.response.candidates || []) {
      const imagePart = candidate.content?.parts?.find(
        part => part.inlineData?.mimeType?.startsWith('image/')
      );
      
      if (imagePart?.inlineData?.data) {
        images.push({
          type: 'base64' as const,
          data: imagePart.inlineData.data,
        });
      }
    }

    return {
      images,
      metadata: {
        model: 'imagen-3',
        aspectRatio,
      },
    };
  } catch (error) {
    apiLogger.error('Imagen 3 generation failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
}

/**
 * Check if Vertex AI is properly configured
 */
export function isVertexAIConfigured(): boolean {
  return !!(
    process.env.GCLOUD_PROJECT && 
    process.env.GOOGLE_APPLICATION_CREDENTIALS &&
    require('fs').existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS)
  );
}

/**
 * Get available Vertex AI models
 */
export function getAvailableVertexModels() {
  return {
    chat: [
      'gemini-1.5-pro',
      'gemini-1.5-pro-latest',
      'gemini-1.5-flash',
      'gemini-1.5-flash-latest',
      'gemini-2.0-flash-thinking',
      'gemini-2.0-flash-exp',
    ],
    vision: [
      'gemini-1.5-pro-vision',
      'gemini-1.5-flash-vision',
    ],
    imageGeneration: [
      'imagen-3',
      'imagen-3-fast',
      'imagen-3-quality',
    ],
  };
}