/**
 * Google AI Models Configuration
 * Maps database model names to Google Generative AI API models
 */

export interface GoogleModelConfig {
  apiName: string;
  displayName: string;
  type: 'chat' | 'vision' | 'embedding' | 'code';
  maxTokens: number;
  contextWindow: number;
  supportsFunctionCalling: boolean;
  supportsSystemInstruction: boolean;
  supportsJsonMode: boolean;
  supportsCodeExecution: boolean;
  pricing: {
    inputPer1M: number;
    outputPer1M: number;
  };
}

/**
 * Map of database canonical names (with google/ prefix) to Google API model names
 * Updated to use google/ prefix for direct API access
 */
export const GOOGLE_MODEL_MAPPING: Record<string, GoogleModelConfig> = {
  // Gemini 1.5 Flash Series
  'google/gemini-1.5-flash': {
    apiName: 'gemini-1.5-flash',
    displayName: 'Gemini 1.5 Flash',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.075, outputPer1M: 0.30 }
  },
  'google/gemini-1.5-flash-002': {
    apiName: 'gemini-1.5-flash-002',
    displayName: 'Gemini 1.5 Flash v002',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.075, outputPer1M: 0.30 }
  },
  'google/gemini-1.5-flash-8b': {
    apiName: 'gemini-1.5-flash-8b',
    displayName: 'Gemini 1.5 Flash 8B',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.0375, outputPer1M: 0.15 }
  },
  'google/gemini-1.5-flash-8b-latest': {
    apiName: 'gemini-1.5-flash-8b-latest',
    displayName: 'Gemini 1.5 Flash 8B Latest',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.0375, outputPer1M: 0.15 }
  },
  'google/gemini-1.5-flash-latest': {
    apiName: 'gemini-1.5-flash-latest',
    displayName: 'Gemini 1.5 Flash (Latest)',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.075, outputPer1M: 0.30 }
  },

  // Gemini 1.5 Pro Series
  'google/gemini-1.5-pro': {
    apiName: 'gemini-1.5-pro',
    displayName: 'Gemini 1.5 Pro',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 2000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: true,
    pricing: { inputPer1M: 1.25, outputPer1M: 5.00 }
  },
  'google/gemini-1.5-pro-002': {
    apiName: 'gemini-1.5-pro-002',
    displayName: 'Gemini 1.5 Pro v002',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 2000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: true,
    pricing: { inputPer1M: 1.25, outputPer1M: 5.00 }
  },
  'google/gemini-1.5-pro-latest': {
    apiName: 'gemini-1.5-pro-latest',
    displayName: 'Gemini 1.5 Pro (Latest)',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 2000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: true,
    pricing: { inputPer1M: 1.25, outputPer1M: 5.00 }
  },

  // Gemini 2.0 Flash Series
  'google/gemini-2.0-flash': {
    apiName: 'gemini-2.0-flash',
    displayName: 'Gemini 2.0 Flash',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.075, outputPer1M: 0.30 }
  },
  'google/gemini-2.0-flash-exp': {
    apiName: 'gemini-2.0-flash-exp',
    displayName: 'Gemini 2.0 Flash Experimental',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0, outputPer1M: 0 } // Free during experimental
  },
  'google/gemini-2.0-flash-lite': {
    apiName: 'gemini-2.0-flash-lite',
    displayName: 'Gemini 2.0 Flash Lite',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0, outputPer1M: 0 } // Free tier
  },
  'google/gemini-2.0-flash-thinking-exp': {
    apiName: 'gemini-2.0-flash-thinking-exp',
    displayName: 'Gemini 2.0 Flash Thinking',
    type: 'chat',
    maxTokens: 32768,
    contextWindow: 32768,
    supportsFunctionCalling: false, // Thinking models may not support functions
    supportsSystemInstruction: true,
    supportsJsonMode: false,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0, outputPer1M: 0 } // Experimental
  },

  // Gemini 2.5 Series
  'google/gemini-2.5-flash': {
    apiName: 'gemini-2.5-flash',
    displayName: 'Gemini 2.5 Flash',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0.075, outputPer1M: 0.30 }
  },
  'google/gemini-2.5-flash-lite-preview-06-17': {
    apiName: 'gemini-2.5-flash-lite-preview-06-17',
    displayName: 'Gemini 2.5 Flash Lite',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 1000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: false,
    pricing: { inputPer1M: 0, outputPer1M: 0 } // Preview/free
  },
  'google/gemini-2.5-pro': {
    apiName: 'gemini-2.5-pro',
    displayName: 'Gemini 2.5 Pro',
    type: 'chat',
    maxTokens: 8192,
    contextWindow: 2000000,
    supportsFunctionCalling: true,
    supportsSystemInstruction: true,
    supportsJsonMode: true,
    supportsCodeExecution: true,
    pricing: { inputPer1M: 1.25, outputPer1M: 5.00 }
  }
};

/**
 * Get Google API model name from database canonical name
 */
export function getGoogleApiModel(canonicalName: string): string | null {
  console.log('[GoogleModels] getGoogleApiModel input:', canonicalName);
  // Support both google/ and gemini/ prefixes for backward compatibility
  const normalizedName = canonicalName.replace('gemini/', 'google/');
  console.log('[GoogleModels] Normalized name:', normalizedName);
  const config = GOOGLE_MODEL_MAPPING[normalizedName];
  console.log('[GoogleModels] Found config:', !!config, config?.apiName);
  return config?.apiName || null;
}

/**
 * Check if a model is a Google model
 */
export function isGoogleModel(modelId: string): boolean {
  return modelId.startsWith('google/') || modelId.startsWith('gemini/'); // Support both for backward compatibility
}

/**
 * Get model configuration
 */
export function getGoogleModelConfig(canonicalName: string): GoogleModelConfig | null {
  // Support both google/ and gemini/ prefixes for backward compatibility
  const normalizedName = canonicalName.replace('gemini/', 'google/');
  return GOOGLE_MODEL_MAPPING[normalizedName] || null;
}

/**
 * List all available Google models
 */
export function listGoogleModels(): string[] {
  return Object.keys(GOOGLE_MODEL_MAPPING);
}