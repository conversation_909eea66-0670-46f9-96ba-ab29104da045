import { PredictionServiceClient, helpers } from '@google-cloud/aiplatform';
import { apiLogger } from '@/lib/logger';
const location = 'us-central1';
const clientOptions = {
  apiEndpoint: `${location}-aiplatform.googleapis.com`,
};

let predictionClient: InstanceType<typeof PredictionServiceClient> | null = null;

// Initialize client with proper error handling
function getClient() {
  if (!predictionClient) {
    predictionClient = new PredictionServiceClient(clientOptions);
  }
  return predictionClient;
}

export interface Imagen4Params {
  prompt: string;
  aspectRatio?: '1:1' | '9:16' | '16:9' | '4:3' | '3:4';
  sampleCount?: number;
  safetyFilterLevel?: 'block_most' | 'block_some' | 'block_few' | 'block_least';
  addWatermark?: boolean;
  quality?: 'standard' | 'high';
}

export interface Imagen4Result {
  images: Array<{
    type: 'base64';
    data: string;
  }>;
  usage: {
    model: string;
    estimated_cost: number;
    duration: number;
  };
  metadata: {
    model: string;
    aspectRatio: string;
    safetyFilterLevel: string;
    addWatermark: boolean;
  };
}

export async function generateImagen4(params: Imagen4Params, userId?: string): Promise<Imagen4Result> {
  const {
    prompt,
    aspectRatio = '1:1',
    sampleCount = 1,
    safetyFilterLevel = 'block_some',
    addWatermark = true,
    quality = 'standard'
  } = params;

  const projectId = process.env.GCLOUD_PROJECT;
  
  if (!projectId) {
    throw new Error('GCLOUD_PROJECT environment variable is required for Imagen 4');
  }

  apiLogger.info('Imagen 4 generation request', {
    userId,
    promptLength: prompt.length,
    aspectRatio,
    sampleCount,
    safetyFilterLevel,
    addWatermark
  });

  const startTime = Date.now();
  
  try {
    const client = getClient();
    
    // Fully-qualified endpoint for Imagen 4 preview
    const endpoint = `projects/${projectId}/locations/${location}/publishers/google/models/imagen-4.0-generate-preview-06-06`;

    // Wrap prompt into a protobuf Value
    const instance = helpers.toValue({ prompt });
    const instances = [instance];

    // Set parameters based on quality and other options
    const parameters = helpers.toValue({
      sampleCount,
      aspectRatio,
      safetyFilterLevel,
      addWatermark,
      // Add quality-specific parameters
      ...(quality === 'high' && {
        guidance_scale: 15, // Higher guidance for better quality
        steps: 50 // More steps for better quality
      })
    });

    const request = {
      endpoint,
      instances: instances as any[],
      parameters: parameters as any,
    };

    // Call the API
    console.log('[Imagen4] Making request to endpoint:', endpoint);
    const response = await client.predict(request);
    const [predictionResult] = response;
    const predictions = predictionResult.predictions;

    if (!predictions || predictions.length === 0) {
      throw new Error('No image generated – check your prompt and parameters.');
    }

    const duration = Date.now() - startTime;

    // Process the predictions
    const images = [];
    for (const pred of predictions) {
      if (pred.structValue?.fields?.bytesBase64Encoded?.stringValue) {
        const base64Data = pred.structValue.fields.bytesBase64Encoded.stringValue;
        images.push({
          type: 'base64' as const,
          data: base64Data
        });
      }
    }

    if (images.length === 0) {
      throw new Error('No valid images found in response');
    }

    // Calculate cost (Imagen 4: $0.04 USD per output image)
    const estimatedCost = 0.04 * sampleCount;

    apiLogger.info('Imagen 4 generation successful', {
      userId,
      duration,
      imageCount: images.length,
      estimatedCost
    });

    return {
      images,
      usage: {
        model: 'imagen-4.0-generate-preview-06-06',
        estimated_cost: estimatedCost,
        duration
      },
      metadata: {
        model: 'imagen-4.0-generate-preview-06-06',
        aspectRatio,
        safetyFilterLevel,
        addWatermark
      }
    };

  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    apiLogger.error('Imagen 4 generation failed', {
      userId,
      error: error.message,
      duration,
      code: error.code
    });

    // Handle specific Google Cloud errors
    if (error.code === 3) { // INVALID_ARGUMENT
      throw new Error(`Invalid request to Imagen 4: ${error.message}`);
    } else if (error.code === 7) { // PERMISSION_DENIED
      throw new Error('Permission denied for Imagen 4. Check your Google Cloud credentials and API access.');
    } else if (error.code === 8) { // RESOURCE_EXHAUSTED
      throw new Error('Imagen 4 quota exceeded. Please try again later.');
    } else if (error.code === 14) { // UNAVAILABLE
      throw new Error('Imagen 4 service is temporarily unavailable. Please try again later.');
    }

    throw new Error(`Imagen 4 generation failed: ${error.message}`);
  }
}

/**
 * Check if Imagen 4 is properly configured
 */
export function isImagen4Available(): boolean {
  return !!(process.env.GCLOUD_PROJECT && process.env.GOOGLE_APPLICATION_CREDENTIALS);
}

/**
 * Get Imagen 4 configuration status
 */
export function getImagen4Status() {
  return {
    available: isImagen4Available(),
    projectId: process.env.GCLOUD_PROJECT,
    hasCredentials: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
    location
  };
}