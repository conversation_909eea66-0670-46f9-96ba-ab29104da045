/**
 * Google Generative AI Client
 * Direct integration with Google AI API, bypassing LiteLLM
 */

import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';
import { apiLogger } from '@/lib/logger';
import { getGoogleApiModel, getGoogleModelConfig } from './models';

// Initialize Google Generative AI client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || '');

// Default safety settings
export const DEFAULT_SAFETY_SETTINGS = [
  {
    category: HarmCategory.HARM_CATEGORY_HARASSMENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
  {
    category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
  },
];

export interface GoogleChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  images?: Array<{
    type: 'url' | 'base64';
    data: string;
    mimeType?: string;
  }>;
}

export interface GoogleChatOptions {
  model: string;
  temperature?: number;
  maxOutputTokens?: number;
  topP?: number;
  topK?: number;
  stopSequences?: string[];
  systemInstruction?: string;
  tools?: any[];
  responseFormat?: 'text' | 'json';
  safetySettings?: typeof DEFAULT_SAFETY_SETTINGS;
}

export interface GoogleChatResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
  safetyRatings?: any[];
  citations?: any[];
}

/**
 * Convert chat messages to Google format
 */
function convertToGoogleFormat(messages: GoogleChatMessage[]) {
  const contents: any[] = [];
  let systemInstruction: string | undefined;

  for (const message of messages) {
    if (message.role === 'system') {
      // Google doesn't support system role in contents, use systemInstruction
      systemInstruction = message.content;
      continue;
    }

    const parts: any[] = [{ text: message.content }];

    // Add images if present
    if (message.images && message.images.length > 0) {
      for (const image of message.images) {
        if (image.type === 'base64') {
          parts.push({
            inlineData: {
              mimeType: image.mimeType || 'image/jpeg',
              data: image.data
            }
          });
        } else if (image.type === 'url') {
          // For URLs, we'd need to fetch and convert to base64
          apiLogger.warn('URL images not yet supported for Google AI', { url: image.data });
        }
      }
    }

    contents.push({
      role: message.role === 'assistant' ? 'model' : 'user',
      parts
    });
  }

  return { contents, systemInstruction };
}

/**
 * Chat with Google Generative AI
 */
export async function chatWithGoogle(
  messages: GoogleChatMessage[],
  options: GoogleChatOptions
): Promise<GoogleChatResponse> {
  const {
    model: canonicalName,
    temperature = 0.7,
    maxOutputTokens = 8192,
    topP = 0.95,
    topK = 40,
    stopSequences,
    systemInstruction: explicitSystemInstruction,
    tools,
    responseFormat,
    safetySettings = DEFAULT_SAFETY_SETTINGS
  } = options;

  try {
    // Get the Google API model name
    const apiModelName = getGoogleApiModel(canonicalName);
    if (!apiModelName) {
      throw new Error(`Unknown Google model: ${canonicalName}`);
    }

    const modelConfig = getGoogleModelConfig(canonicalName);
    if (!modelConfig) {
      throw new Error(`No configuration found for model: ${canonicalName}`);
    }

    apiLogger.info('Google AI chat request', {
      model: canonicalName,
      apiModel: apiModelName,
      messageCount: messages.length,
      temperature,
      maxOutputTokens
    });

    // Convert messages to Google format
    const { contents, systemInstruction } = convertToGoogleFormat(messages);

    // Initialize the model
    const generationConfig: any = {
      temperature,
      topP,
      topK,
      maxOutputTokens: Math.min(maxOutputTokens, modelConfig.maxTokens),
    };

    if (stopSequences) {
      generationConfig.stopSequences = stopSequences;
    }

    if (responseFormat === 'json' && modelConfig.supportsJsonMode) {
      generationConfig.responseMimeType = 'application/json';
    }

    const model = genAI.getGenerativeModel({
      model: apiModelName,
      generationConfig,
      safetySettings,
      systemInstruction: explicitSystemInstruction || systemInstruction,
      tools: tools && modelConfig.supportsFunctionCalling ? tools : undefined,
    });

    // Generate content
    const result = await model.generateContent({ contents });
    const response = result.response;

    if (!response) {
      throw new Error('No response from Google AI');
    }

    const text = response.text();
    const usage = response.usageMetadata;
    
    // Debug logging
    apiLogger.info('Google AI response received', {
      hasText: !!text,
      textLength: text?.length,
      usage
    });

    apiLogger.info('Google AI chat successful', {
      model: canonicalName,
      promptTokens: usage?.promptTokenCount,
      completionTokens: usage?.candidatesTokenCount,
      totalTokens: usage?.totalTokenCount
    });

    return {
      content: text,
      usage: {
        promptTokens: usage?.promptTokenCount || 0,
        completionTokens: usage?.candidatesTokenCount || 0,
        totalTokens: usage?.totalTokenCount || 0
      },
      finishReason: response.candidates?.[0]?.finishReason,
      safetyRatings: response.candidates?.[0]?.safetyRatings,
      citations: response.candidates?.[0]?.citationMetadata?.citationSources || undefined
    };
  } catch (error) {
    apiLogger.error('Google AI chat failed', {
      model: canonicalName,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Stream chat with Google Generative AI
 */
export async function* streamChatWithGoogle(
  messages: GoogleChatMessage[],
  options: GoogleChatOptions
): AsyncGenerator<{ content: string; isComplete: boolean }> {
  const {
    model: canonicalName,
    temperature = 0.7,
    maxOutputTokens = 8192,
    topP = 0.95,
    topK = 40,
    stopSequences,
    systemInstruction: explicitSystemInstruction,
    tools,
    safetySettings = DEFAULT_SAFETY_SETTINGS
  } = options;

  try {
    const apiModelName = getGoogleApiModel(canonicalName);
    if (!apiModelName) {
      throw new Error(`Unknown Google model: ${canonicalName}`);
    }

    const modelConfig = getGoogleModelConfig(canonicalName);
    if (!modelConfig) {
      throw new Error(`No configuration found for model: ${canonicalName}`);
    }

    const { contents, systemInstruction } = convertToGoogleFormat(messages);

    const generationConfig: any = {
      temperature,
      topP,
      topK,
      maxOutputTokens: Math.min(maxOutputTokens, modelConfig.maxTokens),
    };

    if (stopSequences) {
      generationConfig.stopSequences = stopSequences;
    }

    const model = genAI.getGenerativeModel({
      model: apiModelName,
      generationConfig,
      safetySettings,
      systemInstruction: explicitSystemInstruction || systemInstruction,
      tools: tools && modelConfig.supportsFunctionCalling ? tools : undefined,
    });

    const result = await model.generateContentStream({ contents });

    let hasContent = false;
    try {
      for await (const chunk of result.stream) {
        const text = chunk.text();
        apiLogger.info('Google AI stream chunk received', { 
          model: canonicalName, 
          hasText: !!text,
          textLength: text?.length || 0,
          chunkType: typeof chunk
        });
        if (text) {
          hasContent = true;
          yield { content: text, isComplete: false };
        }
      }
    } catch (streamError) {
      apiLogger.error('Error during Google AI streaming', {
        model: canonicalName,
        error: streamError instanceof Error ? streamError.message : 'Unknown error'
      });
      throw streamError;
    }

    // If no content was streamed, try to get the final response
    if (!hasContent) {
      const response = await result.response;
      const finalText = response.text();
      apiLogger.info('Google AI no stream content, using final response', {
        model: canonicalName,
        hasText: !!finalText,
        textLength: finalText?.length
      });
      if (finalText) {
        yield { content: finalText, isComplete: false };
      }
    }

    yield { content: '', isComplete: true };
  } catch (error) {
    apiLogger.error('Google AI streaming failed', {
      model: canonicalName,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Check if Google AI is properly configured
 */
export function isGoogleAIConfigured(): boolean {
  return !!process.env.GOOGLE_API_KEY;
}

/**
 * List available models from Google AI
 */
export async function listAvailableModels(): Promise<string[]> {
  try {
    // This would need the REST API endpoint
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
      headers: {
        'x-goog-api-key': process.env.GOOGLE_API_KEY || ''
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to list models: ${response.statusText}`);
    }

    const data = await response.json();
    return data.models?.map((m: any) => m.name.replace('models/', '')) || [];
  } catch (error) {
    apiLogger.error('Failed to list Google models', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
}