/**
 * Model testing utility using AI SDK providers
 * Replaces LiteLLM integration for model testing
 * Created in Phase 7.2
 */

import { createAISDK<PERSON>rovider, AI_SDK_PROVIDERS, type AISDKProviderName } from '@/lib/ai/providers';
import { apiLogger } from '@/lib/logger';

interface ModelTestResult {
  success: boolean;
  latency?: number;
  response?: string;
  error?: string;
  details?: {
    provider: string;
    modelId: string;
    timestamp: string;
  };
}

class ModelTester {
  private providers = new Map<string, any>();

  /**
   * Get provider name from canonical model name
   */
  private extractProviderFromModel(canonicalName: string): string {
    // Extract provider from canonical name (e.g., "openai/gpt-4o" -> "openai")
    if (canonicalName.includes('/')) {
      return canonicalName.split('/')[0];
    }
    
    // Fallback mapping for models without provider prefix
    const providerMapping: Record<string, string> = {
      'gpt': 'openai',
      'claude': 'anthropic',
      'gemini': 'google',
      'llama': 'groq',
      'mistral': 'mistral',
      'deepseek': 'deepseek',
      'grok': 'xai',
      'command': 'cohere',
      'qwen': 'qwen'
    };
    
    for (const [modelPattern, provider] of Object.entries(providerMapping)) {
      if (canonicalName.toLowerCase().includes(modelPattern)) {
        return provider;
      }
    }
    
    return 'openai'; // Default fallback
  }

  /**
   * Get or initialize provider for a model
   */
  private async getProvider(canonicalName: string) {
    const providerName = this.extractProviderFromModel(canonicalName);
    
    if (!this.providers.has(providerName)) {
      try {
        const provider = createAISDKProvider(providerName as AISDKProviderName);
        
        // Validate provider configuration
        const isValid = await provider.validateConfig();
        if (!isValid) {
          throw new Error(`Provider ${providerName} configuration invalid`);
        }
        
        this.providers.set(providerName, provider);
        console.log(`[ModelTester] Initialized provider: ${providerName}`);
      } catch (error) {
        console.error(`[ModelTester] Failed to initialize provider ${providerName}:`, error);
        throw error;
      }
    }
    
    return {
      provider: this.providers.get(providerName),
      name: providerName
    };
  }

  /**
   * Extract model ID from canonical name for AI SDK
   */
  private extractModelId(canonicalName: string): string {
    // Remove provider prefix for AI SDK (e.g., "openai/gpt-4o" -> "gpt-4o")
    if (canonicalName.includes('/')) {
      return canonicalName.split('/').slice(1).join('/');
    }
    return canonicalName;
  }

  /**
   * Test a model with a given prompt
   */
  async testModel(
    canonicalName: string,
    testPrompt: string = "Hello, please respond with 'Test successful' to confirm you're working correctly."
  ): Promise<ModelTestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`[ModelTester] Testing model: ${canonicalName}`);
      
      // Get provider for this model
      const { provider, name: providerName } = await this.getProvider(canonicalName);
      const modelId = this.extractModelId(canonicalName);
      
      console.log(`[ModelTester] Using provider: ${providerName}, modelId: ${modelId}`);
      
      // Generate test response
      const response = await provider.generateCompletion({
        model: modelId,
        messages: [
          {
            role: 'user',
            content: testPrompt
          }
        ],
        temperature: 0.1,
        maxTokens: 100,
        topP: 0.9
      });
      
      const latency = Date.now() - startTime;
      
      console.log(`[ModelTester] Test successful for ${canonicalName}: ${latency}ms`);
      
      apiLogger.info('Model test successful', {
        canonicalName,
        provider: providerName,
        modelId,
        latency,
        responseLength: response?.length || 0
      });
      
      return {
        success: true,
        latency,
        response: response || '',
        details: {
          provider: providerName,
          modelId,
          timestamp: new Date().toISOString()
        }
      };
      
    } catch (error) {
      const latency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`[ModelTester] Test failed for ${canonicalName}:`, error);
      
      apiLogger.error('Model test failed', {
        canonicalName,
        latency,
        error: errorMessage
      });
      
      return {
        success: false,
        latency,
        error: errorMessage,
        details: {
          provider: this.extractProviderFromModel(canonicalName),
          modelId: this.extractModelId(canonicalName),
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Test multiple models concurrently
   */
  async testModels(
    modelSpecs: Array<{ canonicalName: string; testPrompt?: string }>
  ): Promise<Array<{ canonicalName: string; result: ModelTestResult }>> {
    const results = await Promise.allSettled(
      modelSpecs.map(async ({ canonicalName, testPrompt }) => ({
        canonicalName,
        result: await this.testModel(canonicalName, testPrompt)
      }))
    );
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          canonicalName: modelSpecs[index].canonicalName,
          result: {
            success: false,
            error: `Test execution failed: ${result.reason}`,
            details: {
              provider: this.extractProviderFromModel(modelSpecs[index].canonicalName),
              modelId: this.extractModelId(modelSpecs[index].canonicalName),
              timestamp: new Date().toISOString()
            }
          }
        };
      }
    });
  }

  /**
   * Get provider health status
   */
  async getProviderHealth(): Promise<Record<string, { healthy: boolean; error?: string }>> {
    const providers = Object.values(AI_SDK_PROVIDERS);
    const health: Record<string, { healthy: boolean; error?: string }> = {};
    
    for (const providerName of providers) {
      try {
        const provider = createAISDKProvider(providerName as AISDKProviderName);
        const isValid = await provider.validateConfig();
        
        health[providerName] = {
          healthy: isValid
        };
        
        if (isValid) {
          this.providers.set(providerName, provider);
        }
      } catch (error) {
        health[providerName] = {
          healthy: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    return health;
  }
}

// Export singleton instance
export const modelTester = new ModelTester();

// Export types
export type { ModelTestResult };