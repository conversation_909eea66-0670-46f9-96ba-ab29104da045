import OpenAI from 'openai';
import { apiLogger } from '@/lib/logger';
import { promptEnhancer, EnhancementOptions } from './advanced-prompt-enhancer';
import { generateImagen4, Imagen4Params, isImagen4Available } from './google/imagen4';

// OpenAI client configured for LiteLLM proxy
const openai = new OpenAI({
  apiKey: process.env.LITELLM_MASTER_KEY || 'sk-simplechat-master-2025',
  baseURL: process.env.LITELLM_BASE_URL || 'http://litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com/v1',
});

export interface ImageGenerationParams {
  prompt: string;
  model?: 'gpt-image-1' | 'dall-e-3' | 'dall-e-2' | 'imagen-4';
  size?: string;
  n?: number;
  quality?: 'standard' | 'hd';
  style?: 'vivid' | 'natural';
  response_format?: 'url' | 'b64_json';
  enhancePrompt?: boolean;
  enhancementOptions?: EnhancementOptions;
  // Imagen 4 specific parameters
  aspectRatio?: '1:1' | '9:16' | '16:9' | '4:3' | '3:4';
  safetyFilterLevel?: 'block_most' | 'block_some' | 'block_few' | 'block_least';
  addWatermark?: boolean;
}

export interface ImageGenerationResult {
  images: Array<{
    type: 'url' | 'base64';
    url?: string;
    data?: string;
    revised_prompt?: string;
  }>;
  usage: {
    model: string;
    prompt_tokens: number;
    total_tokens: number;
    estimated_cost: number;
    duration: number;
  };
  metadata: {
    model: string;
    size: string;
    remaining_quota: number;
    originalPrompt?: string;
    enhancedPrompt?: string;
    enhancementResult?: any;
  };
}

export async function generateImage(params: ImageGenerationParams, userId?: string): Promise<ImageGenerationResult> {
  const {
    prompt: originalPrompt,
    model = 'gpt-image-1',
    size = '1024x1024', // Default to smaller size instead of 'auto'
    n = 1,
    quality = 'standard', // Use standard quality instead of HD
    style,
    response_format = 'b64_json', // Explicitly set format
    enhancePrompt = false,
    enhancementOptions = {},
    // Imagen 4 specific parameters
    aspectRatio = '1:1',
    safetyFilterLevel = 'block_some',
    addWatermark = true
  } = params;

  // Handle Imagen 4 model specifically
  if (model === 'imagen-4') {
    if (!isImagen4Available()) {
      throw new Error('Imagen 4 is not configured. Please check Google Cloud credentials.');
    }

    let prompt = originalPrompt;
    let enhancementResult = null;

    // Apply prompt enhancement if requested
    if (enhancePrompt) {
      try {
        enhancementResult = await promptEnhancer.enhancePrompt(originalPrompt, {
          ...enhancementOptions,
          stylePreference: 'photorealistic',
          qualityLevel: quality === 'hd' ? 'professional' : 'enhanced'
        });
        prompt = enhancementResult.enhanced;
      } catch (error) {
        apiLogger.warn('Prompt enhancement failed for Imagen 4, using original', {
          userId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const imagen4Result = await generateImagen4({
      prompt,
      aspectRatio,
      sampleCount: n,
      safetyFilterLevel,
      addWatermark,
      quality: quality === 'hd' ? 'high' : quality
    }, userId);

    // Convert Imagen 4 result to standard format
    return {
      images: imagen4Result.images,
      usage: {
        model: imagen4Result.usage.model,
        prompt_tokens: 0,
        total_tokens: 0,
        estimated_cost: imagen4Result.usage.estimated_cost,
        duration: imagen4Result.usage.duration
      },
      metadata: {
        model: imagen4Result.metadata.model,
        size: aspectRatio,
        remaining_quota: -1,
        originalPrompt: originalPrompt,
        enhancedPrompt: enhancePrompt ? prompt : undefined,
        enhancementResult: enhancementResult ? {
          qualityScore: enhancementResult.qualityScore,
          category: enhancementResult.category,
          improvements: enhancementResult.improvements.length
        } : undefined
      }
    };
  }

  let prompt = originalPrompt;
  let enhancementResult = null;

  // Apply advanced prompt enhancement if requested
  if (enhancePrompt) {
    try {
      apiLogger.info('Starting prompt enhancement', {
        userId,
        originalPromptLength: originalPrompt.length,
        model
      });

      enhancementResult = await promptEnhancer.enhancePrompt(originalPrompt, {
        ...enhancementOptions,
        // Set style preference based on model
        stylePreference: model === 'dall-e-3' ? 'photorealistic' : 
                        model === 'gpt-image-1' ? 'mixed' : 'artistic',
        qualityLevel: quality === 'hd' ? 'professional' : 'enhanced'
      });

      prompt = enhancementResult.enhanced;

      apiLogger.info('Prompt enhancement completed', {
        userId,
        originalLength: originalPrompt.length,
        enhancedLength: prompt.length,
        qualityScore: enhancementResult.qualityScore,
        category: enhancementResult.category.type,
        improvements: enhancementResult.improvements.length
      });

    } catch (error) {
      apiLogger.warn('Prompt enhancement failed, using original', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Fall back to original prompt if enhancement fails
      prompt = originalPrompt;
    }
  }

  apiLogger.info('Image generation request', {
    userId,
    model,
    promptLength: prompt.length,
    size,
    n
  });

  // Build request parameters based on model
  const openAIParams: OpenAI.ImageGenerateParams = {
    prompt,
    model,
    n,
    size: size as any, // Type assertion needed due to OpenAI SDK types
  };

  // Add optional parameters based on model support
  if (model === 'dall-e-3') {
    if (quality) openAIParams.quality = quality;
    if (style) openAIParams.style = style;
    // Default to URL format for better compatibility with LiteLLM
    openAIParams.response_format = response_format || 'url';
  } else if (model === 'dall-e-2') {
    // Default to URL format for better compatibility with LiteLLM
    openAIParams.response_format = response_format || 'url';
  }
  // Note: gpt-image-1 doesn't support these optional parameters but LiteLLM might default to URL

  // Make the request to LiteLLM proxy
  const startTime = Date.now();
  
  try {
    const response = await openai.images.generate(openAIParams);
    
    const duration = Date.now() - startTime;
    
    console.log('[Image Generate] Raw response:', JSON.stringify({
      hasData: !!response.data,
      dataLength: response.data?.length,
      firstItem: response.data?.[0],
      created: response.created
    }, null, 2));
    
    apiLogger.info('Image generation successful', {
      userId,
      model,
      duration,
      imageCount: response.data?.length || 0
    });

    // Process the response
    const images = (response.data || []).map((image) => {
      if ('url' in image && image.url) {
        return {
          type: 'url' as const,
          url: image.url,
          revised_prompt: 'revised_prompt' in image ? image.revised_prompt : undefined
        };
      } else if ('b64_json' in image && image.b64_json) {
        // Clean the base64 data - remove any potential data URI prefix if it exists
        let cleanBase64 = image.b64_json;
        if (cleanBase64.startsWith('data:')) {
          // Extract just the base64 part after the comma
          const commaIndex = cleanBase64.indexOf(',');
          if (commaIndex !== -1) {
            cleanBase64 = cleanBase64.substring(commaIndex + 1);
          }
        }
        return {
          type: 'base64' as const,
          data: cleanBase64,
          revised_prompt: 'revised_prompt' in image ? image.revised_prompt : undefined
        };
      }
      throw new Error('Invalid image response format');
    });

    // Track usage (simplified - in production, calculate actual costs)
    // Note: imagen-4 is handled earlier in the function, so only OpenAI models reach here
    const estimatedCost = model === 'gpt-image-1' ? 0.042 : 
                         model === 'dall-e-3' ? 0.080 : 0.020; // dall-e-2 default

    return {
      images,
      usage: {
        model,
        prompt_tokens: 0, // Not provided by image API
        total_tokens: 0,
        estimated_cost: estimatedCost * n,
        duration
      },
      metadata: {
        model,
        size,
        remaining_quota: -1, // Rate limiting disabled for now
        originalPrompt: originalPrompt,
        enhancedPrompt: enhancePrompt ? prompt : undefined,
        enhancementResult: enhancementResult ? {
          qualityScore: enhancementResult.qualityScore,
          category: enhancementResult.category,
          improvements: enhancementResult.improvements.length
        } : undefined
      }
    };

  } catch (error: any) {
    // Handle OpenAI/LiteLLM errors
    if (error?.status === 400) {
      apiLogger.error('Image generation bad request', {
        userId,
        error: error.message,
        model
      });
      
      // Special handling for model-specific errors
      if (error.message?.includes('response_format')) {
        throw new Error(`Model ${model} does not support response_format parameter`);
      }
      
      throw new Error(error.message || 'Invalid image generation request');
    }

    throw error;
  }
}

/**
 * Generate enhanced images with automatic prompt optimization
 */
export async function generateEnhancedImage(
  prompt: string,
  options: {
    model?: 'gpt-image-1' | 'dall-e-3' | 'dall-e-2' | 'imagen-4';
    size?: string;
    quality?: 'standard' | 'hd';
    style?: 'vivid' | 'natural';
    enhancementOptions?: EnhancementOptions;
    onProgress?: (progress: { step: string; progress: number }) => void;
    // Imagen 4 specific options
    aspectRatio?: '1:1' | '9:16' | '16:9' | '4:3' | '3:4';
    safetyFilterLevel?: 'block_most' | 'block_some' | 'block_few' | 'block_least';
    addWatermark?: boolean;
  } = {},
  userId?: string
): Promise<ImageGenerationResult> {
  const {
    model = 'gpt-image-1',
    size = '1024x1024',
    quality = 'standard',
    style,
    enhancementOptions = {},
    onProgress,
    aspectRatio = '1:1',
    safetyFilterLevel = 'block_some',
    addWatermark = true
  } = options;

  // Always enhance prompts for this function
  return generateImage({
    prompt,
    model,
    size,
    quality,
    style,
    enhancePrompt: true,
    enhancementOptions: {
      ...enhancementOptions,
      streamingCallback: onProgress
    },
    aspectRatio,
    safetyFilterLevel,
    addWatermark
  }, userId);
}

/**
 * Stream enhanced prompt generation with real-time updates
 */
export async function streamEnhancedPrompt(
  prompt: string,
  onProgress: (update: { step: string; progress: number; currentText: string }) => void,
  options: EnhancementOptions = {}
): Promise<string> {
  try {
    return await promptEnhancer.enhancePromptStreaming(prompt, onProgress, options);
  } catch (error) {
    console.error('Stream enhancement failed:', error);
    return prompt; // Fallback to original
  }
}