/**
 * Dynamic Configuration System for AI Provider Routing
 * 
 * @description
 * This module provides a dynamic configuration system that allows real-time
 * updates to routing rules, provider priorities, cost optimization, and 
 * quality requirements without requiring application restarts.
 * 
 * Features:
 * - Real-time configuration updates
 * - Rule-based routing with conditions
 * - Provider health-aware routing
 * - Cost optimization algorithms  
 * - Quality-based model selection
 * - A/B testing support
 * - Geographic routing rules
 * - Time-based routing rules
 * - Configuration validation and rollback
 * 
 * @module lib/ai/dynamic-config
 */

import { EventEmitter } from 'events';
import { apiLogger } from '@/lib/logger';
import { HealthMonitor, HealthMetrics } from './health-monitor';

/**
 * Routing rule condition types
 */
export type ConditionOperator = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'regex';

export interface RoutingCondition {
  field: string;
  operator: ConditionOperator;
  value: any;
}

/**
 * Routing rule with conditions and actions
 */
export interface RoutingRule {
  id: string;
  name: string;
  description?: string;
  priority: number; // Higher number = higher priority
  enabled: boolean;
  conditions: RoutingCondition[];
  actions: {
    preferredProviders?: string[];
    excludedProviders?: string[];
    modelOverride?: string;
    maxCostPerToken?: number;
    minQualityScore?: number;
    routingStrategy?: 'fastest' | 'cheapest' | 'highest_quality' | 'load_balanced';
    fallbackProviders?: string[];
  };
  // Time-based rules
  schedule?: {
    timezone?: string;
    startTime?: string; // HH:MM format
    endTime?: string;
    daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc.
    startDate?: string; // ISO date
    endDate?: string;
  };
  // A/B testing
  abTest?: {
    enabled: boolean;
    percentage: number; // 0-100
    variant: string;
  };
  // Geographic rules
  geographic?: {
    includedRegions?: string[];
    excludedRegions?: string[];
    includedCountries?: string[];
    excludedCountries?: string[];
  };
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  tags?: string[];
}

/**
 * Provider configuration with dynamic settings
 */
export interface ProviderConfig {
  id: string;
  name: string;
  enabled: boolean;
  priority: number; // Base priority (1-100)
  healthWeight: number; // How much health affects routing (0-1)
  costWeight: number; // How much cost affects routing (0-1)
  qualityWeight: number; // How much quality affects routing (0-1)
  maxConcurrentRequests?: number;
  timeoutMs: number;
  retryConfig: {
    maxRetries: number;
    baseDelayMs: number;
    maxDelayMs: number;
    backoffFactor: number;
  };
  costLimits: {
    maxCostPerHour?: number;
    maxCostPerDay?: number;
    maxCostPerRequest?: number;
  };
  qualityRequirements: {
    minSuccessRate: number; // 0-1
    maxLatencyMs: number;
    minQualityScore?: number; // Provider-specific quality metric
  };
  // Model-specific overrides
  modelConfigs?: {
    [modelId: string]: Partial<ProviderConfig>;
  };
}

/**
 * Global routing configuration
 */
export interface GlobalRoutingConfig {
  defaultStrategy: 'round_robin' | 'weighted' | 'health_based' | 'cost_optimized' | 'quality_first';
  failoverEnabled: boolean;
  circuitBreakerEnabled: boolean;
  healthCheckIntervalMs: number;
  configRefreshIntervalMs: number;
  
  // Cost optimization
  costOptimization: {
    enabled: boolean;
    maxCostIncreasePercent: number; // Allow X% cost increase for better quality
    costTrackingWindowMs: number;
  };
  
  // Quality requirements
  qualityOptimization: {
    enabled: boolean;
    minQualityThreshold: number;
    qualityMetrics: string[]; // Which metrics to consider
  };
  
  // Load balancing
  loadBalancing: {
    algorithm: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'response_time';
    healthCheckWeight: number; // 0-1
    performanceWeight: number; // 0-1
  };
  
  // A/B testing
  abTesting: {
    enabled: boolean;
    defaultVariant: string;
    variants: {
      [variant: string]: {
        percentage: number;
        config: Partial<GlobalRoutingConfig>;
      };
    };
  };
}

/**
 * Request context for routing decisions
 */
export interface RoutingContext {
  userId?: string;
  sessionId?: string;
  requestId: string;
  model: string;
  messages: any[];
  
  // Request characteristics
  estimatedTokens?: number;
  priorityLevel?: 'low' | 'normal' | 'high' | 'critical';
  maxLatencyMs?: number;
  maxCostPerToken?: number;
  
  // Geographic context
  country?: string;
  region?: string;
  timezone?: string;
  
  // User context
  userTier?: 'free' | 'premium' | 'enterprise';
  abTestVariant?: string;
  
  // Request metadata
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Routing decision result
 */
export interface RoutingDecision {
  selectedProvider: string;
  selectedModel: string;
  fallbackProviders: string[];
  reasoning: string[];
  appliedRules: string[];
  metrics: {
    healthScore: number;
    costScore: number;
    qualityScore: number;
    finalScore: number;
  };
  estimatedCost?: number;
  estimatedLatency?: number;
  configVersion: string;
}

/**
 * Configuration change event
 */
export interface ConfigChangeEvent {
  type: 'rule_added' | 'rule_updated' | 'rule_removed' | 'provider_updated' | 'global_updated';
  timestamp: Date;
  data: any;
  version: string;
  userId?: string;
}

/**
 * Dynamic Configuration Manager
 */
export class DynamicConfigManager extends EventEmitter {
  private rules: Map<string, RoutingRule> = new Map();
  private providers: Map<string, ProviderConfig> = new Map();
  private globalConfig: GlobalRoutingConfig;
  private configVersion: string;
  private healthMonitor?: HealthMonitor;
  private refreshTimer?: NodeJS.Timeout;
  
  constructor(
    initialGlobalConfig?: Partial<GlobalRoutingConfig>,
    healthMonitor?: HealthMonitor
  ) {
    super();
    
    this.globalConfig = {
      defaultStrategy: 'health_based',
      failoverEnabled: true,
      circuitBreakerEnabled: true,
      healthCheckIntervalMs: 30000,
      configRefreshIntervalMs: 60000,
      costOptimization: {
        enabled: true,
        maxCostIncreasePercent: 25,
        costTrackingWindowMs: 3600000 // 1 hour
      },
      qualityOptimization: {
        enabled: true,
        minQualityThreshold: 0.8,
        qualityMetrics: ['success_rate', 'latency', 'error_rate']
      },
      loadBalancing: {
        algorithm: 'weighted_round_robin',
        healthCheckWeight: 0.4,
        performanceWeight: 0.6
      },
      abTesting: {
        enabled: false,
        defaultVariant: 'control',
        variants: {}
      },
      ...initialGlobalConfig
    };
    
    this.configVersion = this.generateVersion();
    this.healthMonitor = healthMonitor;
    
    this.startConfigRefresh();
  }

  /**
   * Add or update a routing rule
   */
  addRule(rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'>): void {
    const now = new Date();
    const fullRule: RoutingRule = {
      ...rule,
      createdAt: this.rules.get(rule.id)?.createdAt || now,
      updatedAt: now
    };
    
    // Validate rule
    this.validateRule(fullRule);
    
    const isUpdate = this.rules.has(rule.id);
    this.rules.set(rule.id, fullRule);
    
    this.updateVersion();
    
    this.emit('configChanged', {
      type: isUpdate ? 'rule_updated' : 'rule_added',
      timestamp: now,
      data: fullRule,
      version: this.configVersion
    } as ConfigChangeEvent);
    
    apiLogger.info(`Routing rule ${isUpdate ? 'updated' : 'added'}`, {
      ruleId: rule.id,
      ruleName: rule.name,
      version: this.configVersion
    });
  }

  /**
   * Remove a routing rule
   */
  removeRule(ruleId: string): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      return false;
    }
    
    this.rules.delete(ruleId);
    this.updateVersion();
    
    this.emit('configChanged', {
      type: 'rule_removed',
      timestamp: new Date(),
      data: { ruleId },
      version: this.configVersion
    } as ConfigChangeEvent);
    
    apiLogger.info('Routing rule removed', {
      ruleId,
      version: this.configVersion
    });
    
    return true;
  }

  /**
   * Update provider configuration
   */
  updateProvider(providerConfig: ProviderConfig): void {
    this.validateProviderConfig(providerConfig);
    
    this.providers.set(providerConfig.id, providerConfig);
    this.updateVersion();
    
    this.emit('configChanged', {
      type: 'provider_updated',
      timestamp: new Date(),
      data: providerConfig,
      version: this.configVersion
    } as ConfigChangeEvent);
    
    apiLogger.info('Provider configuration updated', {
      providerId: providerConfig.id,
      version: this.configVersion
    });
  }

  /**
   * Update global configuration
   */
  updateGlobalConfig(updates: Partial<GlobalRoutingConfig>): void {
    this.globalConfig = { ...this.globalConfig, ...updates };
    this.updateVersion();
    
    this.emit('configChanged', {
      type: 'global_updated',
      timestamp: new Date(),
      data: updates,
      version: this.configVersion
    } as ConfigChangeEvent);
    
    apiLogger.info('Global configuration updated', {
      updates: Object.keys(updates),
      version: this.configVersion
    });
  }

  /**
   * Make routing decision based on current configuration
   */
  async makeRoutingDecision(context: RoutingContext): Promise<RoutingDecision> {
    const reasoning: string[] = [];
    const appliedRules: string[] = [];
    
    // Get applicable rules sorted by priority
    const applicableRules = this.getApplicableRules(context);
    reasoning.push(`Found ${applicableRules.length} applicable rules`);
    
    // Get available providers
    const availableProviders = await this.getAvailableProviders(context);
    reasoning.push(`${availableProviders.length} providers available`);
    
    if (availableProviders.length === 0) {
      throw new Error('No available providers for routing decision');
    }
    
    // Apply rules to filter and prioritize providers
    let candidateProviders = availableProviders;
    
    for (const rule of applicableRules) {
      appliedRules.push(rule.id);
      
      if (rule.actions.excludedProviders) {
        candidateProviders = candidateProviders.filter(p => 
          !rule.actions.excludedProviders!.includes(p.id)
        );
        reasoning.push(`Rule ${rule.name}: excluded ${rule.actions.excludedProviders.length} providers`);
      }
      
      if (rule.actions.preferredProviders) {
        const preferred = candidateProviders.filter(p => 
          rule.actions.preferredProviders!.includes(p.id)
        );
        if (preferred.length > 0) {
          candidateProviders = preferred;
          reasoning.push(`Rule ${rule.name}: filtered to ${preferred.length} preferred providers`);
        }
      }
    }
    
    if (candidateProviders.length === 0) {
      candidateProviders = availableProviders;
      reasoning.push('No providers left after rules, using all available');
    }
    
    // Score and rank providers
    const scoredProviders = await this.scoreProviders(candidateProviders, context);
    reasoning.push(`Scored ${scoredProviders.length} providers`);
    
    // Select best provider
    const selectedProvider = scoredProviders[0];
    const fallbackProviders = scoredProviders.slice(1, 4).map(p => p.provider.id);
    
    // Determine model
    let selectedModel = context.model;
    const modelOverrideRule = applicableRules.find(r => r.actions.modelOverride);
    if (modelOverrideRule) {
      selectedModel = modelOverrideRule.actions.modelOverride!;
      reasoning.push(`Model overridden by rule ${modelOverrideRule.name}: ${selectedModel}`);
    }
    
    reasoning.push(`Selected provider: ${selectedProvider.provider.name} (score: ${selectedProvider.score.toFixed(3)})`);
    
    return {
      selectedProvider: selectedProvider.provider.id,
      selectedModel,
      fallbackProviders,
      reasoning,
      appliedRules,
      metrics: {
        healthScore: selectedProvider.healthScore,
        costScore: selectedProvider.costScore,
        qualityScore: selectedProvider.qualityScore,
        finalScore: selectedProvider.score
      },
      estimatedCost: selectedProvider.estimatedCost,
      estimatedLatency: selectedProvider.estimatedLatency,
      configVersion: this.configVersion
    };
  }

  /**
   * Get current configuration state
   */
  getConfiguration(): {
    rules: RoutingRule[];
    providers: ProviderConfig[];
    globalConfig: GlobalRoutingConfig;
    version: string;
  } {
    return {
      rules: Array.from(this.rules.values()),
      providers: Array.from(this.providers.values()),
      globalConfig: this.globalConfig,
      version: this.configVersion
    };
  }

  /**
   * Load configuration from external source
   */
  async loadConfiguration(source: 'database' | 'file' | 'api', params?: any): Promise<void> {
    try {
      let config;
      
      switch (source) {
        case 'database':
          config = await this.loadFromDatabase(params);
          break;
        case 'file':
          config = await this.loadFromFile(params?.filePath);
          break;
        case 'api':
          config = await this.loadFromAPI(params?.endpoint);
          break;
        default:
          throw new Error(`Unsupported configuration source: ${source}`);
      }
      
      this.applyConfiguration(config);
      
      apiLogger.info('Configuration loaded successfully', {
        source,
        version: this.configVersion,
        rulesCount: this.rules.size,
        providersCount: this.providers.size
      });
      
    } catch (error) {
      apiLogger.error('Failed to load configuration', { source, error });
      throw error;
    }
  }

  /**
   * Export current configuration
   */
  exportConfiguration(): any {
    return {
      version: this.configVersion,
      timestamp: new Date().toISOString(),
      rules: Array.from(this.rules.values()),
      providers: Array.from(this.providers.values()),
      globalConfig: this.globalConfig
    };
  }

  /**
   * Validate a configuration before applying
   */
  validateConfiguration(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (config.rules) {
      for (const rule of config.rules) {
        try {
          this.validateRule(rule);
        } catch (error) {
          errors.push(`Rule ${rule.id}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }
    
    if (config.providers) {
      for (const provider of config.providers) {
        try {
          this.validateProviderConfig(provider);
        } catch (error) {
          errors.push(`Provider ${provider.id}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get applicable rules for a context
   */
  private getApplicableRules(context: RoutingContext): RoutingRule[] {
    const now = new Date();
    
    return Array.from(this.rules.values())
      .filter(rule => {
        if (!rule.enabled) return false;
        
        // Check schedule
        if (rule.schedule && !this.isScheduleActive(rule.schedule, now)) {
          return false;
        }
        
        // Check A/B test
        if (rule.abTest?.enabled && !this.isAbTestActive(rule.abTest, context)) {
          return false;
        }
        
        // Check geographic rules
        if (rule.geographic && !this.isGeographicallyApplicable(rule.geographic, context)) {
          return false;
        }
        
        // Check conditions
        return this.evaluateConditions(rule.conditions, context);
      })
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Score providers based on health, cost, and quality
   */
  private async scoreProviders(
    providers: ProviderConfig[],
    context: RoutingContext
  ): Promise<Array<{
    provider: ProviderConfig;
    score: number;
    healthScore: number;
    costScore: number;
    qualityScore: number;
    estimatedCost?: number;
    estimatedLatency?: number;
  }>> {
    const scored = await Promise.all(
      providers.map(async provider => {
        const healthScore = await this.calculateHealthScore(provider);
        const costScore = this.calculateCostScore(provider, context);
        const qualityScore = this.calculateQualityScore(provider, context);
        
        // Weighted final score
        const finalScore = 
          (healthScore * provider.healthWeight) +
          (costScore * provider.costWeight) +
          (qualityScore * provider.qualityWeight) +
          (provider.priority / 100);
        
        return {
          provider,
          score: finalScore,
          healthScore,
          costScore,
          qualityScore,
          estimatedCost: this.estimateCost(provider, context),
          estimatedLatency: this.estimateLatency(provider, context)
        };
      })
    );
    
    return scored.sort((a, b) => b.score - a.score);
  }

  // Implementation methods for various calculations and validations...
  
  private async getAvailableProviders(context: RoutingContext): Promise<ProviderConfig[]> {
    return Array.from(this.providers.values()).filter(p => p.enabled);
  }

  private async calculateHealthScore(provider: ProviderConfig): Promise<number> {
    if (!this.healthMonitor) return 1.0;
    
    const metrics = this.healthMonitor.getMetrics(provider.id);
    if (!metrics) return 1.0;
    
    return metrics.healthScore / 100;
  }

  private calculateCostScore(provider: ProviderConfig, context: RoutingContext): number {
    // Higher score = lower cost (better)
    // This would integrate with actual cost calculation logic
    return 0.8; // Placeholder
  }

  private calculateQualityScore(provider: ProviderConfig, context: RoutingContext): number {
    // Higher score = better quality
    // This would integrate with quality metrics
    return 0.9; // Placeholder
  }

  private estimateCost(provider: ProviderConfig, context: RoutingContext): number {
    // Cost estimation logic
    return 0.001; // Placeholder
  }

  private estimateLatency(provider: ProviderConfig, context: RoutingContext): number {
    // Latency estimation logic
    return 500; // Placeholder
  }

  private evaluateConditions(conditions: RoutingCondition[], context: RoutingContext): boolean {
    return conditions.every(condition => this.evaluateCondition(condition, context));
  }

  private evaluateCondition(condition: RoutingCondition, context: any): boolean {
    const value = this.getContextValue(condition.field, context);
    
    switch (condition.operator) {
      case 'eq': return value === condition.value;
      case 'ne': return value !== condition.value;
      case 'gt': return value > condition.value;
      case 'gte': return value >= condition.value;
      case 'lt': return value < condition.value;
      case 'lte': return value <= condition.value;
      case 'in': return Array.isArray(condition.value) && condition.value.includes(value);
      case 'nin': return Array.isArray(condition.value) && !condition.value.includes(value);
      case 'contains': return String(value).includes(condition.value);
      case 'regex': return new RegExp(condition.value).test(String(value));
      default: return true;
    }
  }

  private getContextValue(field: string, context: any): any {
    return field.split('.').reduce((obj, key) => obj?.[key], context);
  }

  private isScheduleActive(schedule: NonNullable<RoutingRule['schedule']>, now: Date): boolean {
    // Schedule validation logic
    return true; // Placeholder
  }

  private isAbTestActive(abTest: NonNullable<RoutingRule['abTest']>, context: RoutingContext): boolean {
    // A/B test logic
    return true; // Placeholder
  }

  private isGeographicallyApplicable(geographic: NonNullable<RoutingRule['geographic']>, context: RoutingContext): boolean {
    // Geographic filtering logic
    return true; // Placeholder
  }

  private validateRule(rule: RoutingRule): void {
    if (!rule.id || !rule.name) {
      throw new Error('Rule must have id and name');
    }
    
    if (rule.priority < 0 || rule.priority > 1000) {
      throw new Error('Rule priority must be between 0 and 1000');
    }
    
    // Additional validation logic...
  }

  private validateProviderConfig(config: ProviderConfig): void {
    if (!config.id || !config.name) {
      throw new Error('Provider config must have id and name');
    }
    
    if (config.priority < 1 || config.priority > 100) {
      throw new Error('Provider priority must be between 1 and 100');
    }
    
    // Additional validation logic...
  }

  private generateVersion(): string {
    return `v${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateVersion(): void {
    this.configVersion = this.generateVersion();
  }

  private startConfigRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    
    this.refreshTimer = setInterval(() => {
      this.emit('configRefresh');
    }, this.globalConfig.configRefreshIntervalMs);
  }

  private async loadFromDatabase(params: any): Promise<any> {
    // Database loading logic
    throw new Error('Database loading not implemented');
  }

  private async loadFromFile(filePath: string): Promise<any> {
    // File loading logic
    throw new Error('File loading not implemented');
  }

  private async loadFromAPI(endpoint: string): Promise<any> {
    // API loading logic
    throw new Error('API loading not implemented');
  }

  private applyConfiguration(config: any): void {
    if (config.rules) {
      this.rules.clear();
      for (const rule of config.rules) {
        this.rules.set(rule.id, rule);
      }
    }
    
    if (config.providers) {
      this.providers.clear();
      for (const provider of config.providers) {
        this.providers.set(provider.id, provider);
      }
    }
    
    if (config.globalConfig) {
      this.globalConfig = { ...this.globalConfig, ...config.globalConfig };
    }
    
    this.updateVersion();
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    this.removeAllListeners();
  }
}

// Singleton instance
export const dynamicConfigManager = new DynamicConfigManager();