import { HealthMonitor, HealthMetrics, HealthAlert } from '../health-monitor';
import { AIProvider, ModelInfo } from '../providers/types';

// Mock AIProvider for testing
class MockAIProvider extends AIProvider {
  public readonly providerName = 'MockProvider';
  
  constructor(private shouldValidate: boolean = true) {
    super();
  }

  async generateCompletion(): Promise<string> {
    return 'mock response';
  }

  async *generateStream() {
    yield { content: 'mock stream' };
  }

  async listModels(): Promise<ModelInfo[]> {
    return [
      {
        id: 'mock-model-1',
        name: 'Mock Model 1',
        provider: 'mock',
        contextWindow: 4096,
        maxOutput: 2048,
        inputCost: 0.001,
        outputCost: 0.002,
        capabilities: ['chat'],
        supportsStreaming: true
      }
    ];
  }

  async validateConfig(): Promise<boolean> {
    return this.shouldValidate;
  }

  setValidationResult(result: boolean) {
    this.shouldValidate = result;
  }
}

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('HealthMonitor', () => {
  let healthMonitor: HealthMonitor;
  let mockProvider: MockAIProvider;

  beforeEach(() => {
    jest.useFakeTimers();
    
    healthMonitor = new HealthMonitor({
      healthCheckInterval: 1000, // 1 second for testing
      monitoringWindow: 10000, // 10 seconds for testing
      healthyThreshold: 0.9,
      degradedThreshold: 0.7,
      unhealthyThreshold: 0.5,
      latencyThreshold: 5000,
      consecutiveFailureThreshold: 3,
      minRequestsForScore: 5
    });

    mockProvider = new MockAIProvider();
  });

  afterEach(() => {
    healthMonitor.stop();
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  describe('provider registration', () => {
    test('should register provider without models', () => {
      healthMonitor.registerProvider(mockProvider);

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics).toBeTruthy();
      expect(metrics?.provider).toBe('MockProvider');
      expect(metrics?.model).toBeUndefined();
      expect(metrics?.isHealthy).toBe(true);
      expect(metrics?.status).toBe('healthy');
    });

    test('should register provider with specific models', () => {
      healthMonitor.registerProvider(mockProvider, ['model-1', 'model-2']);

      const model1Metrics = healthMonitor.getMetrics('MockProvider-model-1');
      const model2Metrics = healthMonitor.getMetrics('MockProvider-model-2');

      expect(model1Metrics).toBeTruthy();
      expect(model2Metrics).toBeTruthy();
      expect(model1Metrics?.model).toBe('model-1');
      expect(model2Metrics?.model).toBe('model-2');
    });

    test('should start health checks after registration', async () => {
      const validateSpy = jest.spyOn(mockProvider, 'validateConfig');
      
      healthMonitor.registerProvider(mockProvider);
      healthMonitor.start();

      // Fast-forward past health check interval
      jest.advanceTimersByTime(1100);
      await Promise.resolve(); // Allow async operations to complete

      expect(validateSpy).toHaveBeenCalled();
    });
  });

  describe('request recording', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider);
    });

    test('should record successful requests', () => {
      healthMonitor.recordRequest('MockProvider', true, 1000);

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.totalRequests).toBe(1);
      expect(metrics?.totalSuccesses).toBe(1);
      expect(metrics?.totalFailures).toBe(0);
      expect(metrics?.successRate).toBe(1.0);
      expect(metrics?.averageLatency).toBe(1000);
      expect(metrics?.consecutiveSuccesses).toBe(1);
      expect(metrics?.consecutiveFailures).toBe(0);
    });

    test('should record failed requests', () => {
      healthMonitor.recordRequest('MockProvider', false, 2000);

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.totalRequests).toBe(1);
      expect(metrics?.totalSuccesses).toBe(0);
      expect(metrics?.totalFailures).toBe(1);
      expect(metrics?.successRate).toBe(0.0);
      expect(metrics?.averageLatency).toBe(2000);
      expect(metrics?.consecutiveSuccesses).toBe(0);
      expect(metrics?.consecutiveFailures).toBe(1);
    });

    test('should calculate metrics correctly for mixed results', () => {
      // Record multiple requests
      healthMonitor.recordRequest('MockProvider', true, 1000);
      healthMonitor.recordRequest('MockProvider', true, 1500);
      healthMonitor.recordRequest('MockProvider', false, 2000);
      healthMonitor.recordRequest('MockProvider', true, 1200);

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.totalRequests).toBe(4);
      expect(metrics?.totalSuccesses).toBe(3);
      expect(metrics?.totalFailures).toBe(1);
      expect(metrics?.successRate).toBe(0.75);
      expect(metrics?.averageLatency).toBe(1425); // (1000+1500+2000+1200)/4
      expect(metrics?.consecutiveSuccesses).toBe(1); // Last request was success
      expect(metrics?.consecutiveFailures).toBe(0);
    });

    test('should auto-initialize provider if not found', () => {
      healthMonitor.recordRequest('UnknownProvider', true, 1000);

      const metrics = healthMonitor.getMetrics('UnknownProvider');
      expect(metrics).toBeTruthy();
      expect(metrics?.provider).toBe('UnknownProvider');
    });
  });

  describe('health status calculation', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider);
    });

    test('should calculate healthy status', () => {
      // Record high success rate
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', true, 1000);
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.status).toBe('healthy');
      expect(metrics?.healthScore).toBeGreaterThan(90);
      expect(metrics?.isHealthy).toBe(true);
    });

    test('should calculate degraded status', () => {
      // Record medium success rate
      for (let i = 0; i < 10; i++) {
        const isSuccess = i < 8; // 80% success rate
        healthMonitor.recordRequest('MockProvider', isSuccess, 1000);
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.status).toBe('degraded');
      expect(metrics?.healthScore).toBeLessThan(90);
      expect(metrics?.healthScore).toBeGreaterThan(60);
      expect(metrics?.isHealthy).toBe(true); // Degraded is still considered healthy
    });

    test('should calculate unhealthy status', () => {
      // Record low success rate
      for (let i = 0; i < 10; i++) {
        const isSuccess = i < 6; // 60% success rate
        healthMonitor.recordRequest('MockProvider', isSuccess, 1000);
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.status).toBe('unhealthy');
      expect(metrics?.healthScore).toBeLessThan(70);
      expect(metrics?.isHealthy).toBe(false);
    });

    test('should calculate critical status for consecutive failures', () => {
      // Record consecutive failures
      for (let i = 0; i < 5; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.status).toBe('critical');
      expect(metrics?.isHealthy).toBe(false);
    });

    test('should penalize high latency', () => {
      // Record requests with high latency
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', true, 8000); // High latency
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.healthScore).toBeLessThan(100);
      expect(metrics?.status).toBe('unhealthy'); // High latency should make it unhealthy
    });
  });

  describe('uptime calculation', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider);
    });

    test('should calculate 100% uptime for all successful requests', () => {
      const now = Date.now();
      jest.setSystemTime(now);

      // Record requests over time
      for (let i = 0; i < 5; i++) {
        jest.setSystemTime(now + i * 2000); // Every 2 seconds
        healthMonitor.recordRequest('MockProvider', true, 1000);
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.uptime).toBe(100);
    });

    test('should calculate partial uptime for mixed results', () => {
      const now = Date.now();
      jest.setSystemTime(now);

      // Record mixed success/failure over time
      for (let i = 0; i < 10; i++) {
        jest.setSystemTime(now + i * 1000); // Every second
        const isSuccess = i % 2 === 0; // 50% success rate
        healthMonitor.recordRequest('MockProvider', isSuccess, 1000);
      }

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.uptime).toBeLessThan(100);
      expect(metrics?.uptime).toBeGreaterThan(0);
    });
  });

  describe('alerting system', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider);
    });

    test('should generate alerts for unhealthy providers', () => {
      // Make provider unhealthy
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }

      const alerts = healthMonitor.getAlerts();
      expect(alerts.length).toBeGreaterThan(0);
      
      const alert = alerts[0];
      expect(alert.provider).toBe('MockProvider');
      expect(alert.severity).toBe('critical');
      expect(alert.resolved).toBe(false);
      expect(alert.message).toContain('critical');
    });

    test('should resolve alerts when provider becomes healthy', () => {
      // Make provider unhealthy first
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }

      let alerts = healthMonitor.getAlerts();
      expect(alerts.length).toBeGreaterThan(0);

      // Make provider healthy again
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', true, 1000);
      }

      alerts = healthMonitor.getAlerts();
      const resolvedAlert = alerts.find(a => a.resolved);
      expect(resolvedAlert).toBeTruthy();
      expect(resolvedAlert?.resolvedAt).toBeTruthy();
    });

    test('should not duplicate alerts for same issue', () => {
      // Make provider unhealthy
      for (let i = 0; i < 5; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }

      const alerts1 = healthMonitor.getAlerts();
      
      // Continue making it unhealthy
      for (let i = 0; i < 5; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }

      const alerts2 = healthMonitor.getAlerts();
      
      // Should not create duplicate alerts
      expect(alerts2.length).toBe(alerts1.length);
    });

    test('should filter resolved alerts when requested', () => {
      // Create and resolve an alert
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }
      
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', true, 1000);
      }

      const allAlerts = healthMonitor.getAlerts(true);
      const activeAlerts = healthMonitor.getAlerts(false);

      expect(allAlerts.length).toBeGreaterThan(activeAlerts.length);
      expect(activeAlerts.every(alert => !alert.resolved)).toBe(true);
    });
  });

  describe('health check automation', () => {
    test('should perform automatic health checks', async () => {
      const validateSpy = jest.spyOn(mockProvider, 'validateConfig');
      
      healthMonitor.registerProvider(mockProvider);
      healthMonitor.start();

      // Fast-forward through multiple health check intervals
      jest.advanceTimersByTime(3100);
      await Promise.resolve();

      expect(validateSpy).toHaveBeenCalledTimes(3);
    });

    test('should record health check results', async () => {
      mockProvider.setValidationResult(false);
      
      healthMonitor.registerProvider(mockProvider);
      healthMonitor.start();

      jest.advanceTimersByTime(1100);
      await Promise.resolve();

      const metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.totalFailures).toBeGreaterThan(0);
      expect(metrics?.lastFailureTime).toBeGreaterThan(0);
    });

    test('should stop health checks when stopped', async () => {
      const validateSpy = jest.spyOn(mockProvider, 'validateConfig');
      
      healthMonitor.registerProvider(mockProvider);
      healthMonitor.start();
      
      jest.advanceTimersByTime(1100);
      await Promise.resolve();
      
      healthMonitor.stop();
      
      jest.advanceTimersByTime(2000);
      await Promise.resolve();

      // Should not call validate after stopping
      expect(validateSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('data cleanup', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider);
    });

    test('should clean up old data outside monitoring window', () => {
      const now = Date.now();
      jest.setSystemTime(now);

      // Record old request
      healthMonitor.recordRequest('MockProvider', true, 1000);

      // Move time forward beyond monitoring window
      jest.setSystemTime(now + 15000); // 15 seconds (beyond 10s window)

      // Record new request to trigger cleanup
      healthMonitor.recordRequest('MockProvider', true, 1000);

      const metrics = healthMonitor.getMetrics('MockProvider');
      // Should only count the recent request in success rate calculation
      expect(metrics?.totalRequests).toBe(2); // Total is cumulative
      // But success rate should be based on monitoring window
    });
  });

  describe('provider queries', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider, ['model-1', 'model-2']);
      healthMonitor.registerProvider(new MockAIProvider(), ['model-3']);
    });

    test('should get all metrics', () => {
      const allMetrics = healthMonitor.getAllMetrics();
      
      expect(allMetrics.size).toBeGreaterThan(0);
      expect(allMetrics.has('MockProvider-model-1')).toBe(true);
      expect(allMetrics.has('MockProvider-model-2')).toBe(true);
    });

    test('should get provider-specific metrics', () => {
      const providerMetrics = healthMonitor.getProviderMetrics('MockProvider');
      
      expect(providerMetrics.length).toBe(2); // model-1 and model-2
      expect(providerMetrics.every(m => m.provider === 'MockProvider')).toBe(true);
    });

    test('should return health summary', () => {
      // Record some data
      healthMonitor.recordRequest('MockProvider-model-1', true, 1000);
      healthMonitor.recordRequest('MockProvider-model-2', false, 2000);

      const summary = healthMonitor.getHealthSummary();

      expect(summary.totalProviders).toBeGreaterThan(0);
      expect(summary.healthyProviders).toBeGreaterThanOrEqual(0);
      expect(summary.unhealthyProviders).toBeGreaterThanOrEqual(0);
      expect(summary.averageHealthScore).toBeGreaterThanOrEqual(0);
      expect(summary.averageHealthScore).toBeLessThanOrEqual(100);
    });

    test('should get health score for specific provider', () => {
      healthMonitor.recordRequest('MockProvider', true, 1000);
      
      const healthScore = healthMonitor.getHealthScore('MockProvider');
      expect(healthScore).toBeGreaterThan(0);
      expect(healthScore).toBeLessThanOrEqual(100);
    });

    test('should check if provider is healthy', () => {
      healthMonitor.recordRequest('MockProvider', true, 1000);
      
      const isHealthy = healthMonitor.isHealthy('MockProvider');
      expect(isHealthy).toBe(true);
    });
  });

  describe('metrics reset', () => {
    beforeEach(() => {
      healthMonitor.registerProvider(mockProvider);
    });

    test('should reset metrics for specific provider', () => {
      // Record some data
      healthMonitor.recordRequest('MockProvider', false, 1000);
      healthMonitor.recordRequest('MockProvider', false, 1000);

      let metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.totalFailures).toBe(2);

      // Reset
      healthMonitor.resetMetrics('MockProvider');

      metrics = healthMonitor.getMetrics('MockProvider');
      expect(metrics?.totalRequests).toBe(0);
      expect(metrics?.totalFailures).toBe(0);
      expect(metrics?.status).toBe('healthy');
    });

    test('should clear related alerts when resetting', () => {
      // Create unhealthy provider with alert
      for (let i = 0; i < 10; i++) {
        healthMonitor.recordRequest('MockProvider', false, 1000);
      }

      let alerts = healthMonitor.getAlerts();
      expect(alerts.length).toBeGreaterThan(0);

      // Reset
      healthMonitor.resetMetrics('MockProvider');

      alerts = healthMonitor.getAlerts();
      expect(alerts.length).toBe(0);
    });
  });
});