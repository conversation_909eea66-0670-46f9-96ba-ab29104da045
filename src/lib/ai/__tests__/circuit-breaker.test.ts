import { CircuitBreaker, CircuitBreakerError } from '../circuit-breaker';

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('CircuitBreaker', () => {
  let circuitBreaker: CircuitBreaker;
  
  beforeEach(() => {
    circuitBreaker = new CircuitBreaker({
      failureThreshold: 3,
      recoveryTimeout: 1000,
      monitoringPeriod: 5000,
      halfOpenMaxCalls: 2,
      successThreshold: 2,
      failureRateThreshold: 0.5
    });
  });

  afterEach(() => {
    circuitBreaker.resetAll();
  });

  describe('CLOSED state behavior', () => {
    test('should allow operations in CLOSED state', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await circuitBreaker.execute('test-key', mockOperation);
      
      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    test('should record successful operations', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      await circuitBreaker.execute('test-key', mockOperation);
      
      const metrics = circuitBreaker.getMetrics('test-key');
      expect(metrics?.state).toBe('CLOSED');
      expect(metrics?.successCount).toBe(1);
      expect(metrics?.failureCount).toBe(0);
    });

    test('should transition to OPEN after failure threshold', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'));
      
      // Trigger failures up to threshold
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute('test-key', mockOperation);
        } catch (error) {
          // Expected failures
        }
      }
      
      const metrics = circuitBreaker.getMetrics('test-key');
      expect(metrics?.state).toBe('OPEN');
      expect(metrics?.failureCount).toBe(3);
    });
  });

  describe('OPEN state behavior', () => {
    beforeEach(async () => {
      // Force circuit to OPEN state
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'));
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute('test-key', mockOperation);
        } catch (error) {
          // Expected failures
        }
      }
    });

    test('should reject operations immediately in OPEN state', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      await expect(
        circuitBreaker.execute('test-key', mockOperation)
      ).rejects.toThrow(CircuitBreakerError);
      
      expect(mockOperation).not.toHaveBeenCalled();
    });

    test('should provide retry time in error', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      try {
        await circuitBreaker.execute('test-key', mockOperation);
        fail('Should have thrown CircuitBreakerError');
      } catch (error) {
        expect(error).toBeInstanceOf(CircuitBreakerError);
        expect((error as CircuitBreakerError).nextRetryTime).toBeGreaterThan(Date.now());
      }
    });

    test('should transition to HALF_OPEN after recovery timeout', async () => {
      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const mockOperation = jest.fn().mockResolvedValue('success');
      await circuitBreaker.execute('test-key', mockOperation);
      
      const metrics = circuitBreaker.getMetrics('test-key');
      expect(metrics?.state).toBe('HALF_OPEN');
    });
  });

  describe('HALF_OPEN state behavior', () => {
    beforeEach(async () => {
      // Force circuit to OPEN state
      const mockFailure = jest.fn().mockRejectedValue(new Error('Test error'));
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute('test-key', mockFailure);
        } catch (error) {
          // Expected failures
        }
      }
      
      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      // Trigger transition to HALF_OPEN
      const mockSuccess = jest.fn().mockResolvedValue('success');
      await circuitBreaker.execute('test-key', mockSuccess);
    });

    test('should allow limited operations in HALF_OPEN state', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      // Should allow up to halfOpenMaxCalls
      await circuitBreaker.execute('test-key', mockOperation);
      
      const metrics = circuitBreaker.getMetrics('test-key');
      expect(metrics?.state).toBe('HALF_OPEN');
    });

    test('should transition to CLOSED after success threshold', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      // Execute successful operations to meet success threshold
      await circuitBreaker.execute('test-key', mockOperation);
      
      const metrics = circuitBreaker.getMetrics('test-key');
      expect(metrics?.state).toBe('CLOSED');
    });

    test('should transition to OPEN on any failure', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new Error('Test error'));
      
      try {
        await circuitBreaker.execute('test-key', mockOperation);
        fail('Should have thrown error');
      } catch (error) {
        // Expected
      }
      
      const metrics = circuitBreaker.getMetrics('test-key');
      expect(metrics?.state).toBe('OPEN');
    });

    test('should reject operations after reaching half-open call limit', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      // Execute up to the limit
      await circuitBreaker.execute('test-key', mockOperation);
      
      // This should be rejected due to call limit
      await expect(
        circuitBreaker.execute('test-key', mockOperation)
      ).rejects.toThrow(CircuitBreakerError);
    });
  });

  describe('Failure rate monitoring', () => {
    test('should open circuit based on failure rate threshold', async () => {
      const config = {
        failureThreshold: 10, // High threshold to test rate-based opening
        failureRateThreshold: 0.6 // 60% failure rate
      };
      
      // Execute mixed success/failure operations
      for (let i = 0; i < 10; i++) {
        const shouldFail = i < 7; // 70% failure rate
        const mockOperation = shouldFail
          ? jest.fn().mockRejectedValue(new Error('Test error'))
          : jest.fn().mockResolvedValue('success');
        
        try {
          await circuitBreaker.execute('rate-test-key', mockOperation, config);
        } catch (error) {
          // Expected failures
        }
      }
      
      const metrics = circuitBreaker.getMetrics('rate-test-key');
      expect(metrics?.state).toBe('OPEN');
      expect(metrics?.failureRate).toBeGreaterThan(0.6);
    });
  });

  describe('Metrics and monitoring', () => {
    test('should provide accurate metrics', async () => {
      const successOp = jest.fn().mockResolvedValue('success');
      const failureOp = jest.fn().mockRejectedValue(new Error('Test error'));
      
      // Execute mixed operations
      await circuitBreaker.execute('metrics-key', successOp);
      await circuitBreaker.execute('metrics-key', successOp);
      
      try {
        await circuitBreaker.execute('metrics-key', failureOp);
      } catch (error) {
        // Expected
      }
      
      const metrics = circuitBreaker.getMetrics('metrics-key');
      expect(metrics?.totalRequests).toBe(3);
      expect(metrics?.successCount).toBe(2);
      expect(metrics?.failureCount).toBe(1);
      expect(metrics?.failureRate).toBeCloseTo(0.33, 2);
    });

    test('should track time since last failure', async () => {
      const failureOp = jest.fn().mockRejectedValue(new Error('Test error'));
      
      try {
        await circuitBreaker.execute('time-key', failureOp);
      } catch (error) {
        // Expected
      }
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const metrics = circuitBreaker.getMetrics('time-key');
      expect(metrics?.timeSinceLastFailure).toBeGreaterThan(50);
    });

    test('should return null for non-existent keys', () => {
      const metrics = circuitBreaker.getMetrics('non-existent-key');
      expect(metrics).toBeNull();
    });

    test('should provide all metrics', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      await circuitBreaker.execute('key1', mockOperation);
      await circuitBreaker.execute('key2', mockOperation);
      
      const allMetrics = circuitBreaker.getAllMetrics();
      expect(allMetrics.size).toBe(2);
      expect(allMetrics.has('key1')).toBe(true);
      expect(allMetrics.has('key2')).toBe(true);
    });
  });

  describe('Reset functionality', () => {
    test('should reset specific circuit breaker', async () => {
      const failureOp = jest.fn().mockRejectedValue(new Error('Test error'));
      
      // Cause some failures
      for (let i = 0; i < 2; i++) {
        try {
          await circuitBreaker.execute('reset-key', failureOp);
        } catch (error) {
          // Expected
        }
      }
      
      let metrics = circuitBreaker.getMetrics('reset-key');
      expect(metrics?.failureCount).toBe(2);
      
      // Reset
      circuitBreaker.reset('reset-key');
      
      metrics = circuitBreaker.getMetrics('reset-key');
      expect(metrics?.state).toBe('CLOSED');
      expect(metrics?.failureCount).toBe(0);
      expect(metrics?.totalRequests).toBe(0);
    });

    test('should reset all circuit breakers', async () => {
      const failureOp = jest.fn().mockRejectedValue(new Error('Test error'));
      
      // Create failures in multiple circuits
      for (const key of ['key1', 'key2', 'key3']) {
        try {
          await circuitBreaker.execute(key, failureOp);
        } catch (error) {
          // Expected
        }
      }
      
      // Reset all
      circuitBreaker.resetAll();
      
      // Check all are reset
      for (const key of ['key1', 'key2', 'key3']) {
        const metrics = circuitBreaker.getMetrics(key);
        expect(metrics?.state).toBe('CLOSED');
        expect(metrics?.failureCount).toBe(0);
      }
    });
  });

  describe('Error handling', () => {
    test('should preserve original error details', async () => {
      const originalError = new Error('Original error message');
      originalError.stack = 'Original stack trace';
      
      const mockOperation = jest.fn().mockRejectedValue(originalError);
      
      try {
        await circuitBreaker.execute('error-key', mockOperation);
        fail('Should have thrown error');
      } catch (error) {
        expect(error).toBe(originalError);
        expect(error.message).toBe('Original error message');
      }
    });

    test('should handle async errors properly', async () => {
      const mockOperation = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        throw new Error('Async error');
      });
      
      await expect(
        circuitBreaker.execute('async-error-key', mockOperation)
      ).rejects.toThrow('Async error');
    });
  });

  describe('Concurrent operations', () => {
    test('should handle concurrent operations correctly', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      // Execute multiple operations concurrently
      const promises = Array(10).fill(null).map((_, i) => 
        circuitBreaker.execute(`concurrent-${i}`, mockOperation)
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      expect(results.every(result => result === 'success')).toBe(true);
      expect(mockOperation).toHaveBeenCalledTimes(10);
    });

    test('should handle mixed concurrent success/failure', async () => {
      const operations = Array(10).fill(null).map((_, i) => {
        return i % 2 === 0
          ? jest.fn().mockResolvedValue(`success-${i}`)
          : jest.fn().mockRejectedValue(new Error(`error-${i}`));
      });
      
      const promises = operations.map((op, i) => 
        circuitBreaker.execute(`mixed-${i}`, op).catch(error => error)
      );
      
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      
      // Check success results
      const successes = results.filter((_, i) => i % 2 === 0);
      expect(successes.every(result => typeof result === 'string' && result.startsWith('success'))).toBe(true);
      
      // Check error results
      const errors = results.filter((_, i) => i % 2 === 1);
      expect(errors.every(result => result instanceof Error)).toBe(true);
    });
  });

  describe('Configuration validation', () => {
    test('should use default configuration when none provided', () => {
      const defaultCircuitBreaker = new CircuitBreaker();
      
      // Test with default config - should work
      expect(async () => {
        const mockOperation = jest.fn().mockResolvedValue('success');
        await defaultCircuitBreaker.execute('default-test', mockOperation);
      }).not.toThrow();
    });

    test('should merge partial configuration with defaults', () => {
      const partialConfigBreaker = new CircuitBreaker({
        failureThreshold: 10 // Only override one setting
      });
      
      // Should work with merged config
      expect(async () => {
        const mockOperation = jest.fn().mockResolvedValue('success');
        await partialConfigBreaker.execute('partial-test', mockOperation);
      }).not.toThrow();
    });
  });
});