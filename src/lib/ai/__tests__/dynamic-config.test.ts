/**
 * Tests for Dynamic Configuration System
 * 
 * Tests the core dynamic configuration manager functionality including:
 * - Rule management and evaluation
 * - Provider configuration
 * - Routing decision logic
 * - Configuration validation
 * - Event handling
 */

import { 
  DynamicConfigManager, 
  RoutingRule, 
  ProviderConfig, 
  RoutingContext,
  RoutingCondition 
} from '../dynamic-config';
import { HealthMonitor } from '../health-monitor';

// Mock HealthMonitor
const mockHealthMonitor = {
  getMetrics: jest.fn().mockReturnValue({
    provider: 'test-provider',
    isHealthy: true,
    healthScore: 85,
    successRate: 0.95,
    averageLatency: 500,
    status: 'healthy'
  }),
  registerProvider: jest.fn(),
  start: jest.fn(),
  stop: jest.fn()
} as unknown as HealthMonitor;

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('DynamicConfigManager', () => {
  let configManager: DynamicConfigManager;

  beforeEach(() => {
    configManager = new DynamicConfigManager(undefined, mockHealthMonitor);
  });

  afterEach(() => {
    configManager.destroy();
    jest.clearAllMocks();
  });

  describe('Rule Management', () => {
    test('should add a new routing rule', () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'test-rule-1',
        name: 'Test Rule',
        description: 'A test routing rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {
          preferredProviders: ['openai', 'anthropic']
        }
      };

      configManager.addRule(rule);

      const config = configManager.getConfiguration();
      expect(config.rules).toHaveLength(1);
      expect(config.rules[0].id).toBe('test-rule-1');
      expect(config.rules[0].name).toBe('Test Rule');
      expect(config.rules[0].createdAt).toBeInstanceOf(Date);
      expect(config.rules[0].updatedAt).toBeInstanceOf(Date);
    });

    test('should update an existing routing rule', () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'test-rule-1',
        name: 'Test Rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {}
      };

      configManager.addRule(rule);
      
      const originalCreatedAt = configManager.getConfiguration().rules[0].createdAt;
      
      // Wait a bit to ensure different updatedAt
      setTimeout(() => {
        const updatedRule = { ...rule, name: 'Updated Test Rule' };
        configManager.addRule(updatedRule);

        const config = configManager.getConfiguration();
        expect(config.rules).toHaveLength(1);
        expect(config.rules[0].name).toBe('Updated Test Rule');
        expect(config.rules[0].createdAt).toEqual(originalCreatedAt);
        expect(config.rules[0].updatedAt.getTime()).toBeGreaterThan(originalCreatedAt.getTime());
      }, 10);
    });

    test('should remove a routing rule', () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'test-rule-1',
        name: 'Test Rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {}
      };

      configManager.addRule(rule);
      expect(configManager.getConfiguration().rules).toHaveLength(1);

      const removed = configManager.removeRule('test-rule-1');
      expect(removed).toBe(true);
      expect(configManager.getConfiguration().rules).toHaveLength(0);
    });

    test('should return false when removing non-existent rule', () => {
      const removed = configManager.removeRule('non-existent');
      expect(removed).toBe(false);
    });

    test('should validate rule priority range', () => {
      const invalidRule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'invalid-rule',
        name: 'Invalid Rule',
        priority: 1001, // Invalid priority
        enabled: true,
        conditions: [],
        actions: {}
      };

      expect(() => configManager.addRule(invalidRule)).toThrow('Rule priority must be between 0 and 1000');
    });

    test('should validate required rule fields', () => {
      const invalidRule = {
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {}
      } as any;

      expect(() => configManager.addRule(invalidRule)).toThrow('Rule must have id and name');
    });
  });

  describe('Provider Configuration', () => {
    test('should update provider configuration', () => {
      const providerConfig: ProviderConfig = {
        id: 'openai',
        name: 'OpenAI',
        enabled: true,
        priority: 80,
        healthWeight: 0.3,
        costWeight: 0.4,
        qualityWeight: 0.3,
        timeoutMs: 30000,
        retryConfig: {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2
        },
        costLimits: {
          maxCostPerRequest: 0.1
        },
        qualityRequirements: {
          minSuccessRate: 0.9,
          maxLatencyMs: 5000
        }
      };

      configManager.updateProvider(providerConfig);

      const config = configManager.getConfiguration();
      expect(config.providers).toHaveLength(1);
      expect(config.providers[0].id).toBe('openai');
      expect(config.providers[0].priority).toBe(80);
    });

    test('should validate provider priority range', () => {
      const invalidProvider: ProviderConfig = {
        id: 'invalid',
        name: 'Invalid Provider',
        enabled: true,
        priority: 101, // Invalid priority
        healthWeight: 0.3,
        costWeight: 0.4,
        qualityWeight: 0.3,
        timeoutMs: 30000,
        retryConfig: {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2
        },
        costLimits: {},
        qualityRequirements: {
          minSuccessRate: 0.9,
          maxLatencyMs: 5000
        }
      };

      expect(() => configManager.updateProvider(invalidProvider)).toThrow('Provider priority must be between 1 and 100');
    });
  });

  describe('Global Configuration', () => {
    test('should update global configuration', () => {
      const updates = {
        defaultStrategy: 'cost_optimized' as const,
        failoverEnabled: false
      };

      configManager.updateGlobalConfig(updates);

      const config = configManager.getConfiguration();
      expect(config.globalConfig.defaultStrategy).toBe('cost_optimized');
      expect(config.globalConfig.failoverEnabled).toBe(false);
    });
  });

  describe('Routing Decision Logic', () => {
    beforeEach(() => {
      // Set up test providers
      const providers: ProviderConfig[] = [
        {
          id: 'openai',
          name: 'OpenAI',
          enabled: true,
          priority: 80,
          healthWeight: 0.3,
          costWeight: 0.4,
          qualityWeight: 0.3,
          timeoutMs: 30000,
          retryConfig: {
            maxRetries: 3,
            baseDelayMs: 1000,
            maxDelayMs: 10000,
            backoffFactor: 2
          },
          costLimits: {},
          qualityRequirements: {
            minSuccessRate: 0.9,
            maxLatencyMs: 5000
          }
        },
        {
          id: 'anthropic',
          name: 'Anthropic',
          enabled: true,
          priority: 75,
          healthWeight: 0.4,
          costWeight: 0.3,
          qualityWeight: 0.3,
          timeoutMs: 30000,
          retryConfig: {
            maxRetries: 3,
            baseDelayMs: 1000,
            maxDelayMs: 10000,
            backoffFactor: 2
          },
          costLimits: {},
          qualityRequirements: {
            minSuccessRate: 0.85,
            maxLatencyMs: 6000
          }
        }
      ];

      providers.forEach(provider => configManager.updateProvider(provider));
    });

    test('should make basic routing decision', async () => {
      const context: RoutingContext = {
        requestId: 'test-request-1',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date()
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.selectedProvider).toBeTruthy();
      expect(decision.selectedModel).toBe('gpt-4');
      expect(decision.fallbackProviders).toBeInstanceOf(Array);
      expect(decision.reasoning).toBeInstanceOf(Array);
      expect(decision.metrics.finalScore).toBeGreaterThanOrEqual(0);
    });

    test('should apply routing rules', async () => {
      // Add a rule that prefers OpenAI
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'prefer-openai',
        name: 'Prefer OpenAI',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {
          preferredProviders: ['openai']
        }
      };

      configManager.addRule(rule);

      const context: RoutingContext = {
        requestId: 'test-request-2',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date()
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.selectedProvider).toBe('openai');
      expect(decision.appliedRules).toContain('prefer-openai');
    });

    test('should exclude providers based on rules', async () => {
      // Add a rule that excludes OpenAI
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'exclude-openai',
        name: 'Exclude OpenAI',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {
          excludedProviders: ['openai']
        }
      };

      configManager.addRule(rule);

      const context: RoutingContext = {
        requestId: 'test-request-3',
        model: 'claude-3',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date()
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.selectedProvider).toBe('anthropic');
      expect(decision.appliedRules).toContain('exclude-openai');
    });

    test('should handle model override in rules', async () => {
      // Add a rule that overrides the model
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'model-override',
        name: 'Model Override',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {
          modelOverride: 'gpt-3.5-turbo'
        }
      };

      configManager.addRule(rule);

      const context: RoutingContext = {
        requestId: 'test-request-4',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date()
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.selectedModel).toBe('gpt-3.5-turbo');
      expect(decision.appliedRules).toContain('model-override');
    });

    test('should throw error when no providers available', async () => {
      // Disable all providers
      const config = configManager.getConfiguration();
      for (const provider of config.providers) {
        configManager.updateProvider({ ...provider, enabled: false });
      }

      const context: RoutingContext = {
        requestId: 'test-request-5',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date()
      };

      await expect(configManager.makeRoutingDecision(context)).rejects.toThrow('No available providers');
    });
  });

  describe('Condition Evaluation', () => {
    test('should evaluate equality conditions', async () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'user-tier-rule',
        name: 'Premium User Rule',
        priority: 100,
        enabled: true,
        conditions: [
          {
            field: 'userTier',
            operator: 'eq',
            value: 'premium'
          }
        ],
        actions: {
          preferredProviders: ['openai']
        }
      };

      configManager.addRule(rule);
      
      // Set up a provider
      configManager.updateProvider({
        id: 'openai',
        name: 'OpenAI',
        enabled: true,
        priority: 80,
        healthWeight: 0.3,
        costWeight: 0.4,
        qualityWeight: 0.3,
        timeoutMs: 30000,
        retryConfig: {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2
        },
        costLimits: {},
        qualityRequirements: {
          minSuccessRate: 0.9,
          maxLatencyMs: 5000
        }
      });

      const context: RoutingContext = {
        requestId: 'test-request-6',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date(),
        userTier: 'premium'
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.appliedRules).toContain('user-tier-rule');
    });

    test('should evaluate comparison conditions', async () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'token-limit-rule',
        name: 'High Token Count Rule',
        priority: 100,
        enabled: true,
        conditions: [
          {
            field: 'estimatedTokens',
            operator: 'gt',
            value: 1000
          }
        ],
        actions: {
          preferredProviders: ['anthropic']
        }
      };

      configManager.addRule(rule);
      
      // Set up providers
      ['openai', 'anthropic'].forEach(id => {
        configManager.updateProvider({
          id,
          name: id.charAt(0).toUpperCase() + id.slice(1),
          enabled: true,
          priority: 80,
          healthWeight: 0.3,
          costWeight: 0.4,
          qualityWeight: 0.3,
          timeoutMs: 30000,
          retryConfig: {
            maxRetries: 3,
            baseDelayMs: 1000,
            maxDelayMs: 10000,
            backoffFactor: 2
          },
          costLimits: {},
          qualityRequirements: {
            minSuccessRate: 0.9,
            maxLatencyMs: 5000
          }
        });
      });

      const context: RoutingContext = {
        requestId: 'test-request-7',
        model: 'claude-3',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date(),
        estimatedTokens: 1500
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.appliedRules).toContain('token-limit-rule');
    });

    test('should evaluate "in" conditions', async () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'priority-rule',
        name: 'High Priority Rule',
        priority: 100,
        enabled: true,
        conditions: [
          {
            field: 'priorityLevel',
            operator: 'in',
            value: ['high', 'critical']
          }
        ],
        actions: {
          routingStrategy: 'fastest'
        }
      };

      configManager.addRule(rule);
      
      // Set up provider
      configManager.updateProvider({
        id: 'openai',
        name: 'OpenAI',
        enabled: true,
        priority: 80,
        healthWeight: 0.3,
        costWeight: 0.4,
        qualityWeight: 0.3,
        timeoutMs: 30000,
        retryConfig: {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2
        },
        costLimits: {},
        qualityRequirements: {
          minSuccessRate: 0.9,
          maxLatencyMs: 5000
        }
      });

      const context: RoutingContext = {
        requestId: 'test-request-8',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date(),
        priorityLevel: 'critical'
      };

      const decision = await configManager.makeRoutingDecision(context);

      expect(decision.appliedRules).toContain('priority-rule');
    });
  });

  describe('A/B Testing', () => {
    test('should apply A/B test rules based on percentage', async () => {
      const rule: Omit<RoutingRule, 'createdAt' | 'updatedAt'> = {
        id: 'ab-test-rule',
        name: 'A/B Test Rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {
          preferredProviders: ['openai']
        },
        abTest: {
          enabled: true,
          percentage: 50,
          variant: 'test_variant'
        }
      };

      configManager.addRule(rule);
      
      // Set up provider
      configManager.updateProvider({
        id: 'openai',
        name: 'OpenAI',
        enabled: true,
        priority: 80,
        healthWeight: 0.3,
        costWeight: 0.4,
        qualityWeight: 0.3,
        timeoutMs: 30000,
        retryConfig: {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2
        },
        costLimits: {},
        qualityRequirements: {
          minSuccessRate: 0.9,
          maxLatencyMs: 5000
        }
      });

      // The A/B test evaluation is currently a placeholder that returns true
      // In a real implementation, this would check the percentage
      const context: RoutingContext = {
        requestId: 'test-request-9',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Hello' }],
        timestamp: new Date(),
        abTestVariant: 'test_variant'
      };

      const decision = await configManager.makeRoutingDecision(context);

      // Since our A/B test is a placeholder, we just verify the rule can be processed
      expect(decision.selectedProvider).toBeTruthy();
    });
  });

  describe('Configuration Export/Import', () => {
    test('should export configuration', () => {
      // Add some test data
      configManager.addRule({
        id: 'test-rule',
        name: 'Test Rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {}
      });

      configManager.updateProvider({
        id: 'test-provider',
        name: 'Test Provider',
        enabled: true,
        priority: 80,
        healthWeight: 0.3,
        costWeight: 0.4,
        qualityWeight: 0.3,
        timeoutMs: 30000,
        retryConfig: {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 10000,
          backoffFactor: 2
        },
        costLimits: {},
        qualityRequirements: {
          minSuccessRate: 0.9,
          maxLatencyMs: 5000
        }
      });

      const exported = configManager.exportConfiguration();

      expect(exported.version).toBeTruthy();
      expect(exported.timestamp).toBeTruthy();
      expect(exported.rules).toHaveLength(1);
      expect(exported.providers).toHaveLength(1);
      expect(exported.globalConfig).toBeTruthy();
    });

    test('should validate configuration', () => {
      const validConfig = {
        rules: [{
          id: 'valid-rule',
          name: 'Valid Rule',
          priority: 100,
          enabled: true,
          conditions: [],
          actions: {},
          createdAt: new Date(),
          updatedAt: new Date()
        }],
        providers: [{
          id: 'valid-provider',
          name: 'Valid Provider',
          enabled: true,
          priority: 80,
          healthWeight: 0.3,
          costWeight: 0.4,
          qualityWeight: 0.3,
          timeoutMs: 30000,
          retryConfig: {
            maxRetries: 3,
            baseDelayMs: 1000,
            maxDelayMs: 10000,
            backoffFactor: 2
          },
          costLimits: {},
          qualityRequirements: {
            minSuccessRate: 0.9,
            maxLatencyMs: 5000
          }
        }]
      };

      const validation = configManager.validateConfiguration(validConfig);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should detect invalid configuration', () => {
      const invalidConfig = {
        rules: [{
          id: '', // Invalid: empty id
          name: 'Invalid Rule',
          priority: 1001, // Invalid: priority too high
          enabled: true,
          conditions: [],
          actions: {},
          createdAt: new Date(),
          updatedAt: new Date()
        }],
        providers: [{
          id: 'invalid-provider',
          name: '', // Invalid: empty name
          enabled: true,
          priority: 101, // Invalid: priority too high
          healthWeight: 0.3,
          costWeight: 0.4,
          qualityWeight: 0.3,
          timeoutMs: 30000,
          retryConfig: {
            maxRetries: 3,
            baseDelayMs: 1000,
            maxDelayMs: 10000,
            backoffFactor: 2
          },
          costLimits: {},
          qualityRequirements: {
            minSuccessRate: 0.9,
            maxLatencyMs: 5000
          }
        }]
      };

      const validation = configManager.validateConfiguration(invalidConfig);

      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Event Handling', () => {
    test('should emit configuration change events', (done) => {
      configManager.on('configChanged', (event) => {
        expect(event.type).toBe('rule_added');
        expect(event.data.id).toBe('event-test-rule');
        expect(event.version).toBeTruthy();
        done();
      });

      configManager.addRule({
        id: 'event-test-rule',
        name: 'Event Test Rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {}
      });
    });
  });

  describe('Version Management', () => {
    test('should update version when configuration changes', () => {
      const initialConfig = configManager.getConfiguration();
      const initialVersion = initialConfig.version;

      configManager.addRule({
        id: 'version-test-rule',
        name: 'Version Test Rule',
        priority: 100,
        enabled: true,
        conditions: [],
        actions: {}
      });

      const updatedConfig = configManager.getConfiguration();
      const updatedVersion = updatedConfig.version;

      expect(updatedVersion).not.toBe(initialVersion);
    });
  });
});