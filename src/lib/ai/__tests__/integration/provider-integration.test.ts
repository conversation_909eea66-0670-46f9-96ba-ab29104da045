/**
 * Integration Tests for AI Provider Endpoints
 * 
 * @description
 * Tests actual provider integrations with real API endpoints.
 * These tests validate that our provider implementations work correctly
 * with the actual services, including authentication, request formatting,
 * response parsing, and error handling.
 * 
 * @module lib/ai/__tests__/integration/provider-integration
 */

import { OpenAIProvider } from '../../providers/openai';
import { AnthropicProvider } from '../../providers/anthropic';
import { GoogleProvider } from '../../providers/google';
import { GroqProvider } from '../../providers/groq';
import { DeepSeekProvider } from '../../providers/deepseek';
import { MistralProvider } from '../../providers/mistral';
import { CohereProvider } from '../../providers/cohere';
import { PerplexityProvider } from '../../providers/perplexity';
import { XAIProvider } from '../../providers/xai';
import { TogetherAIProvider } from '../../providers/together-ai';
import { OpenRouterProvider } from '../../providers/openrouter';
import { QwenProvider } from '../../providers/qwen';
import { EnhancedProviderBase } from '../../enhanced-provider-base';
import { GenerationOptions, StreamChunk } from '../../providers/types';

// Integration test configuration
const INTEGRATION_TEST_CONFIG = {
  // Test timeout for API calls (30 seconds)
  timeout: 30000,
  
  // Number of retry attempts for flaky tests
  retries: 2,
  
  // Models to test for each provider
  testModels: {
    openai: ['gpt-4o-mini', 'gpt-3.5-turbo'],
    anthropic: ['claude-3-haiku-20240307', 'claude-3-5-sonnet-20241022'],
    google: ['gemini-1.5-flash', 'gemini-1.5-pro'],
    groq: ['llama3-8b-8192', 'mixtral-8x7b-32768'],
    deepseek: ['deepseek-chat'],
    mistral: ['mistral-small-latest', 'mistral-large-latest'],
    cohere: ['command-r', 'command-light'],
    perplexity: ['llama-3.1-sonar-small-128k-online'],
    xai: ['grok-3'],
    together: ['meta-llama/Llama-3.1-8B-Instruct-Turbo'],
    openrouter: ['openai/gpt-4o-mini', 'anthropic/claude-3-haiku'],
    qwen: ['qwen/qwen-2.5-7b-instruct']
  },
  
  // Test prompts
  prompts: {
    simple: 'Hello! Please respond with "Integration test successful"',
    complex: 'Explain the concept of artificial intelligence in exactly 50 words.',
    streaming: 'Count from 1 to 5, with each number on a new line.'
  }
};

// Helper function to check if environment variables are set
function hasValidApiKey(provider: string): boolean {
  const envVars = {
    openai: 'OPENAI_API_KEY',
    anthropic: 'ANTHROPIC_API_KEY', 
    google: 'GOOGLE_API_KEY',
    groq: 'GROQ_API_KEY',
    deepseek: 'DEEPSEEK_API_KEY',
    mistral: 'MISTRAL_API_KEY',
    cohere: 'COHERE_API_KEY',
    perplexity: 'PERPLEXITY_API_KEY',
    xai: 'XAI_API_KEY',
    together: 'TOGETHER_API_KEY',
    openrouter: 'OPENROUTER_API_KEY',
    qwen: 'QWEN_API_KEY'
  };
  
  const envVar = envVars[provider as keyof typeof envVars];
  return !!(envVar && process.env[envVar] && process.env[envVar] !== 'test-key');
}

// Helper function to create provider instance
function createProvider(providerName: string): EnhancedProviderBase | null {
  const apiKey = process.env[`${providerName.toUpperCase()}_API_KEY`];
  if (!apiKey || apiKey === 'test-key') return null;
  
  switch (providerName) {
    case 'openai':
      return new OpenAIProvider({ apiKey });
    case 'anthropic':
      return new AnthropicProvider({ apiKey });
    case 'google':
      return new GoogleProvider({ apiKey });
    case 'groq':
      return new GroqProvider({ apiKey });
    case 'deepseek':
      return new DeepSeekProvider({ apiKey });
    case 'mistral':
      return new MistralProvider({ apiKey });
    case 'cohere':
      return new CohereProvider({ apiKey });
    case 'perplexity':
      return new PerplexityProvider({ apiKey });
    case 'xai':
      return new XAIProvider({ apiKey });
    case 'together':
      return new TogetherAIProvider({ apiKey });
    case 'openrouter':
      return new OpenRouterProvider({ apiKey });
    case 'qwen':
      return new QwenProvider({ apiKey });
    default:
      return null;
  }
}

// Helper function to create test options
function createTestOptions(model: string, prompt: string = INTEGRATION_TEST_CONFIG.prompts.simple): GenerationOptions {
  return {
    model,
    messages: [
      { role: 'user', content: prompt }
    ],
    maxTokens: 100,
    temperature: 0.1
  };
}

// Skip integration tests if running in CI without API keys
const shouldSkipIntegration = process.env.CI === 'true' && !process.env.RUN_INTEGRATION_TESTS;

describe('Provider Integration Tests', () => {
  // Set timeout for all integration tests
  jest.setTimeout(INTEGRATION_TEST_CONFIG.timeout);
  
  beforeAll(() => {
    if (shouldSkipIntegration) {
      console.log('Skipping integration tests in CI environment');
    }
  });

  // Test each provider individually
  Object.keys(INTEGRATION_TEST_CONFIG.testModels).forEach(providerName => {
    describe(`${providerName.toUpperCase()} Provider Integration`, () => {
      let provider: EnhancedProviderBase | null;
      
      beforeAll(() => {
        provider = createProvider(providerName);
      });
      
      beforeEach(() => {
        if (shouldSkipIntegration || !hasValidApiKey(providerName)) {
          pending(`Skipping ${providerName} tests - API key not available`);
        }
        
        if (!provider) {
          pending(`Skipping ${providerName} tests - provider not initialized`);
        }
      });

      describe('Basic functionality', () => {
        test('should validate configuration', async () => {
          const isValid = await provider!.validateConfig();
          expect(isValid).toBe(true);
        });

        test('should list available models', async () => {
          const models = await provider!.listModels();
          expect(Array.isArray(models)).toBe(true);
          expect(models.length).toBeGreaterThan(0);
          
          // Check that models have required properties
          models.forEach(model => {
            expect(model).toHaveProperty('id');
            expect(model).toHaveProperty('name');
            expect(model).toHaveProperty('provider');
            expect(typeof model.id).toBe('string');
            expect(typeof model.name).toBe('string');
          });
        });
      });

      describe('Text generation', () => {
        const testModels = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels];
        
        testModels.forEach(model => {
          test(`should generate text with ${model}`, async () => {
            const options = createTestOptions(model);
            
            const result = await provider!.generateCompletion(options);
            
            expect(typeof result).toBe('string');
            expect(result.length).toBeGreaterThan(0);
            expect(result.toLowerCase()).toContain('integration');
          });

          test(`should handle temperature parameter with ${model}`, async () => {
            const options = createTestOptions(model);
            options.temperature = 0.9;
            
            const result = await provider!.generateCompletion(options);
            
            expect(typeof result).toBe('string');
            expect(result.length).toBeGreaterThan(0);
          });

          test(`should respect maxTokens limit with ${model}`, async () => {
            const options = createTestOptions(model);
            options.maxTokens = 10;
            
            const result = await provider!.generateCompletion(options);
            
            expect(typeof result).toBe('string');
            // With very low maxTokens, response should be short
            expect(result.length).toBeLessThan(100);
          });
        });
      });

      describe('Streaming generation', () => {
        const primaryModel = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0];
        
        test(`should stream text generation with ${primaryModel}`, async () => {
          const options = createTestOptions(primaryModel, INTEGRATION_TEST_CONFIG.prompts.streaming);
          
          const chunks: StreamChunk[] = [];
          let completeText = '';
          
          for await (const chunk of provider!.generateStream(options)) {
            chunks.push(chunk);
            if (chunk.content) {
              completeText += chunk.content;
            }
          }
          
          expect(chunks.length).toBeGreaterThan(0);
          expect(completeText.length).toBeGreaterThan(0);
          
          // Should have finish_reason in final chunk
          const finalChunk = chunks[chunks.length - 1];
          expect(['stop', 'length', 'content_filter']).toContain(finalChunk.finish_reason);
        });

        test(`should handle streaming errors gracefully with ${primaryModel}`, async () => {
          const options = createTestOptions('invalid-model-name-12345');
          
          try {
            for await (const chunk of provider!.generateStream(options)) {
              // Should not reach here for invalid model
              break;
            }
            fail('Expected streaming to fail with invalid model');
          } catch (error) {
            expect(error).toBeTruthy();
          }
        });
      });

      describe('Error handling', () => {
        test('should handle invalid model names', async () => {
          const options = createTestOptions('invalid-model-name-12345');
          
          await expect(provider!.generateCompletion(options)).rejects.toThrow();
        });

        test('should handle malformed requests', async () => {
          const invalidOptions = {
            model: INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0],
            messages: [], // Empty messages array
            maxTokens: -1 // Invalid maxTokens
          } as GenerationOptions;
          
          await expect(provider!.generateCompletion(invalidOptions)).rejects.toThrow();
        });
      });

      describe('Health monitoring integration', () => {
        test('should report health metrics after requests', async () => {
          const model = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0];
          const options = createTestOptions(model);
          
          // Make a successful request
          await provider!.generateCompletion(options);
          
          const metrics = provider!.getHealthMetrics();
          expect(metrics).toBeTruthy();
          expect(metrics.provider).toBe(provider!.providerName);
          expect(metrics.totalRequests).toBeGreaterThan(0);
          expect(metrics.totalSuccesses).toBeGreaterThan(0);
        });

        test('should start and stop health monitoring', () => {
          provider!.startHealthMonitoring();
          expect(provider!.isHealthy()).toBe(true);
          
          provider!.stopHealthMonitoring();
          // Should still be able to get metrics after stopping
          const metrics = provider!.getHealthMetrics();
          expect(metrics).toBeTruthy();
        });
      });

      describe('Performance characteristics', () => {
        test('should complete requests within reasonable time', async () => {
          const model = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0];
          const options = createTestOptions(model);
          
          const startTime = Date.now();
          await provider!.generateCompletion(options);
          const duration = Date.now() - startTime;
          
          // Should complete within 20 seconds
          expect(duration).toBeLessThan(20000);
        });

        test('should handle concurrent requests', async () => {
          const model = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0];
          const options = createTestOptions(model);
          
          // Make 3 concurrent requests
          const promises = Array(3).fill(null).map(() => 
            provider!.generateCompletion(options)
          );
          
          const results = await Promise.all(promises);
          
          expect(results).toHaveLength(3);
          results.forEach(result => {
            expect(typeof result).toBe('string');
            expect(result.length).toBeGreaterThan(0);
          });
        });
      });
    });
  });

  describe('Cross-provider compatibility', () => {
    beforeEach(() => {
      if (shouldSkipIntegration) {
        pending('Skipping cross-provider tests in CI environment');
      }
    });

    test('should handle same prompt consistently across providers', async () => {
      const prompt = 'What is 2 + 2? Answer with just the number.';
      const results: { provider: string; result: string }[] = [];
      
      for (const providerName of Object.keys(INTEGRATION_TEST_CONFIG.testModels)) {
        if (!hasValidApiKey(providerName)) continue;
        
        const provider = createProvider(providerName);
        if (!provider) continue;
        
        try {
          const model = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0];
          const options = createTestOptions(model, prompt);
          const result = await provider.generateCompletion(options);
          
          results.push({ provider: providerName, result });
        } catch (error) {
          console.warn(`Provider ${providerName} failed:`, error);
        }
      }
      
      expect(results.length).toBeGreaterThan(0);
      
      // Most providers should return "4" for 2+2
      const containsFour = results.filter(r => r.result.includes('4'));
      expect(containsFour.length).toBeGreaterThan(results.length * 0.5);
    });

    test('should handle streaming consistently across providers', async () => {
      const prompt = 'Count: 1, 2, 3';
      const results: { provider: string; chunkCount: number }[] = [];
      
      for (const providerName of Object.keys(INTEGRATION_TEST_CONFIG.testModels)) {
        if (!hasValidApiKey(providerName)) continue;
        
        const provider = createProvider(providerName);
        if (!provider) continue;
        
        try {
          const model = INTEGRATION_TEST_CONFIG.testModels[providerName as keyof typeof INTEGRATION_TEST_CONFIG.testModels][0];
          const options = createTestOptions(model, prompt);
          
          let chunkCount = 0;
          for await (const chunk of provider.generateStream(options)) {
            chunkCount++;
          }
          
          results.push({ provider: providerName, chunkCount });
        } catch (error) {
          console.warn(`Streaming failed for ${providerName}:`, error);
        }
      }
      
      expect(results.length).toBeGreaterThan(0);
      
      // All providers should return at least one chunk
      results.forEach(result => {
        expect(result.chunkCount).toBeGreaterThan(0);
      });
    });
  });

  describe('Error recovery and resilience', () => {
    beforeEach(() => {
      if (shouldSkipIntegration) {
        pending('Skipping resilience tests in CI environment');
      }
    });

    test('should recover from temporary failures', async () => {
      // Test with a provider that has good API availability
      if (!hasValidApiKey('openai')) {
        pending('OpenAI API key not available for resilience testing');
      }
      
      const provider = createProvider('openai');
      if (!provider) {
        pending('OpenAI provider not initialized');
      }
      
      // Make multiple requests to test resilience
      const requests = Array(5).fill(null).map(async (_, index) => {
        const options = createTestOptions('gpt-4o-mini', `Test request ${index + 1}`);
        return await provider!.generateCompletion(options);
      });
      
      const results = await Promise.allSettled(requests);
      
      // At least 80% should succeed
      const successful = results.filter(r => r.status === 'fulfilled');
      expect(successful.length).toBeGreaterThanOrEqual(Math.floor(requests.length * 0.8));
    });
  });
});