/**
 * Integration tests for the complete error handling and provider system
 * 
 * These tests verify that all components work together correctly:
 * - EnhancedProviderBase with error handling
 * - Circuit breaker functionality 
 * - Health monitoring and alerting
 * - Retry logic with exponential backoff
 * - Real-world failure scenarios
 */

import { EnhancedProviderBase } from '../enhanced-provider-base';
import { EnhancedError<PERSON>andler } from '../enhanced-error-handler';
import { HealthMonitor, HealthAlert } from '../health-monitor';
import { CircuitBreaker, CircuitBreakerError } from '../circuit-breaker';
import { GenerationOptions, StreamChunk, ModelInfo, ProviderConfig } from '../providers/types';

// Realistic mock provider that simulates real-world scenarios
class IntegrationTestProvider extends EnhancedProviderBase {
  public readonly providerName = 'IntegrationTestProvider';
  
  private scenarios: {
    intermittentFailures: boolean;
    rateLimitHits: boolean;
    networkIssues: boolean;
    serviceOutage: boolean;
    authProblems: boolean;
  } = {
    intermittentFailures: false,
    rateLimitHits: false,
    networkIssues: false,
    serviceOutage: false,
    authProblems: false
  };

  private requestCount = 0;
  private rateLimitResets = 0;

  constructor(config: ProviderConfig = {}) {
    super(config);
  }

  setScenario(scenario: keyof typeof this.scenarios, enabled: boolean) {
    this.scenarios[scenario] = enabled;
    this.requestCount = 0;
    this.rateLimitResets = 0;
  }

  resetScenarios() {
    Object.keys(this.scenarios).forEach(key => {
      this.scenarios[key as keyof typeof this.scenarios] = false;
    });
    this.requestCount = 0;
    this.rateLimitResets = 0;
  }

  async executeRawCompletion(options: GenerationOptions): Promise<string> {
    this.requestCount++;
    
    await this.simulateLatency();
    this.checkForErrors();
    
    return `Response ${this.requestCount} for model ${options.model}`;
  }

  async *executeRawStream(options: GenerationOptions): AsyncGenerator<StreamChunk> {
    this.requestCount++;
    
    await this.simulateLatency();
    this.checkForErrors();
    
    const chunks = ['Hello', ' from', ' stream', ' response', ` ${this.requestCount}`];
    
    for (let i = 0; i < chunks.length; i++) {
      yield {
        content: chunks[i],
        type: 'content',
        provider: this.providerName,
        model: options.model,
        finish_reason: i === chunks.length - 1 ? 'stop' : undefined
      };
      
      // Simulate chunk delay
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  async listModels(): Promise<ModelInfo[]> {
    return [
      {
        id: 'integration-test-model',
        name: 'Integration Test Model',
        provider: 'integration-test',
        contextWindow: 8192,
        maxOutput: 4096,
        inputCost: 0.001,
        outputCost: 0.002,
        capabilities: ['chat', 'streaming'],
        supportsStreaming: true
      }
    ];
  }

  async validateConfig(): Promise<boolean> {
    if (this.scenarios.authProblems) {
      return false;
    }
    return true;
  }

  private async simulateLatency() {
    let baseLatency = 100;
    
    if (this.scenarios.networkIssues) {
      baseLatency = 2000 + Math.random() * 3000; // High latency
    }
    
    await new Promise(resolve => setTimeout(resolve, baseLatency + Math.random() * 100));
  }

  private checkForErrors() {
    // Simulate intermittent failures (20% chance)
    if (this.scenarios.intermittentFailures && Math.random() < 0.2) {
      const error = new Error('Intermittent service error');
      (error as any).status = 503;
      throw error;
    }
    
    // Simulate rate limiting (every 5th request)
    if (this.scenarios.rateLimitHits && this.requestCount % 5 === 0) {
      const error = new Error('Rate limit exceeded');
      (error as any).status = 429;
      (error as any).retryAfter = 2; // 2 seconds
      throw error;
    }
    
    // Simulate network issues (timeout)
    if (this.scenarios.networkIssues && Math.random() < 0.15) {
      const error = new Error('Connection timeout');
      (error as any).code = 'ETIMEDOUT';
      throw error;
    }
    
    // Simulate service outage (all requests fail)
    if (this.scenarios.serviceOutage) {
      const error = new Error('Service temporarily unavailable');
      (error as any).status = 503;
      throw error;
    }
    
    // Simulate auth problems
    if (this.scenarios.authProblems) {
      const error = new Error('Invalid API key');
      (error as any).status = 401;
      throw error;
    }
  }
}

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('Integration Tests - Complete Error Handling System', () => {
  let provider: IntegrationTestProvider;
  let mockOptions: GenerationOptions;

  beforeEach(() => {
    jest.useFakeTimers();
    
    provider = new IntegrationTestProvider({
      apiKey: 'test-integration-key',
      timeout: 10000
    });

    mockOptions = {
      model: 'integration-test-model',
      messages: [
        { role: 'user', content: 'Hello, this is an integration test!' }
      ],
      temperature: 0.7,
      maxTokens: 150,
      userId: 'integration-test-user',
      sessionId: 'integration-test-session'
    };
    
    provider.resetScenarios();
  });

  afterEach(() => {
    provider.stopHealthMonitoring();
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  describe('Normal Operation Scenarios', () => {
    test('should handle successful requests with health tracking', async () => {
      provider.startHealthMonitoring();
      
      // Make several successful requests
      const results = await Promise.all([
        provider.generateCompletion(mockOptions),
        provider.generateCompletion(mockOptions),
        provider.generateCompletion(mockOptions)
      ]);
      
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result).toContain(`Response ${index + 1}`);
      });
      
      // Check health metrics
      const metrics = provider.getHealthMetrics();
      expect(metrics.totalRequests).toBe(3);
      expect(metrics.totalSuccesses).toBe(3);
      expect(metrics.successRate).toBe(1.0);
      expect(metrics.status).toBe('healthy');
      expect(metrics.isHealthy).toBe(true);
    });

    test('should handle concurrent streaming requests', async () => {
      const streamPromises = Array(3).fill(null).map(async (_, index) => {
        const chunks: StreamChunk[] = [];
        for await (const chunk of provider.generateStream({
          ...mockOptions,
          userId: `user-${index}`
        })) {
          chunks.push(chunk);
        }
        return chunks;
      });
      
      const results = await Promise.all(streamPromises);
      
      expect(results).toHaveLength(3);
      results.forEach((chunks, streamIndex) => {
        expect(chunks.length).toBeGreaterThan(0);
        expect(chunks[chunks.length - 1].finish_reason).toBe('stop');
        // Each stream should have unique response number
        const lastChunk = chunks[chunks.length - 1];
        expect(lastChunk.content).toContain(`${streamIndex + 1}`);
      });
    });
  });

  describe('Intermittent Failure Recovery', () => {
    test('should recover from intermittent failures with retry', async () => {
      provider.setScenario('intermittentFailures', true);
      
      // Despite 20% failure rate, should eventually succeed with retries
      const results = await Promise.all([
        provider.generateCompletion(mockOptions),
        provider.generateCompletion(mockOptions),
        provider.generateCompletion(mockOptions),
        provider.generateCompletion(mockOptions),
        provider.generateCompletion(mockOptions)
      ]);
      
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toContain('Response');
      });
      
      // Health metrics should show some failures but overall success
      const metrics = provider.getHealthMetrics();
      expect(metrics.totalRequests).toBeGreaterThan(5); // Due to retries
      expect(metrics.totalSuccesses).toBe(5);
      expect(metrics.totalFailures).toBeGreaterThan(0);
    });

    test('should handle streaming failures with retry', async () => {
      provider.setScenario('intermittentFailures', true);
      
      const chunks: StreamChunk[] = [];
      for await (const chunk of provider.generateStream(mockOptions)) {
        chunks.push(chunk);
      }
      
      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[chunks.length - 1].finish_reason).toBe('stop');
    });
  });

  describe('Rate Limiting Scenarios', () => {
    test('should handle rate limiting with proper backoff', async () => {
      provider.setScenario('rateLimitHits', true);
      
      const start = Date.now();
      
      // Make 10 requests (every 5th will rate limit)
      const promises = Array(10).fill(null).map((_, index) =>
        provider.generateCompletion({
          ...mockOptions,
          userId: `user-${index}`
        })
      );
      
      const results = await Promise.all(promises);
      const duration = Date.now() - start;
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toContain('Response');
      });
      
      // Should have taken time due to rate limit backoff
      expect(duration).toBeGreaterThan(4000); // At least 2 rate limit delays
    });
  });

  describe('Network Issues Recovery', () => {
    test('should recover from network timeouts', async () => {
      provider.setScenario('networkIssues', true);
      
      const result = await provider.generateCompletion(mockOptions);
      
      expect(result).toContain('Response');
      
      // Should show high latency in metrics
      const metrics = provider.getHealthMetrics();
      expect(metrics.averageLatency).toBeGreaterThan(1000);
    });
  });

  describe('Circuit Breaker Scenarios', () => {
    test('should open circuit breaker after consecutive failures', async () => {
      provider.setScenario('serviceOutage', true);
      
      // Make requests that will consistently fail
      const promises = Array(5).fill(null).map(() =>
        provider.generateCompletion(mockOptions).catch(error => error)
      );
      
      await Promise.all(promises);
      
      // Circuit breaker should be open now
      const circuitBreakerMetrics = provider.getCircuitBreakerMetrics();
      const providerMetrics = circuitBreakerMetrics.get('IntegrationTestProvider');
      
      expect(providerMetrics?.state).toBe('OPEN');
      
      // Further requests should fail immediately with circuit breaker error
      await expect(provider.generateCompletion(mockOptions))
        .rejects.toThrow(CircuitBreakerError);
    });

    test('should attempt recovery when circuit breaker enters half-open state', async () => {
      provider.setScenario('serviceOutage', true);
      
      // Trigger circuit breaker to open
      for (let i = 0; i < 5; i++) {
        try {
          await provider.generateCompletion(mockOptions);
        } catch (error) {
          // Expected failures
        }
      }
      
      // Fix the service
      provider.setScenario('serviceOutage', false);
      
      // Fast-forward time to trigger half-open state
      jest.advanceTimersByTime(60000); // 1 minute
      
      // Should be able to make requests again
      const result = await provider.generateCompletion(mockOptions);
      expect(result).toContain('Response');
    });
  });

  describe('Health Monitoring and Alerting', () => {
    test('should generate alerts for unhealthy providers', async () => {
      provider.startHealthMonitoring();
      provider.setScenario('intermittentFailures', true);
      
      // Make many requests to generate health data
      const promises = Array(20).fill(null).map((_, index) =>
        provider.generateCompletion({
          ...mockOptions,
          userId: `user-${index}`
        }).catch(error => error)
      );
      
      await Promise.all(promises);
      
      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const alerts = healthMonitor.getAlerts();
      
      if (alerts.length > 0) {
        const alert = alerts[0];
        expect(alert.provider).toBe('IntegrationTestProvider');
        expect(alert.resolved).toBe(false);
        expect(['high', 'critical']).toContain(alert.severity);
      }
    });

    test('should resolve alerts when provider becomes healthy', async () => {
      provider.startHealthMonitoring();
      
      // Make provider unhealthy
      provider.setScenario('serviceOutage', true);
      for (let i = 0; i < 10; i++) {
        try {
          await provider.generateCompletion(mockOptions);
        } catch (error) {
          // Expected failures
        }
      }
      
      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      let alerts = healthMonitor.getAlerts();
      const initialAlertCount = alerts.length;
      
      // Fix the service
      provider.setScenario('serviceOutage', false);
      
      // Make successful requests
      for (let i = 0; i < 10; i++) {
        await provider.generateCompletion(mockOptions);
      }
      
      alerts = healthMonitor.getAlerts();
      const resolvedAlerts = alerts.filter(alert => alert.resolved);
      
      if (initialAlertCount > 0) {
        expect(resolvedAlerts.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Authentication and Authorization', () => {
    test('should not retry authentication errors', async () => {
      provider.setScenario('authProblems', true);
      
      const start = Date.now();
      
      await expect(provider.generateCompletion(mockOptions)).rejects.toThrow();
      
      const duration = Date.now() - start;
      
      // Should fail quickly without retries
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('Load and Stress Testing', () => {
    test('should handle high concurrent load', async () => {
      const concurrentRequests = 50;
      const promises = Array(concurrentRequests).fill(null).map((_, index) =>
        provider.generateCompletion({
          ...mockOptions,
          userId: `load-test-user-${index}`,
          sessionId: `load-test-session-${index}`
        })
      );
      
      const start = Date.now();
      const results = await Promise.all(promises);
      const duration = Date.now() - start;
      
      expect(results).toHaveLength(concurrentRequests);
      results.forEach(result => {
        expect(result).toContain('Response');
      });
      
      // Check that all requests were tracked
      const metrics = provider.getHealthMetrics();
      expect(metrics.totalRequests).toBe(concurrentRequests);
      expect(metrics.totalSuccesses).toBe(concurrentRequests);
      
      console.log(`Processed ${concurrentRequests} concurrent requests in ${duration}ms`);
    });

    test('should maintain performance under mixed streaming and completion load', async () => {
      const completionPromises = Array(20).fill(null).map((_, index) =>
        provider.generateCompletion({
          ...mockOptions,
          userId: `completion-user-${index}`
        })
      );
      
      const streamingPromises = Array(20).fill(null).map(async (_, index) => {
        const chunks: StreamChunk[] = [];
        for await (const chunk of provider.generateStream({
          ...mockOptions,
          userId: `streaming-user-${index}`
        })) {
          chunks.push(chunk);
        }
        return chunks;
      });
      
      const [completionResults, streamingResults] = await Promise.all([
        Promise.all(completionPromises),
        Promise.all(streamingPromises)
      ]);
      
      expect(completionResults).toHaveLength(20);
      expect(streamingResults).toHaveLength(20);
      
      streamingResults.forEach(chunks => {
        expect(chunks.length).toBeGreaterThan(0);
        expect(chunks[chunks.length - 1].finish_reason).toBe('stop');
      });
      
      // All 40 requests should be tracked
      const metrics = provider.getHealthMetrics();
      expect(metrics.totalRequests).toBe(40);
    });
  });

  describe('Error Recovery Patterns', () => {
    test('should implement exponential backoff correctly', async () => {
      provider.setScenario('intermittentFailures', true);
      
      const timings: number[] = [];
      
      for (let i = 0; i < 3; i++) {
        const start = Date.now();
        try {
          await provider.generateCompletion(mockOptions);
        } catch (error) {
          // May fail, that's ok for this test
        }
        timings.push(Date.now() - start);
      }
      
      // Each subsequent request should potentially take longer due to backoff
      // (though success on first try would be immediate)
      expect(timings.length).toBe(3);
    });
  });

  describe('Resource Cleanup', () => {
    test('should properly clean up resources', () => {
      provider.startHealthMonitoring();
      
      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const stopSpy = jest.spyOn(healthMonitor, 'stop');
      
      provider.stopHealthMonitoring();
      
      expect(stopSpy).toHaveBeenCalled();
    });

    test('should reset metrics when requested', () => {
      // Generate some metrics
      provider.generateCompletion(mockOptions);
      
      let metrics = provider.getHealthMetrics();
      expect(metrics.totalRequests).toBeGreaterThan(0);
      
      // Reset
      provider.resetHealthMetrics();
      
      metrics = provider.getHealthMetrics();
      expect(metrics.totalRequests).toBe(0);
    });
  });
});