import { EnhancedProviderBase } from '../enhanced-provider-base';
import { EnhancedErrorHandler } from '../enhanced-error-handler';
import { HealthMonitor } from '../health-monitor';
import { CircuitBreaker } from '../circuit-breaker';
import { GenerationOptions, StreamChunk, ModelInfo, ProviderConfig } from '../providers/types';

// Mock concrete implementation for testing
class MockEnhancedProvider extends EnhancedProviderBase {
  public readonly providerName = 'MockEnhancedProvider';
  private shouldFail: boolean = false;
  private failCount: number = 0;
  private maxFails: number = 0;

  constructor(config: ProviderConfig = {}) {
    super(config);
  }

  setFailureMode(shouldFail: boolean, maxFails: number = 0) {
    this.shouldFail = shouldFail;
    this.maxFails = maxFails;
    this.failCount = 0;
  }

  async executeRawCompletion(options: GenerationOptions): Promise<string> {
    if (this.shouldFail && this.failCount < this.maxFails) {
      this.failCount++;
      const error = new Error('Simulated provider failure');
      (error as any).status = 500;
      throw error;
    }
    
    return `Mock response for model ${options.model}`;
  }

  async *executeRawStream(options: GenerationOptions): AsyncGenerator<StreamChunk> {
    if (this.shouldFail && this.failCount < this.maxFails) {
      this.failCount++;
      const error = new Error('Simulated stream failure');
      (error as any).status = 500;
      throw error;
    }

    yield { content: 'Mock', type: 'content' };
    yield { content: ' stream', type: 'content' };
    yield { content: ' response', type: 'content', finish_reason: 'stop' };
  }

  async listModels(): Promise<ModelInfo[]> {
    return [
      {
        id: 'mock-model-1',
        name: 'Mock Model 1',
        provider: 'mock',
        contextWindow: 4096,
        maxOutput: 2048,
        inputCost: 0.001,
        outputCost: 0.002,
        capabilities: ['chat'],
        supportsStreaming: true
      }
    ];
  }

  async validateConfig(): Promise<boolean> {
    return !this.shouldFail;
  }
}

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('EnhancedProviderBase', () => {
  let provider: MockEnhancedProvider;
  let mockOptions: GenerationOptions;

  beforeEach(() => {
    jest.useFakeTimers();
    
    provider = new MockEnhancedProvider({
      apiKey: 'test-key',
      timeout: 5000
    });

    mockOptions = {
      model: 'mock-model-1',
      messages: [
        { role: 'user', content: 'Hello, world!' }
      ],
      temperature: 0.7,
      maxTokens: 100
    };
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    test('should initialize with default components', () => {
      expect(provider).toBeInstanceOf(EnhancedProviderBase);
      expect(provider.providerName).toBe('MockEnhancedProvider');
    });

    test('should have error handler, health monitor, and circuit breaker', () => {
      // Access protected members through any casting
      const enhancedProvider = provider as any;
      
      expect(enhancedProvider.errorHandler).toBeInstanceOf(EnhancedErrorHandler);
      expect(enhancedProvider.healthMonitor).toBeInstanceOf(HealthMonitor);
      expect(enhancedProvider.circuitBreaker).toBeInstanceOf(CircuitBreaker);
    });
  });

  describe('generateCompletion with error handling', () => {
    test('should execute successful completion', async () => {
      const result = await provider.generateCompletion(mockOptions);
      
      expect(result).toBe('Mock response for model mock-model-1');
    });

    test('should handle and retry on retryable errors', async () => {
      // Configure to fail first 2 attempts, then succeed
      provider.setFailureMode(true, 2);

      const result = await provider.generateCompletion(mockOptions);
      
      expect(result).toBe('Mock response for model mock-model-1');
    });

    test('should record metrics in health monitor', async () => {
      await provider.generateCompletion(mockOptions);

      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const metrics = healthMonitor.getMetrics('MockEnhancedProvider');
      
      expect(metrics).toBeTruthy();
      expect(metrics?.totalRequests).toBeGreaterThan(0);
      expect(metrics?.totalSuccesses).toBeGreaterThan(0);
    });

    test('should record failed requests in health monitor', async () => {
      provider.setFailureMode(true, 10); // Fail all attempts

      await expect(provider.generateCompletion(mockOptions)).rejects.toThrow();

      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const metrics = healthMonitor.getMetrics('MockEnhancedProvider');
      
      expect(metrics?.totalFailures).toBeGreaterThan(0);
    });

    test('should include request context in error handling', async () => {
      provider.setFailureMode(true, 10);

      try {
        await provider.generateCompletion({
          ...mockOptions,
          userId: 'test-user-123',
          sessionId: 'test-session-456'
        });
      } catch (error) {
        // Should have gone through error handler with context
        const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
        const metrics = healthMonitor.getMetrics('MockEnhancedProvider');
        expect(metrics?.totalFailures).toBeGreaterThan(0);
      }
    });
  });

  describe('generateStream with error handling', () => {
    test('should execute successful streaming', async () => {
      const chunks: StreamChunk[] = [];
      
      for await (const chunk of provider.generateStream(mockOptions)) {
        chunks.push(chunk);
      }
      
      expect(chunks).toHaveLength(3);
      expect(chunks[0].content).toBe('Mock');
      expect(chunks[1].content).toBe(' stream');
      expect(chunks[2].content).toBe(' response');
      expect(chunks[2].finish_reason).toBe('stop');
    });

    test('should handle streaming errors with retry', async () => {
      // Configure to fail first attempt, then succeed
      provider.setFailureMode(true, 1);

      const chunks: StreamChunk[] = [];
      
      for await (const chunk of provider.generateStream(mockOptions)) {
        chunks.push(chunk);
      }
      
      expect(chunks).toHaveLength(3);
    });

    test('should record streaming metrics', async () => {
      for await (const chunk of provider.generateStream(mockOptions)) {
        // Consume stream
      }

      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const metrics = healthMonitor.getMetrics('MockEnhancedProvider');
      
      expect(metrics?.totalRequests).toBeGreaterThan(0);
      expect(metrics?.totalSuccesses).toBeGreaterThan(0);
    });

    test('should add provider metadata to stream chunks', async () => {
      const chunks: StreamChunk[] = [];
      
      for await (const chunk of provider.generateStream(mockOptions)) {
        chunks.push(chunk);
      }
      
      // Enhanced provider should add metadata
      chunks.forEach(chunk => {
        expect(chunk.provider).toBe('MockEnhancedProvider');
        expect(chunk.model).toBe('mock-model-1');
      });
    });
  });

  describe('health monitoring integration', () => {
    test('should start health monitoring on provider registration', () => {
      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const startSpy = jest.spyOn(healthMonitor, 'start');
      
      provider.startHealthMonitoring();
      
      expect(startSpy).toHaveBeenCalled();
    });

    test('should get health metrics', () => {
      const metrics = provider.getHealthMetrics();
      
      expect(metrics).toBeTruthy();
      expect(metrics.provider).toBe('MockEnhancedProvider');
    });

    test('should report health status', async () => {
      // Record some successful requests
      await provider.generateCompletion(mockOptions);
      await provider.generateCompletion(mockOptions);
      
      const isHealthy = provider.isHealthy();
      expect(isHealthy).toBe(true);
    });

    test('should report unhealthy status after failures', async () => {
      provider.setFailureMode(true, 10);
      
      // Try multiple failed requests
      for (let i = 0; i < 5; i++) {
        try {
          await provider.generateCompletion(mockOptions);
        } catch (error) {
          // Expected to fail
        }
      }
      
      const isHealthy = provider.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });

  describe('circuit breaker integration', () => {
    test('should respect circuit breaker state', async () => {
      const circuitBreaker = (provider as any).circuitBreaker as CircuitBreaker;
      
      // Force circuit breaker to OPEN state
      const mockState = {
        state: 'OPEN' as const,
        failureCount: 5,
        successCount: 0,
        lastFailureTime: Date.now() - 1000,
        nextAttemptTime: Date.now() + 10000
      };
      
      jest.spyOn(circuitBreaker, 'getState').mockReturnValue(mockState);
      
      await expect(provider.generateCompletion(mockOptions)).rejects.toThrow(/circuit breaker/i);
    });

    test('should get circuit breaker metrics', () => {
      const metrics = provider.getCircuitBreakerMetrics();
      
      expect(metrics).toBeInstanceOf(Map);
    });

    test('should reset circuit breaker', () => {
      const circuitBreaker = (provider as any).circuitBreaker as CircuitBreaker;
      const resetSpy = jest.spyOn(circuitBreaker, 'reset');
      
      provider.resetCircuitBreaker();
      
      expect(resetSpy).toHaveBeenCalledWith('MockEnhancedProvider');
    });
  });

  describe('error standardization', () => {
    test('should standardize provider errors', async () => {
      provider.setFailureMode(true, 10);
      
      let caughtError: any;
      try {
        await provider.generateCompletion(mockOptions);
      } catch (error) {
        caughtError = error;
      }
      
      expect(caughtError).toBeTruthy();
      // Should have gone through standardization
    });

    test('should include troubleshooting information', async () => {
      provider.setFailureMode(true, 10);
      
      try {
        await provider.generateCompletion(mockOptions);
      } catch (error: any) {
        // Error should have been processed by error handler
        expect(error).toBeTruthy();
      }
    });
  });

  describe('configuration and validation', () => {
    test('should validate configuration', async () => {
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(true);
    });

    test('should handle invalid configuration', async () => {
      provider.setFailureMode(true, 10);
      
      const isValid = await provider.validateConfig();
      expect(isValid).toBe(false);
    });

    test('should list available models', async () => {
      const models = await provider.listModels();
      
      expect(models).toHaveLength(1);
      expect(models[0].id).toBe('mock-model-1');
      expect(models[0].provider).toBe('mock');
    });

    test('should check model availability', async () => {
      const isAvailable = await provider.checkModelAvailability('mock-model-1');
      expect(isAvailable).toBe(true);
      
      const isNotAvailable = await provider.checkModelAvailability('non-existent-model');
      expect(isNotAvailable).toBe(false);
    });
  });

  describe('token counting', () => {
    test('should count tokens', async () => {
      const text = 'This is a test message for token counting';
      const tokenCount = await provider.countTokens(text, 'mock-model-1');
      
      expect(tokenCount).toBeGreaterThan(0);
      expect(typeof tokenCount).toBe('number');
    });
  });

  describe('request context tracking', () => {
    test('should track request metadata', async () => {
      const optionsWithContext = {
        ...mockOptions,
        userId: 'test-user-123',
        sessionId: 'test-session-456',
        conversationId: 'test-conv-789',
        traceId: 'test-trace-abc'
      };
      
      const result = await provider.generateCompletion(optionsWithContext);
      
      expect(result).toBeTruthy();
      // Context should be tracked in health monitoring
      const metrics = provider.getHealthMetrics();
      expect(metrics).toBeTruthy();
    });
  });

  describe('cleanup and resource management', () => {
    test('should stop health monitoring', () => {
      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const stopSpy = jest.spyOn(healthMonitor, 'stop');
      
      provider.stopHealthMonitoring();
      
      expect(stopSpy).toHaveBeenCalled();
    });

    test('should reset all metrics', () => {
      const healthMonitor = (provider as any).healthMonitor as HealthMonitor;
      const resetSpy = jest.spyOn(healthMonitor, 'resetMetrics');
      
      provider.resetHealthMetrics();
      
      expect(resetSpy).toHaveBeenCalledWith('MockEnhancedProvider');
    });
  });
});