import { EnhancedError<PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>ontext, StandardErrorResponse } from '../enhanced-error-handler';
import { CircuitBreaker, CircuitBreakerError } from '../circuit-breaker';

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
});

afterAll(() => {
  Object.assign(console, originalConsole);
});

describe('EnhancedErrorHandler', () => {
  let errorHandler: EnhancedErrorHandler;
  let mockCircuitBreaker: jest.Mocked<CircuitBreaker>;
  
  beforeEach(() => {
    // Create mock circuit breaker
    mockCircuitBreaker = {
      execute: jest.fn(),
      getState: jest.fn(),
      getMetrics: jest.fn(),
      getAllMetrics: jest.fn(),
      reset: jest.fn(),
      resetAll: jest.fn(),
    } as any;

    errorHandler = EnhancedErrorHandler.getInstance(mockCircuitBreaker);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('executeWithErrorHandling', () => {
    const mockContext: ErrorContext = {
      provider: 'test-provider',
      model: 'test-model',
      operation: 'test-operation',
      requestId: 'test-request-123',
      attempt: 1,
      startTime: Date.now()
    };

    test('should execute operation successfully through circuit breaker', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      mockCircuitBreaker.execute.mockImplementation(async (key, operation) => {
        return await operation();
      });

      const result = await errorHandler.executeWithErrorHandling(
        mockOperation,
        mockContext
      );

      expect(result).toBe('success');
      expect(mockCircuitBreaker.execute).toHaveBeenCalledWith(
        'test-provider-test-model-test-operation',
        expect.any(Function)
      );
      expect(mockOperation).toHaveBeenCalled();
    });

    test('should handle circuit breaker errors', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      const circuitBreakerError = new CircuitBreakerError(
        'Circuit breaker is OPEN',
        'OPEN',
        Date.now() + 60000
      );
      
      mockCircuitBreaker.execute.mockRejectedValue(circuitBreakerError);

      await expect(
        errorHandler.executeWithErrorHandling(mockOperation, mockContext)
      ).rejects.toThrow();

      expect(mockOperation).not.toHaveBeenCalled();
    });

    test('should apply custom retry configuration', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      mockCircuitBreaker.execute.mockImplementation(async (key, operation) => {
        return await operation();
      });

      const customConfig = {
        maxRetries: 5,
        baseDelay: 2000,
        maxDelay: 60000
      };

      await errorHandler.executeWithErrorHandling(
        mockOperation,
        mockContext,
        customConfig
      );

      expect(mockCircuitBreaker.execute).toHaveBeenCalled();
      // The circuit breaker should be called with the operation
      const [key, wrappedOperation] = mockCircuitBreaker.execute.mock.calls[0];
      expect(key).toBe('test-provider-test-model-test-operation');
      expect(typeof wrappedOperation).toBe('function');
    });
  });

  describe('standardizeError', () => {
    const mockContext: ErrorContext = {
      provider: 'test-provider',
      model: 'test-model',
      operation: 'test-operation',
      requestId: 'test-request-123',
      attempt: 1,
      startTime: Date.now()
    };

    test('should handle circuit breaker errors', () => {
      const circuitBreakerError = new CircuitBreakerError(
        'Circuit breaker is OPEN',
        'OPEN',
        Date.now() + 60000
      );

      const standardError = errorHandler.standardizeError(circuitBreakerError, mockContext);

      expect(standardError.code).toBe('CIRCUIT_BREAKER_OPEN');
      expect(standardError.category).toBe('server');
      expect(standardError.severity).toBe('high');
      expect(standardError.retryable).toBe(true);
      expect(standardError.retryAfter).toBeGreaterThan(0);
      expect(standardError.troubleshooting).toContain('circuit breaker');
    });

    test('should classify authentication errors', () => {
      const authError = new Error('Unauthorized');
      (authError as any).status = 401;

      const standardError = errorHandler.standardizeError(authError, mockContext);

      expect(standardError.code).toBe('AUTHENTICATION_ERROR');
      expect(standardError.category).toBe('auth');
      expect(standardError.severity).toBe('critical');
      expect(standardError.retryable).toBe(false);
      expect(standardError.troubleshooting).toContain('API key');
    });

    test('should classify rate limit errors', () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;

      const standardError = errorHandler.standardizeError(rateLimitError, mockContext);

      expect(standardError.code).toBe('RATE_LIMIT');
      expect(standardError.category).toBe('rate_limit');
      expect(standardError.severity).toBe('medium');
      expect(standardError.retryable).toBe(true);
      expect(standardError.retryAfter).toBe(60000); // Default 1 minute
    });

    test('should extract retry-after from error headers', () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      (rateLimitError as any).retryAfter = 30; // 30 seconds

      const standardError = errorHandler.standardizeError(rateLimitError, mockContext);

      expect(standardError.retryAfter).toBe(30000); // Converted to milliseconds
    });

    test('should classify server errors', () => {
      const serverError = new Error('Internal server error');
      (serverError as any).status = 500;

      const standardError = errorHandler.standardizeError(serverError, mockContext);

      expect(standardError.code).toBe('SERVER_ERROR');
      expect(standardError.category).toBe('server');
      expect(standardError.severity).toBe('high');
      expect(standardError.retryable).toBe(true);
    });

    test('should classify network errors', () => {
      const networkError = new Error('Connection timeout');
      (networkError as any).code = 'ETIMEDOUT';

      const standardError = errorHandler.standardizeError(networkError, mockContext);

      expect(standardError.code).toBe('NETWORK_ERROR');
      expect(standardError.category).toBe('network');
      expect(standardError.severity).toBe('medium');
      expect(standardError.retryable).toBe(true);
    });

    test('should classify quota exceeded errors', () => {
      const quotaError = new Error('Quota exceeded');

      const standardError = errorHandler.standardizeError(quotaError, mockContext);

      expect(standardError.code).toBe('QUOTA_EXCEEDED');
      expect(standardError.category).toBe('quota');
      expect(standardError.severity).toBe('high');
      expect(standardError.retryable).toBe(false);
    });

    test('should classify content policy violations', () => {
      const contentError = new Error('Content violates policy');

      const standardError = errorHandler.standardizeError(contentError, mockContext);

      expect(standardError.code).toBe('CONTENT_POLICY_VIOLATION');
      expect(standardError.category).toBe('client');
      expect(standardError.severity).toBe('medium');
      expect(standardError.retryable).toBe(false);
    });

    test('should handle unknown errors', () => {
      const unknownError = new Error('Something went wrong');

      const standardError = errorHandler.standardizeError(unknownError, mockContext);

      expect(standardError.code).toBe('UNKNOWN_ERROR');
      expect(standardError.category).toBe('unknown');
      expect(standardError.retryable).toBe(false); // Unknown status defaults to non-retryable
    });

    test('should include context in standardized error', () => {
      const error = new Error('Test error');
      
      const standardError = errorHandler.standardizeError(error, mockContext);

      expect(standardError.provider).toBe('test-provider');
      expect(standardError.model).toBe('test-model');
      expect(standardError.context).toEqual(mockContext);
      expect(standardError.originalError).toBe(error);
    });

    test('should generate appropriate troubleshooting messages', () => {
      const errors = [
        { error: { status: 401 }, expectedTroubleshooting: 'API key' },
        { error: { status: 429 }, expectedTroubleshooting: 'rate limit' },
        { error: { message: 'quota' }, expectedTroubleshooting: 'quota' },
        { error: { message: 'model not found' }, expectedTroubleshooting: 'model name' },
        { error: { status: 500 }, expectedTroubleshooting: 'server issues' },
        { error: { code: 'ETIMEDOUT' }, expectedTroubleshooting: 'connectivity' },
        { error: { message: 'content policy' }, expectedTroubleshooting: 'content policies' }
      ];

      for (const { error, expectedTroubleshooting } of errors) {
        const testError = new Error(error.message || 'Test error');
        Object.assign(testError, error);
        
        const standardError = errorHandler.standardizeError(testError, mockContext);
        expect(standardError.troubleshooting?.toLowerCase()).toContain(expectedTroubleshooting);
      }
    });
  });

  describe('retry logic with exponential backoff', () => {
    let mockOperation: jest.Mock;
    let mockContext: ErrorContext;

    beforeEach(() => {
      mockOperation = jest.fn();
      mockContext = {
        provider: 'test-provider',
        model: 'test-model',
        operation: 'test-operation',
        requestId: 'test-request-123',
        attempt: 1,
        startTime: Date.now()
      };

      // Mock circuit breaker to pass through to retry logic
      mockCircuitBreaker.execute.mockImplementation(async (key, operation) => {
        return await operation();
      });
    });

    test('should retry retryable errors with exponential backoff', async () => {
      const retryableError = new Error('Server error');
      (retryableError as any).status = 500;

      mockOperation
        .mockRejectedValueOnce(retryableError)
        .mockRejectedValueOnce(retryableError)
        .mockResolvedValueOnce('success');

      const start = Date.now();
      const result = await errorHandler.executeWithErrorHandling(
        mockOperation,
        mockContext,
        { maxRetries: 3, baseDelay: 100, maxDelay: 1000 }
      );
      const duration = Date.now() - start;

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(3);
      // Should have some delay from retries (at least base delays)
      expect(duration).toBeGreaterThan(200); // At least 100ms + 200ms delays
    });

    test('should not retry non-retryable errors', async () => {
      const nonRetryableError = new Error('Unauthorized');
      (nonRetryableError as any).status = 401;

      mockOperation.mockRejectedValue(nonRetryableError);

      await expect(
        errorHandler.executeWithErrorHandling(mockOperation, mockContext)
      ).rejects.toThrow();

      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    test('should respect max retries limit', async () => {
      const retryableError = new Error('Server error');
      (retryableError as any).status = 500;

      mockOperation.mockRejectedValue(retryableError);

      await expect(
        errorHandler.executeWithErrorHandling(
          mockOperation,
          mockContext,
          { maxRetries: 2 }
        )
      ).rejects.toThrow();

      expect(mockOperation).toHaveBeenCalledTimes(3); // 1 initial + 2 retries
    });

    test('should handle rate limit with custom retry-after', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      (rateLimitError as any).retryAfter = 2; // 2 seconds

      mockOperation
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValueOnce('success');

      const start = Date.now();
      const result = await errorHandler.executeWithErrorHandling(
        mockOperation,
        mockContext
      );
      const duration = Date.now() - start;

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(2);
      // Should wait for retry-after period
      expect(duration).toBeGreaterThan(2000);
    });

    test('should apply jitter to prevent thundering herd', async () => {
      const retryableError = new Error('Server error');
      (retryableError as any).status = 500;

      mockOperation
        .mockRejectedValueOnce(retryableError)
        .mockResolvedValueOnce('success');

      // Run multiple operations in parallel
      const promises = Array(5).fill(null).map(() =>
        errorHandler.executeWithErrorHandling(
          mockOperation,
          { ...mockContext, requestId: `test-${Math.random()}` },
          { maxRetries: 1, baseDelay: 1000, jitterFactor: 0.5 }
        )
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(5);
      expect(results.every(r => r === 'success')).toBe(true);
    });
  });

  describe('circuit breaker integration', () => {
    test('should get circuit breaker metrics', () => {
      const mockMetrics = new Map([
        ['provider1-model1', { state: 'CLOSED', failureCount: 0 }],
        ['provider2-model2', { state: 'OPEN', failureCount: 5 }]
      ]);

      mockCircuitBreaker.getAllMetrics.mockReturnValue(mockMetrics);

      const metrics = errorHandler.getCircuitBreakerMetrics();

      expect(metrics).toBe(mockMetrics);
      expect(mockCircuitBreaker.getAllMetrics).toHaveBeenCalled();
    });

    test('should reset specific circuit breaker', () => {
      errorHandler.resetCircuitBreaker('test-key');

      expect(mockCircuitBreaker.reset).toHaveBeenCalledWith('test-key');
    });

    test('should reset all circuit breakers', () => {
      errorHandler.resetAllCircuitBreakers();

      expect(mockCircuitBreaker.resetAll).toHaveBeenCalled();
    });
  });

  describe('delay calculation', () => {
    test('should calculate exponential backoff with jitter', () => {
      const handler = new EnhancedErrorHandler();
      
      // Access private method through any casting
      const calculateDelay = (handler as any).calculateDelay;
      
      const config = {
        baseDelay: 1000,
        maxDelay: 10000,
        backoffFactor: 2,
        jitterFactor: 0.1
      };

      const delay1 = calculateDelay.call(handler, 1, config);
      const delay2 = calculateDelay.call(handler, 2, config);
      const delay3 = calculateDelay.call(handler, 3, config);

      // Should increase exponentially
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay3).toBeGreaterThan(delay2);

      // Should respect max delay
      const delayHigh = calculateDelay.call(handler, 10, config);
      expect(delayHigh).toBeLessThanOrEqual(config.maxDelay);

      // Should include jitter (delays should vary)
      const delays = Array(10).fill(null).map((_, i) => calculateDelay.call(handler, 1, config));
      const unique = new Set(delays);
      expect(unique.size).toBeGreaterThan(1); // Should have variation due to jitter
    });
  });

  describe('error retryability classification', () => {
    test('should correctly identify retryable errors', () => {
      const handler = new EnhancedErrorHandler();
      const isRetryableError = (handler as any).isRetryableError;

      const config = {
        retryableErrors: ['RATE_LIMIT', 'SERVER_ERROR'],
        nonRetryableErrors: ['AUTHENTICATION_ERROR']
      };

      // Explicitly retryable
      expect(isRetryableError.call(handler, { code: 'RATE_LIMIT', retryable: false }, config)).toBe(true);
      
      // Explicitly non-retryable
      expect(isRetryableError.call(handler, { code: 'AUTHENTICATION_ERROR', retryable: true }, config)).toBe(false);
      
      // Based on retryable flag
      expect(isRetryableError.call(handler, { code: 'UNKNOWN_ERROR', retryable: true }, config)).toBe(true);
      expect(isRetryableError.call(handler, { code: 'UNKNOWN_ERROR', retryable: false }, config)).toBe(false);
    });
  });
});