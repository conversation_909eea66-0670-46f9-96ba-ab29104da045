/**
 * Dynamic AI Model Statistics
 * Provides real-time model counts and statistics from the database
 */

import { prisma } from '@/lib/prisma'

export interface ModelStats {
  total: number
  active: number
  byProvider: Record<string, number>
  byPlan: {
    free: number
    freemium: number
    plus: number
    advanced: number
    max: number
  }
}

/**
 * Get current model statistics from database
 */
export async function getModelStats(): Promise<ModelStats> {
  try {
    // Get total and active models
    const [total, active] = await Promise.all([
      prisma.models.count(),
      prisma.models.count({
        where: { isEnabled: true }
      })
    ])

    // Get models by provider
    const providerCounts = await prisma.aIModel.groupBy({
      by: ['providerId'],
      where: { isEnabled: true },
      _count: { id: true }
    })

    const byProvider: Record<string, number> = {}
    for (const item of providerCounts) {
      // Get provider slug for the provider ID
      const provider = await prisma.aIProvider.findUnique({
        where: { id: item.providerId },
        select: { slug: true }
      })
      if (provider) {
        byProvider[provider.slug] = item._count.id
      }
    }

    // Get models by plan access using ModelPlanRules
    // Count distinct models that have rules for each plan
    const plans = ['FREE', 'FREEMIUM', 'PLUS', 'ADVANCED', 'MAX'];
    const byPlan = {
      free: 0,
      freemium: 0,
      plus: 0,
      advanced: 0,
      max: 0
    }

    // Count models for each plan
    for (const plan of plans) {
      const count = await prisma.$queryRaw<Array<{count: number}>>`
        SELECT COUNT(DISTINCT modelId) as count
        FROM ModelPlanRules
        WHERE isEnabled = true
        AND JSON_CONTAINS(planIds, JSON_QUOTE(${plan}))
      `;
      
      const planType = plan.toLowerCase();
      if (planType in byPlan) {
        byPlan[planType as keyof typeof byPlan] = Number(count[0]?.count || 0);
      }
    }

    return {
      total,
      active,
      byProvider,
      byPlan
    }
  } catch (error) {
    console.error('Error fetching model stats:', error)
    // Return fallback stats
    return {
      total: 217,
      active: 217,
      byProvider: {},
      byPlan: {
        free: 0,
        freemium: 0,
        plus: 0,
        advanced: 0,
        max: 217
      }
    }
  }
}

/**
 * Get formatted model count for display
 */
export async function getModelCountDisplay(): Promise<{
  total: string
  totalRounded: string
  active: string
  activeRounded: string
}> {
  const stats = await getModelStats()
  
  return {
    total: stats.total.toString(),
    totalRounded: stats.total >= 200 ? `${Math.floor(stats.total / 10) * 10}+` : `${stats.total}+`,
    active: stats.active.toString(),
    activeRounded: stats.active >= 200 ? `${Math.floor(stats.active / 10) * 10}+` : `${stats.active}+`
  }
}

/**
 * Get cached model stats (with 5-minute cache)
 */
let cachedStats: ModelStats | null = null
let cacheTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export async function getCachedModelStats(): Promise<ModelStats> {
  const now = Date.now()
  
  if (cachedStats && (now - cacheTime) < CACHE_DURATION) {
    return cachedStats
  }
  
  cachedStats = await getModelStats()
  cacheTime = now
  
  return cachedStats
}

/**
 * Get model count for specific plan tier
 */
export async function getModelsForPlan(planType: string): Promise<number> {
  try {
    const plan = planType.toUpperCase();
    const count = await prisma.$queryRaw<Array<{count: number}>>`
      SELECT COUNT(DISTINCT modelId) as count
      FROM ModelPlanRules
      WHERE isEnabled = true
      AND JSON_CONTAINS(planIds, JSON_QUOTE(${plan}))
    `;
    
    return Number(count[0]?.count || 0);
  } catch (error) {
    console.error(`Error fetching model count for plan ${planType}:`, error)
    return 0
  }
}

/**
 * Get percentage of models available for a plan
 */
export async function getPlanModelPercentage(planType: string): Promise<string> {
  const stats = await getModelStats()
  const planCount = await getModelsForPlan(planType)
  
  if (stats.total === 0) return '0%'
  
  const percentage = Math.round((planCount / stats.total) * 100)
  return `${percentage}%`
}

/**
 * Get model tier breakdown based on pricing
 */
export async function getModelTiers(): Promise<{
  free: number
  standard: number
  premium: number
  frontier: number
}> {
  try {
    // Get models with metadata that contains cost information
    const models = await prisma.models.findMany({
      where: { isEnabled: true },
      select: {
        id: true,
        canonicalName: true,
        displayName: true,
        extendedMetadata: true
      }
    });

    const tiers = {
      free: 0,
      standard: 0,
      premium: 0,
      frontier: 0
    };

    // Define FRONTIER models manually (ultra-premium)
    const frontierModelIds = [
      'o3-pro', 'o3-pro-2025-04-14',
      'gpt-4.5-research-preview', 'gpt-4.5-preview',
      'claude-4-opus', 'claude-3-opus',
      'o1-pro', 'o1-pro-2025-03-19'
    ];

    models.forEach((model: any) => {
      // Get cost from metadata
      const metadata = model.extendedMetadata;
      const inputCost = metadata?.inputCost || 0;
      const outputCost = metadata?.outputCost || 0;
      const maxCost = Math.max(inputCost, outputCost);
      
      // Check if it's a FRONTIER model first
      if (frontierModelIds.some(id => 
        model.canonicalName.toLowerCase().includes(id.toLowerCase()) ||
        model.displayName.toLowerCase().includes(id.toLowerCase())
      )) {
        tiers.frontier++;
      }
      // FREE: < $0.50 (max of input/output)
      else if (maxCost < 0.5) {
        tiers.free++;
      }
      // STANDARD: ≤ $5 input AND ≤ $10 output
      else if (inputCost <= 5.0 && outputCost <= 10.0) {
        tiers.standard++;
      }
      // PREMIUM: > $5 input OR > $10 output
      else {
        tiers.premium++;
      }
    });

    return tiers;
  } catch (error) {
    console.error('Error calculating model tiers:', error);
    return { free: 48, standard: 86, premium: 39, frontier: 7 };
  }
}

/**
 * Get plan statistics with counts and percentages
 */
export async function getPlanStats(): Promise<{
  total: number
  plans: {
    [key: string]: {
      count: number
      percentage: number
      percentageText: string
    }
  }
  tiers?: {
    free: number
    standard: number
    premium: number
    frontier: number
  }
}> {
  const stats = await getModelStats()
  const planTypes = ['FREE', 'FREEMIUM', 'PLUS', 'ADVANCED', 'MAX']
  
  const plans: { [key: string]: { count: number; percentage: number; percentageText: string } } = {}
  
  for (const planType of planTypes) {
    const count = await getModelsForPlan(planType)
    const percentage = stats.total > 0 ? Math.round((count / stats.total) * 100) : 0
    plans[planType] = {
      count,
      percentage,
      percentageText: `${percentage}%`
    }
  }
  
  // Get tier breakdown
  const tiers = await getModelTiers();
  
  return {
    total: stats.total,
    plans,
    tiers
  }
}