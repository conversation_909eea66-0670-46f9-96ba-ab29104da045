/**
 * OpenRouter Model Expansion
 * Adds 50+ unique models from OpenRouter with proper provider attribution
 * These models show their original provider, not "openrouter"
 */

import { Model, ModelCapability } from '@/types';

// Extended Model type with OpenRouter flag
type OpenRouterModel = Model & { isOpenRouter: true };

// Models accessed via OpenRouter but shown with original provider names
export const OPENROUTER_EXPANSION_MODELS: Record<string, OpenRouterModel> = {
  // ===== MICROSOFT MODELS (via OpenRouter) =====
  'phi-4': {
    id: 'phi-4',
    provider: 'microsoft',
    name: 'Phi-4',
    slug: 'phi-4',
    canonicalName: 'openrouter/microsoft/phi-4',
    description: 'Microsoft\'s latest efficient reasoning model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.REASONING,
      ModelCapability.CODE_GENERATION,
    ],
    contextWindow: 16384,
    maxOutput: 16384,
    inputCost: 0.00015,
    outputCost: 0.0006,
    inputPricePerMillion: 150,
    outputPricePerMillion: 600,
    avgLatency: 500,
    avgTTFT: 200,
    tags: ['efficient', 'reasoning', 'microsoft'],
    recommendedFor: ['general-chat', 'reasoning', 'code'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true as any, // Internal flag for routing
  } as Model & { isOpenRouter: true },

  'wizardlm-2-8x22b': {
    id: 'wizardlm-2-8x22b',
    provider: 'microsoft',
    name: 'WizardLM 2 8x22B',
    slug: 'wizardlm-2-8x22b',
    canonicalName: 'openrouter/microsoft/wizardlm-2-8x22b',
    description: 'Microsoft\'s advanced instruction-following model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 65536,
    maxOutput: 8192,
    inputCost: 0.0006,
    outputCost: 0.0006,
    inputPricePerMillion: 600,
    outputPricePerMillion: 600,
    avgLatency: 800,
    avgTTFT: 300,
    tags: ['instruction', 'microsoft', 'large'],
    recommendedFor: ['complex-instructions', 'analysis'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== NVIDIA MODELS =====
  'llama-3.1-nemotron-70b': {
    id: 'llama-3.1-nemotron-70b',
    provider: 'nvidia',
    name: 'Llama 3.1 Nemotron 70B',
    slug: 'llama-3.1-nemotron-70b',
    canonicalName: 'openrouter/nvidia/llama-3.1-nemotron-70b',
    description: 'NVIDIA-optimized Llama model for high performance',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.ANALYSIS,
      ModelCapability.REASONING,
    ],
    contextWindow: 131072,
    maxOutput: 4096,
    inputCost: 0.00035,
    outputCost: 0.0004,
    inputPricePerMillion: 350,
    outputPricePerMillion: 400,
    avgLatency: 700,
    avgTTFT: 250,
    tags: ['nvidia', 'optimized', 'llama'],
    recommendedFor: ['general-chat', 'code', 'analysis'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },

  // ===== ARCEE AI MODELS =====
  'arcee-ai-maestro': {
    id: 'arcee-ai-maestro',
    provider: 'arcee',
    name: 'Arcee Maestro',
    slug: 'arcee-ai-maestro',
    canonicalName: 'openrouter/arcee/arcee-ai-maestro',
    description: 'Advanced reasoning model from Arcee AI',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.REASONING,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 131072,
    maxOutput: 8192,
    inputCost: 0.00018,
    outputCost: 0.00023,
    inputPricePerMillion: 180,
    outputPricePerMillion: 230,
    avgLatency: 900,
    avgTTFT: 350,
    tags: ['reasoning', 'arcee', 'advanced'],
    recommendedFor: ['reasoning', 'analysis', 'research'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  'arcee-ai-virtuoso': {
    id: 'arcee-ai-virtuoso',
    provider: 'arcee',
    name: 'Arcee Virtuoso',
    slug: 'arcee-ai-virtuoso',
    canonicalName: 'openrouter/arcee/arcee-ai-virtuoso',
    description: 'Creative and versatile model from Arcee AI',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CREATIVE_WRITING,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 131072,
    maxOutput: 8192,
    inputCost: 0.00018,
    outputCost: 0.00023,
    inputPricePerMillion: 180,
    outputPricePerMillion: 230,
    avgLatency: 850,
    avgTTFT: 300,
    tags: ['creative', 'versatile', 'arcee'],
    recommendedFor: ['creative-writing', 'storytelling'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== NOUS RESEARCH MODELS =====
  'hermes-3-llama-3.1-405b': {
    id: 'hermes-3-llama-3.1-405b',
    provider: 'nous',
    name: 'Hermes 3 Llama 405B',
    slug: 'hermes-3-llama-3.1-405b',
    canonicalName: 'openrouter/nous/hermes-3-llama-3.1-405b',
    description: 'Nous Research\'s flagship instruction model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
      ModelCapability.REASONING,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 131072,
    maxOutput: 4096,
    inputCost: 0.0058,
    outputCost: 0.0058,
    inputPricePerMillion: 5800,
    outputPricePerMillion: 5800,
    avgLatency: 1500,
    avgTTFT: 600,
    tags: ['flagship', 'nous', 'instruction'],
    recommendedFor: ['complex-tasks', 'reasoning', 'analysis'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
    isOpenRouter: true,
  },

  'hermes-3-llama-3.1-70b': {
    id: 'hermes-3-llama-3.1-70b',
    provider: 'nous',
    name: 'Hermes 3 Llama 70B',
    slug: 'hermes-3-llama-3.1-70b',
    canonicalName: 'openrouter/nous/hermes-3-llama-3.1-70b',
    description: 'Efficient instruction-following model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 131072,
    maxOutput: 4096,
    inputCost: 0.0008,
    outputCost: 0.0008,
    inputPricePerMillion: 800,
    outputPricePerMillion: 800,
    avgLatency: 800,
    avgTTFT: 300,
    tags: ['efficient', 'nous', 'instruction'],
    recommendedFor: ['general-chat', 'code', 'analysis'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== AI21 MODELS =====
  'jamba-1-5-large': {
    id: 'jamba-1-5-large',
    provider: 'ai21',
    name: 'Jamba 1.5 Large',
    slug: 'jamba-1-5-large',
    canonicalName: 'openrouter/ai21/jamba-1-5-large',
    description: 'AI21\'s large language model with 256K context',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.ANALYSIS,
      ModelCapability.LONG_CONTEXT,
    ],
    contextWindow: 256000,
    maxOutput: 8192,
    inputCost: 0.0018,
    outputCost: 0.0075,
    inputPricePerMillion: 1800,
    outputPricePerMillion: 7500,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['long-context', 'ai21', 'large'],
    recommendedFor: ['document-analysis', 'long-context'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  'jamba-1-5-mini': {
    id: 'jamba-1-5-mini',
    provider: 'ai21',
    name: 'Jamba 1.5 Mini',
    slug: 'jamba-1-5-mini',
    canonicalName: 'openrouter/ai21/jamba-1-5-mini',
    description: 'Efficient model with 256K context window',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.ANALYSIS,
      ModelCapability.LONG_CONTEXT,
    ],
    contextWindow: 256000,
    maxOutput: 8192,
    inputCost: 0.0002,
    outputCost: 0.0004,
    inputPricePerMillion: 200,
    outputPricePerMillion: 400,
    avgLatency: 600,
    avgTTFT: 200,
    tags: ['efficient', 'long-context', 'ai21'],
    recommendedFor: ['document-analysis', 'summarization'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },

  // ===== INFLECTION MODELS =====
  'inflection-3-pi': {
    id: 'inflection-3-pi',
    provider: 'inflection',
    name: 'Inflection 3 Pi',
    slug: 'inflection-3-pi',
    canonicalName: 'openrouter/inflection/inflection-3-pi',
    description: 'Empathetic conversational AI model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
    ],
    contextWindow: 4096,
    maxOutput: 4096,
    inputCost: 0.0008,
    outputCost: 0.0008,
    inputPricePerMillion: 800,
    outputPricePerMillion: 800,
    avgLatency: 700,
    avgTTFT: 250,
    tags: ['empathetic', 'conversational', 'inflection'],
    recommendedFor: ['conversation', 'emotional-support'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  'inflection-3-productivity': {
    id: 'inflection-3-productivity',
    provider: 'inflection',
    name: 'Inflection 3 Productivity',
    slug: 'inflection-3-productivity',
    canonicalName: 'openrouter/inflection/inflection-3-productivity',
    description: 'Task-focused productivity assistant',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 4096,
    maxOutput: 4096,
    inputCost: 0.0008,
    outputCost: 0.0008,
    inputPricePerMillion: 800,
    outputPricePerMillion: 800,
    avgLatency: 700,
    avgTTFT: 250,
    tags: ['productivity', 'task-focused', 'inflection'],
    recommendedFor: ['productivity', 'planning', 'organization'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== REKA AI MODELS =====
  'reka-core': {
    id: 'reka-core',
    provider: 'reka',
    name: 'Reka Core',
    slug: 'reka-core',
    canonicalName: 'openrouter/reka/reka-core',
    description: 'Multimodal model with vision and reasoning',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.VISION,
      ModelCapability.REASONING,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 128000,
    maxOutput: 5120,
    inputCost: 0.003,
    outputCost: 0.015,
    inputPricePerMillion: 3000,
    outputPricePerMillion: 15000,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['multimodal', 'vision', 'reka'],
    recommendedFor: ['image-analysis', 'multimodal-tasks'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  'reka-flash': {
    id: 'reka-flash',
    provider: 'reka',
    name: 'Reka Flash',
    slug: 'reka-flash',
    canonicalName: 'openrouter/reka/reka-flash',
    description: 'Fast multimodal model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 5120,
    inputCost: 0.0008,
    outputCost: 0.002,
    inputPricePerMillion: 800,
    outputPricePerMillion: 2000,
    avgLatency: 600,
    avgTTFT: 200,
    tags: ['fast', 'multimodal', 'reka'],
    recommendedFor: ['quick-analysis', 'image-chat'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },

  // ===== MINIMAX MODELS =====
  'minimax-01': {
    id: 'minimax-01',
    provider: 'minimax',
    name: 'MiniMax-01',
    slug: 'minimax-01',
    canonicalName: 'openrouter/minimax/minimax-01',
    description: 'Advanced Chinese-English bilingual model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.TRANSLATION,
      ModelCapability.TRANSLATION,
    ],
    contextWindow: 128000,
    maxOutput: 4096,
    inputCost: 0.004,
    outputCost: 0.012,
    inputPricePerMillion: 4000,
    outputPricePerMillion: 12000,
    avgLatency: 900,
    avgTTFT: 350,
    tags: ['bilingual', 'chinese', 'minimax'],
    recommendedFor: ['chinese-chat', 'translation'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== LIQUID AI MODELS =====
  'liquid-lfm-40b': {
    id: 'liquid-lfm-40b',
    provider: 'liquid',
    name: 'Liquid LFM 40B',
    slug: 'liquid-lfm-40b',
    canonicalName: 'openrouter/liquid/liquid-lfm-40b',
    description: 'Efficient liquid foundation model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 32768,
    maxOutput: 4096,
    inputCost: 0.00085,
    outputCost: 0.00085,
    inputPricePerMillion: 850,
    outputPricePerMillion: 850,
    avgLatency: 700,
    avgTTFT: 250,
    tags: ['efficient', 'liquid', 'foundation'],
    recommendedFor: ['general-chat', 'code', 'analysis'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },

  // ===== SAMBA NOVA MODELS =====
  'samba-nova-v1': {
    id: 'samba-nova-v1',
    provider: 'sambanova',
    name: 'Samba Nova v1',
    slug: 'samba-nova-v1',
    canonicalName: 'openrouter/sambanova/samba-nova-v1',
    description: 'High-performance enterprise model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 128000,
    maxOutput: 4096,
    inputCost: 0.00001,
    outputCost: 0.00001,
    inputPricePerMillion: 10,
    outputPricePerMillion: 10,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['enterprise', 'fast', 'sambanova'],
    recommendedFor: ['enterprise-chat', 'code'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== 01.AI MODELS =====
  'yi-lightning': {
    id: 'yi-lightning',
    provider: '01-ai',
    name: 'Yi Lightning',
    slug: 'yi-lightning',
    canonicalName: 'openrouter/01-ai/yi-lightning',
    description: 'Ultra-fast model from 01.AI',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
    ],
    contextWindow: 16384,
    maxOutput: 4096,
    inputCost: 0.00003,
    outputCost: 0.00003,
    inputPricePerMillion: 30,
    outputPricePerMillion: 30,
    avgLatency: 200,
    avgTTFT: 50,
    tags: ['ultra-fast', 'efficient', '01-ai'],
    recommendedFor: ['quick-responses', 'chat'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },

  'yi-large': {
    id: 'yi-large',
    provider: '01-ai',
    name: 'Yi Large',
    slug: 'yi-large',
    canonicalName: 'openrouter/01-ai/yi-large',
    description: 'Advanced bilingual model (Chinese-English)',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.TRANSLATION,
      ModelCapability.TRANSLATION,
      ModelCapability.CODE_GENERATION,
    ],
    contextWindow: 32768,
    maxOutput: 4096,
    inputCost: 0.003,
    outputCost: 0.003,
    inputPricePerMillion: 3000,
    outputPricePerMillion: 3000,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['bilingual', 'large', '01-ai'],
    recommendedFor: ['chinese-chat', 'translation', 'code'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== COGNITIVE COMPUTATIONS =====
  'dolphin-mixtral-8x22b': {
    id: 'dolphin-mixtral-8x22b',
    provider: 'cognitive',
    name: 'Dolphin Mixtral 8x22B',
    slug: 'dolphin-mixtral-8x22b',
    canonicalName: 'openrouter/cognitive/dolphin-mixtral-8x22b',
    description: 'Uncensored, helpful assistant model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CODE_GENERATION,
      ModelCapability.CREATIVE_WRITING,
    ],
    contextWindow: 16384,
    maxOutput: 8192,
    inputCost: 0.0009,
    outputCost: 0.0009,
    inputPricePerMillion: 900,
    outputPricePerMillion: 900,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['uncensored', 'helpful', 'cognitive'],
    recommendedFor: ['open-discussion', 'creative-tasks'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== ALIBABA MODELS (Additional) =====
  'qwq-32b-preview': {
    id: 'qwq-32b-preview',
    provider: 'alibaba',
    name: 'QwQ 32B Preview',
    slug: 'qwq-32b-preview',
    canonicalName: 'openrouter/alibaba/qwq-32b-preview',
    description: 'Advanced reasoning model from Alibaba',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.REASONING,
      ModelCapability.MATH,
      ModelCapability.CODE_GENERATION,
    ],
    contextWindow: 32768,
    maxOutput: 8192,
    inputCost: 0.0012,
    outputCost: 0.0012,
    inputPricePerMillion: 1200,
    outputPricePerMillion: 1200,
    avgLatency: 900,
    avgTTFT: 350,
    tags: ['reasoning', 'math', 'alibaba'],
    recommendedFor: ['mathematical-reasoning', 'problem-solving'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== THUDM MODELS =====
  'glm-4-plus': {
    id: 'glm-4-plus',
    provider: 'thudm',
    name: 'GLM-4 Plus',
    slug: 'glm-4-plus',
    canonicalName: 'openrouter/thudm/glm-4-plus',
    description: 'Advanced Chinese language model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.TRANSLATION,
      ModelCapability.CODE_GENERATION,
    ],
    contextWindow: 128000,
    maxOutput: 4096,
    inputCost: 0.005,
    outputCost: 0.005,
    inputPricePerMillion: 5000,
    outputPricePerMillion: 5000,
    avgLatency: 800,
    avgTTFT: 300,
    tags: ['chinese', 'advanced', 'thudm'],
    recommendedFor: ['chinese-chat', 'code'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== EVA MODELS =====
  'eva-unit-01': {
    id: 'eva-unit-01',
    provider: 'eva',
    name: 'EVA Unit-01',
    slug: 'eva-unit-01',
    canonicalName: 'openrouter/eva/eva-unit-01',
    description: 'Specialized anime and creative writing model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CREATIVE_WRITING,
      ModelCapability.CREATIVE_WRITING,
    ],
    contextWindow: 32768,
    maxOutput: 4096,
    inputCost: 0.0015,
    outputCost: 0.0015,
    inputPricePerMillion: 1500,
    outputPricePerMillion: 1500,
    avgLatency: 800,
    avgTTFT: 300,
    tags: ['creative', 'anime', 'roleplay'],
    recommendedFor: ['creative-writing', 'roleplay', 'storytelling'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== SARVAM AI MODELS =====
  'sarvam-2b': {
    id: 'sarvam-2b',
    provider: 'sarvam',
    name: 'Sarvam 2B',
    slug: 'sarvam-2b',
    canonicalName: 'openrouter/sarvam/sarvam-2b',
    description: 'Indian multilingual model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.TRANSLATION,
    ],
    contextWindow: 32768,
    maxOutput: 8192,
    inputCost: 0,
    outputCost: 0,
    inputPricePerMillion: 0,
    outputPricePerMillion: 0,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['indian-languages', 'multilingual', 'free'],
    recommendedFor: ['indian-language-chat', 'translation'],
    available: true,
    speed: 'fast' as const,
    tier: 'free',
    isOpenRouter: true,
  },

  // ===== MOONSHOT AI MODELS =====
  'moonshot-v1-auto': {
    id: 'moonshot-v1-auto',
    provider: 'moonshot',
    name: 'Moonshot v1 Auto',
    slug: 'moonshot-v1-auto',
    canonicalName: 'openrouter/moonshot/moonshot-v1-auto',
    description: 'Auto-routing Chinese language model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.TRANSLATION,
      ModelCapability.CODE_GENERATION,
    ],
    contextWindow: 128000,
    maxOutput: 4096,
    inputCost: 0.0012,
    outputCost: 0.0012,
    inputPricePerMillion: 1200,
    outputPricePerMillion: 1200,
    avgLatency: 700,
    avgTTFT: 250,
    tags: ['chinese', 'auto-routing', 'moonshot'],
    recommendedFor: ['chinese-chat', 'general-use'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },

  // ===== AION LABS MODELS =====
  'aion-v1': {
    id: 'aion-v1',
    provider: 'aion',
    name: 'AION v1',
    slug: 'aion-v1',
    canonicalName: 'openrouter/aion/aion-v1',
    description: 'Advanced reasoning and analysis model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.REASONING,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 131072,
    maxOutput: 8192,
    inputCost: 0.00025,
    outputCost: 0.00125,
    inputPricePerMillion: 250,
    outputPricePerMillion: 1250,
    avgLatency: 900,
    avgTTFT: 350,
    tags: ['reasoning', 'analysis', 'aion'],
    recommendedFor: ['complex-reasoning', 'analysis'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== NEVERSLEEP MODELS =====
  'llama-3-lumimaid-70b': {
    id: 'llama-3-lumimaid-70b',
    provider: 'neversleep',
    name: 'Llama 3 Lumimaid 70B',
    slug: 'llama-3-lumimaid-70b',
    canonicalName: 'openrouter/neversleep/llama-3-lumimaid-70b',
    description: 'Enhanced conversational model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CREATIVE_WRITING,
      ModelCapability.CREATIVE_WRITING,
    ],
    contextWindow: 8192,
    maxOutput: 4096,
    inputCost: 0.0038,
    outputCost: 0.0058,
    inputPricePerMillion: 3800,
    outputPricePerMillion: 5800,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['conversational', 'creative', 'neversleep'],
    recommendedFor: ['conversation', 'roleplay', 'creative-writing'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== SAO10K MODELS =====
  'l3-euryale-70b': {
    id: 'l3-euryale-70b',
    provider: 'sao10k',
    name: 'L3 Euryale 70B',
    slug: 'l3-euryale-70b',
    canonicalName: 'openrouter/sao10k/l3-euryale-70b',
    description: 'Creative writing and roleplay specialist',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CREATIVE_WRITING,
      ModelCapability.CREATIVE_WRITING,
    ],
    contextWindow: 8192,
    maxOutput: 4096,
    inputCost: 0.0015,
    outputCost: 0.0015,
    inputPricePerMillion: 1500,
    outputPricePerMillion: 1500,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['creative', 'roleplay', 'sao10k'],
    recommendedFor: ['creative-writing', 'storytelling', 'roleplay'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== ANTHRACITE MODELS =====
  'magnum-72b': {
    id: 'magnum-72b',
    provider: 'anthracite',
    name: 'Magnum 72B',
    slug: 'magnum-72b',
    canonicalName: 'openrouter/anthracite/magnum-72b',
    description: 'High-quality conversational model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CREATIVE_WRITING,
      ModelCapability.ANALYSIS,
    ],
    contextWindow: 16384,
    maxOutput: 4096,
    inputCost: 0.0025,
    outputCost: 0.0025,
    inputPricePerMillion: 2500,
    outputPricePerMillion: 2500,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['conversational', 'quality', 'anthracite'],
    recommendedFor: ['conversation', 'analysis', 'writing'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
    isOpenRouter: true,
  },

  // ===== PYGMALION MODELS =====
  'mythalion-13b': {
    id: 'mythalion-13b',
    provider: 'pygmalion',
    name: 'Mythalion 13B',
    slug: 'mythalion-13b',
    canonicalName: 'openrouter/pygmalion/mythalion-13b',
    description: 'Roleplay and creative writing model',
    capabilities: [
      ModelCapability.GENERAL_CHAT,
      ModelCapability.CREATIVE_WRITING,
      ModelCapability.CREATIVE_WRITING,
    ],
    contextWindow: 8192,
    maxOutput: 2048,
    inputCost: 0.001125,
    outputCost: 0.001125,
    inputPricePerMillion: 1125,
    outputPricePerMillion: 1125,
    avgLatency: 600,
    avgTTFT: 200,
    tags: ['roleplay', 'creative', 'pygmalion'],
    recommendedFor: ['roleplay', 'creative-writing'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
    isOpenRouter: true,
  },
};

// Helper function to add OpenRouter models to existing registry
export function integrateOpenRouterModels(existingRegistry: Record<string, Model>): Record<string, Model> {
  const combined = { ...existingRegistry };
  
  // Add OpenRouter models, avoiding duplicates
  for (const [id, model] of Object.entries(OPENROUTER_EXPANSION_MODELS)) {
    if (!combined[id]) {
      combined[id] = model;
    }
  }
  
  return combined;
}

// Get count of unique providers
export function getUniqueProviderCount(): number {
  const providers = new Set(
    Object.values(OPENROUTER_EXPANSION_MODELS).map(m => m.provider)
  );
  return providers.size;
}

// Get models by provider
export function getModelsByProvider(provider: string): Model[] {
  return Object.values(OPENROUTER_EXPANSION_MODELS)
    .filter(m => m.provider === provider);
}