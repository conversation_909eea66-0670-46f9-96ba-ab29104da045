/**
 * Database-backed Model Selection and Routing Service
 * Replaces the JSON-based registry with MySQL database queries
 */

import { PrismaClient, Models, AIProvider } from '@prisma/client';
import { Model, UserPlan, ModelCapability } from '@/types';
import { cache } from 'react';
import { sortModelsForDisplay } from './model-ordering-service';

const prisma = new PrismaClient();

// Cache for frequently accessed data
const modelCache = new Map<string, Model>();
const providerCache = new Map<string, any>();
let cacheExpiry = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Plan-based cost score limits for enhanced routing
export const PLAN_COST_LIMITS = {
  [UserPlan.FREE]: 100,      // Only free models (cost score = 100)
  [UserPlan.FREEMIUM]: 97,   // Ultra-value tier (cost score >= 97)
  [UserPlan.PLUS]: 90,       // Performance tier (cost score >= 90)
  [UserPlan.ADVANCED]: 70,   // Most models (cost score >= 70)
  [UserPlan.MAX]: 0,         // All models (no limit)
  [UserPlan.ENTERPRISE]: 0   // All models (no limit)
};

/**
 * Get all available providers from database
 */
export const getProviders = cache(async () => {
  try {
    const providers = await prisma.aIProvider.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });
    
    return providers.map((provider: any) => ({
      id: provider.id,
      slug: provider.slug,
      name: provider.name,
      displayName: provider.name,
      description: provider.description || '',
      website: provider.websiteUrl || '',
      isActive: provider.isActive
    }));
  } catch (error) {
    console.error('Error fetching providers:', error);
    return [];
  }
});

/**
 * Get all models available to a specific user plan
 */
export const getModelsForPlan = cache(async (userPlan: UserPlan): Promise<Model[]> => {
  try {
    console.log(`🔍 Getting models for plan: ${userPlan}`);
    
    // Get plan tier for rule-based access
    const plan = await prisma.$queryRaw<any[]>`
      SELECT * FROM Plans WHERE code = ${userPlan} LIMIT 1
    `.then(results => results[0]);

    if (!plan) {
      console.error(`❌ Plan not found: ${userPlan}`);
      return [];
    }

    // Get models that are available to this plan via rules (ROUTER LOGIC - KEEP INTACT)
    const models = await prisma.$queryRaw<(Models & { providerSlug: string })[]>`
      SELECT DISTINCT m.*, p.slug AS providerSlug
      FROM Models m
      JOIN ModelPlanRules r ON m.id = r.modelId
      JOIN Providers p ON m.providerId = p.id
      WHERE m.isEnabled = true
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${userPlan}))
        OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
      )
      AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= NOW())
      AND (r.effectiveUntil IS NULL OR r.effectiveUntil > NOW())
      ORDER BY m.displayName ASC
    `;

    console.log(`✅ Found ${models.length} models for ${userPlan} plan`);
    
    // Log first few models to debug
    if (models.length > 0) {
      console.log(`First model: ${models[0].canonicalName} - ${models[0].displayName}`);
    }

    const appModels = models.map((dbModel: Models) => convertDbModelToAppModel(dbModel));
    
    console.log(`📦 Converted ${appModels.length} models to app format`);
    
    // Apply intelligent ordering to get diverse models
    const sortedModels = sortModelsForDisplay(appModels);
    
    console.log(`🎯 Sorted models count: ${sortedModels.length}`);
    
    return sortedModels;
  } catch (error) {
    console.error('❌ Error fetching models for plan:', error);
    return [];
  }
});

/**
 * Get ALL models with plan availability for UI display
 * Shows all models with unavailable ones greyed out
 */
export const getAllModelsForUI = cache(async (currentUserPlan: UserPlan = UserPlan.FREE): Promise<Model[]> => {
  try {
    console.log(`🔍 Getting ALL models for UI display for plan: ${currentUserPlan}`);
    
    // Get ALL enabled models with their provider info
    const models = await prisma.$queryRaw<(Models & { providerSlug: string })[]>`
      SELECT DISTINCT m.*, p.slug AS providerSlug
      FROM Models m
      JOIN Providers p ON m.providerId = p.id
      WHERE m.isEnabled = true
      ORDER BY m.displayName ASC
    `;

    console.log(`✅ Found ${models.length} total enabled models for UI`);
    
    // Get plan rules for all models to determine availability
    const modelRules = await prisma.$queryRaw<any[]>`
      SELECT 
        r.modelId,
        r.planIds,
        r.minPlanTier,
        r.isEnabled as ruleEnabled
      FROM ModelPlanRules r
      WHERE r.isEnabled = true
      AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= NOW())
      AND (r.effectiveUntil IS NULL OR r.effectiveUntil > NOW())
    `;

    // Get all plans for tier comparison
    const plans = await prisma.$queryRaw<any[]>`
      SELECT code, tier FROM Plans
    `;
    const planTiers = Object.fromEntries(plans.map(p => [p.code, p.tier]));

    // Create a map of model access rules
    const accessRules = new Map<string, Record<UserPlan, boolean>>();
    const userPlans = [UserPlan.FREE, UserPlan.FREEMIUM, UserPlan.PLUS, UserPlan.ADVANCED, UserPlan.MAX, UserPlan.ENTERPRISE];
    
    for (const model of models) {
      const rules = modelRules.filter(r => r.modelId === model.id);
      const planAvailability: Record<UserPlan, boolean> = {} as Record<UserPlan, boolean>;
      
      for (const plan of userPlans) {
        const planTier = planTiers[plan] || 0;
        
        planAvailability[plan] = rules.some(rule => {
          // Check if plan is explicitly included
          if (rule.planIds) {
            try {
              const allowedPlans = JSON.parse(rule.planIds);
              if (allowedPlans.includes(plan)) return true;
            } catch (e) {
              console.warn(`Invalid planIds JSON for model ${model.id}:`, rule.planIds);
            }
          }
          
          // Check tier-based access
          if (rule.minPlanTier !== null && planTier >= rule.minPlanTier) {
            return true;
          }
          
          return false;
        });
      }
      
      accessRules.set(model.id, planAvailability);
    }

    // Convert to app models with proper plan availability
    const appModels = models.map((dbModel: Models & { providerSlug: string }) => {
      const planAvailability = accessRules.get(dbModel.id) || {
        [UserPlan.FREE]: false,
        [UserPlan.FREEMIUM]: false,
        [UserPlan.PLUS]: false,
        [UserPlan.ADVANCED]: false,
        [UserPlan.MAX]: false,
        [UserPlan.ENTERPRISE]: false,
      };

      // Find minimum plan required
      let minimumPlan = UserPlan.ENTERPRISE;
      for (const plan of userPlans) {
        if (planAvailability[plan]) {
          minimumPlan = plan;
          break;
        }
      }

      const appModel = convertDbModelToAppModel(dbModel);
      
      return {
        ...appModel,
        planAvailability,
        minimumPlan,
        isAvailableForUser: planAvailability[currentUserPlan] || false
      };
    });
    
    console.log(`📦 UI: Converted ${appModels.length} models with plan availability`);
    console.log(`🎯 UI: User ${currentUserPlan} can access ${appModels.filter(m => m.isAvailableForUser).length} models`);
    
    // Apply intelligent ordering to get diverse models
    const sortedModels = sortModelsForDisplay(appModels);
    
    console.log(`🎯 UI: Returning ${sortedModels.length} models (all models with availability info)`);
    
    return sortedModels;
  } catch (error) {
    console.error('❌ Error fetching all models for UI:', error);
    return [];
  }
});

/**
 * Get models for plan with cost score filtering (enhanced routing)
 */
export const getModelsForPlanWithCostLimit = cache(async (userPlan: UserPlan): Promise<Model[]> => {
  try {
    const minCostScore = PLAN_COST_LIMITS[userPlan] || 0;
    
    // Get plan tier for rule-based access
    const plan = await prisma.$queryRaw<any[]>`
      SELECT * FROM Plans WHERE code = ${userPlan} LIMIT 1
    `.then(results => results[0]);

    if (!plan) {
      return [];
    }

    // Fetch models with plan access via rules
    const models = await prisma.$queryRaw<Models[]>`
      SELECT DISTINCT m.*
      FROM Models m
      JOIN ModelPlanRules r ON m.id = r.modelId
      WHERE m.isEnabled = true
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${userPlan}))
        OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
      )
      AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= NOW())
      AND (r.effectiveUntil IS NULL OR r.effectiveUntil > NOW())
    `;

    // Convert and filter by cost score
    const appModels = models
      .map((dbModel: any) => convertDbModelToAppModel(dbModel))
      .filter((model: any) => {
        // Filter by cost score limit for the plan
        const costScore = model.costScore || 50;
        return costScore >= minCostScore;
      });
    
    // Sort by cost score (higher = cheaper = better for cost optimization)
    return appModels.sort((a: any, b: any) => {
      const scoreA = a.costScore || 50;
      const scoreB = b.costScore || 50;
      return scoreB - scoreA; // Higher scores first
    });
  } catch (error) {
    console.error('Error fetching models with cost limit:', error);
    return [];
  }
});

/**
 * Get a specific model by ID
 */
export const getModelById = cache(async (modelId: string): Promise<Model | null> => {
  try {
    const dbModel = await prisma.models.findFirst({
      where: {
        canonicalName: modelId,
        isEnabled: true
      }
    });

    if (!dbModel) return null;
    return convertDbModelToAppModel(dbModel);
  } catch (error) {
    console.error('Error fetching model by ID:', error);
    return null;
  }
});


/**
 * Get models by provider
 */
export const getModelsByProvider = cache(async (providerSlug: string, userPlan: UserPlan): Promise<Model[]> => {
  try {
    // Get provider by slug
    const provider = await prisma.aIProvider.findFirst({
      where: { slug: providerSlug }
    });

    if (!provider) {
      return [];
    }

    // Get plan for tier-based access
    const plan = await prisma.plans.findFirst({
      where: { code: userPlan }
    });

    if (!plan) {
      return [];
    }

    const models = await prisma.$queryRaw<Models[]>`
      SELECT DISTINCT m.*
      FROM Models m
      JOIN ModelPlanRules r ON m.id = r.modelId
      WHERE m.providerId = ${provider.id}
      AND m.isEnabled = true
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${userPlan}))
        OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
      )
      ORDER BY m.displayName ASC
    `;

    // Models already have provider data from the join
    return models.map((dbModel: Models) => {
      return convertDbModelToAppModel(dbModel);
    });
  } catch (error) {
    console.error('Error fetching models by provider:', error);
    return [];
  }
});

/**
 * Get models by capability
 */
export const getModelsByCapability = cache(async (capability: ModelCapability, userPlan: UserPlan): Promise<Model[]> => {
  try {
    // Get plan for tier-based access
    const plan = await prisma.plans.findFirst({
      where: { code: userPlan }
    });

    if (!plan) {
      return [];
    }

    // Get models with capability filtering
    const models = await prisma.$queryRaw<Models[]>`
      SELECT DISTINCT m.*
      FROM Models m
      JOIN ModelPlanRules r ON m.id = r.modelId
      WHERE m.isEnabled = true
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${userPlan}))
        OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
      )
    `;

    // Filter by capability based on boolean fields
    const filteredModels = models.filter((model: Models) => {
      switch (capability) {
        case ModelCapability.VISION:
          return model.supportsVision;
        case ModelCapability.FUNCTION_CALLING:
        case ModelCapability.TOOL_USE:
          return model.supportsFunctionCalling;
        case ModelCapability.WEB_SEARCH:
          return model.supportsWebSearch;
        case ModelCapability.REASONING:
          return model.supportsReasoning;
        default:
          // Check in extended metadata for other capabilities
          const metadata = model.extendedMetadata as any;
          return metadata?.capabilities?.includes(capability) || false;
      }
    });

    return filteredModels.map((dbModel: Models) => convertDbModelToAppModel(dbModel));
  } catch (error) {
    console.error('Error fetching models by capability:', error);
    return [];
  }
});

/**
 * Get optimal model for a task (smart routing)
 */
export const getOptimalModelForTask = cache(async (
  requirements: {
    capability?: ModelCapability;
    maxCost?: number;
    minSpeed?: 'fast' | 'medium' | 'slow';
    requiresVision?: boolean;
    requiresWeb?: boolean;
  },
  userPlan: UserPlan
): Promise<Model | null> => {
  try {
    const models = await getModelsForPlan(userPlan);
    
    // Filter by requirements
    let filteredModels = models.filter(model => {
      // Check capability
      if (requirements.capability && !model.capabilities.includes(requirements.capability)) {
        return false;
      }
      
      // Check cost
      if (requirements.maxCost && model.inputCost > requirements.maxCost) {
        return false;
      }
      
      // Check speed
      if (requirements.minSpeed) {
        const speedOrder = { 'fast': 0, 'medium': 1, 'slow': 2 };
        if (speedOrder[model.speed] > speedOrder[requirements.minSpeed]) {
          return false;
        }
      }
      
      // Check vision
      if (requirements.requiresVision && !model.capabilities.includes(ModelCapability.VISION)) {
        return false;
      }
      
      // Check web search
      if (requirements.requiresWeb && !model.capabilities.includes(ModelCapability.WEB_SEARCH)) {
        return false;
      }
      
      return true;
    });
    
    if (filteredModels.length === 0) return null;
    
    // Sort by quality score (can be enhanced with actual benchmarks)
    filteredModels.sort((a: any, b: any) => {
      // Prefer models with more capabilities
      const aScore = a.capabilities.length;
      const bScore = b.capabilities.length;
      
      if (aScore !== bScore) return bScore - aScore;
      
      // Then by cost efficiency (lower is better)
      return a.inputCost - b.inputCost;
    });
    
    return filteredModels[0];
  } catch (error) {
    console.error('Error finding optimal model:', error);
    return null;
  }
});

/**
 * Check if user has access to a specific model
 */
export const canUserAccessModel = cache(async (modelId: string, userPlan: UserPlan): Promise<boolean> => {
  try {
    // Get plan for tier checking
    const plan = await prisma.plans.findFirst({
      where: { code: userPlan }
    });

    if (!plan) {
      return false;
    }

    // Get model
    const model = await prisma.models.findFirst({
      where: { canonicalName: modelId }
    });

    if (!model) {
      return false;
    }

    // Check if user has access via rules
    const access = await prisma.$queryRaw<any[]>`
      SELECT 1
      FROM ModelPlanRules r
      WHERE r.modelId = ${model.id}
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${userPlan}))
        OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
      )
      AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= NOW())
      AND (r.effectiveUntil IS NULL OR r.effectiveUntil > NOW())
      LIMIT 1
    `;
    
    return access.length > 0;
  } catch (error) {
    console.error('Error checking model access:', error);
    return false;
  }
});

/**
 * Get credit cost for a model and user plan
 */
export const getModelCreditCost = cache(async (modelId: string, userPlan: UserPlan): Promise<number> => {
  try {
    // For now, return a default credit cost
    // This can be enhanced with a new credit cost table if needed
    const model = await prisma.models.findFirst({
      where: { canonicalName: modelId }
    });

    if (!model) {
      return 1;
    }

    // Calculate credits based on cost (example: 1 credit per $0.001)
    const avgCost = (model.inputCostPer1M.toNumber() + model.outputCostPer1M.toNumber()) / 2;
    return Math.max(1, Math.ceil(avgCost / 1000));
  } catch (error) {
    console.error('Error fetching credit cost:', error);
    return 1;
  }
});

/**
 * Get default model for a user plan
 */
export const getDefaultModelForPlan = cache(async (userPlan: UserPlan): Promise<Model | null> => {
  const isDebugMode = process.env.ENABLE_LOCALHOST_DEBUG === 'true';
  
  if (isDebugMode) {
    console.log('🐛 [LOCALHOST-DEBUG] Bypassing default model fetching and returning mock default model');
    return {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'openai',
      slug: 'gpt-4o-mini',
      canonicalName: 'openai/gpt-4o-mini',
      description: 'Fast and efficient model for general tasks',
      capabilities: [ModelCapability.GENERAL_CHAT, ModelCapability.CODE_GENERATION],
      contextWindow: 128000,
      maxOutput: 4096,
      inputCost: 0.15,
      outputCost: 0.6,
      inputPricePerMillion: 150,
      outputPricePerMillion: 600,
      available: true,
      deprecated: false,
      speed: 'fast',
      tier: 'starter',
      tags: [],
      recommendedFor: []
    };
  }

  try {
    // Default models by plan
    const defaultModelIds: Record<UserPlan, string> = {
      [UserPlan.FREE]: 'perplexity/llama-3-sonar-small-32k-online',
      [UserPlan.FREEMIUM]: 'perplexity/llama-3-sonar-small-32k-online',
      [UserPlan.PLUS]: 'anthropic/claude-3.5-sonnet',
      [UserPlan.ADVANCED]: 'google/gemini-1.5-pro-latest',
      [UserPlan.MAX]: 'openai/gpt-4o',
      [UserPlan.ENTERPRISE]: 'openai/gpt-4o',
    };
    
    const defaultModel = defaultModelIds[userPlan] || defaultModelIds[UserPlan.FREEMIUM];
    const model = await getModelById(defaultModel);
    
    // If default not available, fall back to first available model
    if (!model) {
      const models = await getModelsForPlan(userPlan);
      return models[0] || null;
    }
    
    return model;
  } catch (error) {
    console.error('Error getting default model:', error);
    return null;
  }
});

/**
 * Convert database model to application model format
 */
function convertDbModelToAppModel(dbModel: Models & { provider?: any, providerSlug?: string }): Model {
  const metadata = dbModel.extendedMetadata as any;
  
  // Handle capabilities field - ensure it's always an array
  let capabilities: string[] = [ModelCapability.GENERAL_CHAT];
  
  if (metadata?.capabilities) {
    if (Array.isArray(metadata.capabilities)) {
      // Array format: ["reasoning", "chain_of_thought", "complex_analysis"]
      capabilities = metadata.capabilities.filter((cap: any) => typeof cap === 'string' && cap.length > 0);
    } else if (typeof metadata.capabilities === 'object' && metadata.capabilities !== null) {
      // Object format: {"vision": false, "toolUse": false, "jsonMode": false, ...}
      // Convert true values to capability strings
      capabilities = [];
      const capabilityMap: Record<string, string> = {
        'vision': ModelCapability.VISION,
        'toolUse': ModelCapability.TOOL_USE,
        'functionCalling': ModelCapability.FUNCTION_CALLING,
        'webSearch': ModelCapability.WEB_SEARCH,
        'codeExecution': ModelCapability.CODE_GENERATION,
        'codeGeneration': ModelCapability.CODE_GENERATION,
        'multimodal': ModelCapability.VISION,
        'reasoning': ModelCapability.REASONING,
        'analysis': ModelCapability.ANALYSIS,
        'creative_writing': ModelCapability.CREATIVE_WRITING,
        'translation': ModelCapability.TRANSLATION,
        'summarization': ModelCapability.SUMMARIZATION,
        'math': ModelCapability.MATH,
        'long_context': ModelCapability.LONG_CONTEXT,
        'fast_response': ModelCapability.FAST_RESPONSE,
        'thinking_mode': ModelCapability.THINKING_MODE,
        'audio_processing': ModelCapability.AUDIO_PROCESSING
      };
      
      for (const [key, value] of Object.entries(metadata.capabilities)) {
        if (value === true && capabilityMap[key]) {
          capabilities.push(capabilityMap[key]);
        }
      }
      
      // If no capabilities were found, default to general chat
      if (capabilities.length === 0) {
        capabilities = [ModelCapability.GENERAL_CHAT];
      }
    } else if (typeof metadata.capabilities === 'string') {
      // Handle single string capability
      capabilities = [metadata.capabilities];
    }
  }

  // Add capabilities from boolean fields
  if (dbModel.supportsVision) capabilities.push(ModelCapability.VISION);
  if (dbModel.supportsFunctionCalling) capabilities.push(ModelCapability.FUNCTION_CALLING);
  if (dbModel.supportsWebSearch) capabilities.push(ModelCapability.WEB_SEARCH);
  if (dbModel.supportsReasoning) capabilities.push(ModelCapability.REASONING);
  
  // Ensure we always have at least one capability and no duplicates
  if (capabilities.length === 0) {
    capabilities = [ModelCapability.GENERAL_CHAT];
  } else {
    capabilities = [...new Set(capabilities)]; // Remove duplicates
  }
  
  // Plan availability needs to be determined differently with the new system
  // For now, return a default that needs to be enriched separately
  const planAvailability: Record<UserPlan, boolean> = {
    [UserPlan.FREE]: false,
    [UserPlan.FREEMIUM]: false,
    [UserPlan.PLUS]: false,
    [UserPlan.ADVANCED]: false,
    [UserPlan.MAX]: true,
    [UserPlan.ENTERPRISE]: true,
  };
  
  return {
    id: dbModel.canonicalName,
    name: dbModel.displayName,
    provider: dbModel.providerSlug || dbModel.provider?.slug || dbModel.providerId,
    description: metadata?.description || '',
    capabilities: capabilities,
    contextWindow: dbModel.contextWindow,
    maxOutput: dbModel.maxOutput,
    inputCost: dbModel.inputCostPer1M.toNumber() / 1000, // Convert from per 1M to per 1K
    outputCost: dbModel.outputCostPer1M.toNumber() / 1000, // Convert from per 1M to per 1K
    avgLatency: metadata?.avgLatency,
    avgTTFT: metadata?.avgTTFT,
    tags: metadata?.tags || [],
    recommendedFor: metadata?.recommendedFor || [],
    available: dbModel.isEnabled,
    deprecated: metadata?.deprecated || false,
    speed: dbModel.speedRating >= 8 ? 'fast' : dbModel.speedRating >= 5 ? 'medium' : 'slow',
    tier: metadata?.tier || 'starter',
    // Add enhanced scoring data
    costScore: metadata?.costScore || 50,
    intelligenceScore: dbModel.qualityRating * 10, // Convert 1-10 to 0-100
    pricing: { 
      input: dbModel.inputCostPer1M.toNumber(), 
      output: dbModel.outputCostPer1M.toNumber(), 
      blended: (dbModel.inputCostPer1M.toNumber() + dbModel.outputCostPer1M.toNumber()) / 2 
    },
    // Router optimization
    routerIndex: metadata?.routerIndex || 0,
    // Include full metadata for router access
    metadata: metadata,
    planAvailability,
    minimumPlan: UserPlan.MAX
  } as Model;
}

/**
 * Refresh model cache (call this when models are updated)
 */
export const refreshModelCache = () => {
  modelCache.clear();
  providerCache.clear();
  cacheExpiry = 0;
};

/**
 * Get model statistics for admin dashboard
 */
export const getModelStatistics = cache(async () => {
  try {
    const stats = await prisma.$transaction([
      prisma.aIProvider.count({ where: { isActive: true } }),
      prisma.models.count({ where: { isEnabled: true } }),
      prisma.models.count({ where: { isEnabled: false } }),
      prisma.modelPlanRules.count(),
      prisma.models.count() // Using total models instead of credit costs
    ]);
    
    const [activeProviders, enabledModels, disabledModels, planAccess, creditCosts] = stats;
    
    // Get models by provider
    const modelsByProvider = await prisma.aIProvider.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: { models: { where: { isEnabled: true } } }
        }
      }
    });
    
    return {
      activeProviders,
      enabledModels,
      disabledModels,
      totalModels: enabledModels + disabledModels,
      planAccessEntries: planAccess,
      creditCostEntries: creditCosts,
      modelsByProvider: modelsByProvider.map((p: any) => ({
        name: p.name,
        slug: p.slug || p.name, // Fallback to name if slug is not available
        modelCount: p._count.models
      }))
    };
  } catch (error) {
    console.error('Error fetching model statistics:', error);
    return {
      activeProviders: 0,
      enabledModels: 0,
      disabledModels: 0,
      totalModels: 0,
      planAccessEntries: 0,
      creditCostEntries: 0,
      modelsByProvider: []
    };
  }
});

/**
 * Get ALL models from database with plan availability info
 */
export const getAllModels = cache(async (): Promise<Model[]> => {
  try {
    const models = await prisma.models.findMany({
      where: {
        isEnabled: true
      },
      orderBy: [
        { providerId: 'asc' },
        { displayName: 'asc' }
      ]
    });

    // Get all providers for enrichment
    const providerIds = [...new Set(models.map(m => m.providerId))];
    const providers = await prisma.aIProvider.findMany({
      where: { id: { in: providerIds } }
    });
    const providerMap = new Map(providers.map((p: AIProvider) => [p.id, p]));

    const appModels = models.map((dbModel: Models) => {
      const provider = providerMap.get(dbModel.providerId);
      const modelWithProvider = { ...dbModel, provider };
      const model = convertDbModelToAppModel(modelWithProvider);
      
      // TODO: Add plan availability info by querying ModelPlanRules
      // For now, use defaults from convertDbModelToAppModel
      
      return model;
    });
    
    // Apply intelligent ordering
    return sortModelsForDisplay(appModels);
  } catch (error) {
    console.error('Error fetching all models:', error);
    return [];
  }
});

// Export for backwards compatibility with existing code
export {
  getModelById as getModel
};