/**
 * MCP (Model Context Protocol) Image Generation Tools
 * Allows LLMs like Gemini 2.5 Pro to call image generation as a tool
 */

import { generateImage, ImageGenerationParams } from './image-generation';
import { apiLogger } from '@/lib/logger';

export interface MCPImageGenerationTool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
}

export interface MCPToolCall {
  name: string;
  arguments: Record<string, any>;
}

export interface MCPToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: any;
}

/**
 * Image generation tool definition for MCP
 */
export const imageGenerationTool: MCPImageGenerationTool = {
  name: 'generate_image',
  description: 'Generate high-quality images from text descriptions using Google Imagen 4',
  parameters: {
    type: 'object',
    properties: {
      prompt: {
        type: 'string',
        description: 'Detailed description of the image to generate. Be specific about style, composition, colors, and details.'
      },
      model: {
        type: 'string',
        enum: ['imagen-4'],
        description: 'Uses Google Imagen 4 for superior image quality and text rendering.',
        default: 'imagen-4'
      },
      size: {
        type: 'string',
        enum: ['1024x1024', '1792x1024', '1024x1792', '1536x1024', '1024x1536'],
        description: 'Image dimensions. Use 1024x1024 for square, 1792x1024 for landscape, 1024x1792 for portrait.',
        default: '1024x1024'
      },
      quality: {
        type: 'string',
        enum: ['standard', 'hd'],
        description: 'Image quality level. HD provides higher detail but costs more.',
        default: 'standard'
      },
      style: {
        type: 'string',
        enum: ['vivid', 'natural'],
        description: 'Style preference for DALL-E 3. Vivid for dramatic/artistic, natural for realistic.',
        default: 'natural'
      },
      aspectRatio: {
        type: 'string',
        enum: ['1:1', '9:16', '16:9', '4:3', '3:4'],
        description: 'Aspect ratio for Imagen 4. Use 16:9 for landscape, 9:16 for portrait, 1:1 for square.',
        default: '1:1'
      },
      enhancePrompt: {
        type: 'boolean',
        description: 'Whether to automatically enhance the prompt for better image quality.',
        default: false
      }
    },
    required: ['prompt']
  }
};

/**
 * Image editing tool definition for MCP
 */
export const imageEditingTool: MCPImageGenerationTool = {
  name: 'edit_image',
  description: 'Edit existing images by adding, removing, or modifying elements based on text instructions',
  parameters: {
    type: 'object',
    properties: {
      imageUrl: {
        type: 'string',
        description: 'URL or base64 data of the image to edit'
      },
      editPrompt: {
        type: 'string',
        description: 'Description of what to change, add, or remove from the image'
      },
      maskUrl: {
        type: 'string',
        description: 'Optional mask image indicating areas to edit (transparent = edit, opaque = keep)'
      },
      model: {
        type: 'string',
        enum: ['dall-e-2'],
        description: 'Image editing model to use',
        default: 'dall-e-2'
      }
    },
    required: ['imageUrl', 'editPrompt']
  }
};

/**
 * Image variation tool definition for MCP
 */
export const imageVariationTool: MCPImageGenerationTool = {
  name: 'create_image_variations',
  description: 'Create variations of an existing image while maintaining similar style and composition',
  parameters: {
    type: 'object',
    properties: {
      imageUrl: {
        type: 'string',
        description: 'URL or base64 data of the source image'
      },
      numberOfVariations: {
        type: 'number',
        minimum: 1,
        maximum: 4,
        description: 'Number of variations to create (1-4)',
        default: 1
      },
      size: {
        type: 'string',
        enum: ['256x256', '512x512', '1024x1024'],
        description: 'Size of variation images',
        default: '1024x1024'
      }
    },
    required: ['imageUrl']
  }
};

/**
 * Execute MCP image generation tool call
 */
export async function executeMCPImageTool(
  toolCall: MCPToolCall,
  userId?: string
): Promise<MCPToolResult> {
  try {
    apiLogger.info('Executing MCP image tool', {
      toolName: toolCall.name,
      userId,
      arguments: toolCall.arguments
    });

    switch (toolCall.name) {
      case 'generate_image':
        return await handleImageGeneration(toolCall.arguments, userId);
      
      case 'edit_image':
        return await handleImageEditing(toolCall.arguments, userId);
      
      case 'create_image_variations':
        return await handleImageVariations(toolCall.arguments, userId);
      
      default:
        return {
          success: false,
          error: `Unknown tool: ${toolCall.name}`
        };
    }
  } catch (error) {
    apiLogger.error('MCP image tool execution failed', {
      toolName: toolCall.name,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Tool execution failed'
    };
  }
}

/**
 * Handle image generation tool call
 */
async function handleImageGeneration(
  args: Record<string, any>,
  userId?: string
): Promise<MCPToolResult> {
  const {
    prompt,
    model = 'imagen-4',  // Default to Imagen 4
    size = '1024x1024',
    quality = 'standard',
    style = 'natural',
    aspectRatio = '1:1',
    enhancePrompt = false
  } = args;

  if (!prompt || typeof prompt !== 'string') {
    return {
      success: false,
      error: 'Prompt is required and must be a string'
    };
  }

  // Use Imagen 4 directly via Google SDK
  if (model === 'imagen-4' || model === 'gpt-image-1') {
    try {
      const { generateImagen4, isImagen4Available } = await import('./google/imagen4');
      
      if (!isImagen4Available()) {
        return {
          success: false,
          error: 'Imagen 4 is not configured. Please check Google Cloud credentials.'
        };
      }

      const imagen4Result = await generateImagen4({
        prompt,
        aspectRatio: aspectRatio as any,
        sampleCount: 1,
        safetyFilterLevel: 'block_some',
        addWatermark: true,
        quality: quality as any
      }, userId);

      return {
        success: true,
        data: {
          images: imagen4Result.images,
          model: 'imagen-4',
          originalPrompt: prompt,
          cost: imagen4Result.usage.estimated_cost
        },
        metadata: {
          usage: imagen4Result.usage,
          metadata: imagen4Result.metadata
        }
      };
    } catch (error) {
      apiLogger.error('Imagen 4 generation failed', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Image generation failed'
      };
    }
  }

  // Fallback to regular image generation for other models
  const imageParams: ImageGenerationParams = {
    prompt,
    model: model as any,
    size,
    quality: quality as 'standard' | 'hd',
    style: style as 'vivid' | 'natural',
    aspectRatio: aspectRatio as any,
    enhancePrompt
  };

  const result = await generateImage(imageParams, userId);

  return {
    success: true,
    data: {
      images: result.images,
      model: result.metadata.model,
      originalPrompt: result.metadata.originalPrompt,
      enhancedPrompt: result.metadata.enhancedPrompt,
      cost: result.usage.estimated_cost
    },
    metadata: {
      usage: result.usage,
      metadata: result.metadata
    }
  };
}

/**
 * Handle image editing tool call
 */
async function handleImageEditing(
  args: Record<string, any>,
  userId?: string
): Promise<MCPToolResult> {
  // Import editImage dynamically to avoid circular dependencies
  const { editImage } = await import('./image-editing');
  
  const { imageUrl, editPrompt, maskUrl, model = 'dall-e-2' } = args;

  if (!imageUrl || !editPrompt) {
    return {
      success: false,
      error: 'imageUrl and editPrompt are required'
    };
  }

  const result = await editImage({
    originalImage: imageUrl,
    prompt: editPrompt,
    mask: maskUrl,
    model: model as any
  }, userId);

  return {
    success: true,
    data: {
      images: result.images,
      model: result.metadata.model,
      editPrompt,
      cost: result.usage.estimated_cost
    },
    metadata: {
      usage: result.usage,
      metadata: result.metadata
    }
  };
}

/**
 * Handle image variations tool call
 */
async function handleImageVariations(
  args: Record<string, any>,
  userId?: string
): Promise<MCPToolResult> {
  // Import createImageVariations dynamically
  const { createImageVariations } = await import('./image-editing');
  
  const { imageUrl, numberOfVariations = 1, size = '1024x1024' } = args;

  if (!imageUrl) {
    return {
      success: false,
      error: 'imageUrl is required'
    };
  }

  const result = await createImageVariations({
    originalImage: imageUrl,
    n: numberOfVariations,
    size: size as any
  }, userId);

  return {
    success: true,
    data: {
      images: result.images,
      model: result.metadata.model,
      sourceImage: imageUrl,
      variations: numberOfVariations,
      cost: result.usage.estimated_cost
    },
    metadata: {
      usage: result.usage,
      metadata: result.metadata
    }
  };
}

/**
 * Get all available MCP image tools
 */
export function getMCPImageTools(): MCPImageGenerationTool[] {
  return [
    imageGenerationTool,
    imageEditingTool,
    imageVariationTool
  ];
}

/**
 * Check if a tool name is an image-related tool
 */
export function isImageTool(toolName: string): boolean {
  return ['generate_image', 'edit_image', 'create_image_variations'].includes(toolName);
}

/**
 * Convert MCP image tools to OpenAI function calling format
 */
export function getMCPImageToolsForOpenAI() {
  return getMCPImageTools().map(tool => ({
    type: 'function' as const,
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }
  }));
}