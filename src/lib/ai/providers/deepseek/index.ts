import { deepseek, createDeepSeek } from '@ai-sdk/deepseek';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig } from '../types';
/**
 * DeepSeek Provider using AI SDK
 * 
 * Implements DeepSeek models including DeepSeek V3, R1 reasoning models,
 * and specialized coding models
 * 
 * Models are fetched from database via ModelRepository
 */
export class DeepSeekAISDKProvider extends AISdkProviderBase {
  public providerName = 'deepseek';
  protected provider: any;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[DeepSeek] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.DEEPSEEK_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Use createDeepSeek when we have custom configuration
    if (config.apiKey || config.baseUrl || config.headers || config.fetch) {
      const customProvider = createDeepSeek({
        apiKey: config.apiKey || process.env.DEEPSEEK_API_KEY,
        baseURL: config.baseUrl,
        headers: config.headers,
        fetch: config.fetch as any
      });
      
      this.provider = customProvider;
      console.log('[DeepSeek] Created custom provider with configuration');
    } else {
      // Use default deepseek provider instance
      this.provider = deepseek;
      console.log('[DeepSeek] Using default DeepSeek provider');
    }
  }
  async validateConfig(): Promise<boolean> {
    try {
      // For now, just check if we have an API key
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.DEEPSEEK_API_KEY;
      if (!apiKey) {
        console.error('[DeepSeek] No API key found');
        return false;
      }
      
      // Skip the actual API call for now to avoid the provider function issue
      console.log('[DeepSeek] Configuration validated (API key present)');
      return true;
    } catch (error) {
      console.error('DeepSeek configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default DeepSeekAISDKProvider;
