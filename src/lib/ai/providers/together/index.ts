import { createTogetherAI } from '@ai-sdk/togetherai';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig } from '../types';
/**
 * Together AI Provider using AI SDK
 * 
 * Implements Together AI's diverse model collection including Llama, Qwen,
 * DeepSeek, and various other open-source models
 * 
 * Models are fetched from database via ModelRepository
 */
export class TogetherAISDKProvider extends AISdkProviderBase {
  public providerName = 'together';
  protected provider: any;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Together] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.TOGETHER_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // ALWAYS use createTogetherAI for consistency
    const customProvider = createTogetherAI({
      apiKey: config.apiKey || process.env.TOGETHER_API_KEY,
      baseURL: config.baseUrl || 'https://api.together.xyz/v1',
      headers: config.headers,
      fetch: config.fetch as any
    });
    
    this.provider = customProvider;
    console.log('[Together] Created custom provider with configuration');
  }
  async validateConfig(): Promise<boolean> {
    try {
      // For now, just check if we have an API key
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.TOGETHER_API_KEY;
      if (!apiKey) {
        console.error('[Together AI] No API key found');
        return false;
      }
      
      // Skip the actual API call for now to avoid the provider function issue
      console.log('[Together AI] Configuration validated (API key present)');
      return true;
    } catch (error) {
      console.error('Together AI configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default TogetherAISDKProvider;
