import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { AISdkProviderBase } from '../ai-sdk-base';
import { ModelInfo, AISDKProviderConfig, NativeWebSearchOptions } from '../types';
/**
 * OpenRouter Provider using AI SDK
 * 
 * Implements OpenRouter's unified API for accessing multiple AI models
 * from various providers through a single interface
 * 
 * Models are fetched from database via ModelRepository
 */
export class OpenRouterAISD<PERSON><PERSON><PERSON><PERSON> extends AISdkProviderBase {
  public providerName = 'openrouter';
  protected provider: any;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[OpenRouter] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.OPENROUTER_API_KEY),
      baseUrl: config.baseUrl || 'https://openrouter.ai/api/v1'
    });
    
    // OpenRouter only provides createOpenRouter, no default instance
    this.provider = createOpenRouter({
      apiKey: config.apiKey || process.env.OPENROUTER_API_KEY,
      baseURL: config.baseUrl || 'https://openrouter.ai/api/v1',
      headers: config.headers,
      fetch: config.fetch as any, // Type compatibility for fetch function
    });

    // Override the provider to add web search support
    this.provider = this.createWebSearchEnabledProvider(this.provider);
    
    console.log('[OpenRouter] Created provider with configuration');
  }

  /**
   * Create a web search enabled provider wrapper
   * Adds OpenRouter's native web search capabilities
   */
  private createWebSearchEnabledProvider(baseProvider: any) {
    return (modelId: string, settings?: any) => {
      const model = baseProvider(modelId, settings);

      // Override the doStream and doGenerate methods to add web search support
      const originalDoStream = model.doStream;
      const originalDoGenerate = model.doGenerate;

      model.doStream = async (params: any) => {
        const enhancedParams = this.addWebSearchParams(params, modelId);
        return originalDoStream(enhancedParams);
      };

      model.doGenerate = async (params: any) => {
        const enhancedParams = this.addWebSearchParams(params, modelId);
        return originalDoGenerate(enhancedParams);
      };

      return model;
    };
  }

  /**
   * Add OpenRouter web search parameters to the request
   */
  private addWebSearchParams(params: any, modelId: string) {
    if (!params.webSearchEnabled) {
      return params;
    }

    console.log('[OpenRouter] Enabling web search for model:', modelId);

    // Clone params to avoid mutation
    const enhancedParams = { ...params };

    // Method 1: Use :online suffix for any model
    if (!modelId.includes(':online')) {
      enhancedParams.model = `${modelId}:online`;
      console.log('[OpenRouter] Using :online suffix for web search');
    }

    // Method 2: Add web search options if provided
    const webSearchOptions = params.webSearchOptions as NativeWebSearchOptions;
    if (webSearchOptions) {
      enhancedParams.web_search_options = {
        search_context_size: webSearchOptions.searchContextSize || 'medium'
      };

      console.log('[OpenRouter] Web search options:', enhancedParams.web_search_options);
    }

    // Method 3: Add web plugin as fallback
    if (!enhancedParams.plugins) {
      enhancedParams.plugins = [];
    }

    const hasWebPlugin = enhancedParams.plugins.some((p: any) => p.id === 'web');
    if (!hasWebPlugin) {
      const webPlugin: any = { id: 'web' };

      // Add custom web search parameters
      if (webSearchOptions?.maxResults) {
        webPlugin.max_results = webSearchOptions.maxResults;
      }

      if (webSearchOptions?.searchPrompt) {
        webPlugin.search_prompt = webSearchOptions.searchPrompt;
      }

      enhancedParams.plugins.push(webPlugin);
      console.log('[OpenRouter] Added web plugin:', webPlugin);
    }

    return enhancedParams;
  }
  async validateConfig(): Promise<boolean> {
    try {
      // For now, just check if we have an API key
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        console.error('[OpenRouter] No API key found');
        return false;
      }
      
      // Skip the actual API call for now to avoid the provider function issue
      console.log('[OpenRouter] Configuration validated (API key present)');
      return true;
    } catch (error) {
      console.error('OpenRouter configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default OpenRouterAISDKProvider;
export { OpenRouterAISDKProvider as OpenRouterProvider };
