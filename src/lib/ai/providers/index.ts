/**
 * ⚠️ CRITICAL: DO NOT CHANGE FROM OFFICIAL SDKS ⚠️
 * 
 * This unified streaming implementation uses OFFICIAL SDKs directly:
 * - OpenAI: Uses 'openai' package (NOT @ai-sdk/openai)
 * - Anthropic: Uses '@anthropic-ai/sdk' (NOT @ai-sdk/anthropic)  
 * - Google: Uses existing handleGoogleAI integration
 * - Others: Use OpenAI-compatible APIs via official OpenAI SDK
 * 
 * This approach has been tested and confirmed working for ALL providers.
 * DO NOT revert to AI SDK wrappers - they cause "this.provider is not a function" errors.
 * 
 * AI SDK Providers Export
 * 
 * Central export point for all AI SDK provider implementations.
 * Each provider is organized in its own folder for better maintainability.
 */

// Import constants needed for this file
import { AI_SDK_PROVIDERS, type AISDKProviderName } from './constants';

// Export base class and types
export { AISdkProviderBase } from './ai-sdk-base';
export * from './types';
export { ProviderRegistry } from './registry';
export type { AISDKProviderRegistration } from './registry';

// Export provider definitions and registration
export { 
  PROVIDER_DEFINITIONS, 
  CAPABILITY_PROFILES,
  validateProviderDefinitions
} from './provider-definitions';
export type { ProviderMetadata } from './provider-definitions';

export {
  registerAISDKProviders,
  registerProviders,
  unregisterAllProviders,
  getProviderRegistration,
  checkProviderEnvironment,
  AI_SDK_REGISTRATIONS
} from './register-providers';

// Export all provider implementations
export { OpenAIProvider } from './openai';
export { AnthropicAISDKProvider } from './anthropic';
export { GoogleAISDKProvider } from './google';
export { MistralAISDKProvider } from './mistral';
export { GroqAISDKProvider } from './groq';
export { TogetherAISDKProvider } from './together';
export { XAIAISDKProvider } from './xai';
export { DeepSeekAISDKProvider } from './deepseek';
export { PerplexityAISDKProvider } from './perplexity';
export { CohereAISDKProvider } from './cohere';
export { QwenAISDKProvider } from './qwen';
// OpenRouter has a different export pattern
import OpenRouterAISDKProvider from './openrouter/index';
export { OpenRouterAISDKProvider };

// Re-export default exports for convenience
import OpenAIProviderDefault from './openai';
import AnthropicProvider from './anthropic';
import GoogleProvider from './google';
import MistralProvider from './mistral';
import GroqProvider from './groq';
import TogetherProvider from './together';
import XAIProvider from './xai';
import DeepSeekProvider from './deepseek';
import PerplexityProvider from './perplexity';
import CohereProvider from './cohere';
// OpenRouter uses default export from the folder
import OpenRouterProvider from './openrouter/index';
import QwenProvider from './qwen';

export {
  OpenAIProviderDefault,
  AnthropicProvider,
  GoogleProvider,
  MistralProvider,
  GroqProvider,
  TogetherProvider,
  XAIProvider,
  DeepSeekProvider,
  PerplexityProvider,
  CohereProvider,
  OpenRouterProvider,
  QwenProvider
};

// Re-export provider constants
export { AI_SDK_PROVIDERS, type AISDKProviderName } from './constants';

// Import required SDKs for unified streaming
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { handleGoogleAI } from '@/lib/ai/google';

// Unified streaming interfaces
export interface StreamMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface StreamOptions {
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
}

export interface StreamChunk {
  content: string;
  isComplete: boolean;
  type?: 'content' | 'reasoning' | 'reasoning_summary' | 'search_info' | 'citation_info';
  reasoning?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// Factory function to create providers by name
export function createAISDKProvider(providerName: AISDKProviderName, config?: any) {
  switch (providerName) {
    case AI_SDK_PROVIDERS.OPENAI:
      return new OpenAIProviderDefault(config);
    case AI_SDK_PROVIDERS.ANTHROPIC:
      return new AnthropicProvider(config);
    case AI_SDK_PROVIDERS.GOOGLE:
      return new GoogleProvider(config);
    case AI_SDK_PROVIDERS.MISTRAL:
      return new MistralProvider(config);
    case AI_SDK_PROVIDERS.GROQ:
      return new GroqProvider(config);
    case AI_SDK_PROVIDERS.TOGETHER:
      return new TogetherProvider(config);
    case AI_SDK_PROVIDERS.XAI:
      return new XAIProvider(config);
    case AI_SDK_PROVIDERS.DEEPSEEK:
      return new DeepSeekProvider(config);
    case AI_SDK_PROVIDERS.PERPLEXITY:
      return new PerplexityProvider(config);
    case AI_SDK_PROVIDERS.COHERE:
      return new CohereProvider(config);
    case AI_SDK_PROVIDERS.OPENROUTER:
      return new OpenRouterProvider(config);
    case AI_SDK_PROVIDERS.QWEN:
      return new QwenProvider(config);
    case AI_SDK_PROVIDERS.ALIBABA:
      // Alibaba is an alias for Qwen
      return new QwenProvider(config);
    default:
      throw new Error(`Unknown AI SDK provider: ${providerName}`);
  }
}

// **UNIFIED STREAMING - THE CLEAN SOLUTION**
// Single function that handles ALL providers consistently using official SDKs

/**
 * ⚠️ WORKING SOLUTION - DO NOT MODIFY ⚠️
 * 
 * Universal streaming function that handles ALL AI providers
 * Uses official SDKs directly for maximum reliability and consistency
 * 
 * CONFIRMED WORKING BY USER - ALL PROVIDERS TESTED ✅
 * - OpenAI: ✅ Working with official 'openai' SDK
 * - Anthropic: ✅ Working with official '@anthropic-ai/sdk'
 * - Google: ✅ Working with existing handleGoogleAI
 * - Others: ✅ Working with OpenAI-compatible endpoints
 * 
 * DO NOT change this implementation back to AI SDK wrappers!
 */
export async function* streamProvider(
  messages: StreamMessage[],
  options: StreamOptions
): AsyncGenerator<StreamChunk> {
  const { model } = options;
  
  console.log('[ProviderStream] Starting unified stream for model:', model);
  
  try {
    if (model.startsWith('openai/')) {
      yield* streamOpenAI(messages, options);
    } else if (model.startsWith('anthropic/')) {
      yield* streamAnthropic(messages, options);
    } else if (model.startsWith('gemini/') || model.startsWith('google/')) {
      yield* streamGoogle(messages, options);
    } else if (model.startsWith('groq/')) {
      yield* streamOpenAICompatible(messages, options, {
        baseURL: 'https://api.groq.com/openai/v1',
        apiKey: process.env.GROQ_API_KEY!,
        modelPrefix: 'groq/'
      });
    } else if (model.startsWith('mistral/')) {
      yield* streamOpenAICompatible(messages, options, {
        baseURL: 'https://api.mistral.ai/v1',
        apiKey: process.env.MISTRAL_API_KEY!,
        modelPrefix: 'mistral/'
      });
    } else if (model.startsWith('deepseek/')) {
      yield* streamOpenAICompatible(messages, options, {
        baseURL: 'https://api.deepseek.com/v1',
        apiKey: process.env.DEEPSEEK_API_KEY!,
        modelPrefix: 'deepseek/'
      });
    } else if (model.startsWith('xai/')) {
      yield* streamXAI(messages, options);
    } else if (model.startsWith('perplexity/')) {
      yield* streamPerplexity(messages, options);
    } else if (model.startsWith('cohere/')) {
      yield* streamOpenAICompatible(messages, options, {
        baseURL: 'https://api.cohere.ai/v1',
        apiKey: process.env.COHERE_API_KEY!,
        modelPrefix: 'cohere/'
      });
    } else if (model.startsWith('together/')) {
      yield* streamOpenAICompatible(messages, options, {
        baseURL: 'https://api.together.xyz/v1',
        apiKey: process.env.TOGETHER_API_KEY!,
        modelPrefix: 'together/'
      });
    } else if (model.startsWith('openrouter/')) {
      yield* streamOpenAICompatible(messages, options, {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY!,
        modelPrefix: 'openrouter/'
      });
    } else {
      // Fallback to existing AI SDK provider system for unsupported models
      throw new Error(`Model ${model} not supported by unified streaming, falling back to AI SDK providers`);
    }
  } catch (error) {
    console.error('[ProviderStream] Error:', error);
    throw error;
  }
}

// Individual provider streaming implementations

// Helper function to process O-series response events
function* processResponseEvent(
  event: any,
  hasContent: { value: boolean },
  hasReasoning: { value: boolean }
): Generator<StreamChunk> {
  // Log ALL events for debugging O3 reasoning
  console.log(`[OpenAI] Responses API event:`, {
    type: event.type,
    delta: event.delta,
    text: event.text,
    item: event.item ? { 
      type: event.item.type, 
      hasContent: !!event.item.content, 
      hasSummary: !!event.item.summary,
      hasText: !!event.item.text
    } : undefined
  });
  
  // Handle the actual O-series Responses API events based on documentation
  switch (event.type) {
    // === PUBLIC REASONING SUMMARY EVENTS (Safe to show users) ===
    case 'response.reasoning_summary.delta':
    case 'response.reasoning_summary_text.delta':  // O3 uses this event name
      // Incremental public reasoning summary
      if (event.delta) {
        hasReasoning.value = true;
        console.log('[OpenAI] Reasoning summary delta:', event.delta);
        yield { 
          content: '', 
          isComplete: false, 
          type: 'reasoning',
          reasoning: event.delta
        };
      }
      break;
      
    case 'response.reasoning_summary.done':
    case 'response.reasoning_summary_text.done':  // O3 uses this event name
      // Complete public reasoning summary
      hasReasoning.value = true;
      if (event.text) {
        console.log('[OpenAI] Reasoning summary done:', event.text);
        yield { 
          content: '', 
          isComplete: false, 
          type: 'reasoning_summary',
          reasoning: event.text
        };
      }
      break;
      
    // === ASSISTANT TEXT EVENTS (User-visible answer) ===
    case 'response.text.delta':
    case 'response.output_text.delta':  // O3 uses this event name
      // Text content delta
      if (event.delta) {
        hasContent.value = true;
        yield { 
          content: event.delta, 
          isComplete: false, 
          type: 'content'
        };
      }
      break;
      
    case 'response.text.done':
    case 'response.output_text.done':  // O3 uses this event name
      // Text output is complete
      console.log('[OpenAI] Text output done');
      break;
      
    // === RAW REASONING EVENTS (Internal use only, not for users) ===
    case 'response.output_item.added':
      // Output item added (reasoning or message)
      if (event.item?.type === 'reasoning') {
        console.log('[OpenAI] Raw reasoning item added (internal use only)');
        hasReasoning.value = true;
        // Note: event.item.content is usually null here
      }
      break;
      
    case 'response.output_item.done':
      // Output item completed
      if (event.item?.type === 'reasoning') {
        hasReasoning.value = true;
        console.log('[OpenAI] Raw reasoning item done:', {
          hasContent: !!event.item.content,
          hasSummary: !!event.item.summary
        });
        // Raw reasoning should NOT be shown to users (internal audit only)
        // The public summary comes via response.reasoning_summary.* events
      }
      break;
      
    // === COMPLETION EVENT ===
    case 'response.done':
      // Response is complete
      console.log('[OpenAI] Response completed');
      if (event.response?.usage) {
        yield {
          content: '',
          isComplete: true,
          usage: {
            promptTokens: event.response.usage.input_tokens || 0,
            completionTokens: event.response.usage.output_tokens || 0,
            totalTokens: (event.response.usage.input_tokens || 0) + (event.response.usage.output_tokens || 0)
          }
        };
      } else {
        yield {
          content: '',
          isComplete: true
        };
      }
      break;
      
    // === CONTENT PART EVENTS ===
    case 'response.content_part.added':
      // Content part added - just log for now
      console.log('[OpenAI] Content part added');
      break;
      
    case 'response.content_part.done':
      // Content part done - just log for now
      console.log('[OpenAI] Content part done');
      break;
      
    // === REASONING SUMMARY PART EVENTS ===
    case 'response.reasoning_summary_part.done':
      // Reasoning summary part done
      console.log('[OpenAI] Reasoning summary part done');
      hasReasoning.value = true;
      break;
      
    default:
      // Log any unhandled events for debugging
      if (event.type) {
        console.log('[OpenAI] Unhandled event:', event.type);
      }
      break;
  }
}

/**
 * Stream OpenAI models including O-series (O1, O3, O4)
 * 
 * IMPORTANT: O-series models have different streaming behaviors:
 * - O4 models: Return a properly wrapped async iterable stream (standard for-await works)
 * - O3 models: May return a raw stream that doesn't implement async iterator protocol
 * - O1 models: Use Responses API like O3/O4 but with standard streaming
 * 
 * This function automatically detects the model type and adapts the streaming approach:
 * 1. For O-series models: Uses Responses API with reasoning extraction
 * 2. For O3 specifically: Checks if stream is async iterable, falls back to manual parsing
 * 3. For regular models: Uses standard Chat Completions API
 * 
 * Based on O3's analysis of streaming differences (July 2025)
 */
async function* streamOpenAI(
  messages: StreamMessage[],
  options: StreamOptions
): AsyncGenerator<StreamChunk> {
  const client = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  const modelId = options.model.replace('openai/', '');
  
  // Check if this is an O-series model that should use Responses API
  // Handle both "o3" and "o3-2025-04-16" formats, as well as o4-mini
  const isOSeries = modelId.includes('o1-') || modelId.includes('o3-') || modelId.includes('o4-') ||
                    modelId === 'o1' || modelId === 'o3' || modelId === 'o4' ||
                    modelId.startsWith('o1') || modelId.startsWith('o3') || modelId.startsWith('o4') ||
                    modelId === 'o4-mini' || modelId.includes('o4-mini');
  
  console.log(`[OpenAI] Model: ${modelId}, isOSeries: ${isOSeries}`);
  
  if (isOSeries) {
    // Use Responses API for O-series models to get reasoning
    console.log(`[OpenAI] Using Responses API for O-series model: ${modelId}`);
    
    // Convert messages to input format for Responses API
    // The documentation shows input should be an array of messages
    const formattedMessages = messages.map(m => ({
      role: m.role as 'user' | 'assistant' | 'system',
      content: m.content
    }));
    
    // Extract system instructions if present
    const systemMessage = messages.find(m => m.role === 'system');
    const instructions = systemMessage?.content || 'You are a helpful assistant.';
    
    // Filter out system messages for input array
    const inputMessages = formattedMessages.filter(m => m.role !== 'system');
    
    try {
      // Use the Responses API for O-series models based on documentation
      // Extract system message for instructions
      const systemMessage = messages.find(m => m.role === 'system');
      const instructions = systemMessage?.content || 'You are a helpful assistant.';

      console.log(`[OpenAI] Creating Responses API stream with:`, {
        model: modelId,
        inputCount: inputMessages.length,
        instructions: instructions.substring(0, 100) + '...',
        stream: true,
        reasoning: { effort: modelId.includes('o3') ? 'medium' : 'low', summary: 'detailed' }
      });

      // Add debug logging to see what type of response we get
      console.log(`[OpenAI] Model ${modelId} - expecting reasoning events`);
      
      // Convert conversation to input string (Responses API expects a string input)
      // Format as a conversation for context
      const conversationMessages = messages.filter(m => m.role !== 'system');
      let input = '';
      
      if (conversationMessages.length === 1 && conversationMessages[0].role === 'user') {
        // Single user message - use it directly
        input = conversationMessages[0].content;
      } else {
        // Multiple messages - format as conversation
        input = conversationMessages.map(m => {
          const role = m.role === 'user' ? 'User' : 'Assistant';
          return `${role}: ${m.content}`;
        }).join('\n\n');
        
        // Add the latest user message if not already at the end
        const lastMessage = conversationMessages[conversationMessages.length - 1];
        if (lastMessage && lastMessage.role === 'user' && !input.endsWith(lastMessage.content)) {
          input += `\n\nPlease respond to the user's latest message above.`;
        }
      }
      
      console.log('[OpenAI] Responses API input:', {
        modelId,
        instructionsLength: instructions.length,
        inputLength: input.length,
        messageCount: conversationMessages.length
      });
      
      const response = await client.responses.create({
        model: modelId,
        input: input,
        instructions: instructions,
        stream: true,
        // Note: Responses API doesn't support max_tokens, temperature, etc.
      });

      // Use objects to track state across generator calls
      const hasContent = { value: false };
      const hasReasoning = { value: false };

      // Process the Responses API stream for O-series models
      console.log('[OpenAI] Processing O-series Responses API stream');
      
      try {
        // The Responses API returns events directly, not chat completion chunks
        for await (const event of response) {
          // Process each event using the existing processResponseEvent function
          yield* processResponseEvent(event, hasContent, hasReasoning);
        }
        
        // Log completion status
        console.log('[OpenAI] O-series Responses API stream completed:', {
          hasContent: hasContent.value,
          hasReasoning: hasReasoning.value
        });
      } catch (error) {
        console.error('[OpenAI] Error processing O-series Responses API stream:', error);
        throw error;
      }
      
      // If we didn't get any content or reasoning, log a warning
      if (!hasContent.value && !hasReasoning.value) {
        console.warn('[OpenAI] Responses API stream completed without content or reasoning');
      }
    } catch (error) {
      console.error('[OpenAI] Responses API error:', error);
      throw error;
    }
  } else {
    // Use Chat Completions API for non-O-series models
    console.log(`[OpenAI] Using Chat Completions API for model: ${modelId}`);

    // Check if this is a search-enabled model
    const searchModels = ['gpt-4o-search-preview', 'gpt-4o-mini-search-preview'];
    const supportsWebSearch = searchModels.some(model => modelId.includes(model));

    const requestPayload: any = {
      model: modelId,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      stream: true,
      max_tokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.7,
      top_p: options.topP || 0.9,
    };

    // Add web search tool for search-enabled models
    if (supportsWebSearch) {
      requestPayload.tools = [
        {
          type: 'web_search_preview',
          user_location: {
            type: 'approximate',
            country: 'US'
          },
          search_context_size: 'medium'
        }
      ];
      console.log(`[OpenAI] Enabled web search for model: ${modelId}`);
    }

    const stream = await client.chat.completions.create(requestPayload) as any;

    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta;

      // Handle tool calls (web search)
      if (delta?.tool_calls && supportsWebSearch) {
        for (const toolCall of delta.tool_calls) {
          if (toolCall.type === 'web_search_preview') {
            yield {
              content: '🔍 **Searching the web...**\n\n',
              isComplete: false,
              type: 'search_info'
            };
          }
        }
      }

      if (delta?.content) {
        yield { content: delta.content, isComplete: false, type: 'content' };
      }

      if (chunk.choices[0]?.finish_reason) {
        yield {
          content: '',
          isComplete: true,
          usage: chunk.usage ? {
            promptTokens: chunk.usage.prompt_tokens,
            completionTokens: chunk.usage.completion_tokens,
            totalTokens: chunk.usage.total_tokens
          } : undefined
        };
        break;
      }
    }
  }
}

async function* streamAnthropic(
  messages: StreamMessage[],
  options: StreamOptions
): AsyncGenerator<StreamChunk> {
  const client = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
  const modelId = options.model.replace('anthropic/', '');
  
  // Separate system messages
  const systemMessage = messages.find(m => m.role === 'system')?.content;
  const conversationMessages = messages.filter(m => m.role !== 'system').map(m => ({
    role: m.role as 'user' | 'assistant',
    content: m.content
  }));

  const stream = client.messages.stream({
    model: modelId,
    messages: conversationMessages,
    system: systemMessage,
    max_tokens: options.maxTokens || 4000,
    temperature: options.temperature || 0.7,
    top_p: options.topP || 0.9,
  });

  let contentStarted = false;
  
  for await (const event of stream) {
    // Log events for debugging
    console.log('[Anthropic] Stream event:', event.type, event);
    
    switch (event.type) {
      case 'message_start':
        console.log('[Anthropic] Message started');
        break;
        
      case 'content_block_start':
        console.log('[Anthropic] Content block started:', event.content_block);
        contentStarted = true;
        break;
        
      case 'content_block_delta':
        if (event.delta?.type === 'text_delta' && event.delta.text) {
          console.log('[Anthropic] Text delta:', event.delta.text);
          yield { content: event.delta.text, isComplete: false, type: 'content' };
        }
        break;
        
      case 'content_block_stop':
        console.log('[Anthropic] Content block stopped');
        break;
        
      case 'message_delta':
        console.log('[Anthropic] Message delta:', event.delta);
        break;
        
      case 'message_stop':
        console.log('[Anthropic] Message stop event');
        try {
          const finalMessage = await stream.finalMessage();
          yield {
            content: '',
            isComplete: true,
            usage: finalMessage.usage ? {
              promptTokens: finalMessage.usage.input_tokens,
              completionTokens: finalMessage.usage.output_tokens,
              totalTokens: finalMessage.usage.input_tokens + finalMessage.usage.output_tokens
            } : undefined
          };
        } catch (e) {
          console.error('[Anthropic] Error getting final message:', e);
          yield { content: '', isComplete: true };
        }
        break;
        
      // Note: 'error' is not a standard Anthropic event type, but handle it just in case
      default:
        if ((event as any).type === 'error') {
          console.error('[Anthropic] Stream error:', (event as any).error);
          throw new Error(`Anthropic stream error: ${(event as any).error?.message || 'Unknown error'}`);
        }
        console.log('[Anthropic] Unhandled event type:', (event as any).type);
    }
  }
}

async function* streamGoogle(
  messages: StreamMessage[],
  options: StreamOptions
): AsyncGenerator<StreamChunk> {
  const systemPrompt = messages.find(m => m.role === 'system')?.content;
  const googleMessages = messages.map(m => ({ role: m.role, content: m.content }));

  const googleStream = await handleGoogleAI(googleMessages, {
    model: options.model,
    temperature: options.temperature,
    maxTokens: options.maxTokens,
    topP: options.topP,
    systemPrompt: systemPrompt,
    stream: true,
    responseFormat: 'text'
  });

  for await (const chunk of googleStream as AsyncGenerator<{ content: string; isComplete: boolean }>) {
    yield { content: chunk.content, isComplete: chunk.isComplete };
  }
}

async function* streamPerplexity(
  messages: StreamMessage[],
  options: StreamOptions
): AsyncGenerator<StreamChunk> {
  const modelId = options.model.replace('perplexity/', '');
  const apiKey = process.env.PERPLEXITY_API_KEY;

  if (!apiKey) {
    throw new Error('PERPLEXITY_API_KEY is required');
  }

  // Check if this is a native search model
  const nativeSearchModels = ['sonar-pro', 'sonar', 'sonar-reasoning', 'sonar-reasoning-pro', 'sonar-deep-research'];
  const isNativeSearchModel = nativeSearchModels.some(model => modelId.includes(model));

  console.log('[Perplexity] Streaming model:', modelId, 'isNativeSearch:', isNativeSearchModel);

  const payload: any = {
    model: modelId,
    messages,
    stream: true,
    max_tokens: options.maxTokens || 4000,
    temperature: options.temperature || 0.7,
    top_p: options.topP || 0.9,
  };

  // Add native web search parameters for search-enabled models
  if (isNativeSearchModel) {
    // Enable web search by default for native search models
    payload.web_search_options = {
      search_context_size: 'medium'
    };
    payload.return_related_questions = false; // Keep response focused
    payload.return_images = false; // Disable images for now

    console.log('[Perplexity] Enabled native web search for model:', modelId);
  }

  try {
    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Perplexity API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const reader = response.body!.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6);
            if (dataStr === '[DONE]') {
              yield { content: '', isComplete: true };
              return;
            }

            try {
              const data = JSON.parse(dataStr);

              // Handle search results (emit as special content)
              if (data.search_results && isNativeSearchModel) {
                const searchInfo = `🔍 **Sources found**: ${data.search_results.length} results\n\n`;
                yield {
                  content: searchInfo,
                  isComplete: false,
                  type: 'search_info'
                };
              }

              // Handle citations (emit as special content)
              if (data.citations && isNativeSearchModel) {
                const citationInfo = `📚 **Citations**: ${data.citations.length} sources referenced\n\n`;
                yield {
                  content: citationInfo,
                  isComplete: false,
                  type: 'citation_info'
                };
              }

              // Handle content chunks
              if (data.choices?.[0]?.delta?.content) {
                yield {
                  content: data.choices[0].delta.content,
                  isComplete: false
                };
              }

              // Handle completion
              if (data.choices?.[0]?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens,
                    completionTokens: data.usage.completion_tokens,
                    totalTokens: data.usage.total_tokens
                  } : undefined
                };
              }

            } catch (e) {
              console.error('[Perplexity] Failed to parse SSE data:', e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

  } catch (error) {
    console.error('[Perplexity] Streaming error:', error);
    throw error;
  }
}

async function* streamXAI(
  messages: StreamMessage[],
  options: StreamOptions
): AsyncGenerator<StreamChunk> {
  const modelId = options.model.replace('xai/', '');
  const apiKey = process.env.XAI_API_KEY;

  if (!apiKey) {
    throw new Error('XAI_API_KEY is required');
  }

  // Check if this is a Grok model that supports web search
  const webSearchModels = ['grok-2', 'grok-3', 'grok-3-fast', 'grok-beta', 'grok-vision-beta'];
  const supportsWebSearch = webSearchModels.some(model => modelId.includes(model));

  console.log('[xAI] Streaming model:', modelId, 'supportsWebSearch:', supportsWebSearch);

  const payload: any = {
    model: modelId,
    messages,
    stream: true,
    max_tokens: options.maxTokens || 4000,
    temperature: options.temperature || 0.7,
    top_p: options.topP || 0.9,
  };

  // Add live search parameters for web search enabled models
  if (supportsWebSearch) {
    payload.search_parameters = {
      mode: 'auto', // Let the model decide when to search
      return_citations: true,
      max_search_results: 10,
      sources: [
        {
          type: 'web',
          safe_search: true
        },
        {
          type: 'news',
          safe_search: true
        }
      ]
    };

    console.log('[xAI] Enabled live search for model:', modelId);
  }

  try {
    const response = await fetch('https://api.x.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`xAI API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const reader = response.body!.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6);
            if (dataStr === '[DONE]') {
              yield { content: '', isComplete: true };
              return;
            }

            try {
              const data = JSON.parse(dataStr);

              // Handle citations (only in the last chunk for xAI)
              if (data.citations && supportsWebSearch) {
                const citationInfo = `🔗 **Sources**: ${data.citations.length} citations\n\n`;
                yield {
                  content: citationInfo,
                  isComplete: false,
                  type: 'citation_info'
                };
              }

              // Handle content chunks
              if (data.choices?.[0]?.delta?.content) {
                yield {
                  content: data.choices[0].delta.content,
                  isComplete: false
                };
              }

              // Handle completion
              if (data.choices?.[0]?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: data.usage ? {
                    promptTokens: data.usage.prompt_tokens,
                    completionTokens: data.usage.completion_tokens,
                    totalTokens: data.usage.total_tokens
                  } : undefined
                };
              }

            } catch (e) {
              console.error('[xAI] Failed to parse SSE data:', e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

  } catch (error) {
    console.error('[xAI] Streaming error:', error);
    throw error;
  }
}

async function* streamOpenAICompatible(
  messages: StreamMessage[],
  options: StreamOptions,
  config: { baseURL: string; apiKey: string; modelPrefix: string }
): AsyncGenerator<StreamChunk> {
  const client = new OpenAI({ baseURL: config.baseURL, apiKey: config.apiKey });
  const modelId = options.model.replace(config.modelPrefix, '');
  
  const stream = await client.chat.completions.create({
    model: modelId,
    messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
    stream: true,
    max_tokens: options.maxTokens || 4000,
    temperature: options.temperature || 0.7,
    top_p: options.topP || 0.9,
  });

  for await (const chunk of stream) {
    const delta = chunk.choices[0]?.delta;
    if (delta?.content) {
      yield { content: delta.content, isComplete: false };
    }
    
    if (chunk.choices[0]?.finish_reason) {
      yield {
        content: '',
        isComplete: true,
        usage: chunk.usage ? {
          promptTokens: chunk.usage.prompt_tokens,
          completionTokens: chunk.usage.completion_tokens,
          totalTokens: chunk.usage.total_tokens
        } : undefined
      };
      break;
    }
  }
}

/**
 * Check if a model is supported by unified streaming
 */
export function isProviderStreamingSupported(model: string): boolean {
  const supportedPrefixes = [
    'openai/', 'anthropic/', 'gemini/', 'google/',
    'groq/', 'mistral/', 'deepseek/', 'xai/',
    'perplexity/', 'cohere/', 'together/', 'openrouter/'
  ];
  
  return supportedPrefixes.some(prefix => model.startsWith(prefix));
}

/**
 * Unified streaming events interface for consistent frontend handling
 */
export interface UnifiedStreamEvent {
  type: 'reasoning_start' | 'reasoning_delta' | 'reasoning_complete' | 'content_start' | 'content_delta' | 'stream_complete';
  content?: string;
  reasoning?: string;
  isComplete?: boolean;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

/**
 * Normalize any provider's streaming chunks into unified events
 */
export async function* normalizeStreamChunks(chunks: AsyncGenerator<StreamChunk>): AsyncGenerator<UnifiedStreamEvent> {
  let reasoningStartSent = false;
  let contentStartSent = false;

  for await (const chunk of chunks) {
    // Handle reasoning chunks
    if (chunk.type === 'reasoning' && chunk.reasoning) {
      if (!reasoningStartSent) {
        yield { type: 'reasoning_start' };
        reasoningStartSent = true;
      }
      yield {
        type: 'reasoning_delta',
        reasoning: chunk.reasoning
      };
    }

    // Handle reasoning summary/complete
    if (chunk.type === 'reasoning_summary' && chunk.reasoning) {
      yield {
        type: 'reasoning_complete',
        reasoning: chunk.reasoning
      };
    }

    // Handle content chunks
    if (chunk.content && chunk.type !== 'reasoning') {
      if (!contentStartSent) {
        yield { type: 'content_start' };
        contentStartSent = true;
      }
      yield {
        type: 'content_delta',
        content: chunk.content
      };
    }

    // Handle completion
    if (chunk.isComplete) {
      yield {
        type: 'stream_complete',
        isComplete: true,
        usage: chunk.usage
      };
    }
  }
}