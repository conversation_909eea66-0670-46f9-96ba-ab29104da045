import { AIProvider, ProviderConfig, ModelInfo, AISDKProviderConfig, AISDKModelInfo } from './types';
import { AISdkProviderBase } from './ai-sdk-base';
import { apiLogger } from '@/lib/logger';

export interface ProviderRegistration {
  id: string;
  name: string;
  description: string;
  factory: (config?: ProviderConfig) => AIProvider;
  configKeys: string[];
  defaultModels?: string[];
}

// Enhanced registration for AI SDK providers
export interface AISDKProviderRegistration extends ProviderRegistration {
  factory: (config?: AISDKProviderConfig) => AISdkProviderBase;
  capabilities: {
    reasoning?: boolean;
    multimodal?: boolean;
    functionCalling?: boolean;
    structuredOutput?: boolean;
  };
  aiSdkPackage: string; // e.g., '@ai-sdk/openai'
  isAISDK: true; // Flag to identify AI SDK providers
}

export class ProviderRegistry {
  private static instance: ProviderRegistry;
  private providers = new Map<string, ProviderRegistration | AISDKProviderRegistration>();
  private instances = new Map<string, AIProvider>();
  private modelCache = new Map<string, ModelInfo>();
  
  private constructor() {}
  
  static getInstance(): ProviderRegistry {
    if (!ProviderRegistry.instance) {
      ProviderRegistry.instance = new ProviderRegistry();
    }
    return ProviderRegistry.instance;
  }
  
  // Register a new provider (supports both regular and AI SDK providers)
  register(registration: ProviderRegistration | AISDKProviderRegistration) {
    this.providers.set(registration.id, registration);
    
    const isAISDK = 'isAISDK' in registration && registration.isAISDK;
    const logData: any = {
      id: registration.id,
      configKeys: registration.configKeys,
      type: isAISDK ? 'AI SDK' : 'Regular'
    };
    
    if (isAISDK) {
      const aiSdkReg = registration as AISDKProviderRegistration;
      logData.aiSdkPackage = aiSdkReg.aiSdkPackage;
      logData.capabilities = aiSdkReg.capabilities;
    }
    
    apiLogger.info(`Registered AI provider: ${registration.name}`, logData);
  }
  
  // Register an AI SDK provider with enhanced capabilities
  registerAISDK(registration: AISDKProviderRegistration) {
    this.register(registration);
  }
  
  // Get or create provider instance
  getProvider(providerId: string, config?: ProviderConfig): AIProvider {
    const key = `${providerId}-${JSON.stringify(config || {})}`;
    
    if (!this.instances.has(key)) {
      const registration = this.providers.get(providerId);
      if (!registration) {
        throw new Error(`Provider ${providerId} not registered`);
      }
      
      // Create provider instance with config
      const instance = registration.factory(config);
      this.instances.set(key, instance);
    }
    
    return this.instances.get(key)!;
  }
  
  // Get provider by model ID
  async getProviderForModel(modelId: string): Promise<{ provider: AIProvider; model: ModelInfo }> {
    // Check cache first
    if (this.modelCache.has(modelId)) {
      const model = this.modelCache.get(modelId)!;
      const provider = this.getProvider(model.provider);
      return { provider, model };
    }
    
    // Search all providers
    for (const [providerId, registration] of this.providers) {
      try {
        const provider = this.getProvider(providerId);
        const models = await provider.listModels();
        
        const model = models.find(m => 
          m.id === modelId || 
          m.providerModelId === modelId ||
          `${providerId}/${m.id}` === modelId
        );
        
        if (model) {
          // Cache for future lookups
          this.modelCache.set(modelId, model);
          return { provider, model };
        }
      } catch (error) {
        apiLogger.warn(`Failed to list models for provider ${providerId}`, error as Record<string, any>);
      }
    }
    
    throw new Error(`Model ${modelId} not found in any registered provider`);
  }
  
  // Get providers by capability (AI SDK providers only)
  getProvidersByCapability(capability: 'reasoning' | 'multimodal' | 'functionCalling' | 'structuredOutput'): string[] {
    const providerIds: string[] = [];
    
    for (const [providerId, registration] of this.providers) {
      if ('isAISDK' in registration && registration.isAISDK) {
        const aiSdkReg = registration as AISDKProviderRegistration;
        if (aiSdkReg.capabilities[capability]) {
          providerIds.push(providerId);
        }
      }
    }
    
    return providerIds;
  }
  
  // Get all AI SDK providers
  getAISDKProviders(): string[] {
    const providerIds: string[] = [];
    
    for (const [providerId, registration] of this.providers) {
      if ('isAISDK' in registration && registration.isAISDK) {
        providerIds.push(providerId);
      }
    }
    
    return providerIds;
  }
  
  // Get provider capabilities (AI SDK providers only)
  getProviderCapabilities(providerId: string): any {
    const registration = this.providers.get(providerId);
    if (!registration) {
      return null;
    }
    
    if ('isAISDK' in registration && registration.isAISDK) {
      const aiSdkReg = registration as AISDKProviderRegistration;
      return aiSdkReg.capabilities;
    }
    
    return null;
  }
  
  // Check if provider is AI SDK based
  isAISDKProvider(providerId: string): boolean {
    const registration = this.providers.get(providerId);
    return registration ? ('isAISDK' in registration && registration.isAISDK) : false;
  }
  
  // List all available models
  async listAllModels(): Promise<ModelInfo[]> {
    const allModels: ModelInfo[] = [];
    
    for (const [providerId, registration] of this.providers) {
      try {
        const provider = this.getProvider(providerId);
        const models = await provider.listModels();
        allModels.push(...models);
      } catch (error) {
        apiLogger.warn(`Failed to list models for provider ${providerId}`, error as Record<string, any>);
      }
    }
    
    return allModels;
  }
  
  // Get all registered providers
  listProviders(): ProviderRegistration[] {
    return Array.from(this.providers.values());
  }
  
  // Clear caches
  clearCache() {
    this.modelCache.clear();
  }
}