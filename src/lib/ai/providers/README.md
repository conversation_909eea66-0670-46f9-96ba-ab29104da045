# AI SDK Providers Directory Structure

## Overview

This directory contains all AI provider implementations using the Vercel AI SDK. Each provider is organized in its own folder for better maintainability and scalability.

## Directory Structure

```
providers/
├── README.md                    # This file
├── index.ts                     # Main export file for all providers
├── types.ts                     # Shared types and interfaces
├── ai-sdk-base.ts              # Base class for all AI SDK providers
├── registry.ts                  # Provider registry for dynamic loading
│
├── openai/
│   ├── index.ts                # OpenAI provider implementation
│   ├── types.ts                # OpenAI-specific types
│   ├── utils.ts                # OpenAI-specific utilities
│   └── __tests__/              # OpenAI provider tests
│
├── anthropic/
│   ├── index.ts                # Anthropic provider implementation
│   ├── types.ts                # Anthropic-specific types
│   ├── utils.ts                # Anthropic-specific utilities
│   └── __tests__/              # Anthropic provider tests
│
├── google/
│   ├── index.ts                # Google provider implementation
│   ├── types.ts                # Google-specific types
│   ├── utils.ts                # Google-specific utilities
│   └── __tests__/              # Google provider tests
│
├── mistral/
│   ├── index.ts                # Mistral provider implementation
│   ├── types.ts                # Mistral-specific types
│   ├── utils.ts                # Mistral-specific utilities
│   └── __tests__/              # Mistral provider tests
│
├── groq/
│   ├── index.ts                # Groq provider implementation
│   ├── types.ts                # Groq-specific types
│   ├── utils.ts                # Groq-specific utilities
│   └── __tests__/              # Groq provider tests
│
├── together/
│   ├── index.ts                # Together AI provider implementation
│   ├── types.ts                # Together-specific types
│   ├── utils.ts                # Together-specific utilities
│   └── __tests__/              # Together provider tests
│
├── xai/
│   ├── index.ts                # xAI provider implementation
│   ├── types.ts                # xAI-specific types
│   ├── utils.ts                # xAI-specific utilities
│   └── __tests__/              # xAI provider tests
│
├── deepseek/
│   ├── index.ts                # DeepSeek provider implementation
│   ├── types.ts                # DeepSeek-specific types
│   ├── utils.ts                # DeepSeek-specific utilities
│   └── __tests__/              # DeepSeek provider tests
│
├── perplexity/
│   ├── index.ts                # Perplexity provider implementation
│   ├── types.ts                # Perplexity-specific types
│   ├── utils.ts                # Perplexity-specific utilities
│   └── __tests__/              # Perplexity provider tests
│
├── cohere/
│   ├── index.ts                # Cohere provider implementation
│   ├── types.ts                # Cohere-specific types
│   ├── utils.ts                # Cohere-specific utilities
│   └── __tests__/              # Cohere provider tests
│
├── openrouter/
│   ├── index.ts                # OpenRouter provider implementation
│   ├── types.ts                # OpenRouter-specific types
│   ├── utils.ts                # OpenRouter-specific utilities
│   └── __tests__/              # OpenRouter provider tests
│
└── qwen/
    ├── index.ts                # Qwen provider implementation
    ├── types.ts                # Qwen-specific types
    ├── utils.ts                # Qwen-specific utilities
    └── __tests__/              # Qwen provider tests
```

## Provider Implementation Guidelines

Each provider folder should contain:

1. **index.ts** - Main provider class extending `AISdkProviderBase`
2. **types.ts** - Provider-specific type definitions (if needed)
3. **utils.ts** - Provider-specific utility functions (if needed)
4. **__tests__/** - Unit and integration tests for the provider

## Adding a New Provider

1. Create a new folder with the provider name
2. Implement the provider class extending `AISdkProviderBase`
3. Add provider-specific types and utilities as needed
4. Write comprehensive tests
5. Export the provider in the main `index.ts`
6. Register the provider in `registry.ts`

## Common Patterns

All providers follow these patterns:

- Extend `AISdkProviderBase` for consistent behavior
- Use database models via `ModelRepository`
- Implement provider-specific validation in `validateConfig()`
- Support streaming and completion modes
- Handle errors consistently
- Monitor performance metrics

## Testing

Each provider should have:

- Unit tests for individual methods
- Integration tests with real API calls (using test keys)
- Mock tests for CI/CD environments
- Performance benchmarks