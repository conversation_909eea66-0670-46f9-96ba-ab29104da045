/**
 * Provider-agnostic types for AI integrations
 * 
 * @description
 * This module defines the core types and interfaces used across all AI providers
 * in the application. It provides a unified interface for different AI services
 * including OpenAI, Anthropic, Google, and others through the LiteLLM proxy.
 * 
 * @module lib/ai/providers/types
 */

/**
 * Base error class for all provider-related errors
 * 
 * @class ProviderError
 * @extends Error
 * @param {string} message - Error message
 * @param {number} [status] - HTTP status code
 * @param {string} [provider] - Name of the provider that threw the error
 * @param {any} [details] - Additional error details
 */
export class ProviderError extends Error {
  constructor(
    message: string,
    public status?: number,
    public provider?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ProviderError';
  }
}

/**
 * Error thrown when rate limits are exceeded
 * 
 * @class RateLimitError
 * @extends ProviderError
 * @param {string} message - Error message
 * @param {number} [retryAfter] - Seconds to wait before retrying
 * @param {string} [provider] - Provider that rate limited the request
 */
export class RateLimitError extends ProviderError {
  constructor(
    message: string,
    public retryAfter?: number,
    provider?: string
  ) {
    super(message, 429, provider);
    this.name = 'RateLimitError';
  }
}

/**
 * Alias for backward compatibility
 * @deprecated Use ProviderError instead
 */
export class APIError extends ProviderError {}

/**
 * Error thrown when authentication fails
 * 
 * @class AuthenticationError
 * @extends ProviderError
 * @param {string} message - Error message
 * @param {string} [provider] - Provider that rejected authentication
 */
export class AuthenticationError extends ProviderError {
  constructor(message: string, provider?: string) {
    super(message, 401, provider);
    this.name = 'AuthenticationError';
  }
}

/**
 * Standard message format for AI providers
 * 
 * @interface AIMessage
 * @property {'user' | 'assistant' | 'system' | 'function'} role - Message role
 * @property {string} content - Message content
 * @property {string} [name] - Optional name for function messages
 * @property {any} [function_call] - Function call details
 */
export interface AIMessage {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
  name?: string;
  function_call?: any;
}

export interface StreamChunk {
  content?: string;
  role?: string;
  function_call?: any;
  finish_reason?: string;
  type?: string; // For different chunk types
  provider?: string; // Which provider generated this chunk
  model?: string; // Which model generated this chunk
  metrics?: Record<string, any>; // Provider-specific metrics
  error?: string | Error; // Error message or Error object if chunk contains error
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    reasoning_tokens?: number; // For O-series models
  };
  reasoningContent?: string; // For models that support reasoning/thinking content
}

export interface GenerationOptions {
  model: string;
  messages: AIMessage[] | any[]; // Support both AIMessage and Message interfaces
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  stop?: string | string[]; // Alternative to stopSequences
  stream?: boolean;
  user?: string;
  // Provider-specific options
  providerOptions?: Record<string, any>;
  // Model-specific metadata for advanced features
  metadata?: Record<string, any>;
  // Reasoning support for models like Grok-3-mini, o1, etc.
  reasoningEffort?: 'low' | 'medium' | 'high';
  // Web search support
  webSearchEnabled?: boolean;
  searchDomains?: string[];
  searchRecency?: 'auto' | 'hour' | 'day' | 'week' | 'month';
  returnCitations?: boolean;
  // LiteLLM web search options
  web_search_options?: {
    search_context_size?: 'low' | 'medium' | 'high';
  };
  // Native web search parameters (for providers like Perplexity, xAI)
  nativeWebSearch?: NativeWebSearchOptions;
  // Context tracking for logging and analytics
  userId?: string;
  sessionId?: string;
  conversationId?: string;
  ipAddress?: string;
  userAgent?: string;
  traceId?: string;
  // Callback for reasoning content
  onReasoningContent?: (reasoningContent: string) => void;
  // O-series specific parameters for Responses API
  include_reasoning?: boolean;
  stream_options?: {
    include_usage?: boolean;
    include_reasoning_tokens?: boolean;
  };
}

export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  description?: string;
  contextWindow: number;
  maxOutput?: number;
  maxOutputTokens?: number; // Alternative to maxOutput
  inputCost: number;  // per 1k tokens
  outputCost: number; // per 1k tokens
  capabilities?: string[];
  deprecated?: boolean;
  supportsStreaming?: boolean;
  supportsImages?: boolean; // Image input support
  supportsFunctions?: boolean;
  supportsSystemMessages?: boolean;
  // Provider-specific model info
  providerModelId?: string;
  apiVersion?: string;
  tier?: 'fast' | 'balanced' | 'premium' | 'reasoning';
}

export interface StreamOptions extends GenerationOptions {
  stream: true;
}

// Native Web Search Interfaces
export interface NativeWebSearchOptions {
  // Domain filtering
  searchDomainFilter?: string[]; // Domains to include/exclude (prefix with '-' to exclude)

  // Date filtering
  searchAfterDateFilter?: string; // Format: 'MM/DD/YYYY'
  searchBeforeDateFilter?: string; // Format: 'MM/DD/YYYY'
  searchRecencyFilter?: 'auto' | 'hour' | 'day' | 'week' | 'month';

  // Search mode
  searchMode?: 'web' | 'academic';

  // Context and results
  webSearchOptions?: {
    searchContextSize?: 'low' | 'medium' | 'high';
    userLocation?: {
      country?: string; // Two-letter ISO country code
      latitude?: number;
      longitude?: number;
    };
  };

  // Return options
  returnImages?: boolean;
  returnRelatedQuestions?: boolean;

  // Image filtering (for providers that support it)
  imageDomainFilter?: string[];
  imageFormatFilter?: string[]; // e.g., ['gif', 'png', 'jpg']
}

export interface WebSearchResult {
  title: string;
  url: string;
  date?: string; // ISO date string
  snippet?: string;
}

export interface WebSearchResponse {
  searchResults?: WebSearchResult[];
  citations?: string[];
  relatedQuestions?: string[];
  images?: Array<{
    url: string;
    title?: string;
    source?: string;
  }>;
}

// Streaming web search chunk types
export interface StreamingWebSearchChunk {
  type: 'search_start' | 'search_result' | 'citation' | 'content' | 'search_complete';
  data?: any;
  searchResult?: WebSearchResult;
  citation?: string;
  content?: string;
}

export interface ProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  organizationId?: string;
  projectId?: string;
  region?: string;
  location?: string;
  apiVersion?: string;
  headers?: Record<string, string>;
  timeout?: number;
  maxRetries?: number;
}

// AI SDK specific types for enhanced provider capabilities
export interface AISDKProviderConfig extends ProviderConfig {
  fetch?: (input: RequestInfo, init?: RequestInit) => Promise<Response>;
}

export interface AISDKModelInfo extends ModelInfo {
  aiSdkId: string; // AI SDK model identifier
  extendedCapabilities?: {
    reasoning?: boolean;
    multimodal?: boolean;
    functionCalling?: boolean;
    structuredOutput?: boolean;
  };
}

// AI SDK specific interfaces
export interface ReasoningResult {
  text: string;
  reasoning?: string;
}

export interface MultimodalContent {
  type: 'text' | 'image' | 'audio';
  content: string | Buffer;
  mimeType?: string;
}

// Updated generation options for AI SDK compatibility
export interface AISDKGenerationOptions extends GenerationOptions {
  tools?: any[];
  toolChoice?: 'auto' | 'none' | { type: 'function'; name: string };
  responseFormat?: {
    type: 'json_object' | 'text';
    schema?: any;
  };
  extractReasoning?: boolean;
}

export abstract class AIProvider {
  protected config: ProviderConfig;
  abstract providerName: string;
  
  constructor(config: ProviderConfig = {}) {
    this.config = config;
  }
  
  // Core methods every provider must implement
  abstract generateCompletion(options: GenerationOptions): Promise<string>;
  abstract generateStream(options: GenerationOptions): AsyncGenerator<StreamChunk>;
  abstract listModels(): Promise<ModelInfo[]>;
  abstract validateConfig(): Promise<boolean>;
  
  // Optional methods providers can override
  async countTokens(text: string, model: string): Promise<number> {
    // Basic approximation - providers should override with accurate counting
    return Math.ceil(text.length / 4);
  }
  
  async checkModelAvailability(modelId: string): Promise<boolean> {
    const models = await this.listModels();
    return models.some(m => m.id === modelId);
  }
  
  // Helper methods
  protected handleError(error: any): Error {
    // Standardize error handling across providers
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;
      
      if (status === 401) {
        return new Error(`Authentication failed: ${message}`);
      } else if (status === 429) {
        return new Error(`Rate limit exceeded: ${message}`);
      } else if (status === 400) {
        return new Error(`Invalid request: ${message}`);
      } else if (status >= 500) {
        return new Error(`Provider error: ${message}`);
      }
    }
    
    return error instanceof Error ? error : new Error(String(error));
  }
}