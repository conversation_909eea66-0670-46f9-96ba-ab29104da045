import { generateText, streamText, LanguageModel } from 'ai';
import { 
  AIProvider,
  GenerationOptions, 
  StreamChunk, 
  ModelInfo, 
  ProviderConfig,
  AISDKProviderConfig,
  AISDKModelInfo,
  ReasoningResult,
  MultimodalContent,
  AISDKGenerationOptions
} from './types';
import { apiLogger } from '@/lib/logger';
import { ModelRepository } from '@/db/repositories/model.repository';

/**
 * Abstract base class for AI SDK providers
 * 
 * This class provides a unified interface for all AI SDK providers,
 * integrating with the existing error handling and performance monitoring
 * while leveraging the AI SDK framework for model interactions.
 */
export abstract class AISdkProviderBase extends AIProvider {
  protected abstract provider: any; // AI SDK providers can be functions or callable objects
  public abstract providerName: string;

  constructor(config: AISDKProviderConfig = {}) {
    super(config);
  }

  /**
   * Generate a completion using AI SDK generateText
   */
  async generateCompletion(options: GenerationOptions): Promise<string> {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timer = apiLogger.time(`${this.providerName.toLowerCase()}-completion`);
    
    // Remove provider prefix from model ID if present - move outside try block
    // Special handling for OpenRouter models which have format: openrouter/provider/model
    const modelId = (() => {
      if (options.model.startsWith('openrouter/')) {
        // For OpenRouter, keep everything after "openrouter/"
        return options.model.substring('openrouter/'.length);
      } else if (options.model.includes('/')) {
        // For other providers, take everything after the first slash
        return options.model.split('/').slice(1).join('/');
      }
      return options.model;
    })();
    
    try {
      apiLogger.debug(`[${this.providerName}] Starting completion`, {
        model: options.model,
        requestId,
        messageCount: options.messages.length
      });
      
      console.log(`[${this.providerName}] generateCompletion called with:`, {
        originalModel: options.model,
        cleanedModelId: modelId,
        providerType: typeof this.provider,
        providerIsFunction: typeof this.provider === 'function',
        providerValue: this.provider?.toString?.().substring(0, 200) || 'no toString method'
      });
      
      // Safety check and provider model creation
      let model;
      
      // The AI SDK providers return callable objects (functions with properties)
      // We can safely call them as functions regardless of their typeof result
      try {
        model = (this.provider as any)(modelId);
        
        if (!model) {
          throw new Error(`Provider returned null/undefined for model: ${modelId}`);
        }
      } catch (e) {
        const error = e as Error;
        console.error(`[${this.providerName}] Failed to create model:`, {
          modelId,
          providerType: typeof this.provider,
          error: error.message,
          stack: error.stack
        });
        throw new Error(`[${this.providerName}] Failed to create model ${modelId}: ${error.message}`);
      }
      
      const result = await generateText({
        model: model,
        messages: this.convertMessages(options.messages),
        maxTokens: options.maxTokens,
        temperature: options.temperature,
        topP: options.topP,
        presencePenalty: options.presencePenalty,
        frequencyPenalty: options.frequencyPenalty,
        seed: options.metadata?.seed,
      });
      
      // Record successful performance
      const duration = Date.now() - startTime;
      this.recordPerformance(options.model, duration, true, options);
      
      timer();
      apiLogger.debug(`[${this.providerName}] Completion successful`, {
        requestId,
        duration,
        responseLength: result.text.length
      });
      
      return result.text;
      
    } catch (error: any) {
      const duration = Date.now() - startTime;
      this.recordPerformance(options.model, duration, false, options);
      
      timer();
      apiLogger.error(`[${this.providerName}] Completion failed`, {
        requestId,
        duration,
        error: error.message
      });
      
      throw this.handleError(error);
    }
  }

  /**
   * Generate a streaming response using AI SDK streamText
   */
  async *generateStream(options: GenerationOptions): AsyncGenerator<StreamChunk> {
    const startTime = Date.now();
    const requestId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timer = apiLogger.time(`${this.providerName.toLowerCase()}-stream`);
    
    // Remove provider prefix from model ID if present - move outside try block
    // Special handling for OpenRouter models which have format: openrouter/provider/model
    const modelId = (() => {
      if (options.model.startsWith('openrouter/')) {
        // For OpenRouter, keep everything after "openrouter/"
        return options.model.substring('openrouter/'.length);
      } else if (options.model.includes('/')) {
        // For other providers, take everything after the first slash
        return options.model.split('/').slice(1).join('/');
      }
      return options.model;
    })();
    
    try {
      apiLogger.debug(`[${this.providerName}] Starting stream`, {
        model: options.model,
        requestId,
        messageCount: options.messages.length
      });
      
      console.log(`[${this.providerName}] generateStream called with:`, {
        originalModel: options.model,
        cleanedModelId: modelId,
        providerType: typeof this.provider,
        providerIsFunction: typeof this.provider === 'function',
        providerValue: this.provider?.toString?.().substring(0, 200) || 'no toString method'
      });
      
      // Safety check and provider model creation
      let model;
      
      // The AI SDK providers return callable objects (functions with properties)
      // We can safely call them as functions regardless of their typeof result
      try {
        model = (this.provider as any)(modelId);
        
        if (!model) {
          throw new Error(`Provider returned null/undefined for model: ${modelId}`);
        }
      } catch (e) {
        const error = e as Error;
        console.error(`[${this.providerName}] Failed to create model:`, {
          modelId,
          providerType: typeof this.provider,
          error: error.message,
          stack: error.stack
        });
        throw new Error(`[${this.providerName}] Failed to create model ${modelId}: ${error.message}`);
      }
      
      const result = await streamText({
        model: model,
        messages: this.convertMessages(options.messages),
        maxTokens: options.maxTokens,
        temperature: options.temperature,
        topP: options.topP,
        presencePenalty: options.presencePenalty,
        frequencyPenalty: options.frequencyPenalty,
        seed: options.metadata?.seed,
      });

      let chunkCount = 0;
      let totalContent = '';
      
      for await (const chunk of result.textStream) {
        chunkCount++;
        totalContent += chunk;
        
        yield {
          type: 'content',
          content: chunk,
          finish_reason: undefined,
          role: 'assistant',
          provider: this.providerName,
          model: options.model,
        };
      }
      
      // Final chunk with finish reason
      yield {
        type: 'content',
        content: '',
        finish_reason: 'stop',
        role: 'assistant',
        provider: this.providerName,
        model: options.model,
      };

      // Record successful streaming performance
      const duration = Date.now() - startTime;
      this.recordPerformance(options.model, duration, true, options, chunkCount);
      
      timer();
      apiLogger.debug(`[${this.providerName}] Stream completed`, {
        requestId,
        duration,
        chunkCount,
        totalLength: totalContent.length
      });
      
    } catch (error: any) {
      // Record failed performance
      const duration = Date.now() - startTime;
      this.recordPerformance(options.model, duration, false, options);
      
      timer();
      
      // Enhanced error logging
      console.error(`[${this.providerName}] Stream failed - Full error:`, {
        errorType: error?.constructor?.name || 'Unknown',
        errorMessage: error?.message || 'No message',
        errorStack: error?.stack?.substring(0, 500) || 'No stack',
        errorKeys: error ? Object.keys(error) : [],
        errorStringified: error ? JSON.stringify(error, null, 2).substring(0, 500) : 'null',
        providerType: typeof this.provider,
        modelId
      });
      
      apiLogger.error(`[${this.providerName}] Stream failed`, {
        requestId,
        duration,
        error: error?.message || 'Unknown error',
        errorType: error?.constructor?.name || 'Unknown'
      });
      
      // Yield error chunk with better error info
      const errorMessage = error?.message || error?.toString() || 'Unknown error occurred';
      yield {
        type: 'error',
        error: new Error(`[${this.providerName}] ${errorMessage}`),
        provider: this.providerName,
        model: options.model,
      };
    }
  }

  /**
   * List models for this provider from the database
   * Providers can override this to add custom logic or filtering
   */
  async listModels(): Promise<any[]> { // Use any[] for compatibility with ModelInfo variations
    try {
      const dbModels = await ModelRepository.listByProvider(this.providerName);
      
      return dbModels.map(dbModel => ({
        id: dbModel.canonicalName.replace(`${this.providerName}/`, ''), // Remove provider prefix for AI SDK
        name: dbModel.displayName,
        provider: this.providerName,
        description: (dbModel as any).description || dbModel.displayName || '',
        contextWindow: dbModel.contextWindow,
        maxOutputTokens: dbModel.maxOutput,
        inputCost: dbModel.inputCostPer1M,
        outputCost: dbModel.outputCostPer1M,
        capabilities: this.extractCapabilities(dbModel),
        supportsStreaming: dbModel.supportsStreaming,
        supportsImages: dbModel.supportsVision,
        supportsFunctions: dbModel.supportsFunctionCalling,
        supportsSystemMessages: true, // Default to true for most models
        providerModelId: dbModel.canonicalName, // Keep full canonical name
        tier: this.determineTier(dbModel)
      }));
    } catch (error) {
      apiLogger.error(`Failed to list models for ${this.providerName}`, error);
      return []; // Return empty array as fallback
    }
  }

  // Abstract method that each provider must implement
  abstract validateConfig(): Promise<boolean>;

  /**
   * Extract capabilities from database model
   */
  protected extractCapabilities(dbModel: any): string[] {
    const capabilities: string[] = ['chat']; // Default capability
    
    if (dbModel.capabilities && typeof dbModel.capabilities === 'object') {
      // Extract from JSON capabilities field
      const caps = dbModel.capabilities as any;
      if (caps.reasoning) capabilities.push('reasoning');
      if (caps.code) capabilities.push('code');
      if (caps.analysis) capabilities.push('analysis');
      if (caps.multimodal) capabilities.push('multimodal');
      if (caps.multilingual) capabilities.push('multilingual');
      if (caps.functions) capabilities.push('functions');
      if (caps.web_search) capabilities.push('web_search');
      if (caps.citations) capabilities.push('citations');
      if (caps.creative) capabilities.push('creative');
    }
    
    // Add based on support flags
    if (dbModel.supportsVision) capabilities.push('vision');
    if (dbModel.supportsAudio) capabilities.push('audio');
    if (dbModel.supportsFunctions) capabilities.push('functions');
    
    return [...new Set(capabilities)]; // Remove duplicates
  }

  /**
   * Determine model tier based on cost and capabilities
   */
  protected determineTier(dbModel: any): 'premium' | 'balanced' | 'fast' | 'reasoning' {
    // Check for reasoning models
    if (dbModel.capabilities?.reasoning || 
        dbModel.canonicalName.includes('o1') || 
        dbModel.canonicalName.includes('reasoning') ||
        dbModel.canonicalName.includes('r1') ||
        dbModel.canonicalName.includes('qwq')) {
      return 'reasoning';
    }
    
    // Determine by cost
    const totalCost = (dbModel.inputCostPer1M || 0) + (dbModel.outputCostPer1M || 0);
    
    if (totalCost > 10) return 'premium';
    if (totalCost > 1) return 'balanced';
    return 'fast';
  }

  /**
   * Convert messages to AI SDK format
   * Handles different message formats and ensures compatibility
   */
  protected convertMessages(messages: any[]): any[] {
    return messages.map(msg => {
      // Handle different message formats
      if (typeof msg.content === 'string') {
        return {
          role: msg.role,
          content: msg.content
        };
      } else if (Array.isArray(msg.content)) {
        // Handle multimodal content
        return {
          role: msg.role,
          content: msg.content
        };
      } else {
        return msg;
      }
    });
  }

  /**
   * Record performance metrics for monitoring and analytics
   */
  private recordPerformance(
    model: string,
    duration: number,
    success: boolean,
    options: GenerationOptions,
    chunkCount?: number
  ) {
    // Extract provider from model name or use provider name
    const provider = this.extractProviderFromModel(model) || this.providerName;
    
    // Estimate cost and tokens
    const estimatedTokens = this.estimateTokens(options);
    const estimatedCost = this.estimateCost(provider, model, estimatedTokens);
    
    // Log performance data for monitoring
    apiLogger.info(`[${provider}] Performance recorded`, {
      model,
      duration,
      success,
      estimatedCost,
      estimatedTokens,
      chunkCount,
      operation: chunkCount ? 'stream' : 'completion'
    });
  }

  /**
   * Extract provider name from model identifier
   */
  private extractProviderFromModel(model: string): string | null {
    if (model.includes('/')) {
      return model.split('/')[0];
    }
    return null;
  }

  /**
   * Estimate token count for cost calculation
   * Basic approximation - providers can override with more accurate counting
   */
  private estimateTokens(options: GenerationOptions): number {
    const messageText = options.messages
      .map(m => typeof m.content === 'string' ? m.content : JSON.stringify(m.content))
      .join(' ');
    return Math.ceil(messageText.length / 4) + (options.maxTokens || 100);
  }

  /**
   * Estimate cost based on provider and model
   * Simplified cost estimation - can be enhanced with real pricing data
   */
  private estimateCost(provider: string, model: string, tokens: number): number {
    // Cost per token mapping (approximate)
    const costPerToken: { [key: string]: number } = {
      'openai': 0.00002,
      'anthropic': 0.00003,
      'google': 0.000001,
      'groq': 0.000001,
      'mistral': 0.000007,
      'deepseek': 0.000002,
      'xai': 0.000005,
      'together': 0.000002,
      'perplexity': 0.000005,
      'cohere': 0.000015,
      'openrouter': 0.00001,
      'qwen': 0.000001
    };
    
    return tokens * (costPerToken[provider.toLowerCase()] || 0.00001);
  }

  /**
   * Enhanced error handling with provider-specific context
   */
  protected handleError(error: any): Error {
    const baseError = super.handleError(error);
    
    // Add AI SDK specific error handling
    if (error.name === 'AI_InvalidArgumentError') {
      return new Error(`Invalid model configuration: ${error.message}`);
    }
    
    if (error.name === 'AI_APICallError') {
      return new Error(`API call failed: ${error.message}`);
    }
    
    if (error.name === 'AI_RetryError') {
      return new Error(`Request failed after retries: ${error.message}`);
    }
    
    return baseError;
  }
}