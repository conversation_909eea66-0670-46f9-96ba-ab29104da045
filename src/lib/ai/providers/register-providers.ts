/**
 * AI SDK Provider Registration
 * 
 * Improved registration system with better patterns:
 * - Centralized provider definitions
 * - Lazy loading support
 * - Better error handling
 * - Type safety
 * - DRY principles
 */

import { ProviderRegistry, AISDKProviderRegistration } from './registry';
import { 
  AI_SDK_PROVIDERS, 
  AISDKProviderName,
  createAISDKProvider 
} from './index';
import { 
  PROVIDER_DEFINITIONS, 
  validateProviderDefinitions,
  ProviderMetadata 
} from './provider-definitions';
import { apiLogger } from '@/lib/logger';

// Registry singleton
const registry = ProviderRegistry.getInstance();

// Cache for created registrations
const registrationCache = new Map<AISDKProviderName, AISDKProviderRegistration>();

/**
 * Creates an AI SDK provider registration from metadata
 * Implements factory pattern with validation
 */
function createProviderRegistration(
  providerId: AISDKProviderName,
  metadata: ProviderMetadata
): AISDKProviderRegistration {
  // Check cache first
  if (registrationCache.has(providerId)) {
    return registrationCache.get(providerId)!;
  }

  // Validate metadata
  if (!metadata || !metadata.name) {
    throw new Error(`Invalid metadata for provider: ${providerId}`);
  }

  // Create registration with lazy factory
  const registration: AISDKProviderRegistration = {
    id: providerId,
    name: metadata.name,
    description: metadata.description,
    factory: (config) => {
      try {
        // Create provider instance with config validation
        const provider = createAISDKProvider(providerId, config);
        
        // Log successful creation
        apiLogger.info(`Created AI SDK provider instance: ${providerId}`, {
          provider: providerId,
          hasConfig: !!config,
          configKeys: config ? Object.keys(config) : []
        });
        
        return provider;
      } catch (error) {
        apiLogger.error(`Failed to create provider ${providerId}`, {
          provider: providerId,
          error: error instanceof Error ? error.message : String(error)
        });
        throw error;
      }
    },
    configKeys: metadata.configKeys,
    defaultModels: metadata.defaultModels,
    capabilities: metadata.capabilities,
    aiSdkPackage: metadata.aiSdkPackage,
    isAISDK: true
  };

  // Cache the registration
  registrationCache.set(providerId, registration);
  
  return registration;
}

/**
 * Registers a single AI SDK provider with error handling
 */
function registerProvider(providerId: AISDKProviderName): boolean {
  try {
    const metadata = PROVIDER_DEFINITIONS[providerId];
    if (!metadata) {
      throw new Error(`No metadata found for provider: ${providerId}`);
    }

    const registration = createProviderRegistration(providerId, metadata);
    registry.registerAISDK(registration);
    
    return true;
  } catch (error) {
    apiLogger.error(`Failed to register provider ${providerId}`, {
      provider: providerId,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Registers all AI SDK providers with detailed logging
 * Returns statistics about the registration process
 */
export function registerAISDKProviders(): {
  total: number;
  successful: number;
  failed: string[];
} {
  // Validate definitions first
  try {
    validateProviderDefinitions();
  } catch (error) {
    apiLogger.error('Provider definitions validation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }

  const results = {
    total: 0,
    successful: 0,
    failed: [] as string[]
  };

  // Register each provider
  for (const providerId of Object.values(AI_SDK_PROVIDERS)) {
    results.total++;
    
    if (registerProvider(providerId)) {
      results.successful++;
    } else {
      results.failed.push(providerId);
    }
  }

  // Log summary
  apiLogger.info('AI SDK provider registration complete', {
    total: results.total,
    successful: results.successful,
    failed: results.failed.length,
    failedProviders: results.failed
  });

  return results;
}

/**
 * Registers a subset of providers (useful for testing or selective loading)
 */
export function registerProviders(providerIds: AISDKProviderName[]): {
  successful: string[];
  failed: string[];
} {
  const results = {
    successful: [] as string[],
    failed: [] as string[]
  };

  for (const providerId of providerIds) {
    if (registerProvider(providerId)) {
      results.successful.push(providerId);
    } else {
      results.failed.push(providerId);
    }
  }

  return results;
}

/**
 * Unregisters all AI SDK providers (useful for testing)
 */
export function unregisterAllProviders(): void {
  registrationCache.clear();
  // Note: ProviderRegistry doesn't have an unregister method
  // This would need to be added if full cleanup is required
}

/**
 * Gets registration for a specific provider
 */
export function getProviderRegistration(providerId: AISDKProviderName): AISDKProviderRegistration | null {
  try {
    const metadata = PROVIDER_DEFINITIONS[providerId];
    if (!metadata) {
      return null;
    }
    
    return createProviderRegistration(providerId, metadata);
  } catch (error) {
    apiLogger.error(`Failed to get registration for ${providerId}`, {
      provider: providerId,
      error: error instanceof Error ? error.message : String(error)
    });
    return null;
  }
}

/**
 * Checks if all required environment variables are set
 */
export function checkProviderEnvironment(): {
  provider: string;
  envKey: string;
  isSet: boolean;
}[] {
  const results = [];
  
  for (const [providerId, metadata] of Object.entries(PROVIDER_DEFINITIONS)) {
    if (metadata.envKeyName) {
      results.push({
        provider: providerId,
        envKey: metadata.envKeyName,
        isSet: !!process.env[metadata.envKeyName]
      });
    }
  }
  
  return results;
}

// Export registrations for testing
export const AI_SDK_REGISTRATIONS = new Proxy({} as Record<AISDKProviderName, AISDKProviderRegistration>, {
  get(target, prop: string) {
    return getProviderRegistration(prop as AISDKProviderName);
  }
});

// Auto-register providers if not in test environment
if (process.env.NODE_ENV !== 'test') {
  try {
    registerAISDKProviders();
  } catch (error) {
    apiLogger.error('Failed to auto-register AI SDK providers', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}