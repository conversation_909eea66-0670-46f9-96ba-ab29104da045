import { z } from 'zod';
import { <PERSON>Provider, ModelInfo, StreamChunk } from './types';
import { apiLogger } from '@/lib/logger';

interface OpenRouterModel {
  id: string;
  name: string;
  description?: string;
  context_length: number;
  pricing: {
    prompt: string;
    completion: string;
  };
}

export class OpenRouterProvider extends AIProvider {
  providerName = 'OpenRouterProvider';
  private apiKey: string;
  private baseUrl = 'https://openrouter.ai/api/v1';
  private modelPrefix: string;

  constructor(modelPrefix: string = '') {
    super({});
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      throw new Error('OPENROUTER_API_KEY environment variable is not set');
    }
    this.apiKey = apiKey;
    this.modelPrefix = modelPrefix;
  }

  async generateCompletion(options: any): Promise<string> {
    const openRouterModelId = this.mapModelId(options.model);
    
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://justsimple.chat',
        'X-Title': 'JustSimpleChat'
      },
      body: JSON.stringify({
        model: openRouterModelId,
        messages: options.messages,
        temperature: options.temperature ?? 0.7,
        max_tokens: options.maxTokens,
        top_p: options.topP,
        frequency_penalty: options.frequencyPenalty,
        presence_penalty: options.presencePenalty,
        stop: options.stopSequences,
        stream: false,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} ${error}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '';
  }

  async *generateStream(params: {
    model: string;
    messages: Array<{ role: string; content: string }>;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    stopSequences?: string[];
  }): AsyncGenerator<StreamChunk> {
    try {
      // Map our model IDs to OpenRouter model IDs
      const openRouterModelId = this.mapModelId(params.model);
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: openRouterModelId,
          messages: params.messages,
          temperature: params.temperature ?? 0.7,
          max_tokens: params.maxTokens,
          top_p: params.topP,
          frequency_penalty: params.frequencyPenalty,
          presence_penalty: params.presencePenalty,
          stop: params.stopSequences,
          stream: true,
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} ${error}`);
      }

      if (!response.body) {
        throw new Error('Response body is null');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              return;
            }

            try {
              const json = JSON.parse(data);
              const content = json.choices?.[0]?.delta?.content;
              if (content) {
                yield { content };
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      apiLogger.error('OpenRouter stream error:', error);
      throw error;
    }
  }

  async listModels(): Promise<ModelInfo[]> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to list models: ${response.status}`);
      }

      const data = await response.json();
      const models = data.data as OpenRouterModel[];

      // Filter models based on prefix if set
      const filteredModels = this.modelPrefix
        ? models.filter(m => m.id.includes(this.modelPrefix))
        : models;

      return filteredModels.map(model => ({
        id: this.reverseMapModelId(model.id),
        name: model.name,
        provider: 'openrouter',
        contextWindow: model.context_length,
        maxOutput: Math.min(4096, model.context_length),
        inputCost: parseFloat(model.pricing.prompt) * 1000, // Convert to per 1k tokens
        outputCost: parseFloat(model.pricing.completion) * 1000, // Convert to per 1k tokens
        capabilities: ['chat'],
        description: model.description,
        supportsStreaming: true,
      }));
    } catch (error) {
      apiLogger.error('Failed to list OpenRouter models:', error);
      return [];
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private mapModelId(modelId: string): string {
    // Map our model IDs to OpenRouter model IDs
    const modelMap: Record<string, string> = {
      // DeepSeek models
      'deepseek-v3': 'deepseek/deepseek-chat',
      'deepseek-r1': 'deepseek/deepseek-r1',
      'deepseek-r1-llama-distilled': 'deepseek/deepseek-r1-distill-llama-70b',
      'deepseek-coder-v2': 'deepseek/deepseek-coder',
      
      // Groq models (via Llama)
      'groq-3': 'meta-llama/llama-3-70b-instruct',
      'groq-3-mini': 'meta-llama/llama-3-8b-instruct',
      'llama-3.3-70b': 'meta-llama/llama-3.3-70b-instruct',
      'llama-4-scout': 'meta-llama/llama-3-70b-instruct',
      'llama-4-maverick': 'meta-llama/llama-3.1-405b-instruct',
      
      // Mistral models
      'mistral-large': 'mistralai/mistral-large',
      'mixtral-8x7b': 'mistralai/mixtral-8x7b-instruct',
      
      // Perplexity models
      'perplexity-sonar': 'perplexity/sonar-medium-chat',
      
      // Command R
      'command-r-plus': 'cohere/command-r-plus',
      
      // Qwen models
      'gwen-qwen2-35': 'qwen/qwen-2-72b-instruct',
      'gwen-qwq-32b': 'qwen/qwq-32b-preview',
    };

    return modelMap[modelId] || modelId;
  }

  private reverseMapModelId(openRouterModelId: string): string {
    // Reverse map for listing models
    const reverseMap: Record<string, string> = {
      'deepseek/deepseek-chat': 'deepseek-v3',
      'deepseek/deepseek-r1': 'deepseek-r1',
      'deepseek/deepseek-r1-distill-llama-70b': 'deepseek-r1-llama-distilled',
      'deepseek/deepseek-coder': 'deepseek-coder-v2',
      'meta-llama/llama-3-70b-instruct': 'llama-3.3-70b',
      'meta-llama/llama-3-8b-instruct': 'groq-3-mini',
      'meta-llama/llama-3.1-405b-instruct': 'llama-4-maverick',
      'mistralai/mistral-large': 'mistral-large',
      'mistralai/mixtral-8x7b-instruct': 'mixtral-8x7b',
      'perplexity/sonar-medium-chat': 'perplexity-sonar',
      'cohere/command-r-plus': 'command-r-plus',
      'qwen/qwen-2-72b-instruct': 'gwen-qwen2-35',
      'qwen/qwq-32b-preview': 'gwen-qwq-32b',
    };

    return reverseMap[openRouterModelId] || openRouterModelId;
  }
}

// Export specific providers for different model families
export class DeepSeekProvider extends OpenRouterProvider {
  providerName = 'DeepSeekProvider';
  constructor() {
    super('deepseek');
  }
}

export class GroqProvider extends OpenRouterProvider {
  providerName = 'GroqProvider';
  constructor() {
    super('llama');
  }
}

export class PerplexityProvider extends OpenRouterProvider {
  providerName = 'PerplexityProvider';
  constructor() {
    super('perplexity');
  }
}

export class QwenProvider extends OpenRouterProvider {
  providerName = 'QwenProvider';
  constructor() {
    super('qwen');
  }
}