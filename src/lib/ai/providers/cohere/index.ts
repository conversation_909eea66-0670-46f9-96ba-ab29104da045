import { CohereClient } from 'cohere-ai';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig } from '../types';

/**
 * Cohere Provider using Official Cohere SDK
 * 
 * Implements Cohere's Command R series models with support for
 * multilingual capabilities, RAG, and structured output
 * 
 * Uses the official cohere-ai npm package with streaming via chatStream
 * Models are fetched from database via ModelRepository
 */
export class CohereAISDK<PERSON>rovider extends AISdkProviderBase {
  public providerName = 'cohere';
  protected provider: any;
  private cohereClient: CohereClient;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Cohere] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.COHERE_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Initialize the official Cohere client
    this.cohereClient = new CohereClient({
      token: config.apiKey || process.env.COHERE_API_KEY || '',
      // Cohere SDK doesn't have a direct baseURL option in constructor
      // Custom headers can be set via client options if needed
    });
    
    // Create a provider wrapper that matches AI SDK interface
    this.provider = this.createProviderWrapper();
    console.log('[Cohere] Created official Cohere client with provider wrapper');
  }
  
  private createProviderWrapper() {
    // Return a function that creates model instances compatible with AI SDK
    return (modelId: string) => {
      return {
        // For streamText compatibility
        doStream: async (params: any) => {
          try {
            // Convert AI SDK params to Cohere params
            const message = params.messages?.map((msg: any) => msg.content).join('\n\n');
            
            // Extract system message if present
            const systemMessage = params.messages?.find((msg: any) => msg.role === 'system')?.content;
            
            // Build chat history from messages
            const chatHistory = params.messages?.slice(0, -1).map((msg: any) => ({
              role: msg.role === 'assistant' ? 'CHATBOT' : 'USER',
              message: msg.content
            })) || [];
            
            // Use the official SDK's streaming API
            const stream = await this.cohereClient.chatStream({
              model: modelId,
              message: params.messages?.[params.messages.length - 1]?.content || message,
              preamble: systemMessage,
              chatHistory,
              temperature: params.temperature,
              maxTokens: params.maxTokens,
              p: params.topP,
              k: params.topK,
              stopSequences: params.stop,
              seed: params.seed,
            });
            
            // Convert Cohere stream to AI SDK compatible stream
            return {
              stream: this.convertStream(stream),
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Cohere] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack,
              statusCode: error.statusCode,
              body: error.body
            });
            throw error;
          }
        },
        
        // For generateText compatibility
        doGenerate: async (params: any) => {
          try {
            // Convert AI SDK params to Cohere params
            const message = params.messages?.map((msg: any) => msg.content).join('\n\n');
            
            // Extract system message if present
            const systemMessage = params.messages?.find((msg: any) => msg.role === 'system')?.content;
            
            // Build chat history from messages
            const chatHistory = params.messages?.slice(0, -1).map((msg: any) => ({
              role: msg.role === 'assistant' ? 'CHATBOT' : 'USER',
              message: msg.content
            })) || [];
            
            const response = await this.cohereClient.chat({
              model: modelId,
              message: params.messages?.[params.messages.length - 1]?.content || message,
              preamble: systemMessage,
              chatHistory,
              temperature: params.temperature,
              maxTokens: params.maxTokens,
              p: params.topP,
              k: params.topK,
              stopSequences: params.stop,
              seed: params.seed,
            });
            
            return {
              text: response.text || '',
              usage: response.meta?.tokens ? {
                promptTokens: response.meta.tokens.inputTokens || 0,
                completionTokens: response.meta.tokens.outputTokens || 0,
                totalTokens: (response.meta.tokens.inputTokens || 0) + (response.meta.tokens.outputTokens || 0)
              } : {
                promptTokens: 0,
                completionTokens: 0,
                totalTokens: 0
              },
              finishReason: response.finishReason || 'COMPLETE',
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Cohere] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack,
              statusCode: error.statusCode,
              body: error.body
            });
            throw error;
          }
        }
      };
    };
  }
  
  private async *convertStream(cohereStream: any): AsyncGenerator<any> {
    try {
      for await (const event of cohereStream) {
        // Handle text generation events
        if (event.eventType === 'text-generation') {
          yield {
            type: 'text-delta',
            textDelta: event.text
          };
        }
        
        // Handle stream end event
        if (event.eventType === 'stream-end') {
          const response = event.response;
          yield {
            type: 'finish',
            finishReason: response.finishReason || 'COMPLETE',
            usage: response.meta?.tokens ? {
              promptTokens: response.meta.tokens.inputTokens || 0,
              completionTokens: response.meta.tokens.outputTokens || 0,
              totalTokens: (response.meta.tokens.inputTokens || 0) + (response.meta.tokens.outputTokens || 0)
            } : undefined
          };
        }
      }
    } catch (error: any) {
      yield {
        type: 'error',
        error: {
          message: error.message,
          name: error.name,
          stack: error.stack,
          statusCode: error.statusCode,
          body: error.body
        }
      };
      throw error;
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.COHERE_API_KEY;
      if (!apiKey) {
        console.error('[Cohere] No API key found');
        return false;
      }
      
      // Test the configuration with a minimal API call
      try {
        // Check API key validity
        await this.cohereClient.checkApiKey();
        console.log('[Cohere] Configuration validated');
        return true;
      } catch (error: any) {
        console.error('[Cohere] API validation failed:', error.message);
        return false;
      }
    } catch (error) {
      console.error('Cohere configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default CohereAISDKProvider;