import { createQwen } from 'qwen-ai-provider';
import { AISdkProviderBase } from '../ai-sdk-base';
import { ModelInfo, AISDKProviderConfig } from '../types';
/**
 * Qwen Provider using AI SDK
 * 
 * Implements Alibaba's Qwen models including Qwen 2.5, QwQ reasoning,
 * and specialized models for code, math, and multimodal tasks
 * 
 * Models are fetched from database via ModelRepository
 */
export class QwenAISDKProvider extends AISdkProviderBase {
  public providerName = 'qwen';
  protected provider: any;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Qwen] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.QWEN_API_KEY || process.env.ALIBABA_API_KEY),
      baseUrl: config.baseUrl || 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1'
    });
    
    // Qwen provider only provides createQwen, no default instance
    this.provider = createQwen({
      apiKey: config.apiKey || process.env.QWEN_API_KEY || process.env.ALIBABA_API_KEY,
      baseURL: config.baseUrl || 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1', // International endpoint
      headers: config.headers,
      fetch: config.fetch as any,
    } as any);
    
    console.log('[Qwen] Created provider with configuration');
  }
  async validateConfig(): Promise<boolean> {
    try {
      // For now, just check if we have an API key
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.QWEN_API_KEY || process.env.ALIBABA_API_KEY;
      if (!apiKey) {
        console.error('[Qwen] No API key found');
        return false;
      }
      
      // Skip the actual API call for now to avoid the provider function issue
      console.log('[Qwen] Configuration validated (API key present)');
      return true;
    } catch (error) {
      console.error('Qwen configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default QwenAISDKProvider;
