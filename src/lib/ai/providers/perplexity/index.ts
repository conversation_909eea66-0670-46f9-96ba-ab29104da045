import { perplexity, createPerplexity } from '@ai-sdk/perplexity';
import { AISdkProviderBase } from '../ai-sdk-base';
import { ModelInfo, AISDKProviderConfig, NativeWebSearchOptions, WebSearchResult, StreamingWebSearchChunk } from '../types';
/**
 * Perplexity Provider using AI SDK
 * 
 * Implements Perplexity's search-enhanced models including Sonar Pro,
 * Sonar, and base models with real-time web search capabilities
 * 
 * Models are fetched from database via ModelRepository
 */
export class PerplexityAISDKProvider extends AISdkProviderBase {
  public providerName = 'perplexity';
  protected provider: any;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Perplexity] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.PERPLEXITY_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Use createPerplexity when we have custom configuration
    if (config.apiKey || config.baseUrl || config.headers || config.fetch) {
      const customProvider = createPerplexity({
        apiKey: config.apiKey || process.env.PERPLEXITY_API_KEY,
        baseURL: config.baseUrl,
        headers: config.headers,
        fetch: config.fetch as any
      });
      
      this.provider = customProvider;
      console.log('[Perplexity] Created custom provider with configuration');
    } else {
      // Use default perplexity provider instance
      this.provider = perplexity;
      console.log('[Perplexity] Using default Perplexity provider');
    }
  }
  async validateConfig(): Promise<boolean> {
    try {
      // For now, just check if we have an API key
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.PERPLEXITY_API_KEY;
      if (!apiKey) {
        console.error('[Perplexity] No API key found');
        return false;
      }

      // Skip the actual API call for now to avoid the provider function issue
      console.log('[Perplexity] Configuration validated (API key present)');
      return true;
    } catch (error) {
      console.error('Perplexity configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Native web search using official Perplexity API
   * Supports advanced search parameters and streaming with citations
   */
  async *streamChatWithWebSearch(
    model: string,
    messages: any[],
    options: {
      webSearch?: NativeWebSearchOptions;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): AsyncIterable<StreamingWebSearchChunk> {
    const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.PERPLEXITY_API_KEY;
    if (!apiKey) {
      throw new Error('[Perplexity] No API key found for native web search');
    }

    const payload: any = {
      model,
      messages,
      stream: options.stream !== false,
      temperature: options.temperature,
      max_tokens: options.maxTokens,
    };

    // Add native web search parameters
    if (options.webSearch) {
      const ws = options.webSearch;

      if (ws.searchDomainFilter) {
        payload.search_domain_filter = ws.searchDomainFilter;
      }

      if (ws.searchAfterDateFilter) {
        payload.search_after_date_filter = ws.searchAfterDateFilter;
      }

      if (ws.searchBeforeDateFilter) {
        payload.search_before_date_filter = ws.searchBeforeDateFilter;
      }

      if (ws.searchRecencyFilter) {
        payload.search_recency_filter = ws.searchRecencyFilter;
      }

      if (ws.searchMode) {
        payload.search_mode = ws.searchMode;
      }

      if (ws.returnImages) {
        payload.return_images = ws.returnImages;
      }

      if (ws.returnRelatedQuestions) {
        payload.return_related_questions = ws.returnRelatedQuestions;
      }

      if (ws.webSearchOptions) {
        payload.web_search_options = {};

        if (ws.webSearchOptions.searchContextSize) {
          payload.web_search_options.search_context_size = ws.webSearchOptions.searchContextSize;
        }

        if (ws.webSearchOptions.userLocation) {
          payload.web_search_options.user_location = ws.webSearchOptions.userLocation;
        }
      }

      if (ws.imageDomainFilter) {
        payload.image_domain_filter = ws.imageDomainFilter;
      }

      if (ws.imageFormatFilter) {
        payload.image_format_filter = ws.imageFormatFilter;
      }
    }

    console.log('[Perplexity] Making native web search request:', {
      model,
      hasWebSearch: !!options.webSearch,
      payload: JSON.stringify(payload, null, 2)
    });

    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Perplexity API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      if (!payload.stream) {
        // Non-streaming response
        const result = await response.json();

        // Emit search results if available
        if (result.search_results) {
          for (const searchResult of result.search_results) {
            yield {
              type: 'search_result',
              searchResult: {
                title: searchResult.title,
                url: searchResult.url,
                date: searchResult.date,
              }
            };
          }
        }

        // Emit citations if available
        if (result.citations) {
          for (const citation of result.citations) {
            yield {
              type: 'citation',
              citation
            };
          }
        }

        // Emit content
        if (result.choices?.[0]?.message?.content) {
          yield {
            type: 'content',
            content: result.choices[0].message.content
          };
        }

        yield { type: 'search_complete' };
        return;
      }

      // Streaming response
      const reader = response.body!.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      yield { type: 'search_start' };

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const dataStr = line.slice(6);
              if (dataStr === '[DONE]') {
                yield { type: 'search_complete' };
                return;
              }

              try {
                const data = JSON.parse(dataStr);

                // Handle search results
                if (data.search_results) {
                  for (const searchResult of data.search_results) {
                    yield {
                      type: 'search_result',
                      searchResult: {
                        title: searchResult.title,
                        url: searchResult.url,
                        date: searchResult.date,
                      }
                    };
                  }
                }

                // Handle citations
                if (data.citations) {
                  for (const citation of data.citations) {
                    yield {
                      type: 'citation',
                      citation
                    };
                  }
                }

                // Handle content chunks
                if (data.choices?.[0]?.delta?.content) {
                  yield {
                    type: 'content',
                    content: data.choices[0].delta.content
                  };
                }

              } catch (e) {
                console.error('[Perplexity] Failed to parse SSE data:', e);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      console.error('[Perplexity] Native web search error:', error);
      throw error;
    }
  }
}

// Export as default and named export for flexibility
export default PerplexityAISDKProvider;
