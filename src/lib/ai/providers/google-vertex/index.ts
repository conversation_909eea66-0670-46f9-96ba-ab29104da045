import { Vertex<PERSON><PERSON>, HarmBlockThreshold, HarmCategory } from '@google-cloud/vertexai';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig } from '../types';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Google Vertex AI Provider using official Google Cloud SDK
 * 
 * Implements Google Vertex AI models including Gemini models
 * with support for authentication via service account credentials
 * 
 * Models are fetched from database via ModelRepository
 */
export class GoogleVertexAISDKProvider extends AISdkProviderBase {
  protected provider: any;
  private vertexAI: VertexAI;
  public providerName = 'google-vertex';
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    // Ensure GOOGLE_APPLICATION_CREDENTIALS is set
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS || 
                           path.join(process.cwd(), 'google-key.json');
    
    if (!process.env.GOOGLE_APPLICATION_CREDENTIALS && fs.existsSync(credentialsPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;
      console.log('[Google Vertex] Set GOOGLE_APPLICATION_CREDENTIALS to:', credentialsPath);
    }
    
    const project = config.projectId || 
                   process.env.GOOGLE_VERTEX_PROJECT || 
                   process.env.GCLOUD_PROJECT ||
                   'yorkshire3d'; // From google-key.json
                   
    const location = config.location || 
                    process.env.GOOGLE_VERTEX_LOCATION || 
                    process.env.VERTEX_AI_REGION || 
                    'us-central1';
    
    console.log('[Google Vertex] Initializing provider with config:', { 
      project,
      location,
      credentialsPath: process.env.GOOGLE_APPLICATION_CREDENTIALS
    });
    
    // Initialize Vertex AI client
    this.vertexAI = new VertexAI({ project, location });
    
    // Create a wrapper to match AI SDK interface
    this.provider = (modelId: string) => {
      const model = this.vertexAI.getGenerativeModel({
        model: modelId,
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
          }
        ],
        generationConfig: {
          maxOutputTokens: 8192,
          temperature: 0.7,
          topP: 0.95,
          topK: 40
        }
      });
      
      // Return an AI SDK compatible wrapper
      return {
        // For text generation
        async generateText(params: any) {
          const result = await model.generateContent({
            contents: [{
              role: 'user',
              parts: [{ text: params.prompt || params.messages?.[0]?.content || '' }]
            }]
          });
          
          const response = result.response;
          return {
            text: response.candidates?.[0]?.content?.parts?.[0]?.text || '',
            usage: {
              promptTokens: response.usageMetadata?.promptTokenCount || 0,
              completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
              totalTokens: response.usageMetadata?.totalTokenCount || 0
            }
          };
        },
        
        // For streaming
        async *streamText(params: any) {
          const streamingResult = await model.generateContentStream({
            contents: [{
              role: 'user',
              parts: [{ text: params.prompt || params.messages?.[0]?.content || '' }]
            }]
          });
          
          for await (const chunk of streamingResult.stream) {
            const text = chunk.candidates?.[0]?.content?.parts?.[0]?.text || '';
            if (text) {
              yield { text };
            }
          }
        }
      };
    };
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Check if we have either credentials or environment setup
      const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS || 
                             path.join(process.cwd(), 'google-key.json');
      const hasCredentials = fs.existsSync(credentialsPath);
      const hasProject = !!(process.env.GOOGLE_VERTEX_PROJECT || process.env.GCLOUD_PROJECT);
      
      if (!hasCredentials && !hasProject) {
        console.error('[Google Vertex] No credentials or project configuration found');
        return false;
      }
      
      console.log('[Google Vertex] Configuration validated');
      return true;
    } catch (error) {
      console.error('Google Vertex configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default GoogleVertexAISDKProvider;