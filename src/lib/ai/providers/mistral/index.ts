import { Mistral } from '@mistralai/mistralai';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig } from '../types';

/**
 * Mistral Provider using Official Mistral SDK
 * 
 * Implements Mistral models including Mistral Large, Codestral, Mixtral,
 * and Ministral series with support for streaming and function calling
 * 
 * Uses the official @mistralai/mistralai npm package with streaming via chat.stream
 * Models are fetched from database via ModelRepository
 */
export class MistralAISDKProvider extends AISdkProviderBase {
  public providerName = 'mistral';
  protected provider: any;
  private mistralClient: Mistral;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Mistral] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.MISTRAL_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Initialize the official Mistral client
    this.mistralClient = new Mistral({
      apiKey: config.apiKey || process.env.MISTRAL_API_KEY || '',
      // The SDK uses serverURL instead of baseURL
      serverURL: config.baseUrl,
      // Custom headers can be passed via httpClient if needed
    });
    
    // Create a provider wrapper that matches AI SDK interface
    this.provider = this.createProviderWrapper();
    console.log('[Mistral] Created official Mistral client with provider wrapper');
  }
  
  private createProviderWrapper() {
    // Return a function that creates model instances compatible with AI SDK
    return (modelId: string) => {
      return {
        // For streamText compatibility
        doStream: async (params: any) => {
          try {
            // Convert AI SDK params to Mistral params
            const messages = params.messages?.map((msg: any) => ({
              role: msg.role,
              content: msg.content
            }));
            
            // Use the official SDK's streaming API
            const result = await this.mistralClient.chat.stream({
              model: modelId,
              messages,
              temperature: params.temperature,
              maxTokens: params.maxTokens,
              topP: params.topP,
              stop: params.stop,
              randomSeed: params.seed,
            });
            
            // Convert Mistral stream to AI SDK compatible stream
            return {
              stream: this.convertStream(result),
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Mistral] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack
            });
            throw error;
          }
        },
        
        // For generateText compatibility
        doGenerate: async (params: any) => {
          try {
            const messages = params.messages?.map((msg: any) => ({
              role: msg.role,
              content: msg.content
            }));
            
            const result = await this.mistralClient.chat.complete({
              model: modelId,
              messages,
              temperature: params.temperature,
              maxTokens: params.maxTokens,
              topP: params.topP,
              stop: params.stop,
              randomSeed: params.seed,
            });
            
            const choice = result.choices?.[0];
            const message = choice?.message;
            
            return {
              text: message?.content || '',
              usage: result.usage ? {
                promptTokens: result.usage.promptTokens || 0,
                completionTokens: result.usage.completionTokens || 0,
                totalTokens: result.usage.totalTokens || 0
              } : {
                promptTokens: 0,
                completionTokens: 0,
                totalTokens: 0
              },
              finishReason: choice?.finishReason || 'stop',
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Mistral] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack
            });
            throw error;
          }
        }
      };
    };
  }
  
  private async *convertStream(mistralStream: any): AsyncGenerator<any> {
    try {
      for await (const event of mistralStream) {
        // Handle different event types from Mistral stream
        if (event.data?.choices?.[0]?.delta?.content) {
          yield {
            type: 'text-delta',
            textDelta: event.data.choices[0].delta.content
          };
        }
        
        // Handle finish event
        if (event.data?.choices?.[0]?.finishReason) {
          yield {
            type: 'finish',
            finishReason: event.data.choices[0].finishReason,
            usage: event.data.usage ? {
              promptTokens: event.data.usage.promptTokens || 0,
              completionTokens: event.data.usage.completionTokens || 0,
              totalTokens: event.data.usage.totalTokens || 0
            } : undefined
          };
        }
      }
    } catch (error: any) {
      yield {
        type: 'error',
        error: {
          message: error.message,
          name: error.name,
          stack: error.stack
        }
      };
      throw error;
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.MISTRAL_API_KEY;
      if (!apiKey) {
        console.error('[Mistral] No API key found');
        return false;
      }
      
      // Test the configuration with a minimal API call
      try {
        // List available models to validate API connection
        const models = await this.mistralClient.models.list();
        console.log('[Mistral] Configuration validated, found', models.data?.length || 0, 'models');
        return true;
      } catch (error: any) {
        console.error('[Mistral] API validation failed:', error.message);
        return false;
      }
    } catch (error) {
      console.error('Mistral configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default MistralAISDKProvider;