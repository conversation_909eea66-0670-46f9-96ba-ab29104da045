# 🚀 Provider-Specific Caching Implementation Guide

## Overview
This guide details the implementation of native caching for all major AI providers to achieve 75-90% cost reduction on repeated prompts.

## 📊 Current State
- ✅ Ultra-fast router with Redis caching (11ms routing)
- ✅ Google context caching research completed
- ✅ CachedUltraFastRouter implemented for system prompts
- ❌ Provider-specific caching not yet implemented
- ❌ No unified caching interface

## 🏗️ Implementation Plan by Provider

### 1. Google Vertex AI / Gemini Context Caching

#### Implementation Details
```typescript
// src/lib/ai/providers/google/cached-google-provider.ts
import { GoogleGenerativeAI } from '@google/generative-ai';
import { GoogleGenAI, CachedContent } from '@google/genai';

export class CachedGoogleProvider extends GoogleAISDKProvider {
  private googleGenAI: GoogleGenAI;
  private cacheRegistry: Map<string, CachedContent> = new Map();
  
  async createCache(config: {
    systemPrompt?: string;
    documents?: Array<{ uri: string; mimeType: string }>;
    ttlSeconds?: number;
    modelId: string;
  }): Promise<string> {
    const cache = await this.googleGenAI.caches.create({
      model: config.modelId,
      config: {
        contents: this.buildCacheContents(config),
        displayName: `Cache-${Date.now()}`,
        ttl: `${config.ttlSeconds || 3600}s`
      }
    });
    
    this.cacheRegistry.set(cache.name, cache);
    return cache.name;
  }
  
  async generateWithCache(cacheId: string, userPrompt: string) {
    const model = this.googleGenAI.models.generateContent({
      model: this.modelId,
      cachedContent: cacheId,
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }]
    });
    return model;
  }
}
```

#### Key Features
- **Cost**: 75% reduction on cached tokens, 25% premium on cache writes
- **TTL**: Default 1 hour, max 30 days
- **Min tokens**: 32K for Gemini 1.5, 1K for Gemini 2.5 Flash
- **Use cases**: System prompts, document analysis, video processing

### 2. Anthropic Claude Prompt Caching

#### Implementation Details
```typescript
// src/lib/ai/providers/anthropic/cached-anthropic-provider.ts
export class CachedAnthropicProvider extends AnthropicAISDKProvider {
  private cacheEnabled = true;
  
  protected getHeaders(): Record<string, string> {
    return {
      ...super.getHeaders(),
      'anthropic-beta': 'prompt-caching-2024-07-31'
    };
  }
  
  async doStream(params: any): Promise<any> {
    // Add cache_control to system messages
    if (params.messages && params.messages.length > 0) {
      params.messages = this.addCacheControl(params.messages);
    }
    
    const response = await super.doStream(params);
    
    // Track cache usage
    if (response.usage) {
      this.trackCacheUsage({
        cacheCreationTokens: response.usage.cache_creation_input_tokens || 0,
        cacheReadTokens: response.usage.cache_read_input_tokens || 0
      });
    }
    
    return response;
  }
  
  private addCacheControl(messages: Message[]): Message[] {
    return messages.map((msg, index) => {
      // Cache system messages and large context
      if (msg.role === 'system' || 
          (msg.content && msg.content.length > 1024)) {
        return {
          ...msg,
          cache_control: { type: 'ephemeral' }
        };
      }
      return msg;
    });
  }
}
```

#### Key Features
- **Cost**: 90% reduction on cache reads, 25% premium on writes
- **TTL**: Fixed 5 minutes
- **Min tokens**: 1024 tokens
- **Auto-caching**: Content fingerprinting for automatic reuse

### 3. OpenAI Caching Strategy

#### Implementation Details
```typescript
// src/lib/ai/providers/openai/cached-openai-provider.ts
export class CachedOpenAIProvider extends OpenAISDKProvider {
  private assistantCache: Map<string, string> = new Map();
  
  async createCachedAssistant(config: {
    systemPrompt: string;
    model: string;
    tools?: any[];
  }): Promise<string> {
    // Use Assistants API for persistent context
    const assistant = await this.openai.beta.assistants.create({
      instructions: config.systemPrompt,
      model: config.model,
      tools: config.tools || []
    });
    
    this.assistantCache.set(assistant.id, config.systemPrompt);
    return assistant.id;
  }
  
  async generateWithAssistant(
    assistantId: string, 
    userMessage: string
  ): Promise<any> {
    const thread = await this.openai.beta.threads.create();
    
    await this.openai.beta.threads.messages.create(thread.id, {
      role: 'user',
      content: userMessage
    });
    
    const run = await this.openai.beta.threads.runs.create(thread.id, {
      assistant_id: assistantId
    });
    
    // Wait for completion and return response
    return this.waitForCompletion(thread.id, run.id);
  }
}
```

#### Key Features
- **Strategy**: Use Assistants API for persistent contexts
- **Cost**: Standard rates but avoids resending system prompts
- **Persistence**: Contexts stored server-side indefinitely
- **Alternative**: Client-side prompt caching with Redis

### 4. Other Providers - Redis Caching Fallback

#### Implementation Details
```typescript
// src/lib/ai/providers/caching/universal-cache-manager.ts
export class UniversalCacheManager {
  private redis: Redis;
  private providers: Map<string, CacheableProvider>;
  
  async cacheSystemPrompt(config: {
    provider: string;
    modelId: string;
    systemPrompt: string;
    ttlSeconds?: number;
  }): Promise<string> {
    const provider = this.providers.get(config.provider);
    
    if (provider?.supportsCaching()) {
      // Use native caching
      return provider.createCache(config);
    }
    
    // Fallback to Redis caching
    const cacheKey = `prompt:${config.provider}:${crypto.randomUUID()}`;
    await this.redis.setex(
      cacheKey,
      config.ttlSeconds || 3600,
      JSON.stringify({
        systemPrompt: config.systemPrompt,
        modelId: config.modelId,
        createdAt: new Date().toISOString()
      })
    );
    
    return cacheKey;
  }
}
```

## 📈 Cost Impact Analysis

### Token Pricing Comparison
| Provider | Regular Input | Cached Read | Cache Write | Savings |
|----------|--------------|-------------|-------------|---------|
| Google | $1.25/1M | $0.3125/1M | $1.5625/1M | 75% |
| Anthropic | $3/1M | $0.30/1M | $3.75/1M | 90% |
| OpenAI | $0.50/1M | N/A (Assistants) | N/A | ~80% |
| Others | Varies | Redis only | Redis only | ~60% |

### Break-Even Analysis
```typescript
// Calculate when caching becomes cost-effective
function calculateBreakEven(config: {
  tokenCount: number;
  reusesPerHour: number;
  cacheWritePremium: number;
  cacheReadDiscount: number;
}): number {
  const writeCost = config.tokenCount * config.cacheWritePremium;
  const savingsPerUse = config.tokenCount * (1 - config.cacheReadDiscount);
  return Math.ceil(writeCost / savingsPerUse);
}

// Example: Google with 50K tokens
// Break-even: 2 uses (write premium absorbed after 2 reads)
```

## 🔧 Implementation Priority

### Phase 1: High-Impact Providers (Week 1)
1. **Google Vertex AI** - Largest volume, best ROI
2. **Anthropic Claude** - High cost, easy implementation
3. **OpenAI** - Assistants API for system prompts

### Phase 2: Optimization (Week 2)
1. **Unified cache interface**
2. **Cache warming strategies**
3. **Monitoring and analytics**

### Phase 3: Advanced Features (Week 3)
1. **Cross-provider cache sharing**
2. **Intelligent cache eviction**
3. **Cost optimization engine**

## 🎯 Success Metrics
- **Cache hit rate**: >80% for system prompts
- **Cost reduction**: 60-90% on cached content
- **Latency improvement**: 30-50% average
- **Implementation coverage**: 100% of major providers

## 🚀 Next Steps
1. Implement CachedGoogleProvider with context caching
2. Add Anthropic prompt caching headers
3. Create OpenAI Assistant-based caching
4. Build UniversalCacheManager
5. Add monitoring and cost tracking