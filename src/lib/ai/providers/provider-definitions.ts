/**
 * AI SDK Provider Definitions
 * 
 * Centralized configuration for all AI SDK providers.
 * Separated from registration logic for better maintainability.
 */

import { AISDKProviderRegistration } from './registry';
import { AI_SDK_PROVIDERS, AISDKProviderName } from './constants';

// Provider capability profiles for common patterns
export const CAPABILITY_PROFILES = {
  FULL_FEATURED: {
    reasoning: false,
    multimodal: true,
    functionCalling: true,
    structuredOutput: true
  },
  REASONING: {
    reasoning: true,
    multimodal: false,
    functionCalling: true,
    structuredOutput: true
  },
  TEXT_ONLY: {
    reasoning: false,
    multimodal: false,
    functionCalling: true,
    structuredOutput: true
  },
  BASIC: {
    reasoning: false,
    multimodal: false,
    functionCalling: false,
    structuredOutput: false
  },
  MULTIMODAL_REASONING: {
    reasoning: true,
    multimodal: true,
    functionCalling: true,
    structuredOutput: true
  },
  TEXT_ONLY_NO_STRUCTURED: {
    reasoning: false,
    multimodal: false,
    functionCalling: true,
    structuredOutput: false
  }
} as const;

// Provider metadata configuration
export interface ProviderMetadata {
  name: string;
  description: string;
  aiSdkPackage: string;
  configKeys: string[];
  defaultModels: string[];
  capabilities: typeof CAPABILITY_PROFILES[keyof typeof CAPABILITY_PROFILES];
  envKeyName?: string; // Environment variable name for API key
}

// Centralized provider definitions
export const PROVIDER_DEFINITIONS: Record<AISDKProviderName, ProviderMetadata> = {
  [AI_SDK_PROVIDERS.OPENAI]: {
    name: 'OpenAI',
    description: 'OpenAI models including GPT-4o, GPT-4o-mini, and GPT-3.5-turbo with vision and function calling',
    aiSdkPackage: '@ai-sdk/openai',
    configKeys: ['apiKey', 'baseURL', 'organization'],
    defaultModels: ['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo'],
    capabilities: CAPABILITY_PROFILES.FULL_FEATURED,
    envKeyName: 'OPENAI_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.ANTHROPIC]: {
    name: 'Anthropic',
    description: 'Claude models including Claude 3.5 Sonnet and Haiku with vision and tool use',
    aiSdkPackage: '@ai-sdk/anthropic',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022'],
    capabilities: CAPABILITY_PROFILES.FULL_FEATURED,
    envKeyName: 'ANTHROPIC_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.GOOGLE]: {
    name: 'Google',
    description: 'Gemini models including Gemini 2.0 Flash with multimodal capabilities',
    aiSdkPackage: '@ai-sdk/google',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['gemini-2.0-flash-exp', 'gemini-1.5-pro', 'gemini-1.5-flash'],
    capabilities: CAPABILITY_PROFILES.FULL_FEATURED,
    envKeyName: 'GOOGLE_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.MISTRAL]: {
    name: 'Mistral',
    description: 'Mistral models including Mistral Large and Codestral for code generation',
    aiSdkPackage: '@ai-sdk/mistral',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['mistral-large-latest', 'codestral-latest', 'mistral-small-latest'],
    capabilities: CAPABILITY_PROFILES.TEXT_ONLY,
    envKeyName: 'MISTRAL_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.GROQ]: {
    name: 'Groq',
    description: 'Hardware-accelerated models with ultra-fast inference speeds',
    aiSdkPackage: '@ai-sdk/groq',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['llama-3.3-70b-versatile', 'mixtral-8x7b-32768'],
    capabilities: CAPABILITY_PROFILES.TEXT_ONLY_NO_STRUCTURED,
    envKeyName: 'GROQ_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.TOGETHER]: {
    name: 'Together AI',
    description: 'Diverse open-source models including DeepSeek V3 and QwQ reasoning',
    aiSdkPackage: '@ai-sdk/togetherai',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['deepseek-ai/DeepSeek-V3', 'Qwen/QwQ-32B-Preview'],
    capabilities: CAPABILITY_PROFILES.REASONING,
    envKeyName: 'TOGETHER_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.XAI]: {
    name: 'xAI',
    description: 'Grok models with reasoning content and web search capabilities',
    aiSdkPackage: '@ai-sdk/xai',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['grok-3', 'grok-3-mini', 'grok-2-1212'],
    capabilities: CAPABILITY_PROFILES.REASONING,
    envKeyName: 'XAI_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.DEEPSEEK]: {
    name: 'DeepSeek',
    description: 'Advanced reasoning models including DeepSeek V3 and R1 series',
    aiSdkPackage: '@ai-sdk/deepseek',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['deepseek-chat', 'deepseek-reasoner'],
    capabilities: CAPABILITY_PROFILES.REASONING,
    envKeyName: 'DEEPSEEK_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.PERPLEXITY]: {
    name: 'Perplexity',
    description: 'Search-enhanced models with real-time web access and citations',
    aiSdkPackage: '@ai-sdk/perplexity',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['sonar-pro', 'sonar', 'llama-3.1-8b-instruct'],
    capabilities: CAPABILITY_PROFILES.BASIC,
    envKeyName: 'PERPLEXITY_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.COHERE]: {
    name: 'Cohere',
    description: 'Command R series with multilingual support and RAG capabilities',
    aiSdkPackage: '@ai-sdk/cohere',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['command-r-plus-08-2024', 'command-r-08-2024'],
    capabilities: CAPABILITY_PROFILES.TEXT_ONLY,
    envKeyName: 'COHERE_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.OPENROUTER]: {
    name: 'OpenRouter',
    description: 'Unified API for accessing multiple AI providers through a single interface',
    aiSdkPackage: '@openrouter/ai-sdk-provider',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['openai/gpt-4o', 'anthropic/claude-3.5-sonnet'],
    capabilities: CAPABILITY_PROFILES.FULL_FEATURED,
    envKeyName: 'OPENROUTER_API_KEY'
  },
  
  [AI_SDK_PROVIDERS.QWEN]: {
    name: 'Qwen',
    description: 'Alibaba Qwen models including QwQ reasoning and multimodal capabilities',
    aiSdkPackage: 'qwen-ai-provider',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['qwen-max', 'qwq-32b-preview', 'qwen-vl-max'],
    capabilities: CAPABILITY_PROFILES.MULTIMODAL_REASONING,
    envKeyName: 'QWEN_API_KEY'
  },
  
  // Alibaba is an alias for Qwen
  [AI_SDK_PROVIDERS.ALIBABA]: {
    name: 'Alibaba',
    description: 'Alibaba Qwen models including QwQ reasoning and multimodal capabilities',
    aiSdkPackage: 'qwen-ai-provider',
    configKeys: ['apiKey', 'baseURL'],
    defaultModels: ['qwen-max', 'qwq-32b-preview', 'qwen-vl-max'],
    capabilities: CAPABILITY_PROFILES.MULTIMODAL_REASONING,
    envKeyName: 'ALIBABA_API_KEY'
  }
};

// Validate provider definitions at runtime
export function validateProviderDefinitions(): void {
  const providerNames = Object.values(AI_SDK_PROVIDERS);
  const definedProviders = Object.keys(PROVIDER_DEFINITIONS);
  
  // Check all providers are defined
  for (const providerName of providerNames) {
    if (!definedProviders.includes(providerName)) {
      throw new Error(`Missing provider definition for: ${providerName}`);
    }
  }
  
  // Validate each definition
  for (const [providerId, metadata] of Object.entries(PROVIDER_DEFINITIONS)) {
    if (!metadata.name || !metadata.description || !metadata.aiSdkPackage) {
      throw new Error(`Invalid provider definition for: ${providerId}`);
    }
    
    if (!metadata.configKeys || metadata.configKeys.length === 0) {
      throw new Error(`No config keys defined for: ${providerId}`);
    }
    
    if (!metadata.defaultModels || metadata.defaultModels.length === 0) {
      throw new Error(`No default models defined for: ${providerId}`);
    }
  }
}