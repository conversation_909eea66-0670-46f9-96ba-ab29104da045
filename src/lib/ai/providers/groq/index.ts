import Groq from 'groq-sdk';
import { AISdkProviderBase } from '../ai-sdk-base';
import { AISDKProviderConfig } from '../types';
import { Stream } from 'groq-sdk/lib/streaming';

/**
 * Groq Provider using Official Groq SDK
 * 
 * Implements Groq's hardware-accelerated models including Llama, Mixtral,
 * and Gemma with ultra-fast inference speeds
 * 
 * Uses the official groq-sdk npm package with streaming via chat.completions.create
 * Models are fetched from database via ModelRepository
 */
export class GroqAISDKProvider extends AISdkProviderBase {
  public providerName = 'groq';
  protected provider: any;
  private groqClient: Groq;
  
  constructor(config: AISDKProviderConfig = {}) {
    super(config);
    
    console.log('[Groq] Initializing provider with config:', { 
      hasApiKey: !!(config.apiKey || process.env.GROQ_API_KEY),
      baseUrl: config.baseUrl 
    });
    
    // Initialize the official Groq client
    this.groqClient = new Groq({
      apiKey: config.apiKey || process.env.GROQ_API_KEY,
      baseURL: config.baseUrl,
      defaultHeaders: config.headers,
      fetch: config.fetch as any,
    });
    
    // Create a provider wrapper that matches AI SDK interface
    this.provider = this.createProviderWrapper();
    console.log('[Groq] Created official Groq client with provider wrapper');
  }
  
  private createProviderWrapper() {
    // Return a function that creates model instances compatible with AI SDK
    return (modelId: string) => {
      return {
        // For streamText compatibility
        doStream: async (params: any) => {
          try {
            // Convert AI SDK params to Groq params
            const messages = params.messages?.map((msg: any) => ({
              role: msg.role,
              content: msg.content
            }));
            
            // Use the official SDK's streaming API
            const stream = await this.groqClient.chat.completions.create({
              model: modelId,
              messages,
              stream: true,
              temperature: params.temperature,
              max_tokens: params.maxTokens,
              top_p: params.topP,
              frequency_penalty: params.frequencyPenalty,
              presence_penalty: params.presencePenalty,
              stop: params.stop,
              user: params.user,
              seed: params.seed,
            }) as Stream<any>;
            
            // Convert Groq stream to AI SDK compatible stream
            return {
              stream: this.convertStream(stream),
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Groq] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack
            });
            throw error;
          }
        },
        
        // For generateText compatibility
        doGenerate: async (params: any) => {
          try {
            const messages = params.messages?.map((msg: any) => ({
              role: msg.role,
              content: msg.content
            }));
            
            const completion = await this.groqClient.chat.completions.create({
              model: modelId,
              messages,
              stream: false,
              temperature: params.temperature,
              max_tokens: params.maxTokens,
              top_p: params.topP,
              frequency_penalty: params.frequencyPenalty,
              presence_penalty: params.presencePenalty,
              stop: params.stop,
              user: params.user,
              seed: params.seed,
            });
            
            return {
              text: completion.choices[0]?.message?.content || '',
              usage: completion.usage ? {
                promptTokens: completion.usage.prompt_tokens || 0,
                completionTokens: completion.usage.completion_tokens || 0,
                totalTokens: completion.usage.total_tokens || 0
              } : {
                promptTokens: 0,
                completionTokens: 0,
                totalTokens: 0
              },
              finishReason: completion.choices[0]?.finish_reason || 'stop',
              rawCall: { rawPrompt: params.messages, rawSettings: params }
            };
          } catch (error: any) {
            console.error(`[Groq] API Error:`, {
              message: error.message,
              name: error.name,
              stack: error.stack
            });
            throw error;
          }
        }
      };
    };
  }
  
  private async *convertStream(groqStream: Stream<any>): AsyncGenerator<any> {
    try {
      for await (const chunk of groqStream) {
        const delta = chunk.choices[0]?.delta;
        if (delta?.content) {
          yield {
            type: 'text-delta',
            textDelta: delta.content
          };
        }
        
        // Handle finish reason
        if (chunk.choices[0]?.finish_reason) {
          yield {
            type: 'finish',
            finishReason: chunk.choices[0].finish_reason,
            usage: chunk.usage ? {
              promptTokens: chunk.usage.prompt_tokens,
              completionTokens: chunk.usage.completion_tokens,
              totalTokens: chunk.usage.total_tokens
            } : undefined
          };
        }
      }
    } catch (error: any) {
      yield {
        type: 'error',
        error: {
          message: error.message,
          name: error.name,
          stack: error.stack
        }
      };
      throw error;
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const apiKey = (this.config as AISDKProviderConfig)?.apiKey || process.env.GROQ_API_KEY;
      if (!apiKey) {
        console.error('[Groq] No API key found');
        return false;
      }
      
      // Test the configuration with a minimal API call
      try {
        // Create a minimal completion to test the API
        const completion = await this.groqClient.chat.completions.create({
          model: 'llama3-8b-8192',
          messages: [{ role: 'user', content: 'Hi' }],
          max_tokens: 5,
          temperature: 0
        });
        console.log('[Groq] Configuration validated');
        return true;
      } catch (error: any) {
        console.error('[Groq] API validation failed:', error.message);
        return false;
      }
    } catch (error) {
      console.error('Groq configuration validation failed:', error);
      return false;
    }
  }
}

// Export as default and named export for flexibility
export default GroqAISDKProvider;