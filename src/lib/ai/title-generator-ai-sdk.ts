/**
 * AI-powered conversation title generator using AI SDK
 * Automatically generates meaningful titles for conversations
 * Migrated from LiteLLM to AI SDK in Phase 7.2
 */

import { createAISD<PERSON><PERSON>rovider, AI_SDK_PROVIDERS, type AISDKProviderName } from '@/lib/ai/providers';
import { apiLogger } from '@/lib/logger';

interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface TitleGenerationOptions {
  maxLength?: number;
  style?: 'descriptive' | 'question' | 'topic' | 'auto';
  includeEmoji?: boolean;
}

class TitleGenerator {
  private providers = new Map<string, any>();

  constructor() {
    // Pre-initialize fast providers for title generation
    this.initializeFastProviders();
  }

  /**
   * Initialize fast providers suitable for title generation
   */
  private async initializeFastProviders() {
    const fastProviders = ['groq', 'openai', 'anthropic'];
    
    for (const providerName of fastProviders) {
      try {
        const provider = createAISDKProvider(providerName as AISDKProviderName);
        const isValid = await provider.validateConfig();
        
        if (isValid) {
          this.providers.set(providerName, provider);
          console.log(`[TitleGenerator] Initialized fast provider: ${providerName}`);
        }
      } catch (error) {
        console.warn(`[TitleGenerator] Failed to initialize provider ${providerName}:`, error);
      }
    }
  }

  /**
   * Clear cached providers and reinitialize (useful after provider fixes)
   */
  async clearCacheAndReinitialize() {
    console.log('[TitleGenerator] Clearing provider cache and reinitializing...');
    this.providers.clear();
    await this.initializeFastProviders();
  }

  /**
   * Get the best available provider for title generation
   */
  private async getBestProvider(retryAfterCacheClear = true): Promise<{ provider: any; name: string }> {
    // Prefer fast providers in order of speed
    const preferenceOrder = ['groq', 'openai', 'anthropic', 'google', 'mistral'];
    
    for (const providerName of preferenceOrder) {
      if (this.providers.has(providerName)) {
        try {
          const provider = this.providers.get(providerName);
          // Test if cached provider is still valid
          await provider.validateConfig();
          return {
            provider,
            name: providerName
          };
        } catch (error) {
          console.warn(`[TitleGenerator] Cached provider ${providerName} is invalid, removing from cache:`, error);
          this.providers.delete(providerName);
        }
      }
      
      // Try to initialize if not cached
      try {
        const provider = createAISDKProvider(providerName as AISDKProviderName);
        const isValid = await provider.validateConfig();
        
        if (isValid) {
          this.providers.set(providerName, provider);
          return { provider, name: providerName };
        }
      } catch (error) {
        continue; // Try next provider
      }
    }
    
    // If we get here and haven't retried cache clear yet, clear cache and try once more
    if (retryAfterCacheClear && this.providers.size > 0) {
      console.log('[TitleGenerator] All providers failed, clearing cache and retrying...');
      await this.clearCacheAndReinitialize();
      return this.getBestProvider(false); // Retry without cache clear
    }
    
    throw new Error('No available providers for title generation');
  }

  /**
   * Select optimal model for title generation based on provider
   */
  private getOptimalModel(providerName: string): string {
    const fastModels: Record<string, string> = {
      'groq': 'llama3-8b-8192',          // Ultra-fast 750+ tokens/s
      'openai': 'gpt-4o-mini',           // Fast and cheap
      'anthropic': 'claude-3-haiku-20240307', // Fast Anthropic model
      'google': 'gemini-1.5-flash',      // Fast Google model
      'mistral': 'mistral-small-latest'  // Fast Mistral model
    };
    
    return fastModels[providerName] || 'gpt-4o-mini';
  }

  /**
   * Generate a title for a conversation based on the initial messages
   */
  async generateTitle(
    messages: Message[], 
    options: TitleGenerationOptions = {}
  ): Promise<string> {
    const {
      maxLength = 60,
      style = 'auto',
      includeEmoji = true
    } = options;

    try {
      // Take first 3-4 messages for context
      const contextMessages = messages.slice(0, 4);
      
      // Get best available provider
      const { provider, name: providerName } = await this.getBestProvider();
      const modelId = this.getOptimalModel(providerName);
      
      // Create prompt for title generation
      const systemPrompt = this.createSystemPrompt(style, maxLength, includeEmoji);
      const userPrompt = this.formatConversationForPrompt(contextMessages);
      
      console.log('[TitleGenerator] Generating title with:', {
        provider: providerName,
        model: modelId,
        messageCount: contextMessages.length,
        systemPromptLength: systemPrompt.length,
        userPromptLength: userPrompt.length
      });
      
      // Generate title using AI SDK
      const response = await provider.generateCompletion({
        model: modelId,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.4,        // Lower for consistency
        maxTokens: 30,          // Titles are short
        topP: 0.9,              // Focused creativity
      });
      
      console.log('[TitleGenerator] AI SDK response received:', {
        provider: providerName,
        model: modelId,
        responseLength: response?.length || 0
      });
      
      // Clean up the title
      let title = (response || '').trim();
      title = this.cleanTitle(title, maxLength);
      
      console.log('[TitleGenerator] Generated title:', title);
      
      return title || this.generateSimpleTitle(messages, maxLength);
      
    } catch (error) {
      console.error('[TitleGenerator] Failed to generate AI title:', error);
      apiLogger.error('Title generation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        messageCount: messages.length
      });
      return this.generateSimpleTitle(messages, maxLength);
    }
  }

  /**
   * Create system prompt for title generation
   */
  private createSystemPrompt(
    style: string, 
    maxLength: number,
    includeEmoji: boolean
  ): string {
    const emojiNote = includeEmoji ? "ONE emoji at start (only if relevant) or no emoji" : "No emojis";
    
    const styleInstructions = {
      descriptive: "Create a descriptive title that summarizes what the conversation is about.",
      question: "Create a title as a question that the user might be asking.",
      topic: "Create a topic-based title that captures the main subject matter.",
      auto: "Create the most natural and engaging title that best represents this conversation."
    };

    return `You are an expert at creating concise, engaging conversation titles for JustSimpleChat.

Goal: Generate a single-line title that perfectly captures the essence of the conversation.

STRICT RULES:
1. Title ≤ ${maxLength} Unicode characters (including spaces & emoji)
2. ${emojiNote}
3. NO quotes, NO line breaks, NO trailing punctuation
4. Professional yet friendly tone (think tech blog headline)
5. Be specific to the actual topic - avoid generic phrases like "Chat about X"
6. Match the conversation's primary language
7. For sensitive/personal content: "Private Conversation"
8. For empty/greeting-only chats: "New Conversation"
9. For technical discussions: Include the main technology/concept
10. For creative tasks: Highlight the creative output type

${styleInstructions[style as keyof typeof styleInstructions]}

SPECIAL CASES:
- Coding requests → Include language/framework (e.g., "🚀 React TypeScript Component")
- Debugging → Include the error type (e.g., "🐛 Fix TypeScript Compilation Error")
- Writing tasks → Include content type (e.g., "✍️ Short Story: Time Travel")
- Analysis → Include subject (e.g., "📊 Market Analysis: Tech Stocks")
- Web searches → Include query focus (e.g., "🔍 Latest AI Model Benchmarks")

OUTPUT: Return ONLY the title text. No explanations, no markdown, nothing else.`;
  }

  /**
   * Format conversation for the prompt
   */
  private formatConversationForPrompt(messages: Message[]): string {
    const formatted = messages
      .filter(m => m.role !== 'system')
      .map(m => `${m.role.toUpperCase()}: ${m.content.substring(0, 500)}`) // Limit content length
      .join('\n\n');
    
    return `Based on this conversation, generate an appropriate title:\n\n${formatted}\n\nTitle:`;
  }

  /**
   * Fallback simple title generation
   */
  private generateSimpleTitle(messages: Message[], maxLength: number): string {
    const userMessages = messages.filter(m => m.role === 'user');
    
    if (userMessages.length === 0) {
      return "New Conversation";
    }

    const firstMessage = userMessages[0].content;
    
    // Extract key topics/nouns
    const cleaned = firstMessage
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    if (cleaned.length <= maxLength) {
      return cleaned;
    }

    // Try to create a meaningful truncated title
    const words = cleaned.split(' ');
    let title = '';
    
    for (const word of words) {
      if ((title + ' ' + word).length <= maxLength - 3) {
        title += (title ? ' ' : '') + word;
      } else {
        break;
      }
    }
    
    return title + (title.length < cleaned.length ? '...' : '');
  }

  /**
   * Clean and validate generated titles
   */
  private cleanTitle(title: string, maxLength: number): string {
    // Remove quotes, extra whitespace, and newlines
    title = title
      .replace(/^["']|["']$/g, '')
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Remove any "Title:" prefix if present
    title = title.replace(/^Title:\s*/i, '');

    // Truncate if too long
    if (title.length > maxLength) {
      title = title.substring(0, maxLength - 3) + '...';
    }

    // Capitalize first letter (but not if it starts with emoji)
    if (title.length > 0 && !title.match(/^[\u{1F300}-\u{1F9FF}]/u)) {
      title = title.charAt(0).toUpperCase() + title.slice(1);
    }

    return title;
  }

  /**
   * Generate multiple title suggestions
   */
  async generateTitleSuggestions(
    messages: Message[], 
    count: number = 3
  ): Promise<string[]> {
    const suggestions: string[] = [];
    
    const styles: TitleGenerationOptions['style'][] = ['auto', 'descriptive', 'question'];
    
    for (let i = 0; i < count && i < styles.length; i++) {
      try {
        const title = await this.generateTitle(messages, {
          style: styles[i],
          includeEmoji: i === 0, // Only first suggestion gets emoji
          maxLength: 45 // Slightly shorter for suggestions
        });
        
        if (title && !suggestions.includes(title)) {
          suggestions.push(title);
        }
      } catch (error) {
        console.error(`Failed to generate title suggestion ${i + 1}:`, error);
      }
    }

    // Ensure we have at least one fallback
    if (suggestions.length === 0) {
      suggestions.push(this.generateSimpleTitle(messages, 50));
    }

    return suggestions;
  }
}

// Export singleton instance
export const titleGenerator = new TitleGenerator();

// Export types
export type { TitleGenerationOptions, Message };