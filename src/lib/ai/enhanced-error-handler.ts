/**
 * Enhanced Error Handler with Standardization and Retry Logic
 * 
 * @description
 * Comprehensive error handling system that standardizes errors across providers,
 * implements intelligent retry logic, and provides detailed troubleshooting information.
 * 
 * @module lib/ai/enhanced-error-handler
 */

import { apiLogger } from '@/lib/logger';
import { CircuitBreaker, CircuitBreakerError } from './circuit-breaker';

/**
 * Error context information
 */
export interface ErrorContext {
  provider: string;
  model: string;
  operation: string;
  requestId: string;
  attempt: number;
  startTime: number;
  userId?: string;
  sessionId?: string;
}

/**
 * Standardized error response
 */
export interface StandardErrorResponse {
  code: string;
  message: string;
  provider: string;
  model: string;
  retryable: boolean;
  retryAfter?: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'auth' | 'rate_limit' | 'quota' | 'network' | 'server' | 'client' | 'unknown';
  troubleshooting?: string;
  context: ErrorContext;
  originalError: Error;
  timestamp: Date;
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  jitterFactor: number;
  retryableErrors: string[];
  nonRetryableErrors: string[];
}

/**
 * Enhanced Error Handler
 */
export class EnhancedErrorHandler {
  private static instance: EnhancedErrorHandler;
  private circuitBreaker: CircuitBreaker;
  
  private defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    jitterFactor: 0.1,
    retryableErrors: ['RATE_LIMIT', 'SERVER_ERROR', 'NETWORK_ERROR', 'TIMEOUT'],
    nonRetryableErrors: ['AUTHENTICATION_ERROR', 'QUOTA_EXCEEDED', 'CONTENT_POLICY_VIOLATION']
  };

  constructor(circuitBreaker?: CircuitBreaker) {
    this.circuitBreaker = circuitBreaker || new CircuitBreaker();
  }

  /**
   * Get singleton instance
   */
  static getInstance(circuitBreaker?: CircuitBreaker): EnhancedErrorHandler {
    if (!EnhancedErrorHandler.instance) {
      EnhancedErrorHandler.instance = new EnhancedErrorHandler(circuitBreaker);
    }
    return EnhancedErrorHandler.instance;
  }

  /**
   * Execute operation with comprehensive error handling
   */
  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    retryConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.defaultRetryConfig, ...retryConfig };
    const circuitKey = `${context.provider}-${context.model}-${context.operation}`;

    return await this.circuitBreaker.execute(circuitKey, async () => {
      return await this.executeWithRetry(operation, context, config);
    });
  }

  /**
   * Standardize any error into a consistent format
   */
  standardizeError(error: any, context: ErrorContext): StandardErrorResponse {
    // Handle circuit breaker errors
    if (error instanceof CircuitBreakerError) {
      return {
        code: 'CIRCUIT_BREAKER_OPEN',
        message: 'Service temporarily unavailable due to circuit breaker',
        provider: context.provider,
        model: context.model,
        retryable: true,
        retryAfter: Math.max(0, error.nextAttemptTime - Date.now()),
        severity: 'high',
        category: 'server',
        troubleshooting: 'The circuit breaker is open due to repeated failures. Please wait for the recovery period.',
        context,
        originalError: error,
        timestamp: new Date()
      };
    }

    // Extract error information
    const status = error.status || error.response?.status;
    const message = error.message || error.response?.data?.error?.message || 'Unknown error';
    const code = error.code;
    
    // Classify the error
    const classification = this.classifyError(error, status, message, code);
    
    return {
      code: classification.code,
      message: classification.message,
      provider: context.provider,
      model: context.model,
      retryable: classification.retryable,
      retryAfter: classification.retryAfter,
      severity: classification.severity,
      category: classification.category,
      troubleshooting: classification.troubleshooting,
      context,
      originalError: error,
      timestamp: new Date()
    };
  }

  /**
   * Get circuit breaker metrics
   */
  getCircuitBreakerMetrics(): Map<string, any> {
    return this.circuitBreaker.getAllStates();
  }

  /**
   * Reset circuit breaker for specific key
   */
  resetCircuitBreaker(key: string): void {
    this.circuitBreaker.reset(key);
  }

  /**
   * Reset all circuit breakers
   */
  resetAllCircuitBreakers(): void {
    this.circuitBreaker.resetAll();
  }

  /**
   * Private helper methods
   */
  
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    config: RetryConfig
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        const result = await operation();
        
        if (attempt > 0) {
          apiLogger.info('Operation succeeded after retry', {
            provider: context.provider,
            model: context.model,
            attempt,
            requestId: context.requestId
          });
        }
        
        return result;
      } catch (error: any) {
        lastError = error;
        const standardError = this.standardizeError(error, { ...context, attempt });
        
        // Check if error is retryable
        if (!this.isRetryableError(standardError, config) || attempt === config.maxRetries) {
          throw standardError;
        }
        
        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateDelay(attempt, config, standardError.retryAfter);
        
        apiLogger.warn('Operation failed, retrying', {
          provider: context.provider,
          model: context.model,
          attempt: attempt + 1,
          maxRetries: config.maxRetries,
          delay,
          error: standardError.code,
          requestId: context.requestId
        });
        
        await this.delay(delay);
      }
    }
    
    throw lastError || new Error('Retry failed');
  }

  private classifyError(error: any, status?: number, message?: string, code?: string): {
    code: string;
    message: string;
    retryable: boolean;
    retryAfter?: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: 'auth' | 'rate_limit' | 'quota' | 'network' | 'server' | 'client' | 'unknown';
    troubleshooting: string;
  } {
    const msg = (message || '').toLowerCase();
    
    // Authentication errors
    if (status === 401 || msg.includes('unauthorized') || msg.includes('invalid api key')) {
      return {
        code: 'AUTHENTICATION_ERROR',
        message: 'Authentication failed',
        retryable: false,
        severity: 'critical',
        category: 'auth',
        troubleshooting: 'Check your API key and ensure it is valid and has the necessary permissions.'
      };
    }
    
    // Rate limiting
    if (status === 429 || msg.includes('rate limit') || msg.includes('too many requests')) {
      const retryAfter = error.retryAfter ? error.retryAfter * 1000 : 60000; // Default 1 minute
      return {
        code: 'RATE_LIMIT',
        message: 'Rate limit exceeded',
        retryable: true,
        retryAfter,
        severity: 'medium',
        category: 'rate_limit',
        troubleshooting: 'You are making requests too frequently. Please slow down your request rate.'
      };
    }
    
    // Quota exceeded
    if (msg.includes('quota') || msg.includes('billing') || msg.includes('usage limit')) {
      return {
        code: 'QUOTA_EXCEEDED',
        message: 'Usage quota exceeded',
        retryable: false,
        severity: 'high',
        category: 'quota',
        troubleshooting: 'Your usage quota has been exceeded. Check your billing and usage limits.'
      };
    }
    
    // Server errors
    if (status && status >= 500) {
      return {
        code: 'SERVER_ERROR',
        message: 'Server error occurred',
        retryable: true,
        severity: 'high',
        category: 'server',
        troubleshooting: 'The service is experiencing server issues. This is typically temporary.'
      };
    }
    
    // Network errors
    if (code === 'ETIMEDOUT' || code === 'ECONNRESET' || msg.includes('timeout') || msg.includes('connection')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connectivity issue',
        retryable: true,
        severity: 'medium',
        category: 'network',
        troubleshooting: 'Check your internet connectivity and firewall settings.'
      };
    }
    
    // Content policy violations
    if (msg.includes('content policy') || msg.includes('safety') || msg.includes('inappropriate')) {
      return {
        code: 'CONTENT_POLICY_VIOLATION',
        message: 'Content violates policy',
        retryable: false,
        severity: 'medium',
        category: 'client',
        troubleshooting: 'Your request contains content that violates the provider\'s content policies.'
      };
    }
    
    // Model not found
    if (status === 404 || msg.includes('model not found') || msg.includes('not found')) {
      return {
        code: 'MODEL_NOT_FOUND',
        message: 'Model not found or not available',
        retryable: false,
        severity: 'medium',
        category: 'client',
        troubleshooting: 'Check the model name and ensure it is available for your account.'
      };
    }
    
    // Client errors (4xx)
    if (status && status >= 400 && status < 500) {
      return {
        code: 'CLIENT_ERROR',
        message: 'Client request error',
        retryable: false,
        severity: 'medium',
        category: 'client',
        troubleshooting: 'Check your request parameters and ensure they are valid.'
      };
    }
    
    // Unknown errors
    return {
      code: 'UNKNOWN_ERROR',
      message: message || 'An unknown error occurred',
      retryable: false,
      severity: 'medium',
      category: 'unknown',
      troubleshooting: 'An unexpected error occurred. Please check the error details and try again.'
    };
  }

  private isRetryableError(error: StandardErrorResponse, config: RetryConfig): boolean {
    // Check explicit configurations
    if (config.retryableErrors.includes(error.code)) {
      return true;
    }
    
    if (config.nonRetryableErrors.includes(error.code)) {
      return false;
    }
    
    // Use the error's retryable flag
    return error.retryable;
  }

  private calculateDelay(
    attempt: number,
    config: RetryConfig,
    retryAfter?: number
  ): number {
    // Use retryAfter if provided (for rate limiting)
    if (retryAfter && retryAfter > 0) {
      return Math.min(retryAfter, config.maxDelay);
    }
    
    // Exponential backoff with jitter
    const exponentialDelay = config.baseDelay * Math.pow(config.backoffFactor, attempt);
    const jitter = exponentialDelay * config.jitterFactor * Math.random();
    const delay = exponentialDelay + jitter;
    
    return Math.min(delay, config.maxDelay);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}