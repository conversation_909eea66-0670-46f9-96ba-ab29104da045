/**
 * Health Monitor for AI Provider Performance Tracking
 * 
 * @description
 * Monitors the health and performance of AI providers in real-time.
 * Tracks success rates, latency, and other key metrics.
 * 
 * @module lib/ai/health-monitor
 */

import { EventEmitter } from 'events';
import { apiLogger } from '@/lib/logger';
import { AIProvider } from './providers/types';

/**
 * Health metrics for a provider
 */
export interface HealthMetrics {
  provider: string;
  model?: string;
  isHealthy: boolean;
  
  // Performance metrics
  successRate: number;
  averageLatency: number;
  healthScore: number; // 0-100
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical';
  
  // Request statistics
  totalRequests: number;
  totalSuccesses: number;
  totalFailures: number;
  
  // Consecutive metrics
  consecutiveSuccesses: number;
  consecutiveFailures: number;
  
  // Timing information
  lastRequestTime: number;
  lastSuccessTime: number;
  lastFailureTime: number;
  
  // Uptime calculation
  uptime: number; // percentage
  
  // Data collection period
  windowStart: number;
  windowEnd: number;
}

/**
 * Health alert
 */
export interface HealthAlert {
  id: string;
  provider: string;
  model?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * Health monitor configuration
 */
export interface HealthMonitorConfig {
  healthCheckInterval: number; // milliseconds
  monitoringWindow: number; // milliseconds
  healthyThreshold: number; // 0-1
  degradedThreshold: number; // 0-1
  unhealthyThreshold: number; // 0-1
  latencyThreshold: number; // milliseconds
  consecutiveFailureThreshold: number;
  minRequestsForScore: number;
}

/**
 * Request data point
 */
interface RequestDataPoint {
  timestamp: number;
  success: boolean;
  latency: number;
  error?: string;
}

/**
 * Provider health state
 */
interface ProviderHealthState {
  provider: string;
  model?: string;
  requests: RequestDataPoint[];
  consecutiveSuccesses: number;
  consecutiveFailures: number;
  lastHealthCheck: number;
  isRegistered: boolean;
}

/**
 * Health Monitor implementation
 */
export class HealthMonitor extends EventEmitter {
  private config: HealthMonitorConfig;
  private providers: Map<string, ProviderHealthState> = new Map();
  private alerts: Map<string, HealthAlert> = new Map();
  private healthCheckTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;
  private isStarted = false;

  constructor(config: Partial<HealthMonitorConfig> = {}) {
    super();
    
    this.config = {
      healthCheckInterval: 30000, // 30 seconds
      monitoringWindow: 300000, // 5 minutes
      healthyThreshold: 0.95,
      degradedThreshold: 0.8,
      unhealthyThreshold: 0.5,
      latencyThreshold: 5000, // 5 seconds
      consecutiveFailureThreshold: 3,
      minRequestsForScore: 5,
      ...config
    };
  }

  /**
   * Start health monitoring
   */
  start(): void {
    if (this.isStarted) {
      return;
    }
    
    this.isStarted = true;
    
    // Start periodic health checks
    this.healthCheckTimer = setInterval(() => {
      this.performHealthChecks();
    }, this.config.healthCheckInterval);
    
    // Start cleanup timer (every hour)
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldData();
    }, 3600000);
    
    apiLogger.info('Health monitor started', {
      interval: this.config.healthCheckInterval,
      window: this.config.monitoringWindow
    });
    
    this.emit('started');
  }

  /**
   * Stop health monitoring
   */
  stop(): void {
    if (!this.isStarted) {
      return;
    }
    
    this.isStarted = false;
    
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    
    apiLogger.info('Health monitor stopped');
    this.emit('stopped');
  }

  /**
   * Register a provider for monitoring
   */
  registerProvider(provider: AIProvider, models?: string[]): void {
    if (!models || models.length === 0) {
      // Register provider without specific models
      const key = provider.providerName;
      this.providers.set(key, {
        provider: provider.providerName,
        requests: [],
        consecutiveSuccesses: 0,
        consecutiveFailures: 0,
        lastHealthCheck: 0,
        isRegistered: true
      });
    } else {
      // Register provider with specific models
      models.forEach(model => {
        const key = `${provider.providerName}-${model}`;
        this.providers.set(key, {
          provider: provider.providerName,
          model,
          requests: [],
          consecutiveSuccesses: 0,
          consecutiveFailures: 0,
          lastHealthCheck: 0,
          isRegistered: true
        });
      });
    }
    
    apiLogger.debug('Provider registered for health monitoring', {
      provider: provider.providerName,
      models: models || 'all'
    });
  }

  /**
   * Record a request for health tracking
   */
  recordRequest(provider: string, success: boolean, latency: number, error?: string): void {
    const now = Date.now();
    
    // Find or create provider state
    let state = this.providers.get(provider);
    if (!state) {
      // Auto-register unknown providers
      state = {
        provider,
        requests: [],
        consecutiveSuccesses: 0,
        consecutiveFailures: 0,
        lastHealthCheck: 0,
        isRegistered: false
      };
      this.providers.set(provider, state);
    }
    
    // Add request data point
    state.requests.push({
      timestamp: now,
      success,
      latency,
      error
    });
    
    // Update consecutive counters
    if (success) {
      state.consecutiveSuccesses++;
      state.consecutiveFailures = 0;
    } else {
      state.consecutiveFailures++;
      state.consecutiveSuccesses = 0;
    }
    
    // Clean up old requests (outside monitoring window)
    const cutoff = now - this.config.monitoringWindow;
    state.requests = state.requests.filter(r => r.timestamp >= cutoff);
    
    // Check for immediate alerts
    this.checkForAlerts(provider, state);
    
    this.emit('requestRecorded', { provider, success, latency });
  }

  /**
   * Get health metrics for a specific provider
   */
  getMetrics(provider: string): HealthMetrics | null {
    const state = this.providers.get(provider);
    if (!state) {
      return null;
    }
    
    return this.calculateMetrics(provider, state);
  }

  /**
   * Get metrics for all providers
   */
  getAllMetrics(): Map<string, HealthMetrics> {
    const metrics = new Map<string, HealthMetrics>();
    
    this.providers.forEach((state, provider) => {
      metrics.set(provider, this.calculateMetrics(provider, state));
    });
    
    return metrics;
  }

  /**
   * Get metrics for a specific provider (all models)
   */
  getProviderMetrics(provider: string): HealthMetrics[] {
    const metrics: HealthMetrics[] = [];
    
    this.providers.forEach((state, key) => {
      if (state.provider === provider) {
        metrics.push(this.calculateMetrics(key, state));
      }
    });
    
    return metrics;
  }

  /**
   * Get overall health summary
   */
  getHealthSummary(): {
    totalProviders: number;
    healthyProviders: number;
    unhealthyProviders: number;
    averageHealthScore: number;
  } {
    const allMetrics = Array.from(this.getAllMetrics().values());
    
    return {
      totalProviders: allMetrics.length,
      healthyProviders: allMetrics.filter(m => m.isHealthy).length,
      unhealthyProviders: allMetrics.filter(m => !m.isHealthy).length,
      averageHealthScore: allMetrics.length > 0 
        ? allMetrics.reduce((sum, m) => sum + m.healthScore, 0) / allMetrics.length 
        : 0
    };
  }

  /**
   * Get current alerts
   */
  getAlerts(includeResolved = true): HealthAlert[] {
    const alerts = Array.from(this.alerts.values());
    
    if (includeResolved) {
      return alerts;
    }
    
    return alerts.filter(alert => !alert.resolved);
  }

  /**
   * Get health score for a provider
   */
  getHealthScore(provider: string): number {
    const metrics = this.getMetrics(provider);
    return metrics ? metrics.healthScore : 0;
  }

  /**
   * Check if a provider is healthy
   */
  isHealthy(provider: string): boolean {
    const metrics = this.getMetrics(provider);
    return metrics ? metrics.isHealthy : false;
  }

  /**
   * Reset metrics for a provider
   */
  resetMetrics(provider: string): void {
    const state = this.providers.get(provider);
    if (state) {
      state.requests = [];
      state.consecutiveSuccesses = 0;
      state.consecutiveFailures = 0;
      state.lastHealthCheck = 0;
      
      // Clear related alerts
      this.alerts.forEach((alert, alertId) => {
        if (alert.provider === provider) {
          this.alerts.delete(alertId);
        }
      });
      
      apiLogger.info('Health metrics reset for provider', { provider });
    }
  }

  /**
   * Private helper methods
   */
  
  private calculateMetrics(provider: string, state: ProviderHealthState): HealthMetrics {
    const now = Date.now();
    const windowStart = now - this.config.monitoringWindow;
    
    // Filter requests to monitoring window
    const recentRequests = state.requests.filter(r => r.timestamp >= windowStart);
    
    const totalRequests = recentRequests.length;
    const totalSuccesses = recentRequests.filter(r => r.success).length;
    const totalFailures = totalRequests - totalSuccesses;
    const successRate = totalRequests > 0 ? totalSuccesses / totalRequests : 1;
    
    // Calculate average latency
    const averageLatency = totalRequests > 0 
      ? recentRequests.reduce((sum, r) => sum + r.latency, 0) / totalRequests 
      : 0;
    
    // Calculate health score (0-100)
    let healthScore = 100;
    
    if (totalRequests >= this.config.minRequestsForScore) {
      // Success rate component (70% weight)
      healthScore *= 0.7 * successRate;
      
      // Latency component (20% weight)
      const latencyScore = Math.max(0, 1 - (averageLatency / this.config.latencyThreshold));
      healthScore += 20 * latencyScore;
      
      // Consecutive failures penalty (10% weight)
      const failurePenalty = Math.min(1, state.consecutiveFailures / this.config.consecutiveFailureThreshold);
      healthScore += 10 * (1 - failurePenalty);
    }
    
    // Determine status
    let status: HealthMetrics['status'];
    if (state.consecutiveFailures >= this.config.consecutiveFailureThreshold) {
      status = 'critical';
    } else if (successRate >= this.config.healthyThreshold && averageLatency <= this.config.latencyThreshold) {
      status = 'healthy';
    } else if (successRate >= this.config.degradedThreshold) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    const isHealthy = status === 'healthy' || status === 'degraded';
    
    // Calculate uptime
    const uptime = this.calculateUptime(recentRequests);
    
    // Get timing information
    const lastRequestTime = recentRequests.length > 0 
      ? Math.max(...recentRequests.map(r => r.timestamp)) 
      : 0;
    
    const successfulRequests = recentRequests.filter(r => r.success);
    const lastSuccessTime = successfulRequests.length > 0 
      ? Math.max(...successfulRequests.map(r => r.timestamp)) 
      : 0;
    
    const failedRequests = recentRequests.filter(r => !r.success);
    const lastFailureTime = failedRequests.length > 0 
      ? Math.max(...failedRequests.map(r => r.timestamp)) 
      : 0;
    
    return {
      provider: state.provider,
      model: state.model,
      isHealthy,
      successRate,
      averageLatency,
      healthScore,
      status,
      totalRequests,
      totalSuccesses,
      totalFailures,
      consecutiveSuccesses: state.consecutiveSuccesses,
      consecutiveFailures: state.consecutiveFailures,
      lastRequestTime,
      lastSuccessTime,
      lastFailureTime,
      uptime,
      windowStart,
      windowEnd: now
    };
  }

  private calculateUptime(requests: RequestDataPoint[]): number {
    if (requests.length === 0) return 100;
    
    // Simple uptime calculation based on success rate over time
    const successCount = requests.filter(r => r.success).length;
    return (successCount / requests.length) * 100;
  }

  private performHealthChecks(): void {
    this.providers.forEach(async (state, provider) => {
      if (!state.isRegistered) return;
      
      try {
        // Perform health check (this would call provider.validateConfig() in real implementation)
        const isHealthy = await this.checkProviderHealth(state.provider);
        
        // Record health check as a request
        this.recordRequest(provider, isHealthy, 0);
        
        state.lastHealthCheck = Date.now();
      } catch (error) {
        apiLogger.error('Health check failed', { provider, error });
        this.recordRequest(provider, false, 0, 'Health check failed');
      }
    });
  }

  private async checkProviderHealth(provider: string): Promise<boolean> {
    // In a real implementation, this would call the provider's validateConfig method
    // For now, return true as a placeholder
    return true;
  }

  private checkForAlerts(provider: string, state: ProviderHealthState): void {
    const metrics = this.calculateMetrics(provider, state);
    
    // Check for critical consecutive failures
    if (state.consecutiveFailures >= this.config.consecutiveFailureThreshold) {
      this.createAlert(
        provider,
        'critical',
        `Provider ${provider} has ${state.consecutiveFailures} consecutive failures`
      );
    }
    
    // Check for unhealthy status
    if (!metrics.isHealthy && metrics.totalRequests >= this.config.minRequestsForScore) {
      this.createAlert(
        provider,
        'high',
        `Provider ${provider} is unhealthy (${(metrics.successRate * 100).toFixed(1)}% success rate)`
      );
    }
    
    // Check for high latency
    if (metrics.averageLatency > this.config.latencyThreshold) {
      this.createAlert(
        provider,
        'medium',
        `Provider ${provider} has high latency (${metrics.averageLatency}ms average)`
      );
    }
  }

  private createAlert(provider: string, severity: HealthAlert['severity'], message: string): void {
    const alertId = `${provider}-${severity}-${Date.now()}`;
    
    // Check if similar alert already exists
    const existingAlert = Array.from(this.alerts.values()).find(
      alert => alert.provider === provider && alert.severity === severity && !alert.resolved
    );
    
    if (existingAlert) {
      return; // Don't create duplicate alerts
    }
    
    const alert: HealthAlert = {
      id: alertId,
      provider,
      severity,
      message,
      timestamp: new Date(),
      resolved: false
    };
    
    this.alerts.set(alertId, alert);
    
    this.emit('alertCreated', alert);
    
    apiLogger.warn('Health alert created', {
      provider,
      severity,
      message
    });
  }

  private cleanupOldData(): void {
    const now = Date.now();
    const cutoff = now - this.config.monitoringWindow * 2; // Keep 2x monitoring window
    
    // Clean up old request data
    this.providers.forEach((state) => {
      state.requests = state.requests.filter(r => r.timestamp >= cutoff);
    });
    
    // Clean up old alerts (keep for 24 hours)
    const alertCutoff = now - 24 * 60 * 60 * 1000;
    this.alerts.forEach((alert, alertId) => {
      if (alert.timestamp.getTime() < alertCutoff) {
        this.alerts.delete(alertId);
      }
    });
    
    apiLogger.debug('Health monitor cleanup completed');
  }
}