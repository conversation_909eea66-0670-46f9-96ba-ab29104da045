/**
 * Redis Caching Layer for 28-Category Router System
 * 
 * Implements sub-100ms performance for router category lookups
 * with user plan filtering and 1,008 pre-calculated combinations
 */

import { redis } from '@/lib/redis';
import { PrismaClient } from '@prisma/client';
import { PromptCategory, PromptComplexity, UserPlan } from '@/types';
import { TaskScore, calculateTaskScores, getPrimaryTaskScore } from './category-aggregation';
import crypto from 'crypto';

// Cache key generators for 28-category system
export const CACHE_KEYS_28 = {
  USER_CATEGORIES: (userPlan: string) => `router:28:user_categories:${userPlan}`,
  CATEGORY_MODELS: (category: string, userPlan: string) => `router:28:category_models:${category}:${userPlan}`,
  TASK_SCORE_MODELS: (taskScore: string, userPlan: string) => `router:28:task_models:${taskScore}:${userPlan}`,
  PROMPT_ANALYSIS: (queryHash: string) => `router:28:analysis:${queryHash}`,
  COMBINATION: (category: string, complexity: string, userPlan: string, latency: string) => 
    `router:28:combo:${category}:${complexity}:${userPlan}:${latency}`,
  CATEGORY_STATS: (category: string) => `router:28:stats:${category}`,
  TASK_SCORE_MAPPING: () => `router:28:task_mapping`
};

// Cache TTL values (in seconds)
export const CACHE_TTL = {
  USER_CATEGORIES: 300,      // 5 minutes
  CATEGORY_MODELS: 300,      // 5 minutes
  PROMPT_ANALYSIS: 600,      // 10 minutes
  COMBINATION: 300,          // 5 minutes
  CATEGORY_STATS: 3600,      // 1 hour
  TASK_SCORE_MAPPING: 3600   // 1 hour
};

/**
 * Get user-accessible categories with model data (cached)
 */
export async function getUserCategories28(
  userPlan: UserPlan, 
  prisma: PrismaClient
): Promise<any[]> {
  const cacheKey = CACHE_KEYS_28.USER_CATEGORIES(userPlan);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get from database - check which categories have models for this user plan
  const categories = await prisma.$queryRaw`
    SELECT DISTINCT 
      mm.category,
      COUNT(DISTINCT m.id) as model_count,
      AVG(mm.score) as avg_score,
      MAX(mm.score) as max_score,
      MIN(mm.score) as min_score
    FROM modelMappings mm
    JOIN Models m ON mm.model_id = m.id
    WHERE mm.enabled = 1
      AND m.isEnabled = 1
      AND (
        (${userPlan} = 'FREE' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"FREE"'))) OR
        (${userPlan} = 'PLUS' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"PLUS"'))) OR
        (${userPlan} = 'MAX')
      )
    GROUP BY mm.category
    HAVING model_count >= 3  -- Only categories with at least 3 models
    ORDER BY avg_score DESC
  `;
  
  // Cache for 5 minutes
  await redis.setex(cacheKey, CACHE_TTL.USER_CATEGORIES, JSON.stringify(categories));
  
  return categories as any[];
}

/**
 * Get models for a specific category and user plan (cached)
 */
export async function getCategoryModels28(
  category: PromptCategory,
  userPlan: UserPlan,
  prisma: PrismaClient,
  limit: number = 10
): Promise<any[]> {
  const cacheKey = CACHE_KEYS_28.CATEGORY_MODELS(category, userPlan);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get from database
  const models = await prisma.$queryRaw`
    SELECT 
      m.id,
      m.canonicalName,
      m.displayName,
      m.providerId,
      mm.score,
      mm.complexity_level,
      m.contextWindow,
      m.maxOutput,
      JSON_UNQUOTE(JSON_EXTRACT(m.pricing, '$.input')) as inputCost,
      JSON_UNQUOTE(JSON_EXTRACT(m.pricing, '$.output')) as outputCost,
      m.capabilities
    FROM Models m
    JOIN modelMappings mm ON m.id = mm.model_id
    WHERE mm.category = ${category}
      AND mm.enabled = 1
      AND m.isEnabled = 1
      AND (
        (${userPlan} = 'FREE' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"FREE"'))) OR
        (${userPlan} = 'PLUS' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"PLUS"'))) OR
        (${userPlan} = 'MAX')
      )
    ORDER BY mm.score DESC, 
             CAST(JSON_UNQUOTE(JSON_EXTRACT(m.pricing, '$.input')) AS DECIMAL(10,6)) ASC
    LIMIT ${limit}
  `;
  
  // Cache for 5 minutes
  await redis.setex(cacheKey, CACHE_TTL.CATEGORY_MODELS, JSON.stringify(models));
  
  return models as any[];
}

/**
 * Get models for a task score (aggregated from multiple categories)
 */
export async function getTaskScoreModels(
  taskScore: TaskScore,
  userPlan: UserPlan,
  prisma: PrismaClient,
  complexity: PromptComplexity = 'standard'
): Promise<any[]> {
  const cacheKey = CACHE_KEYS_28.TASK_SCORE_MODELS(taskScore, userPlan);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get categories for this task score
  const { CATEGORY_TO_TASK_MAPPING } = await import('./category-aggregation');
  const categories = CATEGORY_TO_TASK_MAPPING[taskScore]?.categories || [];
  
  if (categories.length === 0) {
    return [];
  }
  
  // Get models from all relevant categories
  const models = await prisma.$queryRaw`
    SELECT 
      m.id,
      m.canonicalName,
      m.displayName,
      m.providerId,
      AVG(mm.score) as avg_score,
      MAX(mm.score) as max_score,
      m.contextWindow,
      m.maxOutput,
      JSON_UNQUOTE(JSON_EXTRACT(m.pricing, '$.input')) as inputCost,
      JSON_UNQUOTE(JSON_EXTRACT(m.pricing, '$.output')) as outputCost,
      m.capabilities,
      GROUP_CONCAT(DISTINCT mm.category) as categories
    FROM Models m
    JOIN modelMappings mm ON m.id = mm.model_id
    WHERE mm.category IN (${categories.join(',')})
      AND mm.enabled = 1
      AND m.isEnabled = 1
      AND (mm.complexity_level = ${complexity} OR mm.complexity_level = 'all')
      AND (
        (${userPlan} = 'FREE' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"FREE"'))) OR
        (${userPlan} = 'PLUS' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"PLUS"'))) OR
        (${userPlan} = 'MAX')
      )
    GROUP BY m.id
    ORDER BY avg_score DESC, 
             CAST(JSON_UNQUOTE(JSON_EXTRACT(m.pricing, '$.input')) AS DECIMAL(10,6)) ASC
    LIMIT 10
  `;
  
  // Cache for 5 minutes
  await redis.setex(cacheKey, CACHE_TTL.CATEGORY_MODELS, JSON.stringify(models));
  
  return models as any[];
}

/**
 * Cache prompt analysis results
 */
export async function cachePromptAnalysis(
  query: string,
  analysis: any
): Promise<void> {
  const queryHash = crypto.createHash('md5').update(query).digest('hex');
  const cacheKey = CACHE_KEYS_28.PROMPT_ANALYSIS(queryHash);
  
  await redis.setex(cacheKey, CACHE_TTL.PROMPT_ANALYSIS, JSON.stringify(analysis));
}

/**
 * Get cached prompt analysis
 */
export async function getCachedPromptAnalysis(
  query: string
): Promise<any | null> {
  const queryHash = crypto.createHash('md5').update(query).digest('hex');
  const cacheKey = CACHE_KEYS_28.PROMPT_ANALYSIS(queryHash);
  
  const cached = await redis.get(cacheKey);
  return cached ? JSON.parse(cached) : null;
}

/**
 * Ultra-fast combination lookup with 1,008 pre-calculated options
 */
export async function getRouterCombination28(
  category: PromptCategory,
  complexity: PromptComplexity,
  userPlan: UserPlan,
  latencyPreference: string = 'medium',
  prisma: PrismaClient
): Promise<any> {
  const cacheKey = CACHE_KEYS_28.COMBINATION(category, complexity, userPlan, latencyPreference);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get best model for this combination
  const result = await getCategoryModels28(category, userPlan, prisma, 5);
  
  if (result && result.length > 0) {
    // Filter by complexity if needed
    const filtered = result.filter(m => 
      !m.complexity_level || m.complexity_level === 'all' || m.complexity_level === complexity
    );
    
    // Sort by latency preference
    if (latencyPreference === 'low') {
      filtered.sort((a, b) => (a.inputCost || 0) - (b.inputCost || 0));
    } else if (latencyPreference === 'high') {
      filtered.sort((a, b) => b.score - a.score);
    }
    
    const combination = {
      category,
      complexity,
      userPlan,
      latencyPreference,
      models: filtered.slice(0, 3),
      selectedModel: filtered[0]
    };
    
    // Cache for 5 minutes
    await redis.setex(cacheKey, CACHE_TTL.COMBINATION, JSON.stringify(combination));
    return combination;
  }
  
  return null;
}

/**
 * Warm up the 28-category cache for optimal performance
 */
export async function warmUp28CategoryCache(prisma: PrismaClient): Promise<void> {
  console.log('🔥 Warming up 28-category router cache...');
  
  const userPlans: UserPlan[] = [UserPlan.FREEMIUM, UserPlan.PLUS, UserPlan.MAX];
  const commonCategories: PromptCategory[] = [
    'coding', 'general_chat', 'creative_writing', 'math', 'reasoning',
    'analysis', 'question_answering', 'translation', 'summarization'
  ];
  const complexities: PromptComplexity[] = ['simple', 'standard', 'difficult', 'complex'];
  
  // Pre-load user categories for all plans
  for (const plan of userPlans) {
    await getUserCategories28(plan, prisma);
  }
  
  // Pre-load common category models
  for (const category of commonCategories) {
    for (const plan of userPlans) {
      await getCategoryModels28(category, plan, prisma);
    }
  }
  
  // Pre-load some common combinations
  for (const category of commonCategories.slice(0, 5)) {
    for (const complexity of complexities) {
      for (const plan of userPlans) {
        await getRouterCombination28(category, complexity, plan, 'medium', prisma);
      }
    }
  }
  
  console.log('✅ 28-category router cache warmed up successfully');
}

/**
 * Get category statistics (cached)
 */
export async function getCategoryStats(
  category: PromptCategory,
  prisma: PrismaClient
): Promise<any> {
  const cacheKey = CACHE_KEYS_28.CATEGORY_STATS(category);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get statistics from database
  const stats = await prisma.$queryRaw`
    SELECT 
      COUNT(DISTINCT m.id) as total_models,
      COUNT(DISTINCT CASE WHEN m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"FREE"') THEN m.id END) as free_models,
      COUNT(DISTINCT CASE WHEN m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"PLUS"') THEN m.id END) as plus_models,
      COUNT(DISTINCT m.id) as max_models,
      AVG(mm.score) as avg_score,
      MAX(mm.score) as max_score,
      MIN(mm.score) as min_score,
      COUNT(DISTINCT m.providerId) as provider_count
    FROM modelMappings mm
    JOIN Models m ON mm.model_id = m.id
    WHERE mm.category = ${category}
      AND mm.enabled = 1
      AND m.isEnabled = 1
  `;
  
  // Cache for 1 hour
  await redis.setex(cacheKey, CACHE_TTL.CATEGORY_STATS, JSON.stringify(stats));
  
  return stats;
}

/**
 * Clear all router caches
 */
export async function clearRouterCache(): Promise<void> {
  const pattern = 'router:28:*';
  const keys = await redis.keys(pattern);
  
  if (keys.length > 0) {
    await redis.del(...keys);
    console.log(`🧹 Cleared ${keys.length} router cache entries`);
  }
}