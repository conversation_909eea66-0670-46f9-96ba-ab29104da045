/**
 * 28-CATEGORY ROUTER INTEGRATION WITH ULTRA-FAST CACHING
 * 
 * Integrates the 28-category system with mathematical aggregation,
 * Redis caching for sub-1ms lookups, and comprehensive user data caching
 * 
 * Performance targets:
 * - Redis lookups: ~0.2ms
 * - User data: ~0.1ms (cached)
 * - Category mapping: ~0.1ms (cached)
 * - Total overhead: < 1ms (excluding LLM calls)
 */

import { IntelligentCategoryRouter } from '@/lib/ai/router';
import { 
  getUserCategories28,
  getCategoryModels28,
  getTaskScoreModels,
  getRouterCombination28,
  cachePromptAnalysis,
  getCachedPromptAnalysis,
  warmUp28CategoryCache,
  getCategoryStats
} from './redis-cache';
import {
  getCachedUserProfile,
  getCachedUserPlan,
  checkMessageLimit,
  cacheUserModelAccess,
  cacheUserCategories,
  checkRateLimit,
  warmUserCache,
  getUserLastModel,
  cacheUserLastModel
} from './user-cache';
import {
  calculateTaskScores,
  getPrimaryTaskScore,
  TASK_SCORE_NAMES,
  getCategoriesForTask
} from './category-aggregation';
import { PromptAnalysis, RouterInput, RouterDecision, UserPlan } from '@/types';
import { PrismaClient } from '@prisma/client';
import { redis } from '@/lib/redis';
import crypto from 'crypto';

// Enhanced router configuration for 28-category system
export interface Router28Config {
  enableUserCaching: boolean;
  enableCategoryCache: boolean;
  enablePromptCache: boolean;
  preWarmCache: boolean;
  cacheDebugMode: boolean;
}

export const ROUTER_28_CONFIG: Router28Config = {
  enableUserCaching: true,
  enableCategoryCache: true,
  enablePromptCache: true,
  preWarmCache: true,
  cacheDebugMode: process.env.ROUTER_DEBUG === 'true'
};

/**
 * Enhanced IntelligentCategoryRouter with 28-category system
 */
export class EnhancedRouter28 extends IntelligentCategoryRouter {
  private userCacheHits = 0;
  private userCacheMisses = 0;
  private categoryCacheHits = 0;
  private categoryCacheMisses = 0;
  
  constructor() {
    super();
    
    // Pre-warm caches on initialization
    if (ROUTER_28_CONFIG.preWarmCache) {
      this.warmupCaches();
    }
  }
  
  /**
   * Pre-warm all caches for optimal performance
   */
  private async warmupCaches(): Promise<void> {
    try {
      console.log('🔥 Warming up 28-category router caches...');
      const start = performance.now();
      
      // Warm category cache
      await warmUp28CategoryCache(this.prisma);
      
      // Pre-cache common user plans
      const plans: UserPlan[] = [UserPlan.FREEMIUM, UserPlan.PLUS, UserPlan.MAX];
      for (const plan of plans) {
        await getUserCategories28(plan, this.prisma);
      }
      
      const time = performance.now() - start;
      console.log(`✅ Cache warmup completed in ${time.toFixed(0)}ms`);
    } catch (error) {
      console.error('❌ Cache warmup failed:', error);
    }
  }
  
  /**
   * ULTRA-FAST ROUTE METHOD WITH 28-CATEGORY SYSTEM
   * 
   * Performance breakdown:
   * - User data lookup: ~0.1ms (cached)
   * - Prompt cache check: ~0.2ms
   * - Category analysis: ~50ms (Gemini Flash Lite)
   * - Model selection: ~0.5ms (cached combinations)
   * - Total: ~51ms (excluding actual LLM response)
   */
  async route(input: RouterInput): Promise<RouterDecision> {
    const startTime = performance.now();
    const requestId = crypto.randomUUID();
    
    try {
      // STEP 1: Ultra-fast user data retrieval (all cached)
      const userStart = performance.now();
      const [userPlan, userProfile, messageData, rateLimit] = await Promise.all([
        this.getCachedUserPlanFast(input.userId || 'anonymous'),
        input.userId ? getCachedUserProfile(input.userId, this.prisma) : null,
        input.userId ? checkMessageLimit(input.userId, input.userPlan as UserPlan) : null,
        input.userId ? checkRateLimit(input.userId) : null
      ]);
      const userTime = performance.now() - userStart;
      
      // Check rate limit
      if (rateLimit && !rateLimit.allowed) {
        throw new Error(`Rate limit exceeded. Try again at ${rateLimit.resetsAt.toISOString()}`);
      }
      
      // Check message limit
      if (messageData && messageData.remaining <= 0) {
        throw new Error(`Daily message limit reached. Resets at ${messageData.resetsAt.toISOString()}`);
      }
      
      // STEP 2: Check prompt analysis cache
      const cacheStart = performance.now();
      let analysis: PromptAnalysis | null = null;
      
      if (ROUTER_28_CONFIG.enablePromptCache) {
        analysis = await getCachedPromptAnalysis(input.query);
        if (analysis) {
          this.categoryCacheHits++;
          console.log(`🎯 Prompt cache HIT: ${(performance.now() - cacheStart).toFixed(1)}ms`);
        }
      }
      
      // STEP 3: Analyze prompt if not cached (only bottleneck)
      if (!analysis) {
        this.categoryCacheMisses++;
        const analysisStart = performance.now();
        
        // Use the configured analysis method (Gemini Flash Lite by default)
        analysis = await this.fastGeminiFlashLiteAnalysis(input);
        
        // Add 28-category enhancements
        if (analysis.category_scores) {
          const taskScores = calculateTaskScores(analysis.category_scores);
          const primaryTask = getPrimaryTaskScore(analysis.category_scores);
          
          analysis = {
            ...analysis,
            task_scores: taskScores,
            primary_task_score: primaryTask,
            task_score_name: TASK_SCORE_NAMES[primaryTask]
          };
        }
        
        const analysisTime = performance.now() - analysisStart;
        console.log(`🧠 Prompt analysis: ${analysisTime.toFixed(0)}ms`);
        
        // Cache the analysis
        if (ROUTER_28_CONFIG.enablePromptCache) {
          await cachePromptAnalysis(input.query, analysis);
        }
      }
      
      // STEP 4: Get cached category combination (sub-1ms)
      const comboStart = performance.now();
      const combination = await getRouterCombination28(
        analysis.primary_category,
        analysis.complexity,
        userPlan || UserPlan.FREEMIUM,
        analysis.requirements.latency_sensitivity || 'medium',
        this.prisma
      );
      const comboTime = performance.now() - comboStart;
      
      if (!combination || !combination.selectedModel) {
        // Fallback to standard model selection
        return await this.getFallbackSelection(analysis, input);
      }
      
      // STEP 5: Track user's last model (async, don't wait)
      if (input.userId) {
        cacheUserLastModel(input.userId, combination.selectedModel.canonicalName).catch(() => {});
      }
      
      // Build router decision
      const totalTime = performance.now() - startTime;
      
      const decision: RouterDecision = {
        modelId: combination.selectedModel.id,
        model: combination.selectedModel,
        provider: combination.selectedModel.canonicalName.split('/')[0],
        selectedModel: combination.selectedModel.canonicalName,
        category: analysis.primary_category,
        reason: `28-category routing: ${analysis.primary_category} → ${combination.selectedModel.displayName}`,
        confidence: analysis.confidence,
        reasoning: analysis.reasoning || `28-category router: ${analysis.primary_category} → ${combination.selectedModel.displayName}`,
        alternatives: combination.models?.slice(1, 4) || [],
        performanceMetrics: {
          routingTime: totalTime,
          modelSelectionTime: comboTime,
          cacheHitRate: analysis.cached ? 1 : 0
        }
      };
      
      // Log performance in debug mode
      if (ROUTER_28_CONFIG.cacheDebugMode) {
        console.log('🚀 28-Category Router Performance:', {
          userDataMs: userTime.toFixed(1),
          analysisMs: analysis.cached ? 'CACHED' : (comboTime - (performance.now() - comboStart)).toFixed(1),
          selectionMs: comboTime.toFixed(1),
          totalMs: totalTime.toFixed(1),
          cacheHitRate: `${((this.categoryCacheHits / (this.categoryCacheHits + this.categoryCacheMisses)) * 100).toFixed(1)}%`
        });
      }
      
      return decision;
      
    } catch (error) {
      console.error('[Router28] Error:', error);
      return await this.getFallbackSelection(
        this.getFallbackAnalysis(input),
        input
      );
    }
  }
  
  /**
   * Ultra-fast cached user plan lookup
   */
  private async getCachedUserPlanFast(userId: string): Promise<UserPlan> {
    const cacheKey = `user:plan:${userId}`;
    
    try {
      const cached = await redis.get(cacheKey);
      if (cached) {
        this.userCacheHits++;
        return cached as UserPlan;
      }
      
      this.userCacheMisses++;
      const plan = await getCachedUserPlan(userId, this.prisma);
      return plan;
    } catch (error) {
      return UserPlan.FREEMIUM; // Safe default
    }
  }
  
  /**
   * Get category statistics for debugging
   */
  async getCategoryDebugStats(): Promise<any> {
    const categories = [
      'coding', 'debugging', 'creative_writing', 'general_chat',
      'reasoning', 'math', 'analysis', 'translation', 'summarization'
    ];
    
    const stats = await Promise.all(
      categories.map(async cat => ({
        category: cat,
        stats: await getCategoryStats(cat as any, this.prisma)
      }))
    );
    
    return {
      categories: stats,
      cacheStats: {
        userHitRate: this.userCacheHits / (this.userCacheHits + this.userCacheMisses),
        categoryHitRate: this.categoryCacheHits / (this.categoryCacheHits + this.categoryCacheMisses)
      }
    };
  }
}

// Export enhanced router instance
export const enhancedRouter28 = new EnhancedRouter28();

// Helper function to test performance
export async function testRouter28Performance(): Promise<void> {
  const testQueries = [
    "Write a Python function to sort a list",
    "Create a story about a time traveler",
    "What's the weather like today?",
    "Solve this integral: ∫x²dx",
    "Analyze market trends for EVs"
  ];
  
  console.log('🧪 Testing 28-Category Router Performance...\n');
  
  for (const query of testQueries) {
    const start = performance.now();
    
    const result = await enhancedRouter28.route({
      query,
      conversationLength: 0,
      hasCode: false,
      userPlan: 'MAX',
      userId: 'test-user-123'
    });
    
    const time = performance.now() - start;
    console.log(`Query: "${query.substring(0, 40)}..."`);
    console.log(`  Category: ${result.category}`);
    console.log(`  Model: ${result.selectedModel}`);
    console.log(`  Time: ${time.toFixed(1)}ms\n`);
  }
  
  // Show cache stats
  const stats = await enhancedRouter28.getCategoryDebugStats();
  console.log('📊 Cache Statistics:', stats.cacheStats);
}