import { UserPlan } from '@/types';

/**
 * Maps database plan values to UserPlan enum
 * Database uses: FREE, PLUS, MAX
 * Enum uses: FREE (not logged in), FREEMIUM (logged in free), PLUS, MAX
 */
export function mapDatabasePlanToEnum(dbPlan: string): UserPlan {
  switch (dbPlan) {
    case 'FREE':
      // In database, FREE means logged-in free users (FREEMIUM in enum)
      return UserPlan.FREEMIUM;
    case 'PLUS':
      return UserPlan.PLUS;
    case 'MAX':
      return UserPlan.MAX;
    case 'ADVANCED':
      return UserPlan.ADVANCED;
    case 'ENTERPRISE':
      return UserPlan.ENTERPRISE;
    default:
      // Default to FREEMIUM for unknown plans
      return UserPlan.FREEMIUM;
  }
}

/**
 * Maps UserPlan enum to database values
 */
export function mapEnumToDatabase(enumPlan: UserPlan): string {
  switch (enumPlan) {
    case UserPlan.FREE:
      // Not logged in users don't have database records
      return 'FREE';
    case UserPlan.FREEMIUM:
      // Logged in free users are stored as 'FREE' in database
      return 'FREE';
    case UserPlan.PLUS:
      return 'PLUS';
    case UserPlan.ADVANCED:
      return 'ADVANCED';
    case UserPlan.MAX:
      return 'MAX';
    case UserPlan.ENTERPRISE:
      return 'ENTERPRISE';
    default:
      return 'FREE';
  }
}

/**
 * Database plan values used in queries
 */
export const DATABASE_PLANS = ['FREE', 'PLUS', 'MAX'] as const;
export type DatabasePlan = typeof DATABASE_PLANS[number];

/**
 * Maps UserPlan enum to ModelPlanRules planIds format
 * Used for JSON_CONTAINS checks in queries
 */
export function mapUserPlanToDatabase(plan: UserPlan): string {
  const mapping = {
    [UserPlan.FREE]: 'plan_free',
    [UserPlan.FREEMIUM]: 'plan_freemium',
    [UserPlan.PLUS]: 'plan_plus',
    [UserPlan.ADVANCED]: 'plan_advanced',
    [UserPlan.MAX]: 'plan_max',
    [UserPlan.ENTERPRISE]: 'plan_enterprise'
  };
  return mapping[plan];
}