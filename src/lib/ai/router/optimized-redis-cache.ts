/**
 * Optimized Redis Caching for 28-Category Router
 * 
 * Works with existing Models table that has categoryScores in extendedMetadata
 * Achieves sub-1ms lookups by caching pre-computed category/model mappings
 */

import { redis } from '@/lib/redis';
import { PrismaClient, Prisma } from '@prisma/client';
import { PromptCategory, PromptComplexity, UserPlan } from '@/types';
import { DATABASE_PLANS, mapEnumToDatabase, mapDatabasePlanToEnum, mapUserPlanToDatabase, type DatabasePlan } from './plan-mapping';
import { selectWithVariety, type ModelForSelection } from './variety-selection';
import crypto from 'crypto';

// Cache keys optimized for the existing structure
export const ROUTER_CACHE_KEYS = {
  // Category → Models mapping (the most critical cache)
  CATEGORY_MODELS: (category: string, plan: string) => `router:cat:${category}:${plan}`,
  
  // User's available categories
  USER_CATEGORIES: (plan: string) => `router:ucats:${plan}`,
  
  // Prompt analysis cache
  PROMPT_ANALYSIS: (queryHash: string) => `router:pa:${queryHash}`,
  
  // Pre-computed best model for category+complexity+plan
  BEST_MODEL: (category: string, complexity: string, plan: string, ultraMode: boolean = false) => 
    `router:best:${category}:${complexity}:${plan}:${ultraMode ? 'ultra' : 'normal'}`,
  
  // Model details cache (to avoid repeated DB lookups)
  MODEL_DETAILS: (modelId: string) => `router:model:${modelId}`,
  
  // Category statistics
  CATEGORY_STATS: (category: string) => `router:stats:${category}`,
  
  // Session model stickiness (24 hour TTL)
  SESSION_MODEL: (conversationId: string, category: string, complexity: string) =>
    `session:model:${conversationId}:${category}:${complexity}`,
  
  // Conversation context metadata (24 hour TTL)
  CONVERSATION_CONTEXT: (conversationId: string) =>
    `session:context:${conversationId}`
};

// Cache TTL values (in seconds)
export const ROUTER_CACHE_TTL = {
  CATEGORY_MODELS: 600,    // 10 minutes
  USER_CATEGORIES: 600,    // 10 minutes
  PROMPT_ANALYSIS: 900,    // 15 minutes
  BEST_MODEL: 300,         // 5 minutes
  MODEL_DETAILS: 1800,     // 30 minutes
  CATEGORY_STATS: 3600     // 1 hour
};

interface CategoryModel {
  id: string;
  canonicalName: string;
  displayName: string;
  categoryScore: number;
  inputCost: number;
  outputCost: number;
  contextWindow?: number;
  supportsVision?: boolean;
  supportsFunctionCalling?: boolean;
  supportsWebSearch?: boolean;
  supportsReasoning?: boolean;
  supportsStreaming?: boolean;
  capabilities?: string[];
}

interface ModelDetails {
  id: string;
  canonicalName: string;
  displayName: string;
  providerId: string;
  contextWindow: number;
  maxOutput: number;
  pricing: {
    input: number;
    output: number;
  };
  capabilities: string[];
  categoryScores: Record<string, number>;
  taskScores: Record<string, number>;
}

/**
 * Get models for a specific category from cache or database
 * This is the MOST CRITICAL function for performance
 */
export async function getCategoryModels(
  category: PromptCategory,
  userPlan: UserPlan,
  prisma: PrismaClient,
  limit: number = 10
): Promise<CategoryModel[]> {
  const cacheKey = ROUTER_CACHE_KEYS.CATEGORY_MODELS(category, userPlan);
  
  // Try cache first (should be ~0.2ms)
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Cache miss - query database using the existing categoryScores in extendedMetadata
  const categoryPath = `$.categoryScores.${category}.score`;
  const userPlanDatabase = mapUserPlanToDatabase(userPlan);
  
  const models = await prisma.$queryRaw<CategoryModel[]>`
    SELECT 
      m.id,
      m.canonicalName,
      m.displayName,
      CAST(JSON_UNQUOTE(JSON_EXTRACT(m.extendedMetadata, ${categoryPath})) AS DECIMAL(5,2)) as categoryScore,
      m.inputCostPer1M as inputCost,
      m.outputCostPer1M as outputCost,
      m.contextWindow,
      m.supportsVision,
      m.supportsFunctionCalling,
      m.supportsWebSearch,
      m.supportsReasoning,
      m.supportsStreaming
    FROM Models m
    INNER JOIN ModelPlanRules mpr ON mpr.modelId = m.id
    WHERE m.isEnabled = 1
      AND JSON_EXTRACT(m.extendedMetadata, ${categoryPath}) IS NOT NULL
      AND CAST(JSON_EXTRACT(m.extendedMetadata, ${categoryPath}) AS DECIMAL(5,2)) > 0
      AND mpr.isEnabled = 1
      AND mpr.ruleType = 'INCLUDE'
      AND (
        mpr.planIds IS NULL 
        OR JSON_CONTAINS(mpr.planIds, JSON_QUOTE(${userPlanDatabase}))
      )
    GROUP BY m.id
    ORDER BY 
      categoryScore DESC,
      m.inputCostPer1M ASC
    LIMIT ${limit}
  `;
  
  // Process models to include capabilities array
  const processedModels = models.map(m => ({
    ...m,
    capabilities: [
      m.supportsVision && 'vision',
      m.supportsFunctionCalling && 'function_calling',
      m.supportsWebSearch && 'web_search',
      m.supportsReasoning && 'reasoning',
      m.supportsStreaming && 'streaming'
    ].filter(Boolean) as string[]
  }));
  
  // Cache for 10 minutes
  await redis.setex(cacheKey, ROUTER_CACHE_TTL.CATEGORY_MODELS, JSON.stringify(processedModels));
  
  return processedModels;
}

/**
 * Get the single best model for a category+complexity+plan combination
 * This is what gets called most frequently during routing
 */
export async function getBestModelForCategory(
  category: PromptCategory,
  complexity: PromptComplexity,
  userPlan: UserPlan,
  prisma: PrismaClient,
  options?: {
    conversationId?: string;      // For session stickiness
    ultraMode?: boolean;          // Premium model override
    forceNewSelection?: boolean;  // Override stickiness
    enableVariety?: boolean;      // Enable variety selection
  }
): Promise<CategoryModel | null> {
  // Check for existing session selection first (for stickiness)
  if (options?.conversationId && !options?.forceNewSelection) {
    const sessionKey = ROUTER_CACHE_KEYS.SESSION_MODEL(options.conversationId, category, complexity);
    const existingSelection = await redis.get(sessionKey);
    
    if (existingSelection) {
      const model = JSON.parse(existingSelection);
      // Validate model still available for user's plan
      const isStillAvailable = await validateModelForPlan(model.id, userPlan, prisma);
      
      if (isStillAvailable) {
        // Update conversation context for tracking
        await updateConversationContext(options.conversationId, category, complexity, model);
        return model;
      }
      // If model no longer available, continue to select a new one
    }
  }
  
  // Determine effective plan (ultra mode gets MAX plan access)
  const effectivePlan = options?.ultraMode ? UserPlan.MAX : userPlan;
  
  // Use the updated cache key function
  const cacheKey = ROUTER_CACHE_KEYS.BEST_MODEL(category, complexity, effectivePlan, options?.ultraMode);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get top models for category
  const models = await getCategoryModels(category, effectivePlan, prisma, options?.enableVariety ? 20 : 5);
  
  if (models.length === 0) {
    return null;
  }
  
  let selected: CategoryModel;
  
  // Use variety selection if enabled
  if (options?.enableVariety) {
    // Get recent models from session context if available
    let recentModels: string[] = [];
    if (options.conversationId) {
      const contextKey = ROUTER_CACHE_KEYS.CONVERSATION_CONTEXT(options.conversationId);
      const context = await redis.get(contextKey);
      if (context) {
        const parsedContext = JSON.parse(context);
        recentModels = parsedContext.recentModels || [];
      }
    }
    
    // Convert CategoryModel to ModelForSelection format
    const modelsForSelection: ModelForSelection[] = models.map(m => ({
      id: m.id,
      canonicalName: m.canonicalName,
      displayName: m.displayName,
      categoryScore: m.categoryScore,
      inputCost: m.inputCost,
      outputCost: m.outputCost,
      contextWindow: m.contextWindow || 0
    }));
    
    const varietySelected = selectWithVariety(modelsForSelection, complexity, recentModels);
    if (varietySelected) {
      selected = models.find(m => m.id === varietySelected.id) || models[0];
    } else {
      selected = models[0];
    }
  } else {
    // Check if ultra mode is enabled (ignores cost for PLUS and above)
    if (options?.ultraMode && userPlan !== UserPlan.FREE && userPlan !== UserPlan.FREEMIUM) {
      // Ultra mode: Pick best model by score, ignoring cost
      // Note: Plan restrictions still apply - frontier models only available to MAX users
      selected = models[0]; // Already sorted by score DESC
    } else {
      // Normal mode: Cost-aware selection based on complexity
      switch (complexity) {
        case 'simple':
          // For simple tasks, prefer cheaper models
          selected = models.sort((a, b) => a.inputCost - b.inputCost)[0];
          break;
          
        case 'complex':
        case 'difficult':
          // For complex tasks, prefer highest scoring
          selected = models[0]; // Already sorted by score
          break;
          
        default: // 'standard'
          // Balance between cost and performance
          const balanced = models.slice(0, 3).sort((a, b) => {
            const scoreWeight = 0.8;
            const costWeight = 0.2;
            // Fix: Use reasonable cost scaling (divide by 10 instead of multiply by 1000)
            const aValue = (a.categoryScore * scoreWeight) - (a.inputCost * costWeight * 10);
            const bValue = (b.categoryScore * scoreWeight) - (b.inputCost * costWeight * 10);
            return bValue - aValue;
          });
          selected = balanced[0];
      }
    }
  }
  
  // Cache the selection for 5 minutes
  await redis.setex(cacheKey, ROUTER_CACHE_TTL.BEST_MODEL, JSON.stringify(selected));
  
  // Store in session if conversationId provided (24 hour TTL)
  if (options?.conversationId && selected) {
    const sessionKey = ROUTER_CACHE_KEYS.SESSION_MODEL(options.conversationId, category, complexity);
    await redis.setex(sessionKey, 86400, JSON.stringify(selected));
    
    // Update conversation context
    await updateConversationContext(options.conversationId, category, complexity, selected);
  }
  
  return selected;
}

/**
 * Get all categories available to a user plan
 */
export async function getUserAvailableCategories(
  userPlan: UserPlan,
  prisma: PrismaClient
): Promise<Array<{ category: string; modelCount: number; avgScore: number }>> {
  const cacheKey = ROUTER_CACHE_KEYS.USER_CATEGORIES(userPlan);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get all 28 categories
  const categories: PromptCategory[] = [
    'coding', 'debugging', 'creative_writing', 'general_chat', 'reasoning',
    'math', 'analysis', 'translation', 'summarization', 'question_answering',
    'data_analysis', 'tutorial', 'brainstorming', 'role_play', 'technical_writing',
    'business_writing', 'legal', 'medical', 'scientific', 'philosophical',
    'historical', 'current_events', 'personal_advice', 'image_generation',
    'image_analysis', 'multimodal', 'other', 'factual_qa'
  ];
  
  const categoryStats = await Promise.all(
    categories.map(async (category) => {
      const models = await getCategoryModels(category, userPlan, prisma, 100);
      if (models.length >= 3) { // Only include categories with at least 3 models
        const avgScore = models.reduce((sum, m) => sum + m.categoryScore, 0) / models.length;
        return {
          category,
          modelCount: models.length,
          avgScore: Math.round(avgScore)
        };
      }
      return null;
    })
  );
  
  const validCategories = categoryStats.filter(c => c !== null);
  
  // Cache for 10 minutes
  await redis.setex(cacheKey, ROUTER_CACHE_TTL.USER_CATEGORIES, JSON.stringify(validCategories));
  
  return validCategories;
}

/**
 * Cache prompt analysis result
 */
export async function cachePromptAnalysis(
  query: string,
  analysis: any
): Promise<void> {
  const queryHash = crypto.createHash('md5').update(query).digest('hex');
  const cacheKey = ROUTER_CACHE_KEYS.PROMPT_ANALYSIS(queryHash);
  
  await redis.setex(cacheKey, ROUTER_CACHE_TTL.PROMPT_ANALYSIS, JSON.stringify({
    ...analysis,
    cached: true,
    cachedAt: new Date().toISOString()
  }));
}

/**
 * Get cached prompt analysis
 */
export async function getCachedPromptAnalysis(
  query: string
): Promise<any | null> {
  const queryHash = crypto.createHash('md5').update(query).digest('hex');
  const cacheKey = ROUTER_CACHE_KEYS.PROMPT_ANALYSIS(queryHash);
  
  const cached = await redis.get(cacheKey);
  return cached ? JSON.parse(cached) : null;
}

/**
 * Get full model details (with all category scores)
 */
export async function getModelDetails(
  modelId: string,
  prisma: PrismaClient
): Promise<ModelDetails | null> {
  const cacheKey = ROUTER_CACHE_KEYS.MODEL_DETAILS(modelId);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get from database
  const model = await prisma.models.findUnique({
    where: { id: modelId },
    select: {
      id: true,
      canonicalName: true,
      displayName: true,
      providerId: true,
      contextWindow: true,
      maxOutput: true,
      inputCostPer1M: true,
      outputCostPer1M: true,
      supportsVision: true,
      supportsFunctionCalling: true,
      supportsWebSearch: true,
      supportsReasoning: true,
      supportsStreaming: true,
      extendedMetadata: true
    }
  });
  
  if (!model || !model.extendedMetadata) {
    return null;
  }
  
  const extendedMetadata = model.extendedMetadata as any;
  const details: ModelDetails = {
    id: model.id,
    canonicalName: model.canonicalName,
    displayName: model.displayName,
    providerId: model.providerId,
    contextWindow: model.contextWindow,
    maxOutput: model.maxOutput,
    pricing: {
      input: Number(model.inputCostPer1M),
      output: Number(model.outputCostPer1M)
    },
    capabilities: [
      model.supportsVision && 'vision',
      model.supportsFunctionCalling && 'function_calling',
      model.supportsWebSearch && 'web_search',
      model.supportsReasoning && 'reasoning',
      model.supportsStreaming && 'streaming'
    ].filter(Boolean) as string[],
    categoryScores: extendedMetadata.categoryScores || {},
    taskScores: extendedMetadata.taskScores || {}
  };
  
  // Cache for 30 minutes
  await redis.setex(cacheKey, ROUTER_CACHE_TTL.MODEL_DETAILS, JSON.stringify(details));
  
  return details;
}

/**
 * Warm up cache for common categories and plans
 */
export async function warmUpRouterCache(prisma: PrismaClient): Promise<void> {
  console.log('🔥 Warming up router cache...');
  const start = performance.now();
  
  // Use database plan values directly
  const plans: DatabasePlan[] = [...DATABASE_PLANS];
  const commonCategories: PromptCategory[] = [
    'coding', 'general_chat', 'creative_writing', 'reasoning', 'math',
    'analysis', 'question_answering', 'translation', 'summarization'
  ];
  const complexities: PromptComplexity[] = ['simple', 'standard', 'complex'];
  
  // Warm up user categories for all plans
  for (const plan of plans) {
    const userPlan = mapDatabasePlanToEnum(plan);
    await getUserAvailableCategories(userPlan, prisma);
  }
  
  // Warm up common category models
  for (const category of commonCategories) {
    for (const plan of plans) {
      const userPlan = mapDatabasePlanToEnum(plan);
      await getCategoryModels(category, userPlan, prisma);
      
      // Also warm up best model selections
      for (const complexity of complexities) {
        await getBestModelForCategory(category, complexity, userPlan, prisma);
      }
    }
  }
  
  const time = performance.now() - start;
  console.log(`✅ Router cache warmed up in ${time.toFixed(0)}ms`);
}

/**
 * Get category statistics
 */
export async function getCategoryStats(
  category: PromptCategory,
  prisma: PrismaClient
): Promise<any> {
  const cacheKey = ROUTER_CACHE_KEYS.CATEGORY_STATS(category);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Calculate statistics
  const [freeModels, plusModels, maxModels] = await Promise.all([
    getCategoryModels(category, UserPlan.FREEMIUM, prisma, 100),
    getCategoryModels(category, UserPlan.PLUS, prisma, 100),
    getCategoryModels(category, UserPlan.MAX, prisma, 100)
  ]);
  
  const allScores = maxModels.map(m => m.categoryScore);
  const stats = {
    category,
    totalModels: maxModels.length,
    freeModels: freeModels.length,
    plusModels: plusModels.length,
    avgScore: allScores.reduce((a, b) => a + b, 0) / allScores.length,
    maxScore: Math.max(...allScores),
    minScore: Math.min(...allScores),
    topModel: maxModels[0]?.canonicalName || null
  };
  
  // Cache for 1 hour
  await redis.setex(cacheKey, ROUTER_CACHE_TTL.CATEGORY_STATS, JSON.stringify(stats));
  
  return stats;
}

/**
 * Clear all router caches
 */
export async function clearRouterCache(): Promise<void> {
  const patterns = [
    'router:cat:*',
    'router:ucats:*',
    'router:pa:*',
    'router:best:*',
    'router:model:*',
    'router:stats:*'
  ];
  
  for (const pattern of patterns) {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }
  
  console.log('🧹 Router cache cleared');
}

/**
 * Update conversation context with model selection and track cost savings
 */
export async function updateConversationContext(
  conversationId: string,
  category: string,
  complexity: string,
  selectedModel: CategoryModel
): Promise<void> {
  const contextKey = ROUTER_CACHE_KEYS.CONVERSATION_CONTEXT(conversationId);
  const categoryComplexityKey = `${category}:${complexity}`;
  
  // Get existing context or create new
  const existingContext = await redis.get(contextKey);
  let context = existingContext ? JSON.parse(existingContext) : {
    id: conversationId,
    startedAt: new Date().toISOString(),
    lastMessageAt: new Date().toISOString(),
    modelSelections: {},
    recentModels: [],
    totalMessages: 0,
    estimatedTotalSavings: 0
  };
  
  // Update last message time
  context.lastMessageAt = new Date().toISOString();
  context.totalMessages = (context.totalMessages || 0) + 1;
  
  // Track model selection
  if (!context.modelSelections[categoryComplexityKey]) {
    context.modelSelections[categoryComplexityKey] = {
      model: selectedModel.canonicalName,
      selectedAt: new Date().toISOString(),
      messageCount: 0,
      estimatedSavings: 0
    };
  }
  
  const selection = context.modelSelections[categoryComplexityKey];
  selection.messageCount++;
  
  // Calculate estimated savings (90% discount on cached context after first message)
  if (selection.messageCount > 1) {
    // Assume average context size of 1000 tokens
    const avgContextTokens = 1000;
    const costPer1K = selectedModel.inputCost / 1000;
    const fullCost = avgContextTokens * costPer1K;
    const cachedCost = fullCost * 0.1; // 90% discount
    const savings = fullCost - cachedCost;
    
    selection.estimatedSavings += savings;
    context.estimatedTotalSavings = (context.estimatedTotalSavings || 0) + savings;
  }
  
  // Update recent models list (keep last 10)
  if (!context.recentModels.includes(selectedModel.canonicalName)) {
    context.recentModels.unshift(selectedModel.canonicalName);
    context.recentModels = context.recentModels.slice(0, 10);
  }
  
  // Store updated context (24 hour TTL)
  await redis.setex(contextKey, 86400, JSON.stringify(context));
}

/**
 * Validate if a model is still available for a user's plan
 */
export async function validateModelForPlan(
  modelId: string,
  userPlan: UserPlan,
  prisma: PrismaClient
): Promise<boolean> {
  const userPlanDatabase = mapUserPlanToDatabase(userPlan);
  
  const result = await prisma.$queryRaw<Array<{ count: number }>>`
    SELECT COUNT(*) as count
    FROM Models m
    INNER JOIN ModelPlanRules mpr ON mpr.modelId = m.id
    WHERE m.id = ${modelId}
      AND m.isEnabled = 1
      AND mpr.isEnabled = 1
      AND mpr.ruleType = 'INCLUDE'
      AND (
        mpr.planIds IS NULL 
        OR JSON_CONTAINS(mpr.planIds, JSON_QUOTE(${userPlanDatabase}))
      )
  `;
  
  return result[0]?.count > 0;
}