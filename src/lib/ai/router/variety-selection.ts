/**
 * Variety Selection for Model Router
 * 
 * Implements smart variety selection to provide users with different models
 * for similar queries while maintaining quality and cost efficiency.
 */

import { PromptComplexity } from '@/types';

export interface ModelForSelection {
  id: string;
  canonicalName: string;
  displayName: string;
  categoryScore: number;
  inputCost: number;
  outputCost: number;
  contextWindow: number;
}

export interface PerformanceTier {
  name: string;
  scoreRange: [number, number];
  models: ModelForSelection[];
}

/**
 * Group models into performance tiers for variety selection
 */
export function groupModelsIntoTiers(models: ModelForSelection[]): PerformanceTier[] {
  const tiers: PerformanceTier[] = [
    { name: 'premium', scoreRange: [90, 100], models: [] },
    { name: 'high', scoreRange: [80, 89.99], models: [] },
    { name: 'standard', scoreRange: [70, 79.99], models: [] },
    { name: 'budget', scoreRange: [60, 69.99], models: [] },
    { name: 'ultra-budget', scoreRange: [0, 59.99], models: [] }
  ];
  
  // Sort models into appropriate tiers based on score
  for (const model of models) {
    for (const tier of tiers) {
      if (model.categoryScore >= tier.scoreRange[0] && 
          model.categoryScore <= tier.scoreRange[1]) {
        tier.models.push(model);
        break;
      }
    }
  }
  
  // Remove empty tiers and sort models within each tier by score descending
  return tiers
    .filter(tier => tier.models.length > 0)
    .map(tier => ({
      ...tier,
      models: tier.models.sort((a, b) => b.categoryScore - a.categoryScore)
    }));
}

/**
 * Select appropriate tier based on complexity
 */
export function selectTierByComplexity(
  tiers: PerformanceTier[], 
  complexity: PromptComplexity
): PerformanceTier | undefined {
  switch (complexity) {
    case 'simple':
      // Prefer budget/ultra-budget tiers for simple tasks
      return tiers.find(t => t.name === 'ultra-budget') || 
             tiers.find(t => t.name === 'budget') ||
             tiers.find(t => t.name === 'standard') ||
             tiers[0];
      
    case 'complex':
    case 'difficult':
      // Prefer premium/high tiers for complex tasks
      return tiers.find(t => t.name === 'premium') || 
             tiers.find(t => t.name === 'high') ||
             tiers[0];
      
    default: // 'standard'
      // Prefer standard/high tiers for standard tasks
      return tiers.find(t => t.name === 'standard') || 
             tiers.find(t => t.name === 'high') ||
             tiers.find(t => t.name === 'budget') ||
             tiers[0];
  }
}

/**
 * Weighted random selection from models
 * Higher scored models within a tier are more likely to be selected
 */
export function weightedRandomSelect(
  models: ModelForSelection[],
  topN: number = 5
): ModelForSelection {
  // Take top N models from the array
  const candidates = models.slice(0, Math.min(topN, models.length));
  
  // Create exponential weights (higher scores get exponentially more weight)
  const weights = candidates.map((_, index) => Math.pow(2, topN - index));
  const totalWeight = weights.reduce((sum, w) => sum + w, 0);
  
  // Random selection based on weights
  let random = Math.random() * totalWeight;
  for (let i = 0; i < candidates.length; i++) {
    random -= weights[i];
    if (random <= 0) {
      return candidates[i];
    }
  }
  
  // Fallback to first candidate
  return candidates[0];
}

/**
 * Select a model with variety, avoiding recent models when possible
 */
export function selectWithVariety(
  models: ModelForSelection[],
  complexity: PromptComplexity,
  recentModels: string[] = []
): ModelForSelection | null {
  if (models.length === 0) {
    return null;
  }
  
  // Group models into performance tiers
  const tiers = groupModelsIntoTiers(models);
  
  // Select appropriate tier based on complexity
  const selectedTier = selectTierByComplexity(tiers, complexity);
  
  if (!selectedTier || selectedTier.models.length === 0) {
    return models[0]; // Fallback to best overall model
  }
  
  // Filter out very recently used models (last 3)
  const recentSet = new Set(recentModels.slice(0, 3));
  const availableModels = selectedTier.models.filter(
    m => !recentSet.has(m.canonicalName)
  );
  
  // If all models in tier were recently used, use the full tier
  const finalCandidates = availableModels.length > 0 ? availableModels : selectedTier.models;
  
  // Weighted random selection from candidates
  return weightedRandomSelect(finalCandidates);
}

/**
 * Get a diverse set of models for testing or comparison
 */
export function getDiverseModelSet(
  models: ModelForSelection[],
  count: number = 3
): ModelForSelection[] {
  const tiers = groupModelsIntoTiers(models);
  const selected: ModelForSelection[] = [];
  const selectedIds = new Set<string>();
  
  // Try to get one model from each tier
  for (const tier of tiers) {
    if (selected.length >= count) break;
    if (tier.models.length > 0) {
      // Random selection from top 3 in tier
      const candidates = tier.models.slice(0, Math.min(3, tier.models.length));
      const randomIndex = Math.floor(Math.random() * candidates.length);
      const model = candidates[randomIndex];
      
      if (!selectedIds.has(model.id)) {
        selected.push(model);
        selectedIds.add(model.id);
      }
    }
  }
  
  // Fill remaining slots if needed
  while (selected.length < count && models.length > selected.length) {
    const remaining = models.filter(m => !selectedIds.has(m.id));
    if (remaining.length === 0) break;
    
    const randomModel = remaining[Math.floor(Math.random() * remaining.length)];
    selected.push(randomModel);
    selectedIds.add(randomModel.id);
  }
  
  return selected;
}