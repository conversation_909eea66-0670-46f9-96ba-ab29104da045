/**
 * 28-Category to 8-Task Score Mathematical Aggregation Engine
 * 
 * Implements the mathematical mapping from 28 individual categories to 8 task scores
 * Based on ULTIMATE_SUBAGENT_PROMPT_V6.9.1_CRITICAL_FIXES.md
 */

import { PromptCategory } from '@/types';

// 8 Task Scores (aggregated from 28 categories)
export type TaskScore = 'cod' | 'cre' | 'rea' | 'mat' | 'ana' | 'lng' | 'cha' | 'vis';

// Mathematical aggregation mapping (28→8)
export const CATEGORY_TO_TASK_MAPPING = {
  // cod: coding + debugging (weighted 0.8/0.2)
  cod: {
    categories: ['coding', 'debugging'] as PromptCategory[],
    weights: { coding: 0.8, debugging: 0.2 },
    method: 'weighted_average' as const,
    minCategories: 1
  },
  
  // cre: creative_writing + creative + brainstorming (simple average)
  cre: {
    categories: ['creative_writing', 'brainstorming'] as PromptCategory[],
    weights: { creative_writing: 0.8, brainstorming: 0.2 },
    method: 'simple_average' as const,
    minCategories: 1
  },
  
  // rea: reasoning + mathematical + scientific + philosophical + legal (weighted)
  rea: {
    categories: ['reasoning', 'math', 'scientific', 'philosophical', 'legal', 'medical'] as PromptCategory[],
    weights: { reasoning: 0.35, math: 0.25, scientific: 0.2, philosophical: 0.1, legal: 0.05, medical: 0.05 },
    method: 'weighted_average' as const,
    minCategories: 2
  },
  
  // mat: mathematical (direct copy)
  mat: {
    categories: ['math'] as PromptCategory[],
    weights: { math: 1.0 },
    method: 'direct_copy' as const,
    minCategories: 1
  },
  
  // ana: analysis + data_analysis + historical + current_events (simple average)
  ana: {
    categories: ['analysis', 'data_analysis', 'historical', 'current_events'] as PromptCategory[],
    weights: { analysis: 0.4, data_analysis: 0.3, historical: 0.15, current_events: 0.15 },
    method: 'simple_average' as const,
    minCategories: 1
  },
  
  // lng: translation + summarization + question_answering + technical_writing (simple average)
  lng: {
    categories: ['translation', 'summarization', 'question_answering', 'technical_writing', 'business_writing'] as PromptCategory[],
    weights: { translation: 0.3, summarization: 0.25, question_answering: 0.25, technical_writing: 0.1, business_writing: 0.1 },
    method: 'simple_average' as const,
    minCategories: 1
  },
  
  // cha: general_chat + role_play + personal_advice + tutorial (weighted)
  cha: {
    categories: ['general_chat', 'role_play', 'personal_advice', 'tutorial'] as PromptCategory[],
    weights: { general_chat: 0.5, role_play: 0.2, personal_advice: 0.2, tutorial: 0.1 },
    method: 'weighted_average' as const,
    minCategories: 1
  },
  
  // vis: image_analysis + multimodal (conditional on vision capability)
  vis: {
    categories: ['image_analysis', 'multimodal', 'image_generation'] as PromptCategory[],
    weights: { image_analysis: 0.6, multimodal: 0.3, image_generation: 0.1 },
    method: 'conditional' as const,
    minCategories: 1,
    condition: 'vision_capable'
  }
};

/**
 * Calculate task scores from category scores using mathematical aggregation
 */
export function calculateTaskScores(categoryScores: Record<PromptCategory, number>): Record<TaskScore, number> {
  const taskScores: Record<TaskScore, number> = {} as Record<TaskScore, number>;
  
  for (const [taskId, config] of Object.entries(CATEGORY_TO_TASK_MAPPING)) {
    const availableScores: number[] = [];
    let weightedSum = 0;
    let totalWeight = 0;
    
    // Collect scores for categories in this task
    for (const category of config.categories) {
      if (categoryScores[category] !== undefined && categoryScores[category] > 0) {
        const score = categoryScores[category];
        const weight = (config.weights as any)[category] || 1.0;
        
        availableScores.push(score);
        weightedSum += score * weight;
        totalWeight += weight;
      }
    }
    
    // Check if we have enough categories
    if (availableScores.length < config.minCategories) {
      taskScores[taskId as TaskScore] = 0;
      continue;
    }
    
    // Calculate based on aggregation method
    switch (config.method) {
      case 'direct_copy':
        taskScores[taskId as TaskScore] = availableScores[0] || 0;
        break;
        
      case 'simple_average':
        taskScores[taskId as TaskScore] = availableScores.length > 0 
          ? Math.round(availableScores.reduce((a, b) => a + b, 0) / availableScores.length)
          : 0;
        break;
        
      case 'weighted_average':
        taskScores[taskId as TaskScore] = totalWeight > 0 
          ? Math.round(weightedSum / totalWeight)
          : 0;
        break;
        
      case 'conditional':
        // For vision tasks, this would check model capabilities
        // For now, use simple average if categories are present
        taskScores[taskId as TaskScore] = availableScores.length > 0
          ? Math.round(availableScores.reduce((a, b) => a + b, 0) / availableScores.length)
          : 0;
        break;
    }
  }
  
  return taskScores;
}

/**
 * Get the primary task score from category scores
 */
export function getPrimaryTaskScore(categoryScores: Record<PromptCategory, number>): TaskScore {
  const taskScores = calculateTaskScores(categoryScores);
  
  // Find the highest scoring task
  let maxScore = 0;
  let primaryTask: TaskScore = 'cha'; // Default to chat
  
  for (const [task, score] of Object.entries(taskScores)) {
    if (score > maxScore) {
      maxScore = score;
      primaryTask = task as TaskScore;
    }
  }
  
  return primaryTask;
}

/**
 * Get task score display names
 */
export const TASK_SCORE_NAMES: Record<TaskScore, string> = {
  cod: 'Coding & Development',
  cre: 'Creative Writing',
  rea: 'Reasoning & Logic',
  mat: 'Mathematics',
  ana: 'Analysis & Research',
  lng: 'Language Tasks',
  cha: 'Chat & Assistance',
  vis: 'Vision & Multimodal'
};

/**
 * Get categories for a specific task score
 */
export function getCategoriesForTask(taskScore: TaskScore): PromptCategory[] {
  return CATEGORY_TO_TASK_MAPPING[taskScore]?.categories || [];
}

/**
 * Check if a category belongs to a task score
 */
export function categoryBelongsToTask(category: PromptCategory, taskScore: TaskScore): boolean {
  return getCategoriesForTask(taskScore).includes(category);
}