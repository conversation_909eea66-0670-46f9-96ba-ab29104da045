/**
 * Ultra-Fast User Data Caching Layer
 * 
 * Caches ALL user-related data for sub-1ms responses:
 * - User profile and plan
 * - Message counts and limits
 * - User preferences
 * - Recent conversations
 * - Model access permissions
 * 
 * Goal: Zero database queries during chat operations
 */

import { redis } from '@/lib/redis';
import { PrismaClient } from '@prisma/client';
import { UserPlan } from '@/types';

// Cache keys for user data
export const USER_CACHE_KEYS = {
  PROFILE: (userId: string) => `user:profile:${userId}`,
  PLAN: (userId: string) => `user:plan:${userId}`,
  MESSAGE_COUNT: (userId: string) => `user:messages:${userId}:${new Date().toISOString().split('T')[0]}`,
  MESSAGE_LIMIT: (plan: string) => `user:limit:${plan}`,
  PREFERENCES: (userId: string) => `user:prefs:${userId}`,
  MODEL_ACCESS: (userId: string) => `user:models:${userId}`,
  RECENT_CONVOS: (userId: string) => `user:convos:${userId}`,
  SESSION_DATA: (sessionId: string) => `session:${sessionId}`,
  RATE_LIMIT: (userId: string, action: string) => `rl:${userId}:${action}`,
  USER_CATEGORIES: (userId: string) => `user:categories:${userId}`,
  USER_LAST_MODEL: (userId: string) => `user:last_model:${userId}`
};

// Cache TTL values (in seconds)
export const USER_CACHE_TTL = {
  PROFILE: 900,        // 15 minutes
  PLAN: 900,          // 15 minutes
  MESSAGE_COUNT: 300,  // 5 minutes
  MESSAGE_LIMIT: 3600, // 1 hour
  PREFERENCES: 1800,   // 30 minutes
  MODEL_ACCESS: 600,   // 10 minutes
  RECENT_CONVOS: 300,  // 5 minutes
  SESSION_DATA: 1800,  // 30 minutes
  RATE_LIMIT: 60,      // 1 minute
  USER_CATEGORIES: 600 // 10 minutes
};

interface UserProfile {
  id: string;
  email: string;
  name: string | null;
  image: string | null;
  plan: UserPlan;
  stripeCustomerId: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface UserMessageData {
  count: number;
  limit: number;
  remaining: number;
  resetsAt: Date;
}

/**
 * Get complete user profile (cached)
 */
export async function getCachedUserProfile(
  userId: string,
  prisma: PrismaClient
): Promise<UserProfile | null> {
  const cacheKey = USER_CACHE_KEYS.PROFILE(userId);
  
  // Try cache first (should be ~0.2ms)
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Cache miss - get from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      email: true,
      name: true,
      image: true,
      plan: true,
      stripeCustomerId: true,
      createdAt: true,
      updatedAt: true
    }
  });
  
  if (!user) {
    return null;
  }
  
  // Get user plan from User table directly
  const profile: UserProfile = {
    ...user,
    plan: user.plan as UserPlan || UserPlan.FREEMIUM
  };
  
  // Cache for 15 minutes
  await redis.setex(cacheKey, USER_CACHE_TTL.PROFILE, JSON.stringify(profile));
  
  // Also cache the plan separately for quick access
  await redis.setex(
    USER_CACHE_KEYS.PLAN(userId), 
    USER_CACHE_TTL.PLAN, 
    profile.plan
  );
  
  return profile;
}

/**
 * Get user plan only (ultra-fast)
 */
export async function getCachedUserPlan(
  userId: string,
  prisma: PrismaClient
): Promise<UserPlan> {
  const cacheKey = USER_CACHE_KEYS.PLAN(userId);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return cached as UserPlan;
  }
  
  // Get full profile which will cache the plan
  const profile = await getCachedUserProfile(userId, prisma);
  return profile?.plan || UserPlan.FREEMIUM;
}

/**
 * Get and increment message count atomically
 */
export async function incrementMessageCount(
  userId: string
): Promise<number> {
  const today = new Date().toISOString().split('T')[0];
  const cacheKey = USER_CACHE_KEYS.MESSAGE_COUNT(userId);
  
  // Atomic increment
  const count = await redis.incr(cacheKey);
  
  // Set expiry to end of day if this is the first message
  if (count === 1) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    const ttl = Math.floor((tomorrow.getTime() - Date.now()) / 1000);
    await redis.expire(cacheKey, ttl);
  }
  
  return count;
}

/**
 * Get current message count without incrementing
 */
export async function getMessageCount(
  userId: string
): Promise<number> {
  const cacheKey = USER_CACHE_KEYS.MESSAGE_COUNT(userId);
  const count = await redis.get(cacheKey);
  return count ? parseInt(count) : 0;
}

/**
 * Check message limit with caching
 */
export async function checkMessageLimit(
  userId: string,
  plan: UserPlan
): Promise<UserMessageData> {
  const count = await getMessageCount(userId);
  
  // Get cached limit for plan
  const limitKey = USER_CACHE_KEYS.MESSAGE_LIMIT(plan);
  let limit = await redis.get(limitKey);
  
  if (!limit) {
    // Set default limits
    const limits = {
      [UserPlan.FREE]: 5,
      [UserPlan.FREEMIUM]: 50,
      [UserPlan.PLUS]: 500,
      [UserPlan.ADVANCED]: 1000,
      [UserPlan.MAX]: 2000,
      [UserPlan.ENTERPRISE]: 5000
    };
    limit = String(limits[plan] || 50);
    await redis.setex(limitKey, USER_CACHE_TTL.MESSAGE_LIMIT, limit);
  }
  
  const limitNum = parseInt(limit);
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  
  return {
    count,
    limit: limitNum,
    remaining: Math.max(0, limitNum - count),
    resetsAt: tomorrow
  };
}

/**
 * Cache user preferences
 */
export async function cacheUserPreferences(
  userId: string,
  preferences: Record<string, any>
): Promise<void> {
  const cacheKey = USER_CACHE_KEYS.PREFERENCES(userId);
  await redis.setex(
    cacheKey, 
    USER_CACHE_TTL.PREFERENCES, 
    JSON.stringify(preferences)
  );
}

/**
 * Get cached user preferences
 */
export async function getCachedUserPreferences(
  userId: string
): Promise<Record<string, any> | null> {
  const cacheKey = USER_CACHE_KEYS.PREFERENCES(userId);
  const cached = await redis.get(cacheKey);
  return cached ? JSON.parse(cached) : null;
}

/**
 * Cache user's accessible models
 */
export async function cacheUserModelAccess(
  userId: string,
  plan: UserPlan,
  prisma: PrismaClient
): Promise<string[]> {
  const cacheKey = USER_CACHE_KEYS.MODEL_ACCESS(userId);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get accessible models based on plan
  const models = await prisma.$queryRaw<Array<{canonicalName: string}>>`
    SELECT DISTINCT m.canonicalName
    FROM Models m
    WHERE m.isEnabled = true
      AND (
        (${plan} = 'FREE' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"FREE"'))) OR
        (${plan} = 'PLUS' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"PLUS"'))) OR
        (${plan} = 'MAX')
      )
  `;
  
  const modelNames = models.map(m => m.canonicalName);
  
  // Cache for 10 minutes
  await redis.setex(
    cacheKey, 
    USER_CACHE_TTL.MODEL_ACCESS, 
    JSON.stringify(modelNames)
  );
  
  return modelNames;
}

/**
 * Cache user's accessible categories
 */
export async function cacheUserCategories(
  userId: string,
  plan: UserPlan,
  prisma: PrismaClient
): Promise<string[]> {
  const cacheKey = USER_CACHE_KEYS.USER_CATEGORIES(userId);
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached);
  }
  
  // Get categories with models for this user's plan
  const categories = await prisma.$queryRaw<Array<{category: string}>>`
    SELECT DISTINCT mm.category
    FROM modelMappings mm
    JOIN Models m ON mm.model_id = m.id
    WHERE mm.enabled = 1
      AND m.isEnabled = 1
      AND (
        (${plan} = 'FREE' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"FREE"'))) OR
        (${plan} = 'PLUS' AND (m.planAvailability IS NULL OR JSON_CONTAINS(m.planAvailability, '"PLUS"'))) OR
        (${plan} = 'MAX')
      )
    GROUP BY mm.category
    HAVING COUNT(DISTINCT m.id) >= 3
  `;
  
  const categoryNames = categories.map(c => c.category);
  
  // Cache for 10 minutes
  await redis.setex(
    cacheKey,
    USER_CACHE_TTL.USER_CATEGORIES,
    JSON.stringify(categoryNames)
  );
  
  return categoryNames;
}

/**
 * Store user's last used model
 */
export async function cacheUserLastModel(
  userId: string,
  modelId: string
): Promise<void> {
  const cacheKey = USER_CACHE_KEYS.USER_LAST_MODEL(userId);
  await redis.setex(cacheKey, USER_CACHE_TTL.PREFERENCES, modelId);
}

/**
 * Get user's last used model
 */
export async function getUserLastModel(
  userId: string
): Promise<string | null> {
  const cacheKey = USER_CACHE_KEYS.USER_LAST_MODEL(userId);
  return await redis.get(cacheKey);
}

/**
 * Pre-warm all user caches for optimal performance
 */
export async function warmUserCache(
  userId: string,
  prisma: PrismaClient
): Promise<void> {
  // Run all cache operations in parallel
  const [profile, plan] = await Promise.all([
    getCachedUserProfile(userId, prisma),
    getCachedUserPlan(userId, prisma)
  ]);
  
  if (profile && plan) {
    await Promise.all([
      cacheUserModelAccess(userId, plan, prisma),
      cacheUserCategories(userId, plan, prisma),
      checkMessageLimit(userId, plan)
    ]);
  }
}

/**
 * Clear all user caches (e.g., on plan change)
 */
export async function clearUserCache(userId: string): Promise<void> {
  const patterns = [
    `user:profile:${userId}`,
    `user:plan:${userId}`,
    `user:messages:${userId}:*`,
    `user:prefs:${userId}`,
    `user:models:${userId}`,
    `user:convos:${userId}`,
    `user:categories:${userId}`,
    `user:last_model:${userId}`
  ];
  
  for (const pattern of patterns) {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }
}

/**
 * Rate limiting with Redis (ultra-fast)
 */
export async function checkRateLimit(
  userId: string,
  action: string = 'chat',
  limit: number = 10,
  windowSeconds: number = 60
): Promise<{ allowed: boolean; remaining: number; resetsAt: Date }> {
  const cacheKey = USER_CACHE_KEYS.RATE_LIMIT(userId, action);
  
  // Use Redis INCR with TTL
  const count = await redis.incr(cacheKey);
  
  if (count === 1) {
    await redis.expire(cacheKey, windowSeconds);
  }
  
  const ttl = await redis.ttl(cacheKey);
  const resetsAt = new Date(Date.now() + ttl * 1000);
  
  return {
    allowed: count <= limit,
    remaining: Math.max(0, limit - count),
    resetsAt
  };
}