// Dynamic model statistics - queries database in real-time
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Cache for 5 minutes to avoid excessive DB queries
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
let cachedStats: any = null;
let lastCacheTime = 0;

export async function getModelStats() {
  const now = Date.now();
  
  // Return cached data if fresh
  if (cachedStats && (now - lastCacheTime) < CACHE_DURATION) {
    return cachedStats;
  }
  
  try {
    // Get total enabled models
    const totalEnabled = await prisma.models.count({
      where: { isEnabled: true }
    });
    
    // Get breakdown by provider
    const byProvider = await prisma.aIModel.groupBy({
      by: ['providerId'],
      where: { isEnabled: true },
      _count: { id: true }
    });
    
    // Get model stats by plan access
    const freeModels = await prisma.models.count({
      where: { 
        isEnabled: true,
        planRules: {
          some: {
            planIds: { array_contains: 'FREE' },
            isEnabled: true
          }
        }
      }
    });
    
    const freemiumModels = await prisma.models.count({
      where: { 
        isEnabled: true,
        planRules: {
          some: {
            planIds: { array_contains: 'FREEMIUM' },
            isEnabled: true
          }
        }
      }
    });
    
    const plusModels = await prisma.models.count({
      where: { 
        isEnabled: true
        // TODO: Join with ModelPlanRules for accurate plan-specific counts
      }
    });
    
    // Calculate marketing numbers
    const roundedTotal = Math.round(totalEnabled / 10) * 10;
    const marketingNumber = `${roundedTotal}+`;
    
    const stats = {
      TOTAL_ENABLED: totalEnabled,
      FREE_MODELS: freeModels,
      FREEMIUM_MODELS: freemiumModels,
      PLUS_MODELS: plusModels,
      MARKETING_TOTAL: marketingNumber,
      MARKETING_EXACT: totalEnabled.toString(),
      BY_PROVIDER: byProvider.reduce((acc: any, p) => {
        acc[p.providerId] = p._count.id;
        return acc;
      }, {}),
      GENERATED_AT: new Date().toISOString(),
      BUILD_HASH: process.env.VERCEL_GIT_COMMIT_SHA || 'runtime',
    };
    
    // Cache the results
    cachedStats = stats;
    lastCacheTime = now;
    
    return stats;
  } catch (error) {
    console.error('Error fetching model statistics:', error);
    
    // Return fallback data if query fails
    return {
      TOTAL_ENABLED: 150,
      FREE_MODELS: 120,
      FREEMIUM_MODELS: 120,
      PLUS_MODELS: 140,
      MARKETING_TOTAL: '150+',
      MARKETING_EXACT: '150',
      BY_PROVIDER: {},
      GENERATED_AT: new Date().toISOString(),
      BUILD_HASH: 'fallback',
    };
  }
}

// Static exports for backward compatibility
export const TOTAL_MODELS = 150; // Fallback value
export const TOTAL_MODELS_MARKETING = '150+';
export const FREE_MESSAGE_LIMITS = {
  ANONYMOUS: 5,
  LOGGED_IN: 10,
} as const;