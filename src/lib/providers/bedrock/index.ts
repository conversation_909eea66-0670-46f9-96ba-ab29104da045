/**
 * AWS Bedrock Provider - Enterprise foundation models with AWS integration
 * Based on research: Native AWS SDK streaming support with multiple model providers
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class BedrockProvider extends BaseProvider {
  private region: string;
  private accessKeyId?: string;
  private secretAccessKey?: string;

  constructor(options: {
    region?: string;
    accessKeyId?: string;
    secretAccessKey?: string;
  } = {}) {
    super();
    this.region = options.region || process.env.AWS_REGION || 'us-east-1';
    this.accessKeyId = options.accessKeyId || process.env.AWS_ACCESS_KEY_ID;
    this.secretAccessKey = options.secretAccessKey || process.env.AWS_SECRET_ACCESS_KEY;
  }

  getAvailableModels(): ModelInfo[] {
    return [
      // Amazon Nova models
      {
        id: 'bedrock/amazon.nova-pro-v1:0',
        name: 'Amazon Nova Pro',
        description: 'Amazon Nova Pro multimodal foundation model',
        tier: 'premium',
        contextWindow: 300000,
        inputCost: 0.80, // per 1M input tokens
        outputCost: 3.20, // per 1M output tokens
        capabilities: ['chat', 'reasoning', 'vision', 'multimodal', 'function-calling'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: true,
          vision: true,
          multimodal: true
        }
      },
      {
        id: 'bedrock/amazon.nova-lite-v1:0',
        name: 'Amazon Nova Lite',
        description: 'Amazon Nova Lite fast and cost-effective model',
        tier: 'balanced',
        contextWindow: 300000,
        inputCost: 0.06,
        outputCost: 0.24,
        capabilities: ['chat', 'reasoning', 'vision', 'multimodal'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: false,
          vision: true,
          multimodal: true
        }
      },
      {
        id: 'bedrock/amazon.nova-micro-v1:0',
        name: 'Amazon Nova Micro',
        description: 'Amazon Nova Micro ultra-fast text-only model',
        tier: 'fast',
        contextWindow: 128000,
        inputCost: 0.035,
        outputCost: 0.14,
        capabilities: ['chat', 'fast', 'text-only'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      // Claude models via Bedrock
      {
        id: 'bedrock/anthropic.claude-3-5-sonnet-20241022-v2:0',
        name: 'Claude 3.5 Sonnet (Bedrock)',
        description: 'Anthropic Claude 3.5 Sonnet via AWS Bedrock',
        tier: 'premium',
        contextWindow: 200000,
        inputCost: 3.00,
        outputCost: 15.00,
        capabilities: ['chat', 'reasoning', 'vision', 'function-calling', 'analysis'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: true,
          vision: true,
          multimodal: true
        }
      },
      {
        id: 'bedrock/anthropic.claude-3-5-haiku-20241022-v1:0',
        name: 'Claude 3.5 Haiku (Bedrock)',
        description: 'Anthropic Claude 3.5 Haiku fast model via Bedrock',
        tier: 'balanced',
        contextWindow: 200000,
        inputCost: 0.80,
        outputCost: 4.00,
        capabilities: ['chat', 'reasoning', 'fast', 'analysis'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      // Meta Llama models
      {
        id: 'bedrock/meta.llama3-2-90b-instruct-v1:0',
        name: 'Llama 3.2 90B Instruct (Bedrock)',
        description: 'Meta Llama 3.2 90B via AWS Bedrock',
        tier: 'premium',
        contextWindow: 128000,
        inputCost: 2.00,
        outputCost: 2.00,
        capabilities: ['chat', 'reasoning', 'code', 'multilingual'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      // Mistral models
      {
        id: 'bedrock/mistral.mistral-large-2407-v1:0',
        name: 'Mistral Large 2407 (Bedrock)',
        description: 'Mistral Large model via AWS Bedrock',
        tier: 'premium',
        contextWindow: 128000,
        inputCost: 2.00,
        outputCost: 6.00,
        capabilities: ['chat', 'reasoning', 'code', 'multilingual', 'function-calling'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: true,
          vision: false,
          multimodal: false
        }
      },
      // Cohere models
      {
        id: 'bedrock/cohere.command-r-plus-v1:0',
        name: 'Command R+ (Bedrock)',
        description: 'Cohere Command R+ via AWS Bedrock',
        tier: 'balanced',
        contextWindow: 128000,
        inputCost: 2.50,
        outputCost: 10.00,
        capabilities: ['chat', 'reasoning', 'rag', 'citations'],
        provider: 'bedrock',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const { BedrockRuntimeClient, InvokeModelCommand } = await import('@aws-sdk/client-bedrock-runtime');
      
      const client = new BedrockRuntimeClient({
        region: this.region,
        ...(this.accessKeyId && this.secretAccessKey && {
          credentials: {
            accessKeyId: this.accessKeyId,
            secretAccessKey: this.secretAccessKey
          }
        })
      });

      const modelName = this.stripProviderPrefix(modelId);
      const payload = this.formatPayload(modelName, messages, options);

      const command = new InvokeModelCommand({
        modelId: modelName,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(payload)
      });

      const response = await client.send(command);
      
      if (!response.body) {
        throw new Error('No response body from Bedrock');
      }

      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      const content = this.extractContent(modelName, responseBody);

      return {
        content,
        usage: this.extractUsage(responseBody),
        finishReason: 'stop',
        model: modelId
      };
    } catch (error) {
      console.error('Bedrock chat error:', error);
      throw new Error(`Bedrock API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const { BedrockRuntimeClient, InvokeModelWithResponseStreamCommand } = await import('@aws-sdk/client-bedrock-runtime');
      
      const client = new BedrockRuntimeClient({
        region: this.region,
        ...(this.accessKeyId && this.secretAccessKey && {
          credentials: {
            accessKeyId: this.accessKeyId,
            secretAccessKey: this.secretAccessKey
          }
        })
      });

      const modelName = this.stripProviderPrefix(modelId);
      const payload = this.formatPayload(modelName, messages, options);

      const command = new InvokeModelWithResponseStreamCommand({
        modelId: modelName,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(payload)
      });

      const response = await client.send(command);
      
      if (!response.body) {
        throw new Error('No response body from Bedrock streaming');
      }

      let fullContent = '';

      for await (const event of response.body) {
        if (event.chunk) {
          const chunk = JSON.parse(new TextDecoder().decode(event.chunk.bytes));
          
          if (this.isAnthropicModel(modelName)) {
            // Handle Anthropic model streaming
            if (chunk.type === 'content_block_delta') {
              const content = chunk.delta?.text || '';
              fullContent += content;
              
              yield {
                content,
                isComplete: false,
                type: 'content'
              };
            } else if (chunk.type === 'message_stop') {
              yield {
                content: '',
                isComplete: true,
                usage: this.extractUsage(chunk)
              };
            }
          } else if (this.isAmazonModel(modelName)) {
            // Handle Amazon Nova model streaming
            if (chunk.outputText) {
              const content = chunk.outputText;
              fullContent += content;
              
              yield {
                content,
                isComplete: false,
                type: 'content'
              };
            } else if (chunk.completionReason) {
              yield {
                content: '',
                isComplete: true,
                usage: this.extractUsage(chunk)
              };
            }
          } else {
            // Handle other model types (Meta, Mistral, Cohere)
            const content = this.extractStreamContent(modelName, chunk);
            if (content) {
              fullContent += content;
              
              yield {
                content,
                isComplete: false,
                type: 'content'
              };
            }
          }
        }
      }

      // Final completion if not already sent
      yield {
        content: '',
        isComplete: true,
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: fullContent.length / 4
        }
      };

    } catch (error) {
      console.error('Bedrock streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Bedrock streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private formatPayload(modelName: string, messages: Message[], options: ChatOptions): any {
    if (this.isAnthropicModel(modelName)) {
      return {
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
        anthropic_version: 'bedrock-2023-05-31'
      };
    } else if (this.isAmazonModel(modelName)) {
      const prompt = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
      return {
        inputText: prompt,
        textGenerationConfig: {
          maxTokenCount: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          topP: 0.9
        }
      };
    } else if (this.isMetaModel(modelName)) {
      const prompt = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n');
      return {
        prompt,
        max_gen_len: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
        top_p: 0.9
      };
    } else {
      // Default format for other models
      return {
        prompt: messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n'),
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7
      };
    }
  }

  private extractContent(modelName: string, response: any): string {
    if (this.isAnthropicModel(modelName)) {
      return response.content?.[0]?.text || '';
    } else if (this.isAmazonModel(modelName)) {
      return response.outputText || '';
    } else if (this.isMetaModel(modelName)) {
      return response.generation || '';
    } else {
      return response.completions?.[0]?.data?.text || response.text || '';
    }
  }

  private extractStreamContent(modelName: string, chunk: any): string {
    if (this.isMetaModel(modelName)) {
      return chunk.generation || '';
    } else if (this.isCohereModel(modelName)) {
      return chunk.text || '';
    } else if (this.isMistralModel(modelName)) {
      return chunk.outputs?.[0]?.text || '';
    }
    return '';
  }

  private extractUsage(response: any): any {
    return {
      promptTokens: response.usage?.input_tokens || response.inputTokens || 0,
      completionTokens: response.usage?.output_tokens || response.outputTokens || 0,
      totalTokens: response.usage?.total_tokens || response.totalTokens || 0
    };
  }

  private isAnthropicModel(modelName: string): boolean {
    return modelName.includes('anthropic.claude');
  }

  private isAmazonModel(modelName: string): boolean {
    return modelName.includes('amazon.nova');
  }

  private isMetaModel(modelName: string): boolean {
    return modelName.includes('meta.llama');
  }

  private isCohereModel(modelName: string): boolean {
    return modelName.includes('cohere.command');
  }

  private isMistralModel(modelName: string): boolean {
    return modelName.includes('mistral.mistral');
  }

  private stripProviderPrefix(modelId: string): string {
    return modelId.replace(/^bedrock\//, '');
  }
}