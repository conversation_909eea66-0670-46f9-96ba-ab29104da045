/**
 * Google Vertex AI Provider - Enterprise-grade with Google Cloud integration
 * Based on research: Advanced features like grounding and function calling
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class VertexAIProvider extends BaseProvider {
  private apiKey: string;
  private projectId: string;
  private location: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string, options?: { projectId?: string; location?: string }) {
    super();
    this.apiKey = apiKey;
    this.projectId = options?.projectId || process.env.GOOGLE_CLOUD_PROJECT || 'default-project';
    this.location = options?.location || 'us-central1';
    this.baseURL = baseURL || `https://${this.location}-aiplatform.googleapis.com/v1`;
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'vertex/gemini-2.0-flash-001',
        name: 'Gemini 2.0 Flash (Vertex)',
        description: 'Latest Gemini 2.0 Flash with enterprise features',
        tier: 'premium',
        contextWindow: 1048576, // 1M tokens
        inputCost: 0.075, // per 1M characters
        outputCost: 0.30,
        capabilities: ['chat', 'reasoning', 'vision', 'multimodal', 'function-calling', 'grounding'],
        provider: 'vertex',
        features: {
          streaming: true,
          functionCalling: true,
          vision: true,
          multimodal: true,
          grounding: true
        }
      },
      {
        id: 'vertex/gemini-1.5-pro-002',
        name: 'Gemini 1.5 Pro (Vertex)',
        description: 'High-performance Gemini 1.5 Pro with enterprise security',
        tier: 'premium',
        contextWindow: 2097152, // 2M tokens
        inputCost: 1.25,
        outputCost: 5.00,
        capabilities: ['chat', 'reasoning', 'vision', 'multimodal', 'function-calling', 'long-context'],
        provider: 'vertex',
        features: {
          streaming: true,
          functionCalling: true,
          vision: true,
          multimodal: true,
          longContext: true
        }
      },
      {
        id: 'vertex/gemini-1.5-flash-002',
        name: 'Gemini 1.5 Flash (Vertex)',
        description: 'Fast and efficient Gemini 1.5 Flash for enterprise',
        tier: 'balanced',
        contextWindow: 1048576,
        inputCost: 0.075,
        outputCost: 0.30,
        capabilities: ['chat', 'reasoning', 'vision', 'multimodal', 'function-calling'],
        provider: 'vertex',
        features: {
          streaming: true,
          functionCalling: true,
          vision: true,
          multimodal: true
        }
      },
      {
        id: 'vertex/codechat-bison-32k',
        name: 'Codey Chat Bison 32K',
        description: 'Specialized code generation and chat model',
        tier: 'balanced',
        contextWindow: 32768,
        inputCost: 0.25,
        outputCost: 0.50,
        capabilities: ['code', 'programming', 'debugging', 'chat'],
        provider: 'vertex',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'vertex/code-bison-32k',
        name: 'Codey Code Bison 32K',
        description: 'Code completion and generation model',
        tier: 'balanced',
        contextWindow: 32768,
        inputCost: 0.25,
        outputCost: 0.50,
        capabilities: ['code', 'completion', 'generation'],
        provider: 'vertex',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const model = this.stripProviderPrefix(modelId);
      const endpoint = `${this.baseURL}/projects/${this.projectId}/locations/${this.location}/publishers/google/models/${model}:generateContent`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: this.formatMessages(messages),
          generationConfig: {
            maxOutputTokens: options.maxTokens || 8192,
            temperature: options.temperature || 0.7,
            topP: 0.95,
            topK: 40
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vertex AI API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const candidate = data.candidates?.[0];
      
      if (!candidate?.content?.parts?.[0]?.text) {
        throw new Error('No response content from Vertex AI');
      }

      return {
        content: candidate.content.parts[0].text,
        usage: {
          promptTokens: data.usageMetadata?.promptTokenCount || 0,
          completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
          totalTokens: data.usageMetadata?.totalTokenCount || 0
        },
        finishReason: this.mapFinishReason(candidate.finishReason),
        model: modelId
      };
    } catch (error) {
      console.error('Vertex AI chat error:', error);
      throw new Error(`Vertex AI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const model = this.stripProviderPrefix(modelId);
      const endpoint = `${this.baseURL}/projects/${this.projectId}/locations/${this.location}/publishers/google/models/${model}:streamGenerateContent`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: this.formatMessages(messages),
          generationConfig: {
            maxOutputTokens: options.maxTokens || 8192,
            temperature: options.temperature || 0.7,
            topP: 0.95,
            topK: 40
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vertex AI API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const chunk = JSON.parse(line);
              const candidate = chunk.candidates?.[0];
              
              if (candidate?.content?.parts?.[0]?.text) {
                const content = candidate.content.parts[0].text;
                fullContent += content;
                
                yield {
                  content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (candidate?.finishReason && candidate.finishReason !== 'STOP') {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: chunk.usageMetadata?.promptTokenCount || 0,
                    completionTokens: chunk.usageMetadata?.candidatesTokenCount || 0,
                    totalTokens: chunk.usageMetadata?.totalTokenCount || fullContent.length / 4
                  },
                  finishReason: this.mapFinishReason(candidate.finishReason)
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse Vertex AI chunk:', parseError);
            }
          }
        }
      }

      // Final completion
      yield {
        content: '',
        isComplete: true,
        usage: {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: fullContent.length / 4
        }
      };

    } catch (error) {
      console.error('Vertex AI streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Vertex AI streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private formatMessages(messages: Message[]): any[] {
    return messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    }));
  }

  private mapFinishReason(reason: string): string {
    const reasonMap: { [key: string]: string } = {
      'FINISH_REASON_STOP': 'stop',
      'FINISH_REASON_MAX_TOKENS': 'length',
      'FINISH_REASON_SAFETY': 'content_filter',
      'FINISH_REASON_RECITATION': 'content_filter',
      'FINISH_REASON_OTHER': 'other'
    };
    return reasonMap[reason] || 'unknown';
  }

  private stripProviderPrefix(modelId: string): string {
    return modelId.replace(/^vertex\//, '');
  }
}