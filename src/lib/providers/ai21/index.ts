/**
 * AI21 Labs Provider - Jamba models and Jurassic series
 * Based on research: Accessed via OpenRouter unified API
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class AI21Provider extends BaseProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.apiKey = apiKey;
    this.baseURL = baseURL || 'https://openrouter.ai/api/v1'; // AI21 models via OpenRouter
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'ai21/jamba-1.5-large',
        name: 'Jamba 1.5 Large',
        description: 'AI21 Jamba 1.5 Large hybrid Mamba-Transformer model',
        tier: 'premium',
        contextWindow: 256000,
        inputCost: 2.00, // per 1M tokens
        outputCost: 8.00,
        capabilities: ['chat', 'reasoning', 'long-context', 'hybrid-architecture'],
        provider: 'ai21',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'ai21/jamba-1.5-mini',
        name: 'Jamba 1.5 Mini',
        description: 'AI21 Jamba 1.5 Mini cost-effective hybrid model',
        tier: 'balanced',
        contextWindow: 256000,
        inputCost: 0.20,
        outputCost: 0.40,
        capabilities: ['chat', 'reasoning', 'long-context', 'cost-effective'],
        provider: 'ai21',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'ai21/j2-ultra',
        name: 'Jurassic-2 Ultra',
        description: 'AI21 Jurassic-2 Ultra large language model',
        tier: 'premium',
        contextWindow: 8192,
        inputCost: 15.00,
        outputCost: 15.00,
        capabilities: ['chat', 'reasoning', 'creative'],
        provider: 'ai21',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'ai21/j2-mid',
        name: 'Jurassic-2 Mid',
        description: 'AI21 Jurassic-2 Mid balanced performance model',
        tier: 'balanced',
        contextWindow: 8192,
        inputCost: 10.00,
        outputCost: 10.00,
        capabilities: ['chat', 'reasoning'],
        provider: 'ai21',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`AI21 API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];
      
      if (!choice?.message?.content) {
        throw new Error('No response content from AI21');
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('AI21 chat error:', error);
      throw new Error(`AI21 API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`AI21 API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const event = JSON.parse(data);
              const choice = event.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                yield {
                  content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (choice?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: event.usage?.prompt_tokens || 0,
                    completionTokens: event.usage?.completion_tokens || 0,
                    totalTokens: event.usage?.total_tokens || fullContent.length / 4
                  },
                  finishReason: choice.finish_reason as any
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse AI21 SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('AI21 streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `AI21 streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private getOpenRouterModelName(modelId: string): string {
    const mapping: { [key: string]: string } = {
      'ai21/jamba-1.5-large': 'ai21/jamba-1-5-large',
      'ai21/jamba-1.5-mini': 'ai21/jamba-1-5-mini',
      'ai21/j2-ultra': 'ai21/j2-ultra',
      'ai21/j2-mid': 'ai21/j2-mid'
    };
    return mapping[modelId] || modelId.replace(/^ai21\//, 'ai21/');
  }
}