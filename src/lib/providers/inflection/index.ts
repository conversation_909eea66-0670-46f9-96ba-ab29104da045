/**
 * Inflection AI Provider - Pi models via OpenRouter
 * Based on research: Accessed via OpenRouter unified API
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class InflectionProvider extends BaseProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.apiKey = apiKey;
    this.baseURL = baseURL || 'https://openrouter.ai/api/v1';
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'inflection/inflection-2.5',
        name: 'Inflection 2.5 (Pi)',
        description: 'Inflection AI Pi model for conversational interactions',
        tier: 'premium',
        contextWindow: 8192,
        inputCost: 2.50,
        outputCost: 2.50,
        capabilities: ['chat', 'conversational', 'empathetic'],
        provider: 'inflection',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: 'inflection/inflection-2.5',
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Inflection API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];
      
      return {
        content: choice.message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('Inflection chat error:', error);
      throw new Error(`Inflection API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: 'inflection/inflection-2.5',
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Inflection API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body reader available');

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const event = JSON.parse(data);
              const choice = event.choices?.[0];
              
              if (choice?.delta?.content) {
                yield {
                  content: choice.delta.content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (choice?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: event.usage?.prompt_tokens || 0,
                    completionTokens: event.usage?.completion_tokens || 0,
                    totalTokens: event.usage?.total_tokens || 0
                  },
                  finishReason: choice.finish_reason as any
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse Inflection SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Inflection streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Inflection streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}