/**
 * Groq Provider - Hardware-accelerated inference with sub-100ms latency
 * Based on research: Fast inference with 30-second streaming windows
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';
import OpenAI from 'openai';

export class GroqProvider extends BaseProvider {
  private client: OpenAI;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.client = new OpenAI({
      apiKey,
      baseURL: baseURL || 'https://api.groq.com/openai/v1'
    });
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'groq/llama-3.3-70b-versatile',
        name: 'Llama 3.3 70B Versatile',
        description: 'High-performance Llama model optimized for versatile tasks',
        tier: 'fast',
        contextWindow: 8192,
        inputCost: 0.59, // per 1M tokens
        outputCost: 0.79,
        capabilities: ['chat', 'reasoning', 'multilingual'],
        provider: 'groq',
        features: {
          streaming: true,
          functionCalling: false, // Tool calling limited during streaming
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'groq/llama-3.1-8b-instant',
        name: 'Llama 3.1 8B Instant',
        description: 'Ultra-fast 8B parameter model for instant responses',
        tier: 'fast',
        contextWindow: 8192,
        inputCost: 0.05,
        outputCost: 0.10,
        capabilities: ['chat', 'fast', 'instant'],
        provider: 'groq',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'groq/mixtral-8x7b-32768',
        name: 'Mixtral 8x7B 32K',
        description: 'Mixtral MoE model with 32K context window',
        tier: 'fast',
        contextWindow: 32768,
        inputCost: 0.24,
        outputCost: 0.24,
        capabilities: ['chat', 'reasoning', 'long-context', 'multilingual'],
        provider: 'groq',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'groq/gemma-7b-it',
        name: 'Gemma 7B Instruct',
        description: 'Google Gemma 7B instruction-tuned model',
        tier: 'fast',
        contextWindow: 8192,
        inputCost: 0.10,
        outputCost: 0.10,
        capabilities: ['chat', 'instruction-following'],
        provider: 'groq',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: this.stripProviderPrefix(modelId),
        messages: messages as any,
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
        stream: false
      });

      const choice = response.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No response content from Groq');
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('Groq chat error:', error);
      throw new Error(`Groq API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const stream = await this.client.chat.completions.create({
        model: this.stripProviderPrefix(modelId),
        messages: messages as any,
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
        stream: true
      });

      let fullContent = '';
      let totalTokens = 0;

      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        
        if (choice?.delta?.content) {
          const content = choice.delta.content;
          fullContent += content;
          
          yield {
            content,
            isComplete: false,
            type: 'content'
          };
        }

        // Handle completion
        if (choice?.finish_reason) {
          yield {
            content: '',
            isComplete: true,
            usage: {
              promptTokens: 0, // Groq doesn't provide streaming usage
              completionTokens: 0,
              totalTokens: fullContent.length / 4 // Rough estimate
            },
            finishReason: choice.finish_reason as any
          };
        }
      }
    } catch (error) {
      console.error('Groq streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Groq streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private stripProviderPrefix(modelId: string): string {
    return modelId.replace(/^groq\//, '');
  }
}