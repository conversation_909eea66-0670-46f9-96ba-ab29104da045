/**
 * Provider Registry and Manager
 * Central hub for all AI providers in JustSimpleChat
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from './base';
import { OpenAIProvider } from './openai';
import { AnthropicProvider } from './anthropic';
import { GoogleProvider } from './google';
import { MetaProvider } from './meta';
import { xAIProvider } from './xai';
import { DeepSeekProvider } from './deepseek';
import { MistralProvider } from './mistral';
import { PerplexityProvider } from './perplexity';
import { CohereProvider } from './cohere';
import { QwenProvider } from './qwen';
import { OpenRouterProvider } from './openrouter';
import { GroqProvider } from './groq';
import { TogetherProvider } from './together';
import { VertexAIProvider } from './vertex';
import { BedrockProvider } from './bedrock';
import { AI21Provider } from './ai21';
import { InflectionProvider } from './inflection';
import { MicrosoftProvider } from './microsoft';
import { NVIDIAProvider } from './nvidia';
import { ZhipuProvider } from './zhipu';

// Provider configuration interface
export interface ProviderConfig {
  id: string;
  name: string;
  apiKey: string;
  baseURL?: string;
  enabled: boolean;
  priority: number; // Lower number = higher priority for fallbacks
  rateLimit?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
  healthCheck?: {
    enabled: boolean;
    interval: number; // minutes
    timeout: number; // seconds
  };
}

// Router decision interface
export interface RouterDecision {
  providerId: string;
  modelId: string;
  confidence: number;
  reasoning: string;
  alternatives?: Array<{
    providerId: string;
    modelId: string;
    score: number;
  }>;
}

export class ProviderManager {
  private providers: Map<string, BaseProvider> = new Map();
  private configs: Map<string, ProviderConfig> = new Map();
  private healthStatus: Map<string, boolean> = new Map();
  private usageStats: Map<string, any> = new Map();
  private initialized = false;

  constructor() {
    // Defer initialization to first use to avoid build-time issues
    if (typeof window === 'undefined' && process.env.NODE_ENV !== 'production') {
      // During build, skip initialization
      console.log('⏳ Provider Manager: Deferring initialization (build time)');
    } else {
      this.initializeProviders();
    }
  }

  private ensureInitialized() {
    if (!this.initialized) {
      this.initializeProviders();
    }
  }

  private initializeProviders() {
    if (this.initialized) return;
    this.initialized = true;
    // Default provider configurations
    const defaultConfigs: ProviderConfig[] = [
      {
        id: 'openai',
        name: 'OpenAI',
        apiKey: process.env.OPENAI_API_KEY || '',
        enabled: !!process.env.OPENAI_API_KEY,
        priority: 1
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        apiKey: process.env.ANTHROPIC_API_KEY || '',
        enabled: !!process.env.ANTHROPIC_API_KEY,
        priority: 2
      },
      {
        id: 'google',
        name: 'Google',
        apiKey: process.env.GOOGLE_API_KEY || '',
        enabled: !!process.env.GOOGLE_API_KEY,
        priority: 3
      },
      {
        id: 'meta',
        name: 'Meta/Llama',
        apiKey: process.env.TOGETHER_API_KEY || process.env.GROQ_API_KEY || '',
        enabled: !!(process.env.TOGETHER_API_KEY || process.env.GROQ_API_KEY),
        priority: 4
      },
      {
        id: 'xai',
        name: 'xAI',
        apiKey: process.env.XAI_API_KEY || '',
        enabled: !!process.env.XAI_API_KEY,
        priority: 5
      },
      {
        id: 'deepseek',
        name: 'DeepSeek',
        apiKey: process.env.DEEPSEEK_API_KEY || '',
        enabled: !!process.env.DEEPSEEK_API_KEY,
        priority: 6
      },
      {
        id: 'mistral',
        name: 'Mistral',
        apiKey: process.env.MISTRAL_API_KEY || '',
        enabled: !!process.env.MISTRAL_API_KEY,
        priority: 7
      },
      {
        id: 'perplexity',
        name: 'Perplexity',
        apiKey: process.env.PERPLEXITY_API_KEY || '',
        enabled: !!process.env.PERPLEXITY_API_KEY,
        priority: 8
      },
      {
        id: 'cohere',
        name: 'Cohere',
        apiKey: process.env.COHERE_API_KEY || '',
        enabled: !!process.env.COHERE_API_KEY,
        priority: 9
      },
      {
        id: 'qwen',
        name: 'Qwen/Alibaba',
        apiKey: process.env.QWEN_API_KEY || '',
        enabled: !!process.env.QWEN_API_KEY,
        priority: 10
      },
      {
        id: 'groq',
        name: 'Groq',
        apiKey: process.env.GROQ_API_KEY || '',
        enabled: !!process.env.GROQ_API_KEY,
        priority: 11
      },
      {
        id: 'together',
        name: 'Together AI',
        apiKey: process.env.TOGETHER_API_KEY || '',
        enabled: !!process.env.TOGETHER_API_KEY,
        priority: 12
      },
      {
        id: 'vertex',
        name: 'Google Vertex AI',
        apiKey: process.env.GOOGLE_APPLICATION_CREDENTIALS || process.env.GOOGLE_API_KEY || '',
        enabled: !!(process.env.GOOGLE_APPLICATION_CREDENTIALS || process.env.GOOGLE_API_KEY),
        priority: 13
      },
      {
        id: 'bedrock',
        name: 'AWS Bedrock',
        apiKey: process.env.AWS_ACCESS_KEY_ID || '',
        enabled: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
        priority: 14
      },
      {
        id: 'ai21',
        name: 'AI21 Labs',
        apiKey: process.env.AI21_API_KEY || process.env.OPENROUTER_API_KEY || '',
        enabled: !!(process.env.AI21_API_KEY || process.env.OPENROUTER_API_KEY),
        priority: 15
      },
      {
        id: 'inflection',
        name: 'Inflection AI',
        apiKey: process.env.INFLECTION_API_KEY || process.env.OPENROUTER_API_KEY || '',
        enabled: !!(process.env.INFLECTION_API_KEY || process.env.OPENROUTER_API_KEY),
        priority: 16
      },
      {
        id: 'microsoft',
        name: 'Microsoft',
        apiKey: process.env.MICROSOFT_API_KEY || process.env.OPENROUTER_API_KEY || '',
        enabled: !!(process.env.MICROSOFT_API_KEY || process.env.OPENROUTER_API_KEY),
        priority: 17
      },
      {
        id: 'nvidia',
        name: 'NVIDIA',
        apiKey: process.env.NVIDIA_API_KEY || process.env.OPENROUTER_API_KEY || '',
        enabled: !!(process.env.NVIDIA_API_KEY || process.env.OPENROUTER_API_KEY),
        priority: 18
      },
      {
        id: 'zhipu',
        name: 'Zhipu AI',
        apiKey: process.env.ZHIPU_API_KEY || process.env.OPENROUTER_API_KEY || '',
        enabled: !!(process.env.ZHIPU_API_KEY || process.env.OPENROUTER_API_KEY),
        priority: 19
      },
      {
        id: 'openrouter',
        name: 'OpenRouter',
        apiKey: process.env.OPENROUTER_API_KEY || '',
        enabled: !!process.env.OPENROUTER_API_KEY,
        priority: 99 // Always last as universal fallback
      }
    ];

    // Initialize providers
    for (const config of defaultConfigs) {
      this.configs.set(config.id, config);
      
      if (config.enabled && config.apiKey) {
        try {
          const provider = this.createProvider(config);
          this.providers.set(config.id, provider);
          this.healthStatus.set(config.id, true);
          
          console.log(`✅ Initialized ${config.name} provider`);
        } catch (error) {
          console.warn(`⚠️ Failed to initialize ${config.name} provider:`, error);
          this.healthStatus.set(config.id, false);
        }
      }
    }

    console.log(`🚀 Provider Manager initialized with ${this.providers.size} active providers`);
  }

  private createProvider(config: ProviderConfig): BaseProvider {
    switch (config.id) {
      case 'openai':
        return new OpenAIProvider(config.apiKey, config.baseURL);
      case 'anthropic':
        return new AnthropicProvider(config.apiKey, config.baseURL);
      case 'google':
        return new GoogleProvider(config.apiKey, config.baseURL);
      case 'meta':
        // Use Groq if available, otherwise Together
        const metaProvider = process.env.GROQ_API_KEY ? 'groq' : 'together';
        return new MetaProvider(config.apiKey, metaProvider);
      case 'xai':
        return new xAIProvider(config.apiKey, config.baseURL);
      case 'deepseek':
        return new DeepSeekProvider(config.apiKey, config.baseURL);
      case 'mistral':
        return new MistralProvider(config.apiKey, config.baseURL);
      case 'perplexity':
        return new PerplexityProvider(config.apiKey, config.baseURL);
      case 'cohere':
        return new CohereProvider(config.apiKey, config.baseURL);
      case 'qwen':
        return new QwenProvider(config.apiKey, config.baseURL);
      case 'groq':
        return new GroqProvider(config.apiKey, config.baseURL);
      case 'together':
        return new TogetherProvider(config.apiKey, config.baseURL);
      case 'vertex':
        return new VertexAIProvider(config.apiKey, config.baseURL);
      case 'bedrock':
        return new BedrockProvider();
      case 'ai21':
        return new AI21Provider(config.apiKey, config.baseURL);
      case 'inflection':
        return new InflectionProvider(config.apiKey, config.baseURL);
      case 'microsoft':
        return new MicrosoftProvider(config.apiKey, config.baseURL);
      case 'nvidia':
        return new NVIDIAProvider(config.apiKey, config.baseURL);
      case 'zhipu':
        return new ZhipuProvider(config.apiKey, config.baseURL);
      case 'openrouter':
        return new OpenRouterProvider(config.apiKey, config.baseURL);
      default:
        throw new Error(`Unknown provider: ${config.id}`);
    }
  }

  // Get all available models across all providers
  getAllModels(): Array<ModelInfo & { providerId: string; providerName: string }> {
    this.ensureInitialized();
    const allModels: Array<ModelInfo & { providerId: string; providerName: string }> = [];
    
    for (const [providerId, provider] of this.providers) {
      const config = this.configs.get(providerId);
      if (!config || !this.healthStatus.get(providerId)) continue;
      
      const models = provider.getAvailableModels();
      for (const model of models) {
        allModels.push({
          ...model,
          providerId,
          providerName: config.name
        });
      }
    }
    
    return allModels.sort((a, b) => {
      // Sort by tier, then by cost
      const tierOrder = { 'free': 0, 'fast': 1, 'balanced': 2, 'reasoning': 3, 'premium': 4 };
      const aTier = tierOrder[a.tier as keyof typeof tierOrder] ?? 5;
      const bTier = tierOrder[b.tier as keyof typeof tierOrder] ?? 5;
      
      if (aTier !== bTier) return aTier - bTier;
      return a.inputCost - b.inputCost;
    });
  }

  // Get models by capability
  getModelsByCapability(capability: string): Array<ModelInfo & { providerId: string }> {
    this.ensureInitialized();
    return this.getAllModels().filter(model => 
      model.capabilities.includes(capability)
    );
  }

  // Get free models
  getFreeModels(): Array<ModelInfo & { providerId: string }> {
    this.ensureInitialized();
    return this.getAllModels().filter(model => 
      model.inputCost === 0 && model.outputCost === 0
    );
  }

  // Get fastest models (under $0.1 per 1K tokens)
  getFastModels(): Array<ModelInfo & { providerId: string }> {
    this.ensureInitialized();
    return this.getAllModels().filter(model => 
      model.tier === 'fast' || (model.inputCost + model.outputCost) < 0.2
    );
  }

  // Smart routing based on requirements
  async route(
    prompt: string,
    requirements: {
      maxCost?: number;
      minContextWindow?: number;
      requiredCapabilities?: string[];
      preferFree?: boolean;
      preferSpeed?: boolean;
      excludeProviders?: string[];
      language?: string;
    } = {}
  ): Promise<RouterDecision> {
    const analysis = this.analyzePrompt(prompt);
    const candidateModels = this.filterModelsByRequirements(requirements);
    const scoredModels = this.scoreModels(analysis, candidateModels, requirements);
    
    const bestModel = scoredModels[0];
    
    return {
      providerId: bestModel.providerId,
      modelId: bestModel.id,
      confidence: bestModel.score,
      reasoning: this.generateReasoning(analysis, bestModel, requirements),
      alternatives: scoredModels.slice(1, 4).map(m => ({
        providerId: m.providerId,
        modelId: m.id,
        score: m.score
      }))
    };
  }

  private analyzePrompt(prompt: string): any {
    const analysis = {
      type: 'general',
      complexity: 'medium',
      language: 'english',
      estimatedTokens: Math.ceil(prompt.length / 4),
      features: [] as string[]
    };

    // Detect task type
    const lowerPrompt = prompt.toLowerCase();
    
    if (/code|programming|debug|implement|function|class|algorithm/.test(lowerPrompt)) {
      analysis.type = 'code';
      analysis.features.push('code');
    }
    
    if (/search|news|current|latest|price|weather|today/.test(lowerPrompt)) {
      analysis.type = 'search';
      analysis.features.push('search', 'realtime');
    }
    
    if (/creative|story|poem|haiku|brainstorm|imagine/.test(lowerPrompt)) {
      analysis.type = 'creative';
      analysis.features.push('creative');
    }
    
    if (/math|calculate|solve|equation|proof|formula/.test(lowerPrompt)) {
      analysis.type = 'math';
      analysis.features.push('reasoning', 'math');
    }
    
    if (/think|reason|analyze|explain|why|how/.test(lowerPrompt)) {
      analysis.features.push('reasoning');
    }
    
    if (/image|picture|photo|visual|see|look/.test(lowerPrompt)) {
      analysis.features.push('vision');
    }
    
    if (/tool|function|call|api|search/.test(lowerPrompt)) {
      analysis.features.push('tools');
    }

    // Detect language
    if (/[\u4e00-\u9fff]/.test(prompt)) {
      analysis.language = 'chinese';
      analysis.features.push('chinese');
    }
    
    // Estimate complexity
    if (prompt.length > 1000 || /complex|detailed|comprehensive|thorough/.test(lowerPrompt)) {
      analysis.complexity = 'high';
    } else if (prompt.length < 100 || /quick|simple|brief/.test(lowerPrompt)) {
      analysis.complexity = 'low';
    }

    return analysis;
  }

  private filterModelsByRequirements(requirements: any): Array<ModelInfo & { providerId: string }> {
    let models = this.getAllModels();

    if (requirements.maxCost) {
      models = models.filter(m => (m.inputCost + m.outputCost) <= requirements.maxCost);
    }

    if (requirements.minContextWindow) {
      models = models.filter(m => m.contextWindow >= requirements.minContextWindow);
    }

    if (requirements.requiredCapabilities) {
      models = models.filter(m => 
        requirements.requiredCapabilities.every((cap: string) => m.capabilities.includes(cap))
      );
    }

    if (requirements.preferFree) {
      const freeModels = models.filter(m => m.inputCost === 0 && m.outputCost === 0);
      if (freeModels.length > 0) models = freeModels;
    }

    if (requirements.excludeProviders) {
      models = models.filter(m => !requirements.excludeProviders.includes(m.providerId));
    }

    return models;
  }

  private scoreModels(analysis: any, models: Array<ModelInfo & { providerId: string }>, requirements: any): Array<any> {
    const scoredModels = models.map(model => {
      let score = 0;

      // Base capability matching
      for (const feature of analysis.features) {
        if (model.capabilities.includes(feature)) {
          score += 10;
        }
      }

      // Task type specific scoring
      switch (analysis.type) {
        case 'code':
          if (model.capabilities.includes('code')) score += 20;
          if (model.id.includes('coder') || model.id.includes('code')) score += 15;
          break;
        case 'search':
          if (model.capabilities.includes('search')) score += 25;
          if (model.capabilities.includes('realtime')) score += 15;
          break;
        case 'creative':
          if (model.capabilities.includes('creative')) score += 20;
          if (model.id.includes('opus') || model.id.includes('creative')) score += 15;
          break;
        case 'math':
          if (model.capabilities.includes('reasoning')) score += 20;
          if (model.id.includes('o3') || model.id.includes('math')) score += 15;
          break;
      }

      // Language preference
      if (analysis.language === 'chinese' && model.capabilities.includes('chinese')) {
        score += 15;
      }

      // Cost efficiency (prefer cheaper models when quality is similar)
      const costPer1K = model.inputCost + model.outputCost;
      if (costPer1K === 0) score += 5; // Free models bonus
      else if (costPer1K < 0.5) score += 3; // Cheap models bonus

      // Tier bonuses
      const tierBonus = {
        'free': 5,
        'fast': 3,
        'balanced': 7,
        'reasoning': analysis.type === 'math' ? 15 : 5,
        'premium': analysis.complexity === 'high' ? 10 : 0
      };
      score += tierBonus[model.tier as keyof typeof tierBonus] || 0;

      // Provider health and priority
      const config = this.configs.get(model.providerId);
      if (config && this.healthStatus.get(model.providerId)) {
        score += Math.max(0, 11 - config.priority); // Higher priority = higher score
      } else {
        score -= 50; // Heavily penalize unhealthy providers
      }

      // Speed preference
      if (requirements.preferSpeed && model.tier === 'fast') {
        score += 10;
      }

      return { ...model, score };
    });

    return scoredModels.sort((a, b) => b.score - a.score);
  }

  private generateReasoning(analysis: any, bestModel: any, requirements: any): string {
    const reasons = [];
    
    reasons.push(`Detected ${analysis.type} task with ${analysis.complexity} complexity`);
    
    if (bestModel.capabilities.includes(analysis.features[0])) {
      reasons.push(`Selected ${bestModel.name} for its ${analysis.features[0]} capabilities`);
    }
    
    if (requirements.preferFree && bestModel.inputCost === 0) {
      reasons.push('Chose free model per user preference');
    }
    
    if (requirements.preferSpeed && bestModel.tier === 'fast') {
      reasons.push('Optimized for speed as requested');
    }
    
    return reasons.join('. ');
  }

  // Execute chat with automatic fallback
  async chat(
    modelId: string,
    messages: Message[],
    options: ChatOptions = {}
  ): Promise<ChatResponse & { usedProvider: string; usedModel: string }> {
    // Find provider for model
    const model = this.getAllModels().find(m => m.id === modelId);
    if (!model) {
      throw new Error(`Model not found: ${modelId}`);
    }

    const provider = this.providers.get(model.providerId);
    if (!provider) {
      throw new Error(`Provider not available: ${model.providerId}`);
    }

    try {
      const response = await provider.chat(modelId, messages, options);
      
      // Track usage
      this.trackUsage(model.providerId, modelId, response.usage);
      
      return {
        ...response,
        usedProvider: model.providerId,
        usedModel: modelId
      };
    } catch (error) {
      // Mark provider as unhealthy temporarily
      this.healthStatus.set(model.providerId, false);
      
      // Try fallback with similar models
      if ((options as any).enableFallback !== false) {
        return this.chatWithFallback(modelId, messages, options);
      }
      
      throw error;
    }
  }

  // Fallback chat with similar models
  private async chatWithFallback(
    originalModelId: string,
    messages: Message[],
    options: ChatOptions
  ): Promise<ChatResponse & { usedProvider: string; usedModel: string }> {
    const originalModel = this.getAllModels().find(m => m.id === originalModelId);
    if (!originalModel) throw new Error('Original model not found');

    // Find similar models
    const similarModels = this.getAllModels()
      .filter(m => 
        m.id !== originalModelId &&
        m.tier === originalModel.tier &&
        m.capabilities.some(cap => originalModel.capabilities.includes(cap)) &&
        this.healthStatus.get(m.providerId)
      )
      .sort((a, b) => {
        const aConfig = this.configs.get(a.providerId);
        const bConfig = this.configs.get(b.providerId);
        return (aConfig?.priority || 999) - (bConfig?.priority || 999);
      })
      .slice(0, 3);

    let lastError: Error | null = null;

    for (const model of similarModels) {
      try {
        const provider = this.providers.get(model.providerId);
        if (!provider) continue;

        const response = await provider.chat(model.id, messages, options);
        
        // Track fallback usage
        this.trackUsage(model.providerId, model.id, response.usage);
        
        console.log(`✅ Fallback successful: ${originalModelId} -> ${model.id}`);
        
        return {
          ...response,
          usedProvider: model.providerId,
          usedModel: model.id
        };
      } catch (error) {
        lastError = error as Error;
        this.healthStatus.set(model.providerId, false);
        console.warn(`❌ Fallback failed for ${model.id}:`, error);
      }
    }

    throw new Error(`All fallbacks failed. Last error: ${lastError?.message}`);
  }

  // Stream chat with fallback
  async *streamChat(
    modelId: string,
    messages: Message[],
    options: ChatOptions = {}
  ): AsyncIterable<StreamChunk> {
    const model = this.getAllModels().find(m => m.id === modelId);
    if (!model) {
      throw new Error(`Model not found: ${modelId}`);
    }

    const provider = this.providers.get(model.providerId);
    if (!provider) {
      throw new Error(`Provider not available: ${model.providerId}`);
    }

    try {
      for await (const chunk of provider.streamChat(modelId, messages, options)) {
        yield chunk;
      }
    } catch (error) {
      this.healthStatus.set(model.providerId, false);
      throw error;
    }
  }

  private trackUsage(providerId: string, modelId: string, usage: any) {
    const key = `${providerId}:${modelId}`;
    const stats = this.usageStats.get(key) || {
      requests: 0,
      totalTokens: 0,
      totalCost: 0,
      lastUsed: null
    };

    stats.requests++;
    stats.totalTokens += usage.totalTokens || 0;
    stats.totalCost += usage.cost || 0;
    stats.lastUsed = new Date();

    this.usageStats.set(key, stats);
  }

  // Health check for all providers
  async healthCheck(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();

    for (const [providerId, provider] of this.providers) {
      try {
        // Simple health check - try to get available models
        await provider.getAvailableModels();
        results.set(providerId, true);
        this.healthStatus.set(providerId, true);
      } catch (error) {
        results.set(providerId, false);
        this.healthStatus.set(providerId, false);
        console.warn(`❌ Health check failed for ${providerId}:`, error);
      }
    }

    return results;
  }

  // Get usage statistics
  getUsageStats(): Map<string, any> {
    return new Map(this.usageStats);
  }

  // Get provider status
  getProviderStatus(): Array<{
    id: string;
    name: string;
    enabled: boolean;
    healthy: boolean;
    modelCount: number;
    priority: number;
  }> {
    this.ensureInitialized();
    const status = [];

    for (const [providerId, config] of this.configs) {
      const provider = this.providers.get(providerId);
      const modelCount = provider ? provider.getAvailableModels().length : 0;

      status.push({
        id: providerId,
        name: config.name,
        enabled: config.enabled,
        healthy: this.healthStatus.get(providerId) || false,
        modelCount,
        priority: config.priority
      });
    }

    return status.sort((a, b) => a.priority - b.priority);
  }
}

// Export singleton instance
export const providerManager = new ProviderManager();

// Export all provider classes
export {
  BaseProvider,
  OpenAIProvider,
  AnthropicProvider,
  GoogleProvider,
  MetaProvider,
  xAIProvider,
  DeepSeekProvider,
  MistralProvider,
  PerplexityProvider,
  CohereProvider,
  QwenProvider,
  GroqProvider,
  TogetherProvider,
  VertexAIProvider,
  BedrockProvider,
  AI21Provider,
  InflectionProvider,
  MicrosoftProvider,
  NVIDIAProvider,
  ZhipuProvider,
  OpenRouterProvider
};

// Export types
export type {
  ModelInfo,
  ChatOptions,
  ChatResponse,
  StreamChunk,
  Message
};