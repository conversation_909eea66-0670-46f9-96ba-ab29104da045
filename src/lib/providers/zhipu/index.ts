/**
 * Zhipu AI Provider - GLM models via OpenRouter
 * Based on research: Accessed via OpenRouter unified API
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class ZhipuProvider extends BaseProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.apiKey = apiKey;
    this.baseURL = baseURL || 'https://openrouter.ai/api/v1';
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'zhipu/glm-4-plus',
        name: 'GLM-4-Plus',
        description: 'Zhipu AI GLM-4-Plus advanced reasoning and generation model',
        tier: 'premium',
        contextWindow: 128000,
        inputCost: 5.00, // per 1M tokens
        outputCost: 15.00,
        capabilities: ['chat', 'reasoning', 'chinese', 'multimodal', 'long-context'],
        provider: 'zhipu',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: true
        }
      },
      {
        id: 'zhipu/glm-4-0520',
        name: 'GLM-4-0520',
        description: 'Zhipu AI GLM-4 May 2024 release with enhanced capabilities',
        tier: 'balanced',
        contextWindow: 128000,
        inputCost: 1.00,
        outputCost: 3.00,
        capabilities: ['chat', 'reasoning', 'chinese', 'multilingual'],
        provider: 'zhipu',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'zhipu/glm-4-air',
        name: 'GLM-4-Air',
        description: 'Zhipu AI GLM-4-Air lightweight and efficient model',
        tier: 'fast',
        contextWindow: 128000,
        inputCost: 0.10,
        outputCost: 0.10,
        capabilities: ['chat', 'reasoning', 'chinese', 'efficient'],
        provider: 'zhipu',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'zhipu/glm-4-flash',
        name: 'GLM-4-Flash',
        description: 'Zhipu AI GLM-4-Flash ultra-fast inference model',
        tier: 'fast',
        contextWindow: 128000,
        inputCost: 0.10,
        outputCost: 0.10,
        capabilities: ['chat', 'reasoning', 'chinese', 'fast-inference'],
        provider: 'zhipu',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'zhipu/glm-4v-plus',
        name: 'GLM-4V-Plus',
        description: 'Zhipu AI GLM-4V-Plus multimodal vision model',
        tier: 'premium',
        contextWindow: 8192,
        inputCost: 10.00,
        outputCost: 30.00,
        capabilities: ['chat', 'reasoning', 'vision', 'multimodal', 'chinese'],
        provider: 'zhipu',
        features: {
          streaming: true,
          functionCalling: false,
          vision: true,
          multimodal: true
        }
      },
      {
        id: 'zhipu/codegeex-4',
        name: 'CodeGeeX-4',
        description: 'Zhipu AI CodeGeeX-4 specialized code generation model',
        tier: 'balanced',
        contextWindow: 128000,
        inputCost: 0.10,
        outputCost: 0.10,
        capabilities: ['code', 'programming', 'debugging', 'chinese', 'multilingual'],
        provider: 'zhipu',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Zhipu API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];
      
      if (!choice?.message?.content) {
        throw new Error('No response content from Zhipu');
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('Zhipu chat error:', error);
      throw new Error(`Zhipu API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Zhipu API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const event = JSON.parse(data);
              const choice = event.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                yield {
                  content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (choice?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: event.usage?.prompt_tokens || 0,
                    completionTokens: event.usage?.completion_tokens || 0,
                    totalTokens: event.usage?.total_tokens || fullContent.length / 4
                  },
                  finishReason: choice.finish_reason as any
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse Zhipu SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Zhipu streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Zhipu streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private getOpenRouterModelName(modelId: string): string {
    const mapping: { [key: string]: string } = {
      'zhipu/glm-4-plus': 'zhipu/glm-4-plus',
      'zhipu/glm-4-0520': 'zhipu/glm-4-0520',
      'zhipu/glm-4-air': 'zhipu/glm-4-air',
      'zhipu/glm-4-flash': 'zhipu/glm-4-flash',
      'zhipu/glm-4v-plus': 'zhipu/glm-4v-plus',
      'zhipu/codegeex-4': 'zhipu/codegeex-4'
    };
    return mapping[modelId] || modelId.replace(/^zhipu\//, 'zhipu/');
  }
}