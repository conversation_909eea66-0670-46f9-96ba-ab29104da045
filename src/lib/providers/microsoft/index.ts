/**
 * Microsoft Provider - Phi models via OpenRouter
 * Based on research: Accessed via OpenRouter unified API
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class MicrosoftProvider extends BaseProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.apiKey = apiKey;
    this.baseURL = baseURL || 'https://openrouter.ai/api/v1';
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'microsoft/phi-3-medium-128k-instruct',
        name: 'Phi-3 Medium 128K Instruct',
        description: 'Microsoft Phi-3 Medium with 128K context window',
        tier: 'balanced',
        contextWindow: 128000,
        inputCost: 1.00, // per 1M tokens
        outputCost: 1.00,
        capabilities: ['chat', 'reasoning', 'instruction-following', 'long-context'],
        provider: 'microsoft',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'microsoft/phi-3-mini-128k-instruct',
        name: 'Phi-3 Mini 128K Instruct',
        description: 'Microsoft Phi-3 Mini compact model with 128K context',
        tier: 'fast',
        contextWindow: 128000,
        inputCost: 0.10,
        outputCost: 0.10,
        capabilities: ['chat', 'reasoning', 'instruction-following', 'efficient'],
        provider: 'microsoft',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'microsoft/wizardlm-2-8x22b',
        name: 'WizardLM-2 8x22B',
        description: 'Microsoft WizardLM-2 large mixture of experts model',
        tier: 'premium',
        contextWindow: 65536,
        inputCost: 1.00,
        outputCost: 1.00,
        capabilities: ['chat', 'reasoning', 'complex-tasks', 'mixture-of-experts'],
        provider: 'microsoft',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'microsoft/wizardlm-2-7b',
        name: 'WizardLM-2 7B',
        description: 'Microsoft WizardLM-2 7B efficient model',
        tier: 'balanced',
        contextWindow: 32768,
        inputCost: 0.07,
        outputCost: 0.07,
        capabilities: ['chat', 'reasoning', 'efficient'],
        provider: 'microsoft',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Microsoft API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];
      
      if (!choice?.message?.content) {
        throw new Error('No response content from Microsoft');
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('Microsoft chat error:', error);
      throw new Error(`Microsoft API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Microsoft API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const event = JSON.parse(data);
              const choice = event.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                yield {
                  content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (choice?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: event.usage?.prompt_tokens || 0,
                    completionTokens: event.usage?.completion_tokens || 0,
                    totalTokens: event.usage?.total_tokens || fullContent.length / 4
                  },
                  finishReason: choice.finish_reason as any
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse Microsoft SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Microsoft streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Microsoft streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private getOpenRouterModelName(modelId: string): string {
    const mapping: { [key: string]: string } = {
      'microsoft/phi-3-medium-128k-instruct': 'microsoft/phi-3-medium-128k-instruct',
      'microsoft/phi-3-mini-128k-instruct': 'microsoft/phi-3-mini-128k-instruct',
      'microsoft/wizardlm-2-8x22b': 'microsoft/wizardlm-2-8x22b',
      'microsoft/wizardlm-2-7b': 'microsoft/wizardlm-2-7b'
    };
    return mapping[modelId] || modelId.replace(/^microsoft\//, 'microsoft/');
  }
}