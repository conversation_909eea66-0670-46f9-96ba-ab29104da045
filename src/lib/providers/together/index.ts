/**
 * Together AI Provider - Open-source models with content filtering
 * Based on research: ReadableStream interface with token-based delivery
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class TogetherProvider extends BaseProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.apiKey = apiKey;
    this.baseURL = baseURL || 'https://api.together.xyz/v1';
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'together/mixtral-8x7b-instruct-v0.1',
        name: 'Mixtral 8x7B Instruct',
        description: 'Mistral MoE model optimized for instruction following',
        tier: 'balanced',
        contextWindow: 32768,
        inputCost: 0.2,
        outputCost: 0.2,
        capabilities: ['chat', 'reasoning', 'long-context', 'multilingual'],
        provider: 'together',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'together/llama-3.1-8b-instruct',
        name: 'Llama 3.1 8B Instruct',
        description: 'Meta Llama 3.1 8B instruction-tuned model',
        tier: 'fast',
        contextWindow: 8192,
        inputCost: 0.18,
        outputCost: 0.18,
        capabilities: ['chat', 'reasoning', 'code'],
        provider: 'together',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'together/llama-3.1-70b-instruct',
        name: 'Llama 3.1 70B Instruct',
        description: 'Meta Llama 3.1 70B high-performance model',
        tier: 'balanced',
        contextWindow: 8192,
        inputCost: 0.88,
        outputCost: 0.88,
        capabilities: ['chat', 'reasoning', 'code', 'creative'],
        provider: 'together',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'together/llama-3.3-70b-instruct',
        name: 'Llama 3.3 70B Instruct',
        description: 'Latest Meta Llama 3.3 70B with improved capabilities',
        tier: 'balanced',
        contextWindow: 8192,
        inputCost: 0.88,
        outputCost: 0.88,
        capabilities: ['chat', 'reasoning', 'code', 'creative', 'latest'],
        provider: 'together',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'together/codellama-70b-instruct',
        name: 'Code Llama 70B Instruct',
        description: 'Specialized coding model based on Llama',
        tier: 'balanced',
        contextWindow: 4096,
        inputCost: 0.90,
        outputCost: 0.90,
        capabilities: ['code', 'programming', 'debugging', 'chat'],
        provider: 'together',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'together/qwen-qwq-32b-preview',
        name: 'Qwen QwQ 32B Preview',
        description: 'Qwen reasoning model with strong analytical capabilities',
        tier: 'reasoning',
        contextWindow: 32768,
        inputCost: 1.20,
        outputCost: 1.20,
        capabilities: ['reasoning', 'math', 'analysis', 'chat', 'chinese'],
        provider: 'together',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.stripProviderPrefix(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Together AI API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];
      
      if (!choice?.message?.content) {
        throw new Error('No response content from Together AI');
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('Together AI chat error:', error);
      throw new Error(`Together AI API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.stripProviderPrefix(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: true,
          stream_tokens: true // Together AI specific
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Together AI API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const event = JSON.parse(data);
              const choice = event.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                yield {
                  content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (choice?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: event.usage?.prompt_tokens || 0,
                    completionTokens: event.usage?.completion_tokens || 0,
                    totalTokens: event.usage?.total_tokens || fullContent.length / 4
                  },
                  finishReason: choice.finish_reason as any
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse Together AI SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('Together AI streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `Together AI streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private stripProviderPrefix(modelId: string): string {
    return modelId.replace(/^together\//, '');
  }
}