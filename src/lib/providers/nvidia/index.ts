/**
 * NVIDIA Provider - NIM models and Nemotron via OpenRouter
 * Based on research: Accessed via OpenRouter unified API
 */

import { BaseProvider, ModelInfo, ChatOptions, ChatResponse, StreamChunk, Message } from '../base';

export class NVIDIAProvider extends BaseProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL?: string) {
    super();
    this.apiKey = apiKey;
    this.baseURL = baseURL || 'https://openrouter.ai/api/v1';
  }

  getAvailableModels(): ModelInfo[] {
    return [
      {
        id: 'nvidia/nemotron-4-340b-instruct',
        name: 'Nemotron-4 340B Instruct',
        description: 'NVIDIA Nemotron-4 340B large instruction-following model',
        tier: 'premium',
        contextWindow: 4096,
        inputCost: 4.20, // per 1M tokens
        outputCost: 4.20,
        capabilities: ['chat', 'reasoning', 'instruction-following', 'enterprise'],
        provider: 'nvidia',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'nvidia/llama-3.1-nemotron-70b-instruct',
        name: 'Llama-3.1-Nemotron-70B-Instruct',
        description: 'NVIDIA optimized Llama 3.1 Nemotron 70B model',
        tier: 'premium',
        contextWindow: 131072,
        inputCost: 0.35,
        outputCost: 0.40,
        capabilities: ['chat', 'reasoning', 'long-context', 'optimized'],
        provider: 'nvidia',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'nvidia/llama-3.1-nemotron-51b-instruct',
        name: 'Llama-3.1-Nemotron-51B-Instruct',
        description: 'NVIDIA optimized Llama 3.1 Nemotron 51B model',
        tier: 'balanced',
        contextWindow: 131072,
        inputCost: 0.30,
        outputCost: 0.30,
        capabilities: ['chat', 'reasoning', 'long-context', 'optimized'],
        provider: 'nvidia',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'nvidia/nim-llama-3.1-70b-instruct',
        name: 'NIM Llama-3.1-70B-Instruct',
        description: 'NVIDIA NIM-optimized Llama 3.1 70B for enterprise deployment',
        tier: 'premium',
        contextWindow: 128000,
        inputCost: 0.35,
        outputCost: 0.40,
        capabilities: ['chat', 'reasoning', 'nim-optimized', 'enterprise'],
        provider: 'nvidia',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      },
      {
        id: 'nvidia/nim-mixtral-8x7b-instruct',
        name: 'NIM Mixtral-8x7B-Instruct',
        description: 'NVIDIA NIM-optimized Mixtral 8x7B mixture of experts',
        tier: 'balanced',
        contextWindow: 32768,
        inputCost: 0.24,
        outputCost: 0.24,
        capabilities: ['chat', 'reasoning', 'mixture-of-experts', 'nim-optimized'],
        provider: 'nvidia',
        features: {
          streaming: true,
          functionCalling: false,
          vision: false,
          multimodal: false
        }
      }
    ];
  }

  async chat(modelId: string, messages: Message[], options: ChatOptions = {}): Promise<ChatResponse> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`NVIDIA API error: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      const choice = data.choices[0];
      
      if (!choice?.message?.content) {
        throw new Error('No response content from NVIDIA');
      }

      return {
        content: choice.message.content,
        usage: {
          promptTokens: data.usage?.prompt_tokens || 0,
          completionTokens: data.usage?.completion_tokens || 0,
          totalTokens: data.usage?.total_tokens || 0
        },
        finishReason: choice.finish_reason as any,
        model: modelId
      };
    } catch (error) {
      console.error('NVIDIA chat error:', error);
      throw new Error(`NVIDIA API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *streamChat(modelId: string, messages: Message[], options: ChatOptions = {}): AsyncIterable<StreamChunk> {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        },
        body: JSON.stringify({
          model: this.getOpenRouterModelName(modelId),
          messages: messages,
          max_tokens: options.maxTokens || 4000,
          temperature: options.temperature || 0.7,
          stream: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`NVIDIA API error: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const event = JSON.parse(data);
              const choice = event.choices?.[0];
              
              if (choice?.delta?.content) {
                const content = choice.delta.content;
                fullContent += content;
                
                yield {
                  content,
                  isComplete: false,
                  type: 'content'
                };
              }

              if (choice?.finish_reason) {
                yield {
                  content: '',
                  isComplete: true,
                  usage: {
                    promptTokens: event.usage?.prompt_tokens || 0,
                    completionTokens: event.usage?.completion_tokens || 0,
                    totalTokens: event.usage?.total_tokens || fullContent.length / 4
                  },
                  finishReason: choice.finish_reason as any
                };
              }
            } catch (parseError) {
              console.warn('Failed to parse NVIDIA SSE data:', parseError);
            }
          }
        }
      }
    } catch (error) {
      console.error('NVIDIA streaming error:', error);
      yield {
        content: '',
        isComplete: true,
        error: `NVIDIA streaming error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  private getOpenRouterModelName(modelId: string): string {
    const mapping: { [key: string]: string } = {
      'nvidia/nemotron-4-340b-instruct': 'nvidia/nemotron-4-340b-instruct',
      'nvidia/llama-3.1-nemotron-70b-instruct': 'nvidia/llama-3.1-nemotron-70b-instruct',
      'nvidia/llama-3.1-nemotron-51b-instruct': 'nvidia/llama-3.1-nemotron-51b-instruct',
      'nvidia/nim-llama-3.1-70b-instruct': 'nvidia/llama-3.1-70b-instruct', // NIM variant
      'nvidia/nim-mixtral-8x7b-instruct': 'nvidia/mixtral-8x7b-instruct' // NIM variant
    };
    return mapping[modelId] || modelId.replace(/^nvidia\//, 'nvidia/');
  }
}