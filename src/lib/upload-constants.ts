/**
 * File Upload Constants
 * Centralized configuration for file upload limits and validation
 */

// Maximum file size for uploads (50MB)
export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes

// Human-readable file size limit
export const MAX_FILE_SIZE_DISPLAY = '50MB';

// File size validation function
export function validateFileSize(file: File): { valid: boolean; error?: string } {
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File "${file.name}" is too large. Maximum size is ${MAX_FILE_SIZE_DISPLAY}.`
    };
  }
  return { valid: true };
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}