/**
 * Enhanced Structured Data Generator for SEO
 * Implements JSON-LD schemas for rich snippets and knowledge graph
 */

import { type Model } from '@/types'

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://justsimple.chat'

interface BreadcrumbItem {
  name: string
  url?: string
}

interface Review {
  rating: number
  author: string
  reviewBody: string
  datePublished?: string
}

interface VideoData {
  name: string
  description: string
  thumbnailUrl?: string
  uploadDate?: string
  duration?: string
  contentUrl?: string
  embedUrl?: string
}

interface EventData {
  name: string
  description: string
  startDate: string
  endDate?: string
  location?: string
  eventStatus?: 'EventScheduled' | 'EventCancelled' | 'EventPostponed' | 'EventRescheduled'
}

/**
 * Generate comprehensive organization schema with SearchAction
 */
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    '@id': `${BASE_URL}/#organization`,
    name: 'JustSimpleChat',
    url: BASE_URL,
    logo: `${BASE_URL}/logo-128.png`,
    description: 'Access 150+ AI models through one unified platform. Save 60% compared to individual subscriptions.',
    founder: {
      '@type': 'Person',
      name: 'JustSimpleChat Team'
    },
    foundingDate: '2024-01-01',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer support',
      availableLanguage: ['English', 'Spanish', 'Chinese', 'Hindi', 'Portuguese']
    },
    sameAs: [
      'https://twitter.com/justsimplechat',
      'https://github.com/justsimplechat'
    ],
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${BASE_URL}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    }
  }
}

/**
 * Generate SoftwareApplication schema with ratings
 */
export function generateSoftwareApplicationSchema(reviews: Review[] = []) {
  const defaultReviews: Review[] = [
    {
      rating: 5,
      author: 'TechReviewer2025',
      reviewBody: 'Best AI chat platform! Having all models in one place saves me hours every day.',
      datePublished: '2025-01-15'
    },
    {
      rating: 5,
      author: 'AI Enthusiast',
      reviewBody: 'Smart routing ensures I always get the best model for my task. Incredible value.',
      datePublished: '2025-01-10'
    },
    {
      rating: 4,
      author: 'Developer Mike',
      reviewBody: 'Great platform with extensive model selection. Would love to see more API features.',
      datePublished: '2025-01-05'
    }
  ]

  const allReviews = [...defaultReviews, ...reviews]
  const avgRating = allReviews.reduce((sum, r) => sum + r.rating, 0) / allReviews.length

  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    '@id': `${BASE_URL}/#software`,
    name: 'JustSimpleChat',
    applicationCategory: 'CommunicationApplication',
    applicationSubCategory: 'AI Chat Platform',
    operatingSystem: 'Web, iOS, Android',
    offers: {
      '@type': 'AggregateOffer',
      priceCurrency: 'USD',
      lowPrice: '0',
      highPrice: '99',
      offerCount: '4',
      offers: [
        {
          '@type': 'Offer',
          name: 'Free Plan',
          price: '0',
          priceCurrency: 'USD'
        },
        {
          '@type': 'Offer',
          name: 'Starter Plan',
          price: '9',
          priceCurrency: 'USD'
        },
        {
          '@type': 'Offer',
          name: 'Pro Plan',
          price: '39',
          priceCurrency: 'USD'
        },
        {
          '@type': 'Offer',
          name: 'Business Plan',
          price: '99',
          priceCurrency: 'USD'
        }
      ]
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: avgRating.toFixed(1),
      reviewCount: allReviews.length,
      bestRating: '5',
      worstRating: '1'
    },
    review: allReviews.map(r => ({
      '@type': 'Review',
      reviewRating: {
        '@type': 'Rating',
        ratingValue: r.rating,
        bestRating: '5',
        worstRating: '1'
      },
      author: {
        '@type': 'Person',
        name: r.author
      },
      reviewBody: r.reviewBody,
      datePublished: r.datePublished || new Date().toISOString().split('T')[0]
    })),
    featureList: [
      '150+ AI Models',
      'Smart Model Routing',
      'Multi-Model Conversations',
      'Image Generation',
      'Code Analysis',
      'Document Processing',
      'Real-time Streaming',
      'Model Comparison'
    ],
    screenshot: `${BASE_URL}/screenshots/app-main.png`,
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString()
  }
}

/**
 * Generate VideoObject schema for tutorials
 */
export function generateVideoSchema(video: VideoData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'VideoObject',
    name: video.name,
    description: video.description,
    thumbnailUrl: video.thumbnailUrl || `${BASE_URL}/video-thumb.jpg`,
    uploadDate: video.uploadDate || '2025-01-01',
    duration: video.duration || 'PT5M30S',
    contentUrl: video.contentUrl,
    embedUrl: video.embedUrl,
    publisher: {
      '@type': 'Organization',
      name: 'JustSimpleChat',
      logo: {
        '@type': 'ImageObject',
        url: `${BASE_URL}/logo-128.png`
      }
    },
    potentialAction: {
      '@type': 'WatchAction',
      target: video.embedUrl || video.contentUrl
    }
  }
}

/**
 * Generate dynamic BreadcrumbList schema
 */
export function generateBreadcrumbSchema(items: BreadcrumbItem[]) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url ? `${BASE_URL}${item.url}` : undefined
    }))
  }
}

/**
 * Generate Event schema for product updates
 */
export function generateEventSchema(event: EventData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: event.name,
    description: event.description,
    startDate: event.startDate,
    endDate: event.endDate || event.startDate,
    eventStatus: `https://schema.org/${event.eventStatus || 'EventScheduled'}`,
    eventAttendanceMode: 'https://schema.org/OnlineEventAttendanceMode',
    location: {
      '@type': 'VirtualLocation',
      url: BASE_URL
    },
    organizer: {
      '@type': 'Organization',
      name: 'JustSimpleChat',
      url: BASE_URL
    }
  }
}

/**
 * Generate HowTo schema for getting started
 */
export function generateHowToSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name: 'How to Use JustSimpleChat',
    description: 'Get started with JustSimpleChat in 3 easy steps',
    image: `${BASE_URL}/tutorial-hero.jpg`,
    totalTime: 'PT2M',
    estimatedCost: {
      '@type': 'MonetaryAmount',
      currency: 'USD',
      value: '0'
    },
    supply: [],
    tool: [],
    step: [
      {
        '@type': 'HowToStep',
        text: 'Sign up for a free account',
        image: `${BASE_URL}/step-1.jpg`,
        name: 'Create Account',
        url: `${BASE_URL}/signup`
      },
      {
        '@type': 'HowToStep',
        text: 'Choose your AI model or let smart routing decide',
        image: `${BASE_URL}/step-2.jpg`,
        name: 'Select Model',
        url: `${BASE_URL}/models`
      },
      {
        '@type': 'HowToStep',
        text: 'Start chatting with the most powerful AI models',
        image: `${BASE_URL}/step-3.jpg`,
        name: 'Start Chatting',
        url: `${BASE_URL}/chat`
      }
    ]
  }
}

/**
 * Generate FAQ schema
 */
export function generateFAQSchema() {
  const faqs = [
    {
      question: 'What AI models are available on JustSimpleChat?',
      answer: 'JustSimpleChat provides access to over 150 AI models including GPT-4, Claude 3, Gemini Pro, and many more. We continuously add new models as they become available.'
    },
    {
      question: 'How much does JustSimpleChat cost?',
      answer: 'We offer a free plan with limited usage, and paid plans starting at $9/month. Our Pro plan at $39/month gives you access to all models with generous limits.'
    },
    {
      question: 'How does smart routing work?',
      answer: 'Our intelligent routing system analyzes your query and automatically selects the best AI model for your specific task, optimizing for quality, speed, and cost.'
    },
    {
      question: 'Can I use multiple models in one conversation?',
      answer: 'Yes! You can switch between different AI models within the same conversation to leverage each model\'s unique strengths.'
    },
    {
      question: 'Is my data secure on JustSimpleChat?',
      answer: 'Absolutely. We use enterprise-grade encryption, never train on your data, and you can delete your conversations at any time.'
    }
  ]

  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  }
}

/**
 * Generate Product schema for specific AI models
 */
export function generateModelProductSchema(model: Model) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: model.name,
    description: model.description || `${model.name} AI model available on JustSimpleChat`,
    brand: {
      '@type': 'Brand',
      name: model.provider
    },
    offers: {
      '@type': 'Offer',
      url: `${BASE_URL}/models/${model.id}`,
      priceCurrency: 'USD',
      price: '0',
      priceValidUntil: '2025-12-31',
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: 'JustSimpleChat'
      }
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '150'
    }
  }
}

/**
 * Generate complete structured data for a page
 */
export function generatePageStructuredData(
  pageType: 'home' | 'models' | 'pricing' | 'chat' | 'model-detail' | 'comparison' | 'alternative' | 'feature' | 'use-case' | 'model-comparison',
  additionalData?: any
) {
  const schemas = []

  // Always include organization
  schemas.push(generateOrganizationSchema())

  // Add page-specific schemas
  switch (pageType) {
    case 'home':
      schemas.push(generateSoftwareApplicationSchema())
      schemas.push(generateFAQSchema())
      schemas.push(generateHowToSchema())
      break
    case 'models':
      schemas.push(generateSoftwareApplicationSchema())
      if (additionalData?.models) {
        additionalData.models.slice(0, 5).forEach((model: Model) => {
          schemas.push(generateModelProductSchema(model))
        })
      }
      break
    case 'pricing':
      schemas.push(generateSoftwareApplicationSchema())
      schemas.push(generateFAQSchema())
      break
    case 'model-detail':
      if (additionalData?.model) {
        schemas.push(generateModelProductSchema(additionalData.model))
      }
      break
    case 'comparison':
    case 'model-comparison':
      if (additionalData?.models) {
        additionalData.models.forEach((model: Model) => {
          schemas.push(generateModelProductSchema(model))
        })
      }
      break
    case 'alternative':
    case 'feature':
    case 'use-case':
      schemas.push(generateSoftwareApplicationSchema())
      schemas.push(generateFAQSchema())
      break
  }

  // Add breadcrumbs if provided
  if (additionalData?.breadcrumbs) {
    schemas.push(generateBreadcrumbSchema(additionalData.breadcrumbs))
  }

  return {
    '@context': 'https://schema.org',
    '@graph': schemas
  }
}