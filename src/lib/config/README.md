# Configuration System

This directory contains a type-safe configuration system built with <PERSON><PERSON> for validation.

## Overview

The configuration system provides:
- Type-safe configuration with <PERSON>od schemas
- Automatic validation of environment variables
- Support for partial updates
- Sanitization for logging (removes sensitive data)
- Environment-specific helpers
- Feature flag management

## Usage

### Basic Usage

```typescript
import { getConfig } from '@/lib/config';

// Get the full configuration
const config = getConfig();

// Access specific values
const port = config.server.port;
const redisHost = config.redis.host;
```

### Environment Checks

```typescript
import { isDevelopment, isProduction } from '@/lib/config';

if (isDevelopment()) {
  // Development-specific code
}

if (isProduction()) {
  // Production-specific code
}
```

### Feature Flags

```typescript
import { isFeatureEnabled } from '@/lib/config';

if (isFeatureEnabled('enableStreaming')) {
  // Streaming is enabled
}

if (isFeatureEnabled('enableReasoningModels')) {
  // Reasoning models are enabled
}
```

### Provider Configuration

```typescript
import { getProviderConfig, isProviderConfigured } from '@/lib/config';

// Check if a provider is configured
if (isProviderConfigured('openai')) {
  const openaiConfig = getProviderConfig('openai');
  // Use OpenAI with config
}

// Get all configured providers
import { getConfig } from '@/lib/config';
const config = getConfig();
const configuredProviders = Object.entries(config.aiProviders)
  .filter(([_, provider]) => provider.apiKey)
  .map(([name]) => name);
```

### Validation

```typescript
import { validateConfig, validatePartialConfig, isValidConfig } from '@/lib/config';

// Validate a complete configuration
try {
  const validConfig = validateConfig(someConfig);
} catch (error) {
  console.error('Invalid configuration:', error);
}

// Validate a partial configuration
const partialConfig = validatePartialConfig({
  server: { port: 3001 }
});

// Check if configuration is valid
if (isValidConfig(someConfig)) {
  // Configuration is valid
}
```

### Environment Variables

The configuration system reads from these environment variables:

#### Database
- `DATABASE_URL` - PostgreSQL connection URL (required)
- `DATABASE_MAX_CONNECTIONS` - Maximum database connections (default: 20)
- `DATABASE_CONNECTION_TIMEOUT` - Connection timeout in ms (default: 5000)
- `DATABASE_ENABLE_LOGGING` - Enable query logging (default: false)

#### Redis
- `REDIS_HOST` - Redis host (default: localhost)
- `REDIS_PORT` - Redis port (default: 6379)
- `REDIS_PASSWORD` - Redis password (optional)
- `REDIS_DB` - Redis database number (default: 0)

#### Server
- `PORT` or `SERVER_PORT` - Server port (default: 3000)
- `HOST` or `SERVER_HOST` - Server host (default: 0.0.0.0)
- `NODE_ENV` - Environment (development/staging/production)
- `LOG_LEVEL` - Logging level (debug/info/warn/error)
- `CORS_ORIGINS` - Comma-separated CORS origins
- `TRUST_PROXY` - Trust proxy headers (default: false)

#### Authentication
- `JWT_SECRET` or `AUTH_SECRET` - JWT secret (required, min 32 chars)
- `JWT_EXPIRES_IN` - JWT expiration (default: 7d)
- `SESSION_SECRET` - Session secret (falls back to JWT_SECRET)
- `SESSION_MAX_AGE` - Session max age in ms
- `BCRYPT_ROUNDS` - Bcrypt rounds (default: 12)

#### AI Providers
- `OPENAI_API_KEY` - OpenAI API key
- `ANTHROPIC_API_KEY` - Anthropic API key
- `GOOGLE_API_KEY` or `GEMINI_API_KEY` - Google API key
- `MISTRAL_API_KEY` - Mistral API key
- `GROQ_API_KEY` - Groq API key
- `TOGETHER_API_KEY` - Together AI API key
- `XAI_API_KEY` - xAI API key
- `DEEPSEEK_API_KEY` - DeepSeek API key
- `PERPLEXITY_API_KEY` - Perplexity API key
- `COHERE_API_KEY` - Cohere API key
- `OPENROUTER_API_KEY` - OpenRouter API key
- `QWEN_API_KEY` or `ALIBABA_API_KEY` - Qwen API key

#### Feature Flags
- `ENABLE_STREAMING` - Enable streaming responses (default: true)
- `ENABLE_REASONING_MODELS` - Enable reasoning models (default: true)
- `ENABLE_MULTIMODAL` - Enable multimodal support (default: true)
- `ENABLE_FUNCTION_CALLING` - Enable function calling (default: true)
- `ENABLE_STRUCTURED_OUTPUT` - Enable structured output (default: true)
- `ENABLE_WEB_SEARCH` - Enable web search (default: false)
- `ENABLE_CODE_EXECUTION` - Enable code execution (default: false)
- `ENABLE_MODEL_ROUTING` - Enable model routing (default: true)
- `ENABLE_CACHING` - Enable caching (default: true)

## Configuration Structure

The configuration is organized into these sections:

- **database** - Database connection settings
- **redis** - Redis connection settings
- **server** - Server configuration
- **auth** - Authentication settings
- **aiProviders** - AI provider API keys and settings
- **rateLimit** - Rate limiting configuration
- **storage** - File storage settings
- **features** - Feature flags
- **monitoring** - Monitoring and metrics settings

## Advanced Usage

### Custom Validation

```typescript
import { validateConfigSection } from '@/lib/config';

// Validate just the server configuration
const serverConfig = validateConfigSection('server', {
  port: 3001,
  host: 'localhost'
});
```

### Sanitized Logging

```typescript
import { sanitizeConfig, getConfig } from '@/lib/config';

const config = getConfig();
const safeConfig = sanitizeConfig(config);

// Log configuration without sensitive data
console.log('Configuration:', safeConfig);
// API keys and secrets will be shown as [REDACTED]
```

### Dynamic Updates

```typescript
import { updateConfig } from '@/lib/config';

// Update configuration at runtime (useful for testing)
const newConfig = updateConfig({
  features: {
    enableWebSearch: true
  }
});
```

## Best Practices

1. **Always use the configuration system** - Don't access `process.env` directly
2. **Use type-safe accessors** - Leverage TypeScript for compile-time safety
3. **Check feature flags** - Use `isFeatureEnabled()` for conditional features
4. **Validate external config** - Use validation functions for user-provided config
5. **Log safely** - Use `sanitizeConfig()` when logging configuration
6. **Handle missing config** - Check `isProviderConfigured()` before using providers