/**
 * Configuration Module Export
 * 
 * Central export point for application configuration with <PERSON>od validation.
 */

// Export schemas and types
export * from './schema';

// Export loader and utilities
export {
  loadConfig,
  getConfig,
  reloadConfig,
  updateConfig,
  isDevelopment,
  isProduction,
  isStaging,
  isTest,
  isFeatureEnabled,
  getProviderConfig,
  isProviderConfigured,
  config
} from './loader';

// Export validation utilities
export { validateConfig, validatePartialConfig } from './validators';

// Re-export commonly used items for convenience
export type { AppConfig } from './schema';
export { getConfig as default } from './loader';