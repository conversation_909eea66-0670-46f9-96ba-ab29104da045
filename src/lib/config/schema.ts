/**
 * Configuration Schema with Zod
 * 
 * Type-safe configuration validation for the entire application.
 * Ensures all environment variables and configuration are properly typed and validated.
 */

import { z } from 'zod';

// Database configuration schema
const databaseSchema = z.object({
  url: z.string().url().describe('PostgreSQL connection URL'),
  maxConnections: z.number().int().positive().default(20),
  connectionTimeout: z.number().int().positive().default(5000),
  enableLogging: z.boolean().default(false),
});

// Redis configuration schema
const redisSchema = z.object({
  host: z.string().default('localhost'),
  port: z.number().int().positive().default(6379),
  password: z.string().optional(),
  db: z.number().int().min(0).max(15).default(0),
  keyPrefix: z.string().optional(),
  maxRetriesPerRequest: z.number().int().positive().default(3),
  connectTimeout: z.number().int().positive().default(10000),
});

// Server configuration schema
const serverSchema = z.object({
  port: z.number().int().positive().default(3000),
  host: z.string().default('0.0.0.0'),
  environment: z.enum(['development', 'staging', 'production', 'test']).default('development'),
  logLevel: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  corsOrigins: z.array(z.string()).default(['*']),
  trustProxy: z.boolean().default(false),
  requestTimeout: z.number().int().positive().default(30000),
});

// Authentication configuration schema
const authSchema = z.object({
  jwtSecret: z.string().min(32).describe('JWT secret for token signing'),
  jwtExpiresIn: z.string().default('7d'),
  sessionSecret: z.string().min(32).describe('Session secret'),
  sessionMaxAge: z.number().int().positive().default(7 * 24 * 60 * 60 * 1000), // 7 days
  bcryptRounds: z.number().int().min(10).max(15).default(12),
});

// AI Provider API keys schema
const aiProvidersSchema = z.object({
  openai: z.object({
    apiKey: z.string().optional(),
    organization: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  anthropic: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  google: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  mistral: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  groq: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  together: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  xai: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  deepseek: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  perplexity: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  cohere: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional(),
  }),
  openrouter: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional().default('https://openrouter.ai/api/v1'),
  }),
  qwen: z.object({
    apiKey: z.string().optional(),
    baseURL: z.string().url().optional().default('https://dashscope.aliyuncs.com/compatible-mode/v1'),
  }),
});

// Rate limiting configuration schema
const rateLimitSchema = z.object({
  enabled: z.boolean().default(true),
  windowMs: z.number().int().positive().default(60000), // 1 minute
  maxRequests: z.number().int().positive().default(100),
  skipSuccessfulRequests: z.boolean().default(false),
  skipFailedRequests: z.boolean().default(false),
  keyGenerator: z.enum(['ip', 'user', 'api-key']).default('ip'),
});

// Storage configuration schema
const storageSchema = z.object({
  uploadDir: z.string().default('./uploads'),
  maxFileSize: z.number().int().positive().default(10 * 1024 * 1024), // 10MB
  allowedMimeTypes: z.array(z.string()).default([
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'text/markdown',
  ]),
});

// Feature flags schema
const featureFlagsSchema = z.object({
  enableStreaming: z.boolean().default(true),
  enableReasoningModels: z.boolean().default(true),
  enableMultimodal: z.boolean().default(true),
  enableFunctionCalling: z.boolean().default(true),
  enableStructuredOutput: z.boolean().default(true),
  enableWebSearch: z.boolean().default(false),
  enableCodeExecution: z.boolean().default(false),
  enableModelRouting: z.boolean().default(true),
  enableCaching: z.boolean().default(true),
  enableMetrics: z.boolean().default(true),
  enableHealthChecks: z.boolean().default(true),
});

// Monitoring configuration schema
const monitoringSchema = z.object({
  metrics: z.object({
    enabled: z.boolean().default(true),
    port: z.number().int().positive().default(9090),
    path: z.string().default('/metrics'),
  }),
  healthCheck: z.object({
    enabled: z.boolean().default(true),
    path: z.string().default('/health'),
    interval: z.number().int().positive().default(30000), // 30 seconds
  }),
  openTelemetry: z.object({
    enabled: z.boolean().default(false),
    serviceName: z.string().default('simplechat-ai'),
    endpoint: z.string().url().optional(),
  }),
});

// Complete application configuration schema
export const appConfigSchema = z.object({
  database: databaseSchema,
  redis: redisSchema,
  server: serverSchema,
  auth: authSchema,
  aiProviders: aiProvidersSchema,
  rateLimit: rateLimitSchema,
  storage: storageSchema,
  features: featureFlagsSchema,
  monitoring: monitoringSchema,
});

// Type inference
export type AppConfig = z.infer<typeof appConfigSchema>;
export type DatabaseConfig = z.infer<typeof databaseSchema>;
export type RedisConfig = z.infer<typeof redisSchema>;
export type ServerConfig = z.infer<typeof serverSchema>;
export type AuthConfig = z.infer<typeof authSchema>;
export type AIProvidersConfig = z.infer<typeof aiProvidersSchema>;
export type RateLimitConfig = z.infer<typeof rateLimitSchema>;
export type StorageConfig = z.infer<typeof storageSchema>;
export type FeatureFlags = z.infer<typeof featureFlagsSchema>;
export type MonitoringConfig = z.infer<typeof monitoringSchema>;

// Partial schemas for updates
export const partialAppConfigSchema = appConfigSchema.partial();
export type PartialAppConfig = z.infer<typeof partialAppConfigSchema>;