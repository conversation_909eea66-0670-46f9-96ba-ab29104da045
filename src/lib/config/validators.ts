/**
 * Configuration Validators
 * 
 * Utility functions for validating configuration objects
 * using the Zod schemas.
 */

import { z } from 'zod';
import { appConfigSchema, partialAppConfigSchema, AppConfig } from './schema';
import { apiLogger } from '@/lib/logger';

/**
 * Validate a complete configuration object
 */
export function validateConfig(config: unknown): AppConfig {
  try {
    return appConfigSchema.parse(config);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = formatZodErrors(error);
      apiLogger.error('Configuration validation failed', { errors });
      throw new ConfigValidationError('Invalid configuration', errors);
    }
    throw error;
  }
}

/**
 * Validate a partial configuration object
 */
export function validatePartialConfig(config: unknown): Partial<AppConfig> {
  try {
    return partialAppConfigSchema.parse(config);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = formatZodErrors(error);
      apiLogger.error('Partial configuration validation failed', { errors });
      throw new ConfigValidationError('Invalid partial configuration', errors);
    }
    throw error;
  }
}

/**
 * Check if a configuration object is valid
 */
export function isValidConfig(config: unknown): config is AppConfig {
  try {
    appConfigSchema.parse(config);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if a partial configuration object is valid
 */
export function isValidPartialConfig(config: unknown): config is Partial<AppConfig> {
  try {
    partialAppConfigSchema.parse(config);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get validation errors for a configuration object
 */
export function getConfigErrors(config: unknown): ConfigError[] | null {
  try {
    appConfigSchema.parse(config);
    return null;
  } catch (error) {
    if (error instanceof z.ZodError) {
      return formatZodErrors(error);
    }
    return [{ path: '', message: 'Unknown validation error', code: 'unknown' }];
  }
}

/**
 * Validate specific configuration sections
 */
export function validateConfigSection<K extends keyof AppConfig>(
  section: K,
  value: unknown
): AppConfig[K] {
  const sectionSchema = appConfigSchema.shape[section];
  
  try {
    return sectionSchema.parse(value) as AppConfig[K];
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = formatZodErrors(error);
      apiLogger.error(`Configuration section '${section}' validation failed`, { errors });
      throw new ConfigValidationError(`Invalid ${section} configuration`, errors);
    }
    throw error;
  }
}

/**
 * Sanitize configuration for logging (remove sensitive data)
 */
export function sanitizeConfig(config: AppConfig): Record<string, any> {
  const sanitized = JSON.parse(JSON.stringify(config));
  
  // Remove sensitive auth data
  if (sanitized.auth) {
    sanitized.auth.jwtSecret = sanitized.auth.jwtSecret ? '[REDACTED]' : undefined;
    sanitized.auth.sessionSecret = sanitized.auth.sessionSecret ? '[REDACTED]' : undefined;
  }
  
  // Remove API keys
  if (sanitized.aiProviders) {
    Object.keys(sanitized.aiProviders).forEach(provider => {
      if (sanitized.aiProviders[provider]?.apiKey) {
        sanitized.aiProviders[provider].apiKey = '[REDACTED]';
      }
    });
  }
  
  // Remove database password from URL
  if (sanitized.database?.url) {
    sanitized.database.url = sanitized.database.url.replace(
      /:([^@]+)@/,
      ':[REDACTED]@'
    );
  }
  
  // Remove Redis password
  if (sanitized.redis?.password) {
    sanitized.redis.password = '[REDACTED]';
  }
  
  return sanitized;
}

/**
 * Format Zod errors into a more readable format
 */
function formatZodErrors(error: z.ZodError): ConfigError[] {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));
}

/**
 * Configuration error type
 */
export interface ConfigError {
  path: string;
  message: string;
  code: string;
  expected?: string;
  received?: string;
}

/**
 * Custom error class for configuration validation errors
 */
export class ConfigValidationError extends Error {
  constructor(
    message: string,
    public errors: ConfigError[]
  ) {
    super(message);
    this.name = 'ConfigValidationError';
  }
  
  toString(): string {
    const errorMessages = this.errors
      .map(e => `  - ${e.path}: ${e.message}`)
      .join('\n');
    return `${this.message}\n${errorMessages}`;
  }
}

/**
 * Validate environment variables are set
 */
export function validateEnvironment(): void {
  const required = [
    'DATABASE_URL',
    'JWT_SECRET',
  ];
  
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}\n` +
      'Please check your .env file or environment configuration.'
    );
  }
}

/**
 * Get a configuration value with a path
 */
export function getConfigValue(config: AppConfig, path: string): any {
  return path.split('.').reduce((obj, key) => obj?.[key], config as any);
}

/**
 * Set a configuration value with a path
 */
export function setConfigValue(config: AppConfig, path: string, value: any): AppConfig {
  const keys = path.split('.');
  const newConfig = JSON.parse(JSON.stringify(config));
  
  let current = newConfig;
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  
  return validateConfig(newConfig);
}