/**
 * Configuration Loader
 * 
 * Loads and validates configuration from environment variables
 * using the Zod schemas for type safety and validation.
 */

import { config as dotenvConfig } from 'dotenv';
import { z } from 'zod';
import { appConfigSchema, AppConfig } from './schema';
import { apiLogger } from '@/lib/logger';

// Load environment variables
dotenvConfig();

/**
 * Parse a string value to appropriate type
 */
function parseEnvValue(value: string | undefined, type: 'string' | 'number' | 'boolean' | 'array'): any {
  if (!value) return undefined;
  
  switch (type) {
    case 'number':
      const num = parseInt(value, 10);
      return isNaN(num) ? undefined : num;
    
    case 'boolean':
      return value.toLowerCase() === 'true' || value === '1';
    
    case 'array':
      return value.split(',').map(s => s.trim()).filter(Boolean);
    
    default:
      return value;
  }
}

/**
 * Load configuration from environment variables
 */
export function loadConfig(): AppConfig {
  const env = process.env;
  
  // Build configuration object from environment
  const rawConfig = {
    database: {
      url: env.DATABASE_URL,
      maxConnections: parseEnvValue(env.DATABASE_MAX_CONNECTIONS, 'number'),
      connectionTimeout: parseEnvValue(env.DATABASE_CONNECTION_TIMEOUT, 'number'),
      enableLogging: parseEnvValue(env.DATABASE_ENABLE_LOGGING, 'boolean'),
    },
    
    redis: {
      host: env.REDIS_HOST,
      port: parseEnvValue(env.REDIS_PORT, 'number'),
      password: env.REDIS_PASSWORD,
      db: parseEnvValue(env.REDIS_DB, 'number'),
      keyPrefix: env.REDIS_KEY_PREFIX,
      maxRetriesPerRequest: parseEnvValue(env.REDIS_MAX_RETRIES, 'number'),
      connectTimeout: parseEnvValue(env.REDIS_CONNECT_TIMEOUT, 'number'),
    },
    
    server: {
      port: parseEnvValue(env.PORT || env.SERVER_PORT, 'number'),
      host: env.HOST || env.SERVER_HOST,
      environment: env.NODE_ENV as any,
      logLevel: env.LOG_LEVEL as any,
      corsOrigins: parseEnvValue(env.CORS_ORIGINS, 'array'),
      trustProxy: parseEnvValue(env.TRUST_PROXY, 'boolean'),
      requestTimeout: parseEnvValue(env.REQUEST_TIMEOUT, 'number'),
    },
    
    auth: {
      jwtSecret: env.JWT_SECRET || env.AUTH_SECRET,
      jwtExpiresIn: env.JWT_EXPIRES_IN,
      sessionSecret: env.SESSION_SECRET || env.JWT_SECRET || env.AUTH_SECRET,
      sessionMaxAge: parseEnvValue(env.SESSION_MAX_AGE, 'number'),
      bcryptRounds: parseEnvValue(env.BCRYPT_ROUNDS, 'number'),
    },
    
    aiProviders: {
      openai: {
        apiKey: env.OPENAI_API_KEY,
        organization: env.OPENAI_ORGANIZATION,
        baseURL: env.OPENAI_BASE_URL,
      },
      anthropic: {
        apiKey: env.ANTHROPIC_API_KEY,
        baseURL: env.ANTHROPIC_BASE_URL,
      },
      google: {
        apiKey: env.GOOGLE_API_KEY || env.GEMINI_API_KEY,
        baseURL: env.GOOGLE_BASE_URL,
      },
      mistral: {
        apiKey: env.MISTRAL_API_KEY,
        baseURL: env.MISTRAL_BASE_URL,
      },
      groq: {
        apiKey: env.GROQ_API_KEY,
        baseURL: env.GROQ_BASE_URL,
      },
      together: {
        apiKey: env.TOGETHER_API_KEY || env.TOGETHER_AI_API_KEY,
        baseURL: env.TOGETHER_BASE_URL,
      },
      xai: {
        apiKey: env.XAI_API_KEY || env.X_AI_API_KEY,
        baseURL: env.XAI_BASE_URL,
      },
      deepseek: {
        apiKey: env.DEEPSEEK_API_KEY,
        baseURL: env.DEEPSEEK_BASE_URL,
      },
      perplexity: {
        apiKey: env.PERPLEXITY_API_KEY,
        baseURL: env.PERPLEXITY_BASE_URL,
      },
      cohere: {
        apiKey: env.COHERE_API_KEY,
        baseURL: env.COHERE_BASE_URL,
      },
      openrouter: {
        apiKey: env.OPENROUTER_API_KEY,
        baseURL: env.OPENROUTER_BASE_URL,
      },
      qwen: {
        apiKey: env.QWEN_API_KEY || env.ALIBABA_API_KEY,
        baseURL: env.QWEN_BASE_URL,
      },
    },
    
    rateLimit: {
      enabled: parseEnvValue(env.RATE_LIMIT_ENABLED, 'boolean'),
      windowMs: parseEnvValue(env.RATE_LIMIT_WINDOW_MS, 'number'),
      maxRequests: parseEnvValue(env.RATE_LIMIT_MAX_REQUESTS, 'number'),
      skipSuccessfulRequests: parseEnvValue(env.RATE_LIMIT_SKIP_SUCCESSFUL, 'boolean'),
      skipFailedRequests: parseEnvValue(env.RATE_LIMIT_SKIP_FAILED, 'boolean'),
      keyGenerator: env.RATE_LIMIT_KEY_GENERATOR as any,
    },
    
    storage: {
      uploadDir: env.UPLOAD_DIR,
      maxFileSize: parseEnvValue(env.MAX_FILE_SIZE, 'number'),
      allowedMimeTypes: parseEnvValue(env.ALLOWED_MIME_TYPES, 'array'),
    },
    
    features: {
      enableStreaming: parseEnvValue(env.ENABLE_STREAMING, 'boolean'),
      enableReasoningModels: parseEnvValue(env.ENABLE_REASONING_MODELS, 'boolean'),
      enableMultimodal: parseEnvValue(env.ENABLE_MULTIMODAL, 'boolean'),
      enableFunctionCalling: parseEnvValue(env.ENABLE_FUNCTION_CALLING, 'boolean'),
      enableStructuredOutput: parseEnvValue(env.ENABLE_STRUCTURED_OUTPUT, 'boolean'),
      enableWebSearch: parseEnvValue(env.ENABLE_WEB_SEARCH, 'boolean'),
      enableCodeExecution: parseEnvValue(env.ENABLE_CODE_EXECUTION, 'boolean'),
      enableModelRouting: parseEnvValue(env.ENABLE_MODEL_ROUTING, 'boolean'),
      enableCaching: parseEnvValue(env.ENABLE_CACHING, 'boolean'),
      enableMetrics: parseEnvValue(env.ENABLE_METRICS, 'boolean'),
      enableHealthChecks: parseEnvValue(env.ENABLE_HEALTH_CHECKS, 'boolean'),
    },
    
    monitoring: {
      metrics: {
        enabled: parseEnvValue(env.METRICS_ENABLED, 'boolean'),
        port: parseEnvValue(env.METRICS_PORT, 'number'),
        path: env.METRICS_PATH,
      },
      healthCheck: {
        enabled: parseEnvValue(env.HEALTH_CHECK_ENABLED, 'boolean'),
        path: env.HEALTH_CHECK_PATH,
        interval: parseEnvValue(env.HEALTH_CHECK_INTERVAL, 'number'),
      },
      openTelemetry: {
        enabled: parseEnvValue(env.OTEL_ENABLED, 'boolean'),
        serviceName: env.OTEL_SERVICE_NAME,
        endpoint: env.OTEL_ENDPOINT,
      },
    },
  };
  
  // Remove undefined values to allow defaults
  const cleanConfig = JSON.parse(JSON.stringify(rawConfig));
  
  try {
    // Validate and parse configuration
    const config = appConfigSchema.parse(cleanConfig);
    
    // Log successful configuration load (without sensitive data)
    apiLogger.info('Configuration loaded successfully', {
      environment: config.server.environment,
      port: config.server.port,
      redisHost: config.redis.host,
      enabledFeatures: Object.entries(config.features)
        .filter(([_, enabled]) => enabled)
        .map(([feature]) => feature),
      providersConfigured: Object.entries(config.aiProviders)
        .filter(([_, provider]) => provider.apiKey)
        .map(([name]) => name),
    });
    
    return config;
  } catch (error) {
    if (error instanceof z.ZodError) {
      apiLogger.error('Configuration validation failed', {
        errors: error.errors.map(e => ({
          path: e.path.join('.'),
          message: e.message,
          code: e.code,
        })),
      });
      
      // In development, throw error to fail fast
      if (process.env.NODE_ENV === 'development') {
        throw new Error(`Configuration validation failed: ${error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')}`);
      }
    }
    
    throw error;
  }
}

// Singleton configuration instance
let configInstance: AppConfig | null = null;

/**
 * Get the application configuration (lazy loaded)
 */
export function getConfig(): AppConfig {
  if (!configInstance) {
    configInstance = loadConfig();
  }
  return configInstance;
}

/**
 * Reload configuration (useful for testing)
 */
export function reloadConfig(): AppConfig {
  configInstance = null;
  return getConfig();
}

/**
 * Update configuration (partial update)
 */
export function updateConfig(updates: Partial<AppConfig>): AppConfig {
  const currentConfig = getConfig();
  const merged = deepMerge(currentConfig, updates);
  
  // Validate the merged configuration
  configInstance = appConfigSchema.parse(merged);
  return configInstance;
}

/**
 * Deep merge utility
 */
function deepMerge(target: any, source: any): any {
  const output = { ...target };
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  
  return output;
}

function isObject(item: any): boolean {
  return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * Environment-specific configuration helpers
 */
export const isDevelopment = () => getConfig().server.environment === 'development';
export const isProduction = () => getConfig().server.environment === 'production';
export const isStaging = () => getConfig().server.environment === 'staging';
export const isTest = () => getConfig().server.environment === 'test';

/**
 * Feature flag helpers
 */
export const isFeatureEnabled = (feature: keyof AppConfig['features']): boolean => {
  return getConfig().features[feature] ?? false;
};

/**
 * Provider configuration helpers
 */
export const getProviderConfig = (provider: keyof AppConfig['aiProviders']) => {
  return getConfig().aiProviders[provider];
};

export const isProviderConfigured = (provider: keyof AppConfig['aiProviders']): boolean => {
  const config = getProviderConfig(provider);
  return !!config?.apiKey;
};

// Export the config for direct access if needed
export const config = getConfig;