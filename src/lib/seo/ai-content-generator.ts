import { generateText } from 'ai'
import { anthropic } from '@ai-sdk/anthropic'
import { openai } from '@ai-sdk/openai'
import { google } from '@ai-sdk/google'
import { prisma } from '@/lib/prisma'
import type { Model } from '@/types'

interface ComparisonResult {
  content: string
  metaDescription: string
  title: string
  keywords: string[]
  faqItems: Array<{
    question: string
    answer: string
  }>
  // o3-pro A/B testing enhancements
  metaDescriptionB?: string
  titleB?: string
  schemaMarkup?: string
  // O3-pro Overall Winner section
  overallWinner?: {
    name: string
    compositeScore: number
    reasoning: string
  }
  modelChoiceGuide?: {
    model1: {
      name: string
      chooseIfNeeds: string[]
      keyStrengths: string[]
      pricing: string
    }
    model2: {
      name: string
      chooseIfNeeds: string[]
      keyStrengths: string[]
      pricing: string
    }
  }
  // Enhanced o3-pro specificity fields
  modelRecommendations?: {
    model1: {
      winScenarios: Array<{
        useCase: string
        reason: string
        specificTasks: string[]
        benchmarkEvidence: string
        costJustification: string
      }>
      strengthAreas: Array<{
        category: string
        score: number
        confidence: number
        realWorldApplication: string
        competitiveAdvantage: string
      }>
    }
    model2: {
      winScenarios: Array<{
        useCase: string
        reason: string
        specificTasks: string[]
        benchmarkEvidence: string
        costJustification: string
      }>
      strengthAreas: Array<{
        category: string
        score: number
        confidence: number
        realWorldApplication: string
        competitiveAdvantage: string
      }>
    }
  }
  decisionFramework?: {
    chooseModel1If: string[]
    chooseModel2If: string[]
    chooseBothIf: string[]
  }
}

export async function generateModelComparison(
  model1: Model,
  model2: Model,
  forceRegenerate: boolean = false
): Promise<ComparisonResult> {
  // Get database UUIDs for the models
  const [dbModel1, dbModel2] = await Promise.all([
    prisma.models.findFirst({
      where: { canonicalName: model1.id },
      select: { id: true }
    }),
    prisma.models.findFirst({
      where: { canonicalName: model2.id },
      select: { id: true }
    })
  ])

  if (!dbModel1 || !dbModel2) {
    throw new Error(`Models not found in database: ${model1.id}, ${model2.id}`)
  }

  // Check if we have cached content first
  if (!forceRegenerate) {
    const cached = await getCachedComparison(dbModel1.id, dbModel2.id)
    if (cached && !cached.isStale) {
      return {
        content: cached.content || '',
        metaDescription: cached.metaDescription || '',
        title: cached.title || '',
        keywords: (cached.keywords as string[]) || [],
        faqItems: (cached.faqItems as any[]) || []
      }
    }
  }

  // Generate fresh content only if not cached or force regenerate
  const freshContent = await generateFreshComparison(model1, model2)
  
  // Store in cache using database UUIDs
  await storeCachedComparison(dbModel1.id, dbModel2.id, freshContent)
  
  return freshContent
}

async function getCachedComparison(model1Id: string, model2Id: string) {
  return await prisma.modelComparison.findFirst({
    where: {
      OR: [
        { model1Id, model2Id },
        { model1Id: model2Id, model2Id: model1Id } // Handle reverse order
      ]
    }
  })
}

async function storeCachedComparison(
  model1Id: string, 
  model2Id: string, 
  result: ComparisonResult
) {
  const comparisonId = `${model1Id}-vs-${model2Id}`
  
  return await prisma.modelComparison.upsert({
    where: {
      id: comparisonId
    },
    create: {
      id: comparisonId,
      model1Id,
      model2Id,
      title: result.title,
      metaDescription: result.metaDescription,
      content: result.content,
      keywords: result.keywords,
      faqItems: result.faqItems
    },
    update: {
      title: result.title,
      metaDescription: result.metaDescription,
      content: result.content,
      keywords: result.keywords,
      faqItems: result.faqItems,
      updatedAt: new Date(),
      isStale: false
    }
  })
}

async function generateFreshComparison(model1: Model, model2: Model): Promise<ComparisonResult> {
  // Calculate dynamic cost variables for enhanced psychological impact
  const monthlyIndividualCost = 50 // Average of $20-30 each for 2 models
  const annualIndividualCost = monthlyIndividualCost * 12 // $600/year
  const annualJustSimpleChatCost = 96 // $8 * 12
  const annualSavings = annualIndividualCost - annualJustSimpleChatCost // $504
  const threeYearSavings = annualSavings * 3 // $1,512
  const waitListCount = 8247 // Dynamic - could be pulled from database
  
  // o3-pro optimized system message for direct-response copywriter style
  const systemMessage = `You are a direct-response copywriter specializing in SaaS with a Dale Carnegie, Dan Kennedy and Cialdini blended style. 

CRITICAL INSTRUCTION: Generate ONLY the content requested. NO conversational responses, NO introductions, NO "I'll help you" statements. Start directly with the content.

Every sentence must either:
a) build desire,
b) reduce risk, or
c) call to action.

VOICE GUIDE:
- Tone: Friendly expert, like a savvy friend who has "already tested everything"
- Reading Level: Grade 8–9
- Contractions always (you'll, we've, don't)
- 40% sentences under 12 words
- Focus on outcomes, not features`

  // Get complete model data with rich metadata from database
  const [fullModel1, fullModel2] = await Promise.all([
    prisma.models.findFirst({
      where: { canonicalName: model1.id },
      select: { 
        id: true, 
        extendedMetadata: true, 
        displayName: true,
        family: true,
        modelType: true
      }
    }),
    prisma.models.findFirst({
      where: { canonicalName: model2.id },
      select: { 
        id: true, 
        extendedMetadata: true, 
        displayName: true,
        family: true,
        modelType: true
      }
    })
  ])

  // Extract rich model context from metadata for AI-powered comparison
  const model1Context = extractModelContext(model1, fullModel1?.extendedMetadata as any, fullModel1?.family || 'general')
  const model2Context = extractModelContext(model2, fullModel2?.extendedMetadata as any, fullModel2?.family || 'general')
  
  // O3-pro Overall Winner determination based on composite scores
  const overallWinner = model1Context.compositeScore >= model2Context.compositeScore ? model1 : model2
  const runnerUp = model1Context.compositeScore >= model2Context.compositeScore ? model2 : model1
  const winnerContext = model1Context.compositeScore >= model2Context.compositeScore ? model1Context : model2Context
  const runnerUpContext = model1Context.compositeScore >= model2Context.compositeScore ? model2Context : model1Context
  const scoreDelta = Math.abs(model1Context.compositeScore - model2Context.compositeScore)
  
  // O3-PRO ENHANCED PROMPT with mandatory specificity requirements
  const comparisonPrompt = `${systemMessage}

🎯 CONVERSION-OPTIMIZED COMPARISON GENERATOR

⚠️ CRITICAL SUCCESS METRICS:
- Generate SPECIFIC use cases with numerical evidence from provided metadata
- Include competitive advantages with benchmark proof  
- Create urgency through cost/performance analysis
- Eliminate ALL generic language ("specific features", "ecosystem integration", "provider benefits")
- MANDATORY: Use actual scores, benchmarks, and confidence levels from metadata

📋 CONTEXT: 
- Platform: JustSimpleChat (PRE-LAUNCH - ${waitListCount} early adopters already locked in $8 rate)
- Value Prop: 150+ AI models for $8/month vs $${monthlyIndividualCost}+ individual subscriptions  
- Audience: Cost-conscious consumers & small businesses who hate overpaying
- Goal: Convert browsers to risk-free trial sign-ups with lifetime $8 pricing

🤖 RICH PERFORMANCE DATA (MANDATORY TO USE ALL NUMERICAL EVIDENCE):

**${model1.name} (${model1.provider})**
📋 **Basic Info:**
- Description: ${model1.description}
- Primary Use Case: ${model1Context.primaryUseCase}
- Context Window: ${model1Context.contextWindow.toLocaleString()} tokens
- Max Output: ${model1Context.maxOutput.toLocaleString()} tokens
- Cost Category: ${model1Context.costCategory}

💰 **Pricing Details:**
- Input Cost: $${model1Context.inputCost} per million tokens
- Output Cost: $${model1Context.outputCost} per million tokens
- Individual subscription estimate: $25-30/month

🎯 **Performance Strengths:**
${model1Context.topStrengths.length > 0 ? '- ' + model1Context.topStrengths.join('\n- ') : '- General AI capabilities'}

🏆 **Top Benchmark Scores:**
${model1Context.benchmarks.length > 0 ? '- ' + model1Context.benchmarks.join('\n- ') : '- No benchmark data available'}

🚀 **Key Capabilities:**
${model1Context.capabilities.length > 0 ? '- ' + model1Context.capabilities.join('\n- ') : '- Standard AI capabilities'}

🔧 **Special Features:**
${model1Context.specialFeatures.length > 0 ? '- ' + model1Context.specialFeatures.join('\n- ') : '- Standard AI features'}

📈 **Performance Highlights:**
${model1Context.performanceHighlights.length > 0 ? '- ' + model1Context.performanceHighlights.join('\n- ') : '- General performance metrics'}

✅ **Best Use Cases:**
${model1Context.useCases.length > 0 ? '- ' + model1Context.useCases.join('\n- ') : '- General AI assistance'}

---

**${model2.name} (${model2.provider})**
📋 **Basic Info:**
- Description: ${model2.description}
- Primary Use Case: ${model2Context.primaryUseCase}
- Context Window: ${model2Context.contextWindow.toLocaleString()} tokens
- Max Output: ${model2Context.maxOutput.toLocaleString()} tokens
- Cost Category: ${model2Context.costCategory}

💰 **Pricing Details:**
- Input Cost: $${model2Context.inputCost} per million tokens
- Output Cost: $${model2Context.outputCost} per million tokens
- Individual subscription estimate: $25-30/month

🎯 **Performance Strengths:**
${model2Context.topStrengths.length > 0 ? '- ' + model2Context.topStrengths.join('\n- ') : '- General AI capabilities'}

🏆 **Top Benchmark Scores:**
${model2Context.benchmarks.length > 0 ? '- ' + model2Context.benchmarks.join('\n- ') : '- No benchmark data available'}

🚀 **Key Capabilities:**
${model2Context.capabilities.length > 0 ? '- ' + model2Context.capabilities.join('\n- ') : '- Standard AI capabilities'}

🔧 **Special Features:**
${model2Context.specialFeatures.length > 0 ? '- ' + model2Context.specialFeatures.join('\n- ') : '- Standard AI features'}

📈 **Performance Highlights:**
${model2Context.performanceHighlights.length > 0 ? '- ' + model2Context.performanceHighlights.join('\n- ') : '- General performance metrics'}

✅ **Best Use Cases:**
${model2Context.useCases.length > 0 ? '- ' + model2Context.useCases.join('\n- ') : '- General AI assistance'}

🧠 ENHANCED PSYCHOLOGY FOCUS:
- Loss Aversion Multiplier: Show 3-year cost ($${annualIndividualCost * 3} vs $${annualJustSimpleChatCost * 3})
- Sunk-Cost Flip: "Already paying for ChatGPT Plus? Drop it and cover 2 years of JustSimpleChat"
- Bandwagon Effect: "${waitListCount} early adopters locked in $8 rate—spaces close at 10,000"
- Future Pacing: "Competitors paying $200+ while you ship faster with $8 access"
- Risk Reversal: Zero-risk guarantee with explicit refund policy

🧠 WRITING RULES FOR AI GENERATOR:

CRITICAL: Do NOT include any placeholder text like "[Generate...]", "Test both!", or instruction text in your output. Only generate the actual content that users will read.

MANDATORY FORMATS:
- Performance mentions must include actual numbers from the metadata provided
- Use phrases like "Coding: 92/100 (98% confidence)" not "good for coding"  
- Include specific benchmark results like "HumanEval: 85%" when available
- Cost mentions need context like "$15 justified by 40% efficiency gains"
- NO generic terms like "provider features" or "ecosystem integration"

📝 CONTENT TO GENERATE for ${model1.name} vs ${model2.name}:

Start with this exact structure:

## 🏆 ${model1.name} vs ${model2.name}: Performance Comparison

Looking for AI that delivers measurable results? Here's your data-driven decision guide:

| Choose This Model | When You Need | Performance Evidence | Cost Efficiency |
|-------------------|---------------|---------------------|-----------------|
| ${model1.name} | ${model1Context.topStrengths.length > 0 ? model1Context.topStrengths[0].split(':')[0] : 'Advanced AI tasks'} | ${model1Context.benchmarks.length > 0 ? model1Context.benchmarks[0] : 'Proven performance'} | $${model1Context.inputCost} per million optimizes ROI |
| ${model2.name} | ${model2Context.topStrengths.length > 0 ? model2Context.topStrengths[0].split(':')[0] : 'Specialized tasks'} | ${model2Context.benchmarks.length > 0 ? model2Context.benchmarks[0] : 'Strong performance'} | $${model2Context.inputCost} per million targets efficiency |
| **BOTH Models** | **Complete AI toolchain flexibility** | **Best-in-class performance across ${model1Context.useCases.length + model2Context.useCases.length}+ domains** | **$8/month vs $${monthlyIndividualCost}+ individual plans** |

**Why choose one when you can test both for less than either costs alone?**

### 📊 Performance Breakdown
- **${model1.name}**: ${model1Context.costCategory} tier • ${model1Context.contextWindow.toLocaleString()} context • ${model1Context.topStrengths.length > 0 ? model1Context.topStrengths[0] : 'Strong general performance'}
- **${model2.name}**: ${model2Context.costCategory} tier • ${model2Context.contextWindow.toLocaleString()} context • ${model2Context.topStrengths.length > 0 ? model2Context.topStrengths[0] : 'Reliable performance'}

## 💡 What Makes Each Model Special (Real Problem Solving)
**${model1.name}**: [Explain in everyday language - what specific problem does it solve?]

**${model2.name}**: [Explain in everyday language - what specific problem does it solve?]

**🔮 Picture This**: Imagine switching between these models mid-conversation. ${model1.name} writes your first draft, ${model2.name} refines it for SEO. Total cost? $8/month instead of $50/month for separate subscriptions.

**What could you do with that extra $42 every month?**

## 🔍 60-Second Reality Check: Your Wallet vs Theirs
- **Stick with individual plans?** Pay **$${annualIndividualCost}/year** ($${annualIndividualCost * 3} over 3 years)
- **Switch to JustSimpleChat?** Pay **$${annualJustSimpleChatCost}/year** ($${annualJustSimpleChatCost * 3} over 3 years)
- **You keep $${annualSavings}/year—enough for a new laptop every two years**

## 🎪 Head-to-Head Performance Analysis
Here's how ${model1.name} and ${model2.name} perform on real-world tasks:

| Task Category | ${model1.name} | ${model2.name} | Recommendation |
|---------------|-----------------|-----------------|----------------|
| Writing & Content | ${model1Context.topStrengths.find(s => s.includes('creative') || s.includes('writing'))?.split(':')[1]?.trim() || 'Strong performance'} | ${model2Context.topStrengths.find(s => s.includes('creative') || s.includes('writing'))?.split(':')[1]?.trim() || 'Solid performance'} | Higher scoring model wins |
| Code & Development | ${model1Context.topStrengths.find(s => s.includes('cod') || s.includes('technical'))?.split(':')[1]?.trim() || 'Capable performance'} | ${model2Context.topStrengths.find(s => s.includes('cod') || s.includes('technical'))?.split(':')[1]?.trim() || 'Reliable performance'} | Best for your stack |  
| Analysis & Research | ${model1Context.topStrengths.find(s => s.includes('analysis') || s.includes('reasoning'))?.split(':')[1]?.trim() || 'Good analytical skills'} | ${model2Context.topStrengths.find(s => s.includes('analysis') || s.includes('reasoning'))?.split(':')[1]?.trim() || 'Solid reasoning'} | Data-driven choice |
| Mathematical Tasks | ${model1Context.topStrengths.find(s => s.includes('math') || s.includes('quantitative'))?.split(':')[1]?.trim() || 'Standard math capabilities'} | ${model2Context.topStrengths.find(s => s.includes('math') || s.includes('quantitative'))?.split(':')[1]?.trim() || 'Basic math support'} | Numbers don't lie |

**Smart Strategy**: Access both models for $8/month instead of paying $${monthlyIndividualCost}+ for individual subscriptions.

## ❓ Quick FAQ (Handling Your Concerns)
**Q: "What if I don't like it?"**
A: Try every model FREE for 3 days. Don't save at least $32 your first month? Email us for a full refund. Zero hoops.

**Q: "Is this too good to be true?"**
A: We're pre-launch, so we can offer lifetime $8 pricing. Think about it: you're paying $25+ for one model right now. Why not pay $8 for 150 models? Once we hit 10,000 early adopters, prices rise to $15+.

**Q: "Can I really access all 150+ models?"**
A: Yes! Same models the pros use: GPT-4, Claude, Gemini, and 147 others. Switch between them mid-conversation if one isn't working.

## 🎯 Performance-Based Model Selection (Data-Driven Triggers)

Based on actual benchmark scores and performance data, here's when each model dominates:

**${model1.name} excels when you need:**
${model1Context.useCases.length > 0 && model1Context.topStrengths.length > 0 ? 
  model1Context.useCases.slice(0, 3).map((useCase, index) => {
    const strength = model1Context.topStrengths[index] || 'High performance: 85/100';
    const [category, score] = strength.split(':');
    return `${index + 1}. **${useCase}** - ${category.trim()} excellence (${score?.trim() || '85/100'} performance score)`;
  }).join('\n') : 
  `1. **Advanced AI Processing** - Top-tier performance (85+ benchmark scores)
2. **Complex Problem Solving** - Superior reasoning capabilities  
3. **Professional Applications** - Enterprise-grade reliability`}

**${model2.name} dominates when you need:**
${model2Context.useCases.length > 0 && model2Context.topStrengths.length > 0 ? 
  model2Context.useCases.slice(0, 3).map((useCase, index) => {
    const strength = model2Context.topStrengths[index] || 'Strong performance: 82/100';
    const [category, score] = strength.split(':');
    return `${index + 1}. **${useCase}** - ${category.trim()} specialization (${score?.trim() || '82/100'} performance score)`;
  }).join('\n') : 
  `1. **Specialized Processing** - Focused domain expertise
2. **Cost-Efficient Workflows** - Optimal price-performance ratio
3. **Targeted Applications** - Specific use case optimization`}

## 🏆 Overall Winner: ${overallWinner.name} (${winnerContext.compositeScore}/100)

Based on weighted performance analysis across coding (30%), reasoning (30%), creative (20%), and analysis (20%):

| Performance Metric | ${model1.name} | ${model2.name} | Delta |
|-------------------|-----------------|-----------------|-------|
| **Composite Score** | **${model1Context.compositeScore}/100** | **${model2Context.compositeScore}/100** | **${scoreDelta} points** |
| Top Strength | ${model1Context.topStrengths[0] || 'General performance: 85/100'} | ${model2Context.topStrengths[0] || 'Balanced performance: 82/100'} | See above |

🎯 **Bottom Line**: Choose **${overallWinner.name}** for critical work where a **${scoreDelta}-point performance edge** translates to faster, more accurate results. Choose ${runnerUp.name} for specialized tasks where ${runnerUpContext.topStrengths[0]?.split(':')[0] || 'cost efficiency'} matters most—or get **both for $8/month** and let real-world testing decide.

⏰ **Lock in lifetime $8 access before spots fill up.** Every day of delay costs ≈$${Math.round(annualSavings/365)} in unused efficiency gains. Only ${10000 - waitListCount} early-adopter spots remaining.

**Performance Evidence:**
- **${model1.name}**: ${model1Context.topStrengths.length > 0 ? model1Context.topStrengths[0] + ' - beats industry average by 15%' : 'Strong performance across multiple domains (85+ average scores)'}
- **${model2.name}**: ${model2Context.topStrengths.length > 0 ? model2Context.topStrengths[0] + ' - optimized for efficiency and reliability' : 'Reliable performance with cost optimization (80+ average scores)'}

**Cost-Performance Analysis:**
- **${model1.name}**: $${model1Context.inputCost} per million ${model1Context.inputCost > 10 ? 'justified by premium performance - typical 40% efficiency gains vs alternatives' : 'delivers exceptional value for high-volume processing'}
- **${model2.name}**: $${model2Context.inputCost} per million ${model2Context.inputCost < 10 ? 'perfect for cost-sensitive workflows - maintains quality while optimizing spend' : 'premium tier performance with specialized capabilities'}

**Choose BOTH when:**
You want maximum flexibility across ${model1Context.useCases.length + model2Context.useCases.length}+ use cases for $8/month vs $${monthlyIndividualCost}+ individual subscriptions—test performance differences without financial risk.

## 🚀 Five-Minute Get-Started Walkthrough
1. **Sign up** (30 seconds, no card needed)
2. **Pick your first model** (${model1.name} or ${model2.name})
3. **Start chatting** (test your actual work tasks)
4. **Switch models** mid-conversation if needed
5. **Compare results** and find your favorites

**Total time to productivity: Under 5 minutes. Total cost: $0 for 3 days, then $8/month.**

## 🛡 Zero-Risk Guarantee
Try every model FREE for 3 days. Don't save at least $32 your first month? Email us for a full refund. No hoops.
Your chats are encrypted end-to-end—no data sold.

## 💰 The Sunk-Cost Reality
Already paying for ChatGPT Plus ($20/month)? Drop it next month and you've covered nearly two full years of JustSimpleChat. That's 24 months of access to 150+ models vs 1 month of one model.

**Which sounds smarter to you?**

## 🏃‍♂️ Why Smart Early Adopters Are Rushing to Lock In $8 Pricing
**🔮 Imagine 6 months from now**: Your competitors are still juggling $20+ subscriptions for single models. You're testing, switching, and optimizing with 150+ models for $8/month.

**Who do you think ships better results faster?**

- Test ${model1.name} vs ${model2.name} side-by-side instantly
- Switch models mid-conversation if one isn't working  
- Access cutting-edge models the day they launch
- Pay 85% less than buying separately
- No vendor lock-in or complex subscriptions
- Lifetime $8 pricing (while early adopter spots last)

**CTA-A: Secure Your $8 Pre-Launch Rate Now →**
**CTA-B: Get Instant Access to All 150+ Models for $8/Month →**

Ready to see which model works best for your projects? Join ${waitListCount} early adopters and get immediate access to both ${model1.name} and ${model2.name}—plus 148 other models.

**URGENT: Pre-launch pricing ends at 10,000 sign-ups. Current count: ${waitListCount}**

**WRITING RULES:**
- Use contractions always (you'll, we've, don't)
- 40% sentences under 12 words
- Include specific dollar amounts: $${monthlyIndividualCost} vs $8, $${annualSavings}/year saved
- Add emotional triggers with proof: "${waitListCount} early adopters", "lifetime $8 pricing"
- Use power words: "instantly", "breakthrough", "exclusive", "guaranteed", "locked in"
- End every section with mini-CTA or benefit reminder
- NO corporate jargon—talk like a savvy friend who tested everything
- Focus on outcomes and emotions, not features

**CONVERSION OPTIMIZATION:**
- Mention "$8 vs $${monthlyIndividualCost}+" at least 5 times
- Include wait-list count (${waitListCount}) and closing number (10,000)
- Use questions to engage: "Which sounds smarter to you?"
- Add urgency: "spaces close at 10,000", "pre-launch pricing ends"
- Make cost comparison crystal clear in every section
- Include explicit guarantee and risk reversal
- Force A/B CTAs at the end`

  // Use Claude 3.5 Sonnet with fallback models and retry logic
  const comparisonContent = await generateWithRetry(comparisonPrompt, {
    temperature: 0.5,
    topP: 0.9,
    maxTokens: 2200
  })

  // o3-pro enhanced meta content prompt with schema markup and A/B testing
  const metaPrompt = `🎯 CONVERSION-FOCUSED META CONTENT for ${model1.name} vs ${model2.name}

⚠️ CRITICAL: Generate ONLY the 6 numbered items requested. NO conversational responses. NO introductions. Start directly with "1. **Meta Description A".

📊 CONTEXT: Pre-launch platform, need high CTR, conversions, and rich snippets
💰 VALUE PROP: $8/month for 150+ models vs $${monthlyIndividualCost}+ individual subscriptions  
🎪 STATUS: Limited-time pre-launch access (${waitListCount}/10,000 early adopters)
💾 SCHEMA: Must include JSON-LD for rich SERP results

Generate EXACTLY these 6 items:

1. **Meta Description A (150-155 chars)**: 
"${model1.name} vs ${model2.name} comparison. Access both + 148 models for $8/month on JustSimpleChat. Save $${monthlyIndividualCost - 8} vs individual subscriptions!"

2. **Meta Description B (150-155 chars)**: 
"Compare ${model1.name} and ${model2.name}. Pre-launch: Get all 150+ AI models for $8/month. Limited spots remaining—save 85% vs separate subscriptions."

3. **SEO Title A (55-60 chars)**:
"${model1.name} vs ${model2.name}: Save 85% | JustSimpleChat"

4. **SEO Title B (55-60 chars)**:
"${model1.name} vs ${model2.name} - $8 for Both | JustSimpleChat"

5. **Keywords (9 phrases)**:
Primary: "${model1.name} vs ${model2.name}"
Cost-focused: "cheap AI models", "AI subscription savings", "multiple AI models one price"
Platform: "JustSimpleChat review", "best AI platform 2025"
Intent: "AI model comparison 2025", "${model1.name} alternative", "${model2.name} alternative"
Long-tail: "access multiple AI models cheap"

6. **FAQ Items (5 questions)**:
Q1: Cost comparison question with specific dollar amounts
Q2: Platform reliability/availability question  
Q3: Model access/switching flexibility question
Q4: Pre-launch pricing/commitment question
Q5: How it compares to buying individually

Format as:
Q1: "Question text here"
A1: Answer text here

Q2: "Question text here"  
A2: Answer text here

(etc.)

**ENHANCED CONVERSION RULES:**
- Every FAQ answer ends with a benefit + mini-CTA
- Include specific dollar amounts: "$8 vs $${monthlyIndividualCost}+", "save $${monthlyIndividualCost - 8}/month"
- Use contractions and "you/your" language throughout
- Add scarcity: "${waitListCount}/10,000 early adopters", "spaces close soon"
- Emphasize future pacing: "Imagine paying $8 while competitors pay $${monthlyIndividualCost}+"
- No corporate speak - conversational, urgent tone

**PSYCHOLOGICAL TRIGGERS:**
- Loss aversion: "Stop overpaying $${monthlyIndividualCost - 8} extra every month"
- Future pacing: "Picture yourself with 150+ models while others juggle expensive subscriptions"
- Authority: "Same models the pros use: GPT-4, Claude, Gemini + 147 others"
- Urgency: "Pre-launch pricing ends at 10,000 sign-ups"
- Risk reversal: "3-day free trial with money-back guarantee"

**FAQ CONTENT REQUIREMENTS:**
- Write clear, conversion-focused FAQ questions and answers
- Address real pre-launch concerns with specific benefits
- Include dollar amounts and clear value propositions

Base content analysis on: ${comparisonContent.substring(0, 800)}...`

  const metaContent = await generateWithRetry(metaPrompt, {
    temperature: 0.4,
    topP: 0.9,
    maxTokens: 2000
  }, 'meta')

  // Parse the enhanced meta content with A/B testing and schema
  const metaLines = metaContent.split('\n').filter(line => line.trim())
  
  // Extract A/B variations for testing
  const metaDescriptionA = extractSection(metaLines, 'meta description a') || extractSection(metaLines, 'description a')
  const metaDescriptionB = extractSection(metaLines, 'meta description b') || extractSection(metaLines, 'description b')
  const titleA = extractSection(metaLines, 'seo title a') || extractSection(metaLines, 'title a')
  const titleB = extractSection(metaLines, 'seo title b') || extractSection(metaLines, 'title b')
  
  // Use A variation as primary, store B for future A/B testing
  const metaDescription = metaDescriptionA || metaDescriptionB
  const title = titleA || titleB
  
  const keywords = extractKeywords(metaLines)
  const faqItems = extractFAQs(metaLines)
  
  // Fallback titles and descriptions if parsing fails
  const finalTitle = title || `${model1.name} vs ${model2.name}: Save 85% | JustSimpleChat`
  const finalMetaDescription = metaDescription || `${model1.name} vs ${model2.name} comparison. Access both + 148 models for $8/month on JustSimpleChat. Save $42 vs individual subscriptions!`

  // O3-pro Overall Winner structured data generation
  const overallWinnerPrompt = `🎯 GENERATE OVERALL WINNER STRUCTURED DATA

⚠️ CRITICAL: Return ONLY valid JSON. No explanations, no conversational text.

📊 MODEL CONTEXT:
- ${overallWinner.name}: Composite Score ${winnerContext.compositeScore}/100
- ${runnerUp.name}: Composite Score ${runnerUpContext.compositeScore}/100
- Score Delta: ${scoreDelta} points

🎯 PRICING:
- ${model1.name}: $${model1Context.inputCost}/$${model1Context.outputCost} per million tokens
- ${model2.name}: $${model2Context.inputCost}/$${model2Context.outputCost} per million tokens

💪 STRENGTHS:
Model1: ${model1Context.topStrengths.slice(0, 3).join(', ')}
Model2: ${model2Context.topStrengths.slice(0, 3).join(', ')}

🚀 CAPABILITIES:
Model1: ${model1Context.capabilities.slice(0, 3).join(', ')}
Model2: ${model2Context.capabilities.slice(0, 3).join(', ')}

Generate this EXACT JSON structure:
{
  "overallWinner": {
    "name": "${overallWinner.name}",
    "compositeScore": ${winnerContext.compositeScore},
    "reasoning": "Based on weighted performance analysis (coding 30%, reasoning 30%, creative 20%, analysis 20%)"
  },
  "modelChoiceGuide": {
    "model1": {
      "name": "${model1.name}",
      "chooseIfNeeds": ["SPECIFIC use case based on top strength", "SPECIFIC capability with numbers", "SPECIFIC context window benefit"],
      "keyStrengths": ["${model1Context.topStrengths[0]?.replace(/"/g, '') || 'Strong performance'}", "Context: ${model1Context.contextWindow.toLocaleString()} tokens"],
      "pricing": "$${model1Context.inputCost}/$${model1Context.outputCost} per million tokens"
    },
    "model2": {
      "name": "${model2.name}",
      "chooseIfNeeds": ["SPECIFIC use case based on top strength", "SPECIFIC capability with numbers", "SPECIFIC cost advantage"],
      "keyStrengths": ["${model2Context.topStrengths[0]?.replace(/"/g, '') || 'Reliable performance'}", "Context: ${model2Context.contextWindow.toLocaleString()} tokens"],
      "pricing": "$${model2Context.inputCost}/$${model2Context.outputCost} per million tokens"
    }
  }
}`

  const overallWinnerData = await generateWithRetry(overallWinnerPrompt, {
    temperature: 0.2,
    topP: 0.8,
    maxTokens: 800
  }, 'json')

  // Parse the JSON response (extract from markdown code blocks if present)
  let structuredWinnerData = null
  try {
    // Remove markdown code blocks if present
    const cleanJson = overallWinnerData.replace(/^```json\s*/, '').replace(/\s*```$/, '').trim()
    structuredWinnerData = JSON.parse(cleanJson)
  } catch (e) {
    console.warn('Failed to parse Overall Winner JSON:', e)
    // Fallback structured data
    structuredWinnerData = {
      overallWinner: {
        name: overallWinner.name,
        compositeScore: winnerContext.compositeScore,
        reasoning: "Based on weighted performance analysis"
      },
      modelChoiceGuide: {
        model1: {
          name: model1.name,
          chooseIfNeeds: ["Advanced AI tasks", "High performance requirements"],
          keyStrengths: [`Context: ${model1Context.contextWindow.toLocaleString()} tokens`],
          pricing: `$${model1Context.inputCost}/$${model1Context.outputCost} per million tokens`
        },
        model2: {
          name: model2.name,
          chooseIfNeeds: ["Cost-effective workflows", "Specialized applications"],
          keyStrengths: [`Context: ${model2Context.contextWindow.toLocaleString()} tokens`],
          pricing: `$${model2Context.inputCost}/$${model2Context.outputCost} per million tokens`
        }
      }
    }
  }

  return {
    content: comparisonContent,
    metaDescription: finalMetaDescription,
    title: finalTitle,
    keywords,
    faqItems,
    // o3-pro A/B testing enhancements  
    metaDescriptionB: metaDescriptionB || undefined,
    titleB: titleB || undefined,
    // O3-pro Overall Winner section structured data
    overallWinner: structuredWinnerData?.overallWinner,
    modelChoiceGuide: structuredWinnerData?.modelChoiceGuide
  }
}

export async function generateAlternativeLandingPage(
  targetPlatform: string,
  ourModels: Model[]
): Promise<string> {
  const prompt = `Create an SEO-optimized landing page comparing JustSimpleChat to ${targetPlatform}.

Key points to emphasize:
- Access to ${ourModels.length}+ AI models vs single model
- Cost savings of 60% compared to individual subscriptions
- No rate limits or message restrictions
- Smart routing to best model for each task
- All premium models included (GPT-4, Claude 3.5, Gemini, etc)

Include:
- Compelling headline
- Feature comparison table
- Pricing comparison
- User testimonials placeholder
- Clear CTAs
- FAQ section

Target keywords: "${targetPlatform} alternative", "cheaper than ${targetPlatform}", "${targetPlatform} vs JustSimpleChat"
Format: Markdown optimized for featured snippets`

  const { text } = await generateText({
    model: google('gemini-2.0-flash-exp'),
    prompt,
    temperature: 0.4,
    maxTokens: 2500
  })

  return text
}

export async function generateUseCaseContent(
  industry: string,
  task: string,
  relevantModels: Model[]
): Promise<string> {
  const prompt = `Create an SEO-optimized guide for ${industry} professionals using AI for ${task}.

Include:
1. Step-by-step tutorial
2. Best AI models for this specific task from: ${relevantModels.map(m => m.name).join(', ')}
3. Real examples and templates
4. Cost comparison vs using individual AI subscriptions
5. Pro tips and best practices
6. Common mistakes to avoid

Target keywords: "AI for ${task}", "${industry} AI tools", "best AI model for ${task}"
Format: Comprehensive guide with actionable steps`

  const { text } = await generateText({
    model: anthropic('claude-3-5-sonnet-20241022'),
    prompt,
    temperature: 0.3,
    maxTokens: 3000
  })

  return text
}

export async function optimizeContentForSEO(
  content: string,
  targetKeywords: string[],
  existingMeta?: { title?: string; description?: string }
): Promise<{
  optimizedContent: string
  suggestedTitle: string
  suggestedDescription: string
}> {
  const prompt = `Optimize this content for SEO while maintaining readability and value:

Content: ${content}

Target Keywords: ${targetKeywords.join(', ')}
Current Title: ${existingMeta?.title || 'None'}
Current Description: ${existingMeta?.description || 'None'}

Requirements:
- Natural keyword density (1-2%)
- Add relevant LSI keywords
- Optimize headings for featured snippets
- Identify FAQ schema opportunities
- Maintain E-E-A-T signals
- Ensure mobile readability
- Add internal linking suggestions marked as [INTERNAL_LINK: topic]

Return the optimized content with clear section headings.`

  const { text: optimizedContent } = await generateText({
    model: openai('gpt-4-turbo'),
    prompt,
    temperature: 0.3,
    maxTokens: 4000
  })

  // Generate improved meta tags
  const metaPrompt = `Based on this optimized content, suggest:
1. SEO title (60 chars max) targeting: ${targetKeywords[0]}
2. Meta description (155 chars max) with call-to-action

Content preview: ${optimizedContent.substring(0, 300)}...`

  const { text: metaSuggestions } = await generateText({
    model: google('gemini-2.0-flash-exp'),
    prompt: metaPrompt,
    temperature: 0.5,
    maxTokens: 200
  })

  const [suggestedTitle, suggestedDescription] = metaSuggestions.split('\n').map(line => 
    line.replace(/^\d\.\s*/, '').replace(/^(SEO title|Meta description):\s*/i, '').trim()
  )

  return {
    optimizedContent,
    suggestedTitle,
    suggestedDescription
  }
}

// Helper functions
function extractSection(lines: string[], section: string): string {
  const sectionLower = section.toLowerCase();
  
  // Find the header line that matches our section
  let headerIndex = -1;
  const headerPatterns = [
    new RegExp(`^\\*\\*${section}[^:]*\\*\\*`, 'i'), // **Section**
    new RegExp(`^\\d+\\.\\s*\\*\\*${section}[^:]*\\*\\*`, 'i'), // 1. **Section**
    new RegExp(`^${section}[^:]*:`, 'i'), // Section:
    new RegExp(`^\\d+\\.\\s*${section}[^:]*:`, 'i'), // 1. Section:
    new RegExp(`^[-*]\\s*\\*\\*${section}[^:]*\\*\\*`, 'i'), // - **Section**
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Check if this line is a section header
    for (const pattern of headerPatterns) {
      if (pattern.test(line)) {
        headerIndex = i;
        break;
      }
    }
    
    if (headerIndex !== -1) break;
  }
  
  if (headerIndex === -1) {
    // Fallback: look for lines containing the section name
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].toLowerCase().includes(sectionLower)) {
        headerIndex = i;
        break;
      }
    }
  }
  
  if (headerIndex === -1) return '';
  
  const headerLine = lines[headerIndex].trim();
  
  // Try to extract content from the same line first
  const sameLinePatterns = [
    new RegExp(`^\\*\\*${section}[^:]*\\*\\*:?\\s*(.+)$`, 'i'),
    new RegExp(`^\\d+\\.\\s*\\*\\*${section}[^:]*\\*\\*:?\\s*(.+)$`, 'i'),
    new RegExp(`^${section}[^:]*:\\s*(.+)$`, 'i'),
    new RegExp(`^\\d+\\.\\s*${section}[^:]*:\\s*(.+)$`, 'i'),
  ];
  
  for (const pattern of sameLinePatterns) {
    const match = headerLine.match(pattern);
    if (match && match[1].trim()) {
      return match[1].trim().replace(/["'`]/g, '');
    }
  }
  
  // Look for content in the next few lines
  for (let i = headerIndex + 1; i < Math.min(headerIndex + 5, lines.length); i++) {
    const nextLine = lines[i].trim();
    
    // Skip empty lines
    if (!nextLine) continue;
    
    // Stop if we hit another section header
    if (nextLine.match(/^\d+\.\s*\*\*/) || nextLine.match(/^\*\*[^*]+\*\*/) || nextLine.includes('**')) {
      break;
    }
    
    // Extract quoted content or direct content
    let content = nextLine;
    
    // Remove surrounding quotes
    if ((content.startsWith('"') && content.endsWith('"')) ||
        (content.startsWith("'") && content.endsWith("'"))) {
      content = content.slice(1, -1);
    }
    
    // Clean up and return if we found substantial content
    if (content.length > 5) {
      return content.trim().replace(/[`]/g, '');
    }
  }
  
  return '';
}

function extractKeywords(lines: string[]): string[] {
  // Try to find keywords with various patterns
  const patterns = [
    /^\*\*keywords?\*\*:?\s*(.+)$/i,
    /^\d+\.\s*\*\*keywords?\*\*:?\s*(.+)$/i,
    /^keywords?:\s*(.+)$/i,
    /^\d+\.\s*keywords?:\s*(.+)$/i,
    /^[-*]\s*keywords?:?\s*(.+)$/i
  ]
  
  for (const line of lines) {
    for (const pattern of patterns) {
      const match = line.match(pattern)
      if (match) {
        const keywordText = match[1].trim()
        // Handle both comma-separated and bullet-point lists
        if (keywordText.includes(',')) {
          return keywordText.split(',').map(k => k.trim().replace(/["'`-]/g, ''))
        } else {
          // Look for bullet points in subsequent lines
          const lineIndex = lines.indexOf(line)
          const keywordList = []
          keywordList.push(keywordText.replace(/["'`-]/g, ''))
          
          for (let i = lineIndex + 1; i < lines.length && i < lineIndex + 10; i++) {
            const nextLine = lines[i].trim()
            if (nextLine.match(/^[-*]\s*(.+)$/) || nextLine.match(/^\d+\.\s*(.+)$/)) {
              const keywordMatch = nextLine.match(/^[-*\d.]\s*(.+)$/)
              if (keywordMatch) {
                keywordList.push(keywordMatch[1].replace(/["'`]/g, ''))
              }
            } else if (nextLine.match(/^\*\*[^*]+\*\*/) || nextLine.includes(':')) {
              // Stop at next section
              break
            }
          }
          
          return keywordList.filter(k => k.length > 0)
        }
      }
    }
  }
  
  return []
}

function extractFAQs(lines: string[]): Array<{ question: string; answer: string }> {
  const faqStart = lines.findIndex(l => l.toLowerCase().includes('faq'))
  if (faqStart === -1) return []
  
  const faqs: Array<{ question: string; answer: string }> = []
  let currentQuestion = ''
  let currentAnswer = ''
  
  for (let i = faqStart + 1; i < lines.length && faqs.length < 5; i++) {
    const line = lines[i].trim()
    
    // Enhanced question patterns - look for numbered lists and questions
    const isNumberedQuestion = line.match(/^\d+\.\s*\*\*(.+\?)\*\*$/)
    const isQuestionWithMarkdown = line.match(/^\*\*(.+\?)\*\*$/)
    const isSimpleQuestion = line.match(/^\d+\.\s*(.+\?)$/)
    
    if (isNumberedQuestion || isQuestionWithMarkdown || isSimpleQuestion) {
      // Save previous FAQ if we have both question and answer
      if (currentQuestion && currentAnswer) {
        faqs.push({ 
          question: currentQuestion.replace(/\*\*/g, '').trim(), 
          answer: currentAnswer.trim() 
        })
      }
      
      // Extract new question
      currentQuestion = (isNumberedQuestion?.[1] || isQuestionWithMarkdown?.[1] || isSimpleQuestion?.[1] || '').trim()
      currentAnswer = ''
    } else if (currentQuestion && line && !line.match(/^\d+\.\s*\*\*/) && !line.match(/^\*\*[^*]+\*\*:?\s*$/)) {
      // This is likely an answer line - add to current answer
      if (currentAnswer) {
        currentAnswer += ' ' + line
      } else {
        currentAnswer = line
      }
    }
  }
  
  // Don't forget the last FAQ pair
  if (currentQuestion && currentAnswer) {
    faqs.push({ 
      question: currentQuestion.replace(/\*\*/g, '').trim(), 
      answer: currentAnswer.trim() 
    })
  }
  
  return faqs.map(faq => ({
    question: faq.question.replace(/["'`]/g, '').trim(),
    answer: faq.answer.replace(/["'`]/g, '').trim()
  }))
}

function extractSchemaMarkup(lines: string[]): string | null {
  // Look for JSON-LD schema markup in the meta content
  const schemaStart = lines.findIndex(l => l.includes('{') && (l.includes('@context') || l.includes('FAQPage')))
  if (schemaStart === -1) return null
  
  let schemaContent = ''
  let braceCount = 0
  let foundStart = false
  
  for (let i = schemaStart; i < lines.length; i++) {
    const line = lines[i].trim()
    
    // Count opening and closing braces to capture complete JSON
    for (const char of line) {
      if (char === '{') {
        braceCount++
        foundStart = true
      } else if (char === '}') {
        braceCount--
      }
    }
    
    if (foundStart) {
      schemaContent += line + '\n'
    }
    
    // Stop when we've closed all braces
    if (foundStart && braceCount === 0) {
      break
    }
  }
  
  // Validate that it's proper JSON
  try {
    JSON.parse(schemaContent.trim())
    return schemaContent.trim()
  } catch {
    return null
  }
}

// Batch generation for programmatic SEO
export async function generateComparisonBatch(
  comparisons: Array<{ model1: Model; model2: Model }>,
  concurrency: number = 3
): Promise<Map<string, ComparisonResult>> {
  const results = new Map<string, ComparisonResult>()
  
  // Process in batches to avoid rate limits
  for (let i = 0; i < comparisons.length; i += concurrency) {
    const batch = comparisons.slice(i, i + concurrency)
    const batchResults = await Promise.all(
      batch.map(({ model1, model2 }) => 
        generateModelComparison(model1, model2)
          .then(result => ({ 
            key: `${model1.canonicalName}-vs-${model2.canonicalName}`, 
            result 
          }))
      )
    )
    
    batchResults.forEach(({ key, result }) => {
      results.set(key, result)
    })
    
    // Add delay between batches to respect rate limits
    if (i + concurrency < comparisons.length) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  return results
}

// Helper functions for o3-pro enhanced data extraction
function getTopPerformanceCategory(categoryScores: any): { category: string; score: number; confidence: number } {
  if (!categoryScores) return { category: 'general', score: 85, confidence: 0.9 };
  
  const topEntry = Object.entries(categoryScores)
    .filter(([_, data]: [string, any]) => data?.score && data.score > 80)
    .sort((a, b) => (b[1] as any).score - (a[1] as any).score)[0];
  
  if (topEntry) {
    const [category, data] = topEntry;
    return {
      category: category.replace(/_/g, ' '),
      score: (data as any).score,
      confidence: (data as any).confidence || 0.9
    };
  }
  return { category: 'general AI', score: 85, confidence: 0.9 };
}

function getCompetitiveAdvantage(score1: number, score2: number, category: string): string {
  const difference = Math.abs(score1 - score2);
  const leader = score1 > score2 ? 'model1' : 'model2';
  
  if (difference > 15) return `${difference}% superior ${category} performance`;
  if (difference > 8) return `${difference}% better ${category} results`;
  if (difference > 3) return `${difference}% edge in ${category}`;
  return `Competitive ${category} performance`;
}

function getCostEfficiencyStatement(inputCost: number, outputCost: number, topCategory: string): string {
  if (inputCost > 15) {
    return `Premium $${inputCost} rate justified by ${topCategory} excellence - typical 40% efficiency gains vs alternatives`;
  } else if (inputCost > 5) {
    return `Balanced $${inputCost} pricing optimizes ${topCategory} performance-to-cost ratio`;
  } else {
    return `Efficient $${inputCost} rate perfect for high-volume ${topCategory} workflows`;
  }
}

// Extract comprehensive model context from rich metadata for AI generation
function extractModelContext(model: Model, metadata: any = {}, primaryUseCase: string | null = null) {
  // Safely extract values with fallbacks
  const pricing = metadata?.pricing || {}
  const capabilities = metadata?.capabilities || {}
  const taskScores = metadata?.taskScores || {}
  const categoryScores = metadata?.categoryScores || {}
  const benchmarkScores = metadata?.benchmarkScores || {}
  const contextWindow = metadata?.contextWindow || model.contextWindow || 4096
  const maxOutput = metadata?.maxOutputTokens || model.maxOutput || 4096
  const inputCost = pricing.input || metadata?.inputCost || 0
  const outputCost = pricing.output || metadata?.outputCost || 0
  const costCategory = metadata?.costCategory || 'UNKNOWN'
  const specialCapabilities = metadata?.specialCapabilities || []
  
  // O3-pro weighted composite scoring system
  const weights = { coding: 0.3, reasoning: 0.3, creative: 0.2, analysis: 0.2 }
  
  function to100(x: number) { return Math.round(x * 10) } // Convert 0-10 scale to 0-100
  
  const compositeScore = Math.round(
    (categoryScores.coding?.score || 75) * weights.coding +
    (categoryScores.reasoning?.score || 75) * weights.reasoning +
    to100(taskScores.creative || 7.5) * weights.creative +
    to100(taskScores.analysis || 7.5) * weights.analysis
  )
  
  // Build context structure with rich information
  return {
    // Basic info
    primaryUseCase: primaryUseCase || 'General AI assistant',
    contextWindow,
    maxOutput,
    inputCost,
    outputCost,
    costCategory,
    compositeScore, // O3-pro weighted score for overall winner determination
    
    // Capabilities with confidence levels
    capabilities: Object.entries(capabilities).map(([cap, info]: [string, any]) => {
      if (info?.hasCapability) {
        const confidence = info.confidence ? ` (${Math.round(info.confidence * 100)}% confidence)` : ''
        return `${cap.replace(/_/g, ' ')}${confidence}`
      }
      return null
    }).filter(Boolean),
    
    // Special features
    specialFeatures: [
      ...(specialCapabilities || []).map((cap: string) => cap.replace(/_/g, ' ')),
      ...(metadata?.apiStatus ? [`API Status: ${metadata.apiStatus}`] : []),
      ...(metadata?.planAvailability ? [`Available on: ${metadata.planAvailability.join(', ')} plans`] : [])
    ],
    
    // Top benchmark scores (highest scoring areas)
    benchmarks: Object.entries(benchmarkScores).map(([bench, score]: [string, any]) => {
      if (typeof score === 'number' && score > 80) {
        return `${bench.replace(/_/g, ' ')}: ${score}`
      }
      return null
    }).filter(Boolean).slice(0, 3), // Top 3 benchmarks
    
    // Top strengths from category scores
    topStrengths: Object.entries(categoryScores)
      .filter(([_, scoreData]: [string, any]) => scoreData?.score && scoreData.score > 85)
      .sort((a, b) => (b[1] as any).score - (a[1] as any).score)
      .slice(0, 4)
      .map(([category, scoreData]: [string, any]) => {
        const confidence = scoreData.confidence ? ` (${Math.round(scoreData.confidence * 100)}% confidence)` : ''
        return `${category.replace(/_/g, ' ')}: ${scoreData.score}/100${confidence}`
      }),
    
    // Best use cases based on high-scoring categories
    useCases: Object.entries(categoryScores)
      .filter(([_, scoreData]: [string, any]) => scoreData?.score && scoreData.score > 80)
      .sort((a, b) => (b[1] as any).score - (a[1] as any).score)
      .slice(0, 5)
      .map(([category]: [string, any]) => {
        // Convert category names to user-friendly use cases
        switch (category) {
          case 'coding': return 'Software development and debugging'
          case 'creative_writing': return 'Content creation and storytelling'
          case 'reasoning': return 'Complex problem solving'
          case 'mathematical': return 'Math and quantitative analysis'
          case 'analysis': return 'Data analysis and insights'
          case 'scientific': return 'Research and scientific writing'
          case 'business_writing': return 'Professional communication'
          case 'technical_writing': return 'Documentation and technical content'
          case 'multimodal': return 'Image and document analysis'
          case 'web_search': return 'Real-time information research'
          default: return category.replace(/_/g, ' ')
        }
      }),
    
    // Performance highlights from task scores
    performanceHighlights: Object.entries(taskScores)
      .filter(([_, score]: [string, any]) => typeof score === 'number' && score > 8.0)
      .sort((a, b) => (b[1] as number) - (a[1] as number))
      .slice(0, 3)
      .map(([task, score]: [string, any]) => {
        const taskNames: { [key: string]: string } = {
          ana: 'Analysis',
          cha: 'Chat',
          cod: 'Coding',
          cre: 'Creative',
          lng: 'Language',
          mat: 'Math',
          rea: 'Reasoning',
          vis: 'Vision'
        }
        return `${taskNames[task] || task}: ${score}/10`
      })
  }
}

// Retry logic with multiple models and exponential backoff
async function generateWithRetry(
  prompt: string, 
  options: {
    temperature: number;
    topP: number;
    maxTokens: number;
  },
  type: 'content' | 'meta' | 'json' = 'content'
): Promise<string> {
  const models = [
    // Primary: Claude 4 Sonnet (latest and best)
    anthropic('claude-sonnet-4-20250514'),
    // Fallback 1: Claude 3.5 Sonnet
    anthropic('claude-3-5-sonnet-20241022'),
    // Fallback 2: GPT-4o (fast and reliable)
    openai('gpt-4o'),
    // Fallback 3: Gemini 2.0 Flash (very fast)
    google('gemini-2.0-flash-exp')
  ];

  let lastError: Error = new Error('No models attempted');
  
  for (let modelIndex = 0; modelIndex < models.length; modelIndex++) {
    const model = models[modelIndex];
    const modelName = model.provider + '/' + model.modelId;
    
    // Try each model with exponential backoff
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        console.log(`🤖 Attempting ${type} generation with ${modelName} (attempt ${attempt})`);
        
        const { text } = await generateText({
          model,
          prompt,
          temperature: options.temperature,
          topP: options.topP,
          maxTokens: options.maxTokens
        });
        
        console.log(`✅ Success with ${modelName} on attempt ${attempt}`);
        return text;
        
      } catch (error: any) {
        lastError = error;
        const isOverloaded = error.message?.includes('overloaded') || 
                           error.message?.includes('Overloaded') ||
                           error.message?.includes('rate limit') ||
                           error.status === 429;
        
        console.log(`⚠️ ${modelName} attempt ${attempt} failed: ${error.message}`);
        
        // If overloaded, try exponential backoff for same model
        if (isOverloaded && attempt < 3) {
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
        
        // If not overloaded or max attempts reached, try next model
        if (attempt === 3) {
          console.log(`❌ ${modelName} failed after 3 attempts, trying next model...`);
          break;
        }
      }
    }
  }
  
  // If all models failed, throw the last error
  console.error('💥 All models failed for SEO generation');
  throw new Error(`AI_RetryError: All models failed. Last error: ${lastError?.message || 'Unknown error'}`);
}