/**
 * Redis Client Wrapper
 * 
 * Centralized Redis client with:
 * - Type-safe operations
 * - Connection pooling
 * - Error handling
 * - Retry logic
 * - Performance monitoring
 * - Graceful shutdown
 */

import Redis, { Redis as RedisClient, RedisOptions } from 'ioredis';
import { apiLogger } from '@/lib/logger';

// Redis key prefixes for different data types
export const REDIS_PREFIXES = {
  MODEL_CACHE: 'ai:models:',
  PROVIDER_CACHE: 'ai:providers:',
  CONVERSATION: 'conv:',
  RATE_LIMIT: 'rate:',
  SESSION: 'session:',
  METRICS: 'metrics:',
  QUEUE: 'queue:',
  LOCK: 'lock:'
} as const;

// Default TTL values (in seconds)
export const REDIS_TTL = {
  MODEL_CACHE: 300,      // 5 minutes
  PROVIDER_CACHE: 600,   // 10 minutes
  CONVERSATION: 3600,    // 1 hour
  RATE_LIMIT: 60,        // 1 minute
  SESSION: 86400,        // 24 hours
  METRICS: 300,          // 5 minutes
  LOCK: 30               // 30 seconds
} as const;

// Redis configuration interface
export interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  db?: number;
  keyPrefix?: string;
  maxRetriesPerRequest?: number;
  enableReadyCheck?: boolean;
  connectTimeout?: number;
  lazyConnect?: boolean;
  retryStrategy?: (times: number) => number | void | null;
}

/**
 * Enhanced Redis client wrapper
 */
export class RedisClientWrapper {
  private static instance: RedisClientWrapper;
  private client: RedisClient;
  private isConnected: boolean = false;
  private connectionAttempts: number = 0;

  private constructor(config?: RedisConfig) {
    const defaultConfig: RedisOptions = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      connectTimeout: 10000,
      lazyConnect: false,
      retryStrategy: (times: number) => {
        if (times > 10) {
          apiLogger.error('Redis connection failed after 10 attempts');
          return null;
        }
        const delay = Math.min(times * 200, 2000);
        apiLogger.warn(`Retrying Redis connection in ${delay}ms (attempt ${times})`);
        return delay;
      }
    };

    const finalConfig = { ...defaultConfig, ...config };
    
    // Create Redis client
    this.client = new Redis(finalConfig);
    
    // Set up event handlers
    this.setupEventHandlers();
    
    apiLogger.info('Redis client initialized', {
      host: finalConfig.host,
      port: finalConfig.port,
      db: finalConfig.db
    });
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: RedisConfig): RedisClientWrapper {
    if (!RedisClientWrapper.instance) {
      RedisClientWrapper.instance = new RedisClientWrapper(config);
    }
    return RedisClientWrapper.instance;
  }

  /**
   * Set up Redis event handlers
   */
  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      this.connectionAttempts++;
      apiLogger.info('Redis client connecting...', {
        attempt: this.connectionAttempts
      });
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      apiLogger.info('Redis client ready');
    });

    this.client.on('error', (error: Error) => {
      apiLogger.error('Redis client error', {
        error: error.message,
        stack: error.stack
      });
    });

    this.client.on('close', () => {
      this.isConnected = false;
      apiLogger.warn('Redis client connection closed');
    });

    this.client.on('reconnecting', (delay: number) => {
      apiLogger.info('Redis client reconnecting', { delay });
    });

    this.client.on('end', () => {
      this.isConnected = false;
      apiLogger.info('Redis client connection ended');
    });
  }

  /**
   * Get the raw Redis client (for advanced operations)
   */
  getClient(): RedisClient {
    return this.client;
  }

  /**
   * Check if Redis is connected
   */
  isReady(): boolean {
    return this.isConnected && this.client.status === 'ready';
  }

  /**
   * Wait for Redis to be ready
   */
  async waitForReady(timeoutMs: number = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (!this.isReady()) {
      if (Date.now() - startTime > timeoutMs) {
        throw new Error('Redis connection timeout');
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Get value with type safety
   */
  async get<T = string>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      if (!value) return null;
      
      // Try to parse as JSON, fallback to raw value
      try {
        return JSON.parse(value) as T;
      } catch {
        return value as unknown as T;
      }
    } catch (error) {
      apiLogger.error('Redis get error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Set value with optional TTL
   */
  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<boolean> {
    try {
      const serialized = typeof value === 'string' ? value : JSON.stringify(value);
      
      if (ttlSeconds) {
        const result = await this.client.setex(key, ttlSeconds, serialized);
        return result === 'OK';
      } else {
        const result = await this.client.set(key, serialized);
        return result === 'OK';
      }
    } catch (error) {
      apiLogger.error('Redis set error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Delete key(s)
   */
  async delete(...keys: string[]): Promise<number> {
    try {
      return await this.client.del(...keys);
    } catch (error) {
      apiLogger.error('Redis delete error', {
        keys,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      apiLogger.error('Redis exists error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Set expiration on key
   */
  async expire(key: string, ttlSeconds: number): Promise<boolean> {
    try {
      const result = await this.client.expire(key, ttlSeconds);
      return result === 1;
    } catch (error) {
      apiLogger.error('Redis expire error', {
        key,
        ttlSeconds,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Get multiple values at once
   */
  async mget<T = string>(...keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.client.mget(...keys);
      return values.map(value => {
        if (!value) return null;
        try {
          return JSON.parse(value) as T;
        } catch {
          return value as unknown as T;
        }
      });
    } catch (error) {
      apiLogger.error('Redis mget error', {
        keys,
        error: error instanceof Error ? error.message : String(error)
      });
      return keys.map(() => null);
    }
  }

  /**
   * Increment a counter
   */
  async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      apiLogger.error('Redis incr error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Decrement a counter
   */
  async decr(key: string): Promise<number> {
    try {
      return await this.client.decr(key);
    } catch (error) {
      apiLogger.error('Redis decr error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Add to a set
   */
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sadd(key, ...members);
    } catch (error) {
      apiLogger.error('Redis sadd error', {
        key,
        members,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Get all members of a set
   */
  async smembers(key: string): Promise<string[]> {
    try {
      return await this.client.smembers(key);
    } catch (error) {
      apiLogger.error('Redis smembers error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  /**
   * Push to a list
   */
  async lpush(key: string, ...values: string[]): Promise<number> {
    try {
      return await this.client.lpush(key, ...values);
    } catch (error) {
      apiLogger.error('Redis lpush error', {
        key,
        values,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Pop from a list
   */
  async lpop(key: string): Promise<string | null> {
    try {
      return await this.client.lpop(key);
    } catch (error) {
      apiLogger.error('Redis lpop error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Get hash field
   */
  async hget<T = string>(key: string, field: string): Promise<T | null> {
    try {
      const value = await this.client.hget(key, field);
      if (!value) return null;
      
      try {
        return JSON.parse(value) as T;
      } catch {
        return value as unknown as T;
      }
    } catch (error) {
      apiLogger.error('Redis hget error', {
        key,
        field,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Set hash field
   */
  async hset<T>(key: string, field: string, value: T): Promise<number> {
    try {
      const serialized = typeof value === 'string' ? value : JSON.stringify(value);
      return await this.client.hset(key, field, serialized);
    } catch (error) {
      apiLogger.error('Redis hset error', {
        key,
        field,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Acquire a distributed lock
   */
  async acquireLock(lockKey: string, ttlSeconds: number = 30): Promise<boolean> {
    const lockValue = `${Date.now()}-${Math.random()}`;
    const fullKey = `${REDIS_PREFIXES.LOCK}${lockKey}`;
    
    try {
      const result = await this.client.set(fullKey, lockValue, 'EX', ttlSeconds, 'NX');
      return result === 'OK';
    } catch (error) {
      apiLogger.error('Redis lock acquisition error', {
        lockKey,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Release a distributed lock
   */
  async releaseLock(lockKey: string): Promise<boolean> {
    const fullKey = `${REDIS_PREFIXES.LOCK}${lockKey}`;
    
    try {
      const result = await this.client.del(fullKey);
      return result === 1;
    } catch (error) {
      apiLogger.error('Redis lock release error', {
        lockKey,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Clear all keys with a specific prefix
   */
  async clearPrefix(prefix: string): Promise<number> {
    try {
      const keys = await this.client.keys(`${prefix}*`);
      if (keys.length === 0) return 0;
      
      return await this.client.del(...keys);
    } catch (error) {
      apiLogger.error('Redis clear prefix error', {
        prefix,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  /**
   * Gracefully shutdown Redis connection
   */
  async shutdown(): Promise<void> {
    try {
      await this.client.quit();
      this.isConnected = false;
      apiLogger.info('Redis client shutdown complete');
    } catch (error) {
      apiLogger.error('Redis shutdown error', {
        error: error instanceof Error ? error.message : String(error)
      });
      // Force disconnect if quit fails
      this.client.disconnect();
    }
  }

  /**
   * Ping Redis server
   */
  async ping(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      apiLogger.error('Redis ping error', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Get Redis info
   */
  async info(section?: string): Promise<string> {
    try {
      return await (this.client as any).info(section);
    } catch (error) {
      apiLogger.error('Redis info error', {
        section,
        error: error instanceof Error ? error.message : String(error)
      });
      return '';
    }
  }
}

// Export singleton instance
export const redis = RedisClientWrapper.getInstance();

// Export type-safe key builders
export function buildModelCacheKey(providerId: string): string {
  return `${REDIS_PREFIXES.MODEL_CACHE}${providerId}`;
}

export function buildProviderCacheKey(providerId: string): string {
  return `${REDIS_PREFIXES.PROVIDER_CACHE}${providerId}`;
}

export function buildConversationKey(conversationId: string): string {
  return `${REDIS_PREFIXES.CONVERSATION}${conversationId}`;
}

export function buildRateLimitKey(userId: string, endpoint: string): string {
  return `${REDIS_PREFIXES.RATE_LIMIT}${userId}:${endpoint}`;
}

export function buildSessionKey(sessionId: string): string {
  return `${REDIS_PREFIXES.SESSION}${sessionId}`;
}

export function buildMetricsKey(metric: string, timestamp?: number): string {
  const ts = timestamp || Math.floor(Date.now() / 1000);
  return `${REDIS_PREFIXES.METRICS}${metric}:${ts}`;
}

export function buildQueueKey(queueName: string): string {
  return `${REDIS_PREFIXES.QUEUE}${queueName}`;
}

// Export Redis client class for testing
export { RedisClientWrapper as RedisClient };