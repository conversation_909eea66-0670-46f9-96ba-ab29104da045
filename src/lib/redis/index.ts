/**
 * Redis Module Export
 * 
 * Central export point for Redis functionality
 */

export {
  redis,
  RedisClient,
  RedisClientWrapper,
  REDIS_PREFIXES,
  REDIS_TTL,
  buildModelCacheKey,
  buildProviderCacheKey,
  buildConversationKey,
  buildRateLimitKey,
  buildSessionKey,
  buildMetricsKey,
  buildQueueKey
} from './client';

export type { RedisConfig } from './client';

// Re-export for convenience
export { Redis as IORedis } from 'ioredis';