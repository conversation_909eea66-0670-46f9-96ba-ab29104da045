import Redis from 'ioredis';

// Use Redis URL from environment variable
const getRedisUrl = () => {
  // Always prefer environment variable if set
  return process.env.REDIS_URL || 'redis://localhost:6379';
};

const redisUrl = getRedisUrl();

// Configure Redis client with production-ready settings
export const redis = new Redis(redisUrl, {
  maxRetriesPerRequest: 3,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  // TLS settings only for rediss:// URLs
  ...(redisUrl.startsWith('rediss://') ? {
    tls: {
      rejectUnauthorized: false // ElastiCache uses self-signed certs
    }
  } : {}),
  connectTimeout: 10000,
  enableOfflineQueue: true,
  lazyConnect: false
});

redis.on('error', (error) => {
  console.error('Redis Client Error:', error);
});

redis.on('connect', () => {
  console.log('Redis Client Connected to:', redisUrl.includes('amazonaws.com') ? 'ElastiCache' : 'Local Redis', `(${redisUrl.substring(0, 30)}...)`);
});

// Redis configuration constants
export const REDIS_TTL = {
  MODEL_CACHE: 5 * 60, // 5 minutes
  PROVIDER_CACHE: 5 * 60, // 5 minutes
  USER_CACHE: 15 * 60, // 15 minutes
  DEFAULT: 5 * 60 // 5 minutes
};

export const REDIS_PREFIXES = {
  MODEL_CACHE: 'model:',
  PROVIDER_CACHE: 'provider:',
  USER_CACHE: 'user:',
  SESSION: 'session:',
  RATE_LIMIT: 'rl:'
};