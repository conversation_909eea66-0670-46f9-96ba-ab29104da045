import { prisma } from '@/lib/prisma';
import { UserPlan } from '@/types';
import { getTierConfig } from '@/lib/subscription/tiers';

export interface MessageLimitResult {
  allowed: boolean;
  reason?: string;
  standardUsed: number;
  standardRemaining: number;
  premiumUsed: number;
  premiumRemaining: number;
  resetDate: Date;
  isPremiumModel: boolean;
}

// Define message limits based on pricing documentation
export const MESSAGE_LIMITS = {
  [UserPlan.FREE]: {
    standard: 0, // Daily limit handled by rate limiter
    premium: 0
  },
  [UserPlan.FREEMIUM]: {
    standard: 10, // 10 per day
    premium: 0
  },
  [UserPlan.PLUS]: {
    standard: 1500,
    premium: 100
  },
  [UserPlan.ADVANCED]: {
    standard: 4500,
    premium: 200
  },
  [UserPlan.MAX]: {
    standard: 7000,
    premium: 300
  },
  [UserPlan.ENTERPRISE]: {
    standard: 1000000, // Effectively unlimited
    premium: 1000000
  }
};

// Check if model is premium based on cost (> $5 input OR > $10 output)
export async function isPremiumModel(modelId: string): Promise<boolean> {
  try {
    const model = await prisma.models.findFirst({
      where: {
        canonicalName: modelId,
        isEnabled: true
      },
      select: {
        extendedMetadata: true
      }
    });

    if (!model?.extendedMetadata || typeof model.extendedMetadata !== 'object') {
      return false;
    }

    const metadata = model.extendedMetadata as any;
    const inputCost = parseFloat(metadata.inputCost || '0');
    const outputCost = parseFloat(metadata.outputCost || '0');

    // Premium if > $5 input OR > $10 output
    return inputCost > 5.0 || outputCost > 10.0;
  } catch (error) {
    console.error('Error checking if model is premium:', error);
    return false;
  }
}

// Get the next month reset date
export function getNextMonthResetDate(lastResetDate: Date): Date {
  const nextReset = new Date(lastResetDate);
  nextReset.setMonth(nextReset.getMonth() + 1);
  nextReset.setDate(1); // First day of next month
  nextReset.setHours(0, 0, 0, 0);
  return nextReset;
}

// Check if monthly counters need reset
export function shouldResetMonthly(lastResetDate: Date): boolean {
  const now = new Date();
  const nextReset = getNextMonthResetDate(lastResetDate);
  return now >= nextReset;
}

// Check message limits for authenticated user
export async function checkMessageLimit(userId: string, modelId: string): Promise<MessageLimitResult> {
  try {
    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        plan: true,
        messagesUsedThisMonth: true,
        premiumMessagesUsedThisMonth: true,
        lastResetDate: true
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check if we need to reset monthly counters
    if (shouldResetMonthly(user.lastResetDate)) {
      // Reset counters
      await prisma.user.update({
        where: { id: userId },
        data: {
          messagesUsedThisMonth: 0,
          premiumMessagesUsedThisMonth: 0,
          lastResetDate: new Date()
        }
      });
      
      // Update local user object
      user.messagesUsedThisMonth = 0;
      user.premiumMessagesUsedThisMonth = 0;
      user.lastResetDate = new Date();
    }

    // Get plan limits
    const limits = MESSAGE_LIMITS[user.plan as UserPlan];
    if (!limits) {
      throw new Error('Invalid user plan');
    }

    // Check if this is a premium model
    const isPremium = await isPremiumModel(modelId);

    // Calculate remaining messages
    const standardRemaining = Math.max(0, limits.standard - user.messagesUsedThisMonth);
    const premiumRemaining = Math.max(0, limits.premium - (user.premiumMessagesUsedThisMonth || 0));

    // Determine if allowed
    let allowed = false;
    let reason = '';

    if (isPremium) {
      // Premium model - check premium limit first, then fall back to standard
      if (premiumRemaining > 0) {
        allowed = true;
      } else if (standardRemaining > 0) {
        allowed = true; // Can use standard messages for premium models
      } else {
        reason = 'Monthly premium and standard message limits reached';
      }
    } else {
      // Standard model - only check standard limit
      if (standardRemaining > 0) {
        allowed = true;
      } else {
        reason = 'Monthly message limit reached';
      }
    }

    return {
      allowed,
      reason,
      standardUsed: user.messagesUsedThisMonth,
      standardRemaining,
      premiumUsed: user.premiumMessagesUsedThisMonth || 0,
      premiumRemaining,
      resetDate: getNextMonthResetDate(user.lastResetDate),
      isPremiumModel: isPremium
    };
  } catch (error) {
    console.error('Error checking message limit:', error);
    // Be permissive on error
    return {
      allowed: true,
      standardUsed: 0,
      standardRemaining: 999999,
      premiumUsed: 0,
      premiumRemaining: 999999,
      resetDate: new Date(),
      isPremiumModel: false
    };
  }
}

// Increment message usage for authenticated user
export async function incrementMessageUsage(userId: string, modelId: string): Promise<void> {
  try {
    const isPremium = await isPremiumModel(modelId);

    if (isPremium) {
      // Try to increment premium counter first
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          plan: true,
          premiumMessagesUsedThisMonth: true
        }
      });

      const limits = MESSAGE_LIMITS[user?.plan as UserPlan];
      const premiumRemaining = limits.premium - (user?.premiumMessagesUsedThisMonth || 0);

      if (premiumRemaining > 0) {
        // Use premium message quota
        await prisma.user.update({
          where: { id: userId },
          data: {
            premiumMessagesUsedThisMonth: { increment: 1 }
          }
        });
      } else {
        // Fall back to standard quota
        await prisma.user.update({
          where: { id: userId },
          data: {
            messagesUsedThisMonth: { increment: 1 }
          }
        });
      }
    } else {
      // Standard model - increment standard counter
      await prisma.user.update({
        where: { id: userId },
        data: {
          messagesUsedThisMonth: { increment: 1 }
        }
      });
    }
  } catch (error) {
    console.error('Error incrementing message usage:', error);
  }
}

// Get usage info for display
export async function getUsageInfo(userId: string): Promise<{
  standard: { used: number; total: number };
  premium: { used: number; total: number };
  resetDate: Date;
}> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        plan: true,
        messagesUsedThisMonth: true,
        premiumMessagesUsedThisMonth: true,
        lastResetDate: true
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    const limits = MESSAGE_LIMITS[user.plan as UserPlan];

    return {
      standard: {
        used: user.messagesUsedThisMonth,
        total: limits.standard
      },
      premium: {
        used: user.premiumMessagesUsedThisMonth || 0,
        total: limits.premium
      },
      resetDate: getNextMonthResetDate(user.lastResetDate)
    };
  } catch (error) {
    console.error('Error getting usage info:', error);
    return {
      standard: { used: 0, total: 0 },
      premium: { used: 0, total: 0 },
      resetDate: new Date()
    };
  }
}