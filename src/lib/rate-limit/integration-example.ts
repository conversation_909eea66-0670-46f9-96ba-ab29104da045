// TODO-AUDIT-2025-Q2: Example file - should be removed (integration-example.ts)
/**
 * Integration Example: How to modify the existing chat route to use rate limiting
 * 
 * This shows the minimal changes needed to integrate the rate limiting system
 * with your existing chat API route.
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { ChatRateLimitIntegration } from './chat-integration';
import { rateLimitManager } from './rate-limit-manager';
import { queueProcessor } from './queue-processor';

// Start the queue processor when the application starts
// Add this to your application initialization
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_RATE_LIMITING === 'true') {
  queueProcessor.start();
  console.log('🚀 Rate limiting queue processor started');
}

/**
 * STEP 1: Modify your existing chat route
 * 
 * Add rate limiting checks before processing the request
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Existing authentication code
    const session = await auth();
    const { messages, conversationId, manualModel, webSearchEnabled } = await request.json();
    
    // NEW: Extract rate limiting context
    const rateLimitContext = ChatRateLimitIntegration.extractContext(request, session);
    
    // NEW: Determine provider and model (your existing routing logic)
    const selectedModel = manualModel || 'gpt-4o-mini'; // Your existing model selection
    const providerId = getProviderIdFromModel(selectedModel); // You'd implement this
    
    // NEW: Check rate limits BEFORE processing
    const rateLimitResult = await ChatRateLimitIntegration.checkChatRateLimit(
      providerId,
      selectedModel,
      rateLimitContext,
      messages
    );

    // NEW: Handle rate limiting results
    if (!rateLimitResult.allowed && !rateLimitResult.queued) {
      // Request rejected - return rate limit error
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          rateLimit: {
            currentUsage: rateLimitResult.currentUsage,
            limitValue: rateLimitResult.limitValue,
            resetTime: new Date(rateLimitResult.resetTime!).toISOString(),
            retryAfterMs: rateLimitResult.retryAfterMs
          }
        },
        { 
          status: 429,
          headers: ChatRateLimitIntegration.getRateLimitHeaders(rateLimitResult)
        }
      );
    }

    if (rateLimitResult.queued) {
      // Request queued - return streaming response with queue status
      const queueResult = await ChatRateLimitIntegration.handleQueuedChatRequest(
        providerId,
        selectedModel,
        rateLimitContext,
        messages
      );

      const stream = ChatRateLimitIntegration.createRateLimitedStream(
        {
          ...rateLimitResult,
          queuePosition: queueResult.queuePosition,
          estimatedWaitMs: queueResult.estimatedWaitMs
        },
        conversationId
      );

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          ...ChatRateLimitIntegration.getRateLimitHeaders(rateLimitResult)
        }
      });
    }

    // Request allowed - continue with existing logic
    return await processNormalRequest(
      messages,
      selectedModel,
      conversationId,
      session,
      rateLimitContext
    );
    
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * STEP 2: Create helper functions for integration
 */
function getProviderIdFromModel(modelId: string): string {
  // Map model IDs to provider IDs based on your MODEL_REGISTRY
  if (modelId.includes('gpt-') || modelId.includes('o1-') || modelId.includes('o3-') || modelId.includes('o4-')) {
    return 'openai';
  }
  if (modelId.includes('claude-')) {
    return 'anthropic';
  }
  if (modelId.includes('gemini-')) {
    return 'google';
  }
  if (modelId.includes('llama-')) {
    return 'groq';
  }
  if (modelId.includes('deepseek-')) {
    return 'deepseek';
  }
  if (modelId.includes('mistral-') || modelId.includes('mixtral-')) {
    return 'mistral';
  }
  if (modelId.includes('grok-')) {
    return 'xai';
  }
  
  return 'unknown';
}

async function processNormalRequest(
  messages: any[],
  selectedModel: string,
  conversationId: string,
  session: any,
  rateLimitContext: any
): Promise<Response> {
  // Your existing request processing logic here
  // This is where you'd call your provider and stream the response
  
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      // Send rate limit success info
      controller.enqueue(
        encoder.encode(
          `data: ${JSON.stringify({
            type: 'rate_limit',
            allowed: true,
            requestId: rateLimitContext.requestId
          })}\n\n`
        )
      );

      // Your existing streaming logic here
      // Example:
      controller.enqueue(
        encoder.encode(
          `data: ${JSON.stringify({
            type: 'content',
            content: 'This is where your AI response would stream...'
          })}\n\n`
        )
      );
      
      controller.enqueue(encoder.encode('data: [DONE]\n\n'));
      controller.close();
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  });
}

/**
 * STEP 3: Add middleware to your provider calls
 * 
 * Wrap your existing providers with rate limiting
 */
export class RateLimitedProviderExample {
  private provider: any;
  private providerId: string;

  constructor(provider: any, providerId: string) {
    this.provider = provider;
    this.providerId = providerId;
  }

  async chat(model: string, messages: any[], options: any, context: any): Promise<any> {
    // Check rate limits before making the actual API call
    const rateLimitResult = await rateLimitManager.checkRateLimit(
      this.providerId,
      model,
      context.userId,
      context.sessionId,
      context.ipAddress,
      context.userPlan
    );

    if (rateLimitResult.action !== 'ALLOW') {
      throw new Error('Rate limit exceeded');
    }

    // Make the actual API call
    return await this.provider.chat(model, messages, options);
  }

  async *streamChat(model: string, messages: any[], options: any, context: any): AsyncIterable<any> {
    // Check rate limits for streaming
    const rateLimitResult = await rateLimitManager.checkRateLimit(
      this.providerId,
      model,
      context.userId,
      context.sessionId,
      context.ipAddress,
      context.userPlan
    );

    if (rateLimitResult.action !== 'ALLOW') {
      throw new Error('Rate limit exceeded');
    }

    // Stream the actual response
    for await (const chunk of this.provider.streamChat(model, messages, options)) {
      yield chunk;
    }
  }
}

/**
 * STEP 4: Frontend Integration Example
 * 
 * How to handle rate limiting in your React components
 */
export const FrontendIntegrationExample = `
// In your chat component:
import { QueueStatus, useQueueStatus } from '@/components/chat/QueueStatus';

function ChatComponent() {
  const [isQueued, setIsQueued] = useState(false);
  const [queueInfo, setQueueInfo] = useState(null);
  const { queueStatus, cancelRequest } = useQueueStatus(queueInfo?.requestId);

  const sendMessage = async (message) => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: [...messages, message] })
      });

      if (response.status === 429) {
        // Rate limited
        const errorData = await response.json();
        setRateLimitError(errorData.rateLimit);
        return;
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6));
            
            if (data.type === 'rate_limit') {
              if (data.queued) {
                setIsQueued(true);
                setQueueInfo(data);
              }
            } else if (data.type === 'queued') {
              // Update queue status
              setQueueInfo(prev => ({
                ...prev,
                queuePosition: data.queuePosition,
                estimatedWaitMs: data.estimatedWaitMs
              }));
            } else if (data.type === 'processing') {
              setIsQueued(false);
              // Continue with normal response handling
            } else if (data.type === 'content') {
              // Handle content streaming
              appendToMessage(data.content);
            }
          }
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
    }
  };

  return (
    <div>
      {isQueued && (
        <QueueStatus
          requestId={queueInfo?.requestId}
          queuePosition={queueInfo?.queuePosition}
          estimatedWaitMs={queueInfo?.estimatedWaitMs}
          onCancel={cancelRequest}
        />
      )}
      
      {/* Your existing chat UI */}
    </div>
  );
}
`;

/**
 * STEP 5: Environment Configuration
 */
export const EnvironmentSetupExample = `
# Add to your .env.local file:

# Enable rate limiting
ENABLE_RATE_LIMITING=true

# Redis configuration (if not already configured)
REDIS_URL=redis://localhost:6379

# Rate limiting settings
RATE_LIMIT_CLEANUP_INTERVAL=300000  # 5 minutes
RATE_LIMIT_QUEUE_TIMEOUT=300000     # 5 minutes
RATE_LIMIT_MAX_QUEUE_SIZE=1000      # Maximum global queue size

# Provider-specific overrides (optional)
OPENAI_RATE_LIMIT_RPM=100
ANTHROPIC_RATE_LIMIT_RPM=50
GOOGLE_RATE_LIMIT_RPM=200
`;

/**
 * STEP 6: Database Migration
 */
export const MigrationExample = `
// Run these commands to set up the database:

1. Apply the rate limit table schema:
   npx prisma db push

2. Seed the rate limit configurations:
   npx ts-node src/lib/rate-limit/seed-rate-limits.ts

3. Verify the setup:
   npx prisma studio
   // Check the RateLimitConfig table for seeded data
`;

/**
 * STEP 7: Monitoring and Analytics
 */
export const MonitoringExample = `
// Add these endpoints to monitor your rate limiting:

// GET /api/admin/rate-limits/stats
// - Global queue statistics
// - Provider health status
// - Rate limit violation trends

// GET /api/admin/rate-limits/config
// - Current rate limit configurations
// - Ability to update limits in real-time

// GET /api/admin/rate-limits/violations
// - Recent rate limit violations
// - User behavior analytics
// - Provider performance metrics
`;

console.log('📚 Rate Limiting Integration Guide:');
console.log('1. Database: Run migrations and seed rate limits');
console.log('2. Backend: Integrate rate limiting checks in your chat route');  
console.log('3. Frontend: Add queue status components');
console.log('4. Monitoring: Set up admin dashboard for rate limit management');
console.log('5. Testing: Verify rate limiting works across all user tiers');
console.log('\\n🚀 Your comprehensive rate limiting system is ready!');