import { Server } from 'http';
import { prisma } from './prisma';
import { redis } from './redis';

let isShuttingDown = false;
let server: Server;

export function setupGracefulShutdown(httpServer: Server) {
  server = httpServer;
  
  // Handle PM2 stop signal
  process.on('SIGINT', gracefulShutdown);
  
  // Handle PM2 custom signal for connection draining
  process.on('message', (msg) => {
    if (msg === 'stop-accepting-connections') {
      console.log('Received stop-accepting-connections signal');
      isShuttingDown = true;
      
      // Stop accepting new connections
      server.close(() => {
        console.log('Server stopped accepting new connections');
      });
    }
  });
}

async function gracefulShutdown() {
  if (isShuttingDown) return;
  isShuttingDown = true;
  
  console.log('SIGINT received, shutting down gracefully...');
  
  // Give ongoing requests 30s to complete
  const shutdownTimeout = setTimeout(() => {
    console.error('Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
  
  try {
    // Close server
    await new Promise((resolve) => {
      server.close(resolve);
    });
    
    // Disconnect databases
    await Promise.all([
      prisma.$disconnect(),
      redis.quit()
    ]);
    
    clearTimeout(shutdownTimeout);
    console.log('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
}

// Middleware to reject new requests during shutdown
export function rejectDuringShutdown(req: any, res: any, next: any) {
  if (isShuttingDown) {
    res.status(503).json({ error: 'Server is shutting down' });
    return;
  }
  next();
}