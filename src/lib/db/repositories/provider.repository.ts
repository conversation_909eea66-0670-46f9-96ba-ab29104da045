/**
 * Provider Repository - Database-first provider management
 * 
 * This repository provides access to provider configurations stored in the database.
 * All provider data comes from the Providers table, not from hardcoded TypeScript files.
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { redis } from '@/lib/redis';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

// Cache configuration
const CACHE_TTL = 5 * 60; // 5 minutes
const CACHE_PREFIX = 'provider:';

export interface Provider {
  id: string;
  name: string;
  package: string;
  configKeys: string[];
  isAiSdk: boolean;
  capabilities: {
    supportsStreaming?: boolean;
    supportsFunctionCalling?: boolean;
    supportsVision?: boolean;
    supportsBatch?: boolean;
    supportsEmbeddings?: boolean;
    [key: string]: any;
  };
  baseUrl?: string;
  authHeader?: string;
  authPrefix?: string;
  rateLimit?: number;
  createdAt: Date;
  updatedAt: Date;
}

export class ProviderRepository {
  /**
   * List all active providers
   */
  static async listAll(): Promise<Provider[]> {
    const cacheKey = `${CACHE_PREFIX}all`;
    
    try {
      // Check cache first
      if (redis) {
        const cached = await redis.get(cacheKey);
        if (cached) {
          apiLogger.debug('Provider cache hit for listAll');
          return JSON.parse(cached);
        }
      }
      
      // Query database
      const providers = await prisma.$queryRaw<Provider[]>`
        SELECT 
          id,
          name,
          package,
          config_keys as configKeys,
          is_ai_sdk as isAiSdk,
          capabilities,
          base_url as baseUrl,
          auth_header as authHeader,
          auth_prefix as authPrefix,
          rate_limit as rateLimit,
          created_at as createdAt,
          updated_at as updatedAt
        FROM Providers
        WHERE is_ai_sdk = true
        ORDER BY name ASC
      `;
      
      // Parse JSON fields
      const parsed = providers.map(p => ({
        ...p,
        configKeys: typeof p.configKeys === 'string' ? JSON.parse(p.configKeys) : p.configKeys,
        capabilities: typeof p.capabilities === 'string' ? JSON.parse(p.capabilities) : p.capabilities
      }));
      
      // Cache result
      if (redis) {
        await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(parsed));
      }
      
      apiLogger.info(`Loaded ${parsed.length} providers from database`);
      return parsed;
      
    } catch (error) {
      apiLogger.error('Failed to list providers', error);
      throw error;
    }
  }
  
  /**
   * Get a specific provider by ID
   */
  static async getById(id: string): Promise<Provider | null> {
    const cacheKey = `${CACHE_PREFIX}${id}`;
    
    try {
      // Check cache first
      if (redis) {
        const cached = await redis.get(cacheKey);
        if (cached) {
          apiLogger.debug(`Provider cache hit for ${id}`);
          return JSON.parse(cached);
        }
      }
      
      // Query database
      const providers = await prisma.$queryRaw<Provider[]>`
        SELECT 
          id,
          name,
          package,
          config_keys as configKeys,
          is_ai_sdk as isAiSdk,
          capabilities,
          base_url as baseUrl,
          auth_header as authHeader,
          auth_prefix as authPrefix,
          rate_limit as rateLimit,
          created_at as createdAt,
          updated_at as updatedAt
        FROM Providers
        WHERE id = ${id}
        AND is_ai_sdk = true
        LIMIT 1
      `;
      
      if (providers.length === 0) {
        return null;
      }
      
      const provider = providers[0];
      
      // Parse JSON fields
      const parsed = {
        ...provider,
        configKeys: typeof provider.configKeys === 'string' ? JSON.parse(provider.configKeys) : provider.configKeys,
        capabilities: typeof provider.capabilities === 'string' ? JSON.parse(provider.capabilities) : provider.capabilities
      };
      
      // Cache result
      if (redis) {
        await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(parsed));
      }
      
      return parsed;
      
    } catch (error) {
      apiLogger.error(`Failed to get provider ${id}`, error);
      throw error;
    }
  }
  
  /**
   * Get providers with specific capability
   */
  static async getByCapability(capability: string, value: boolean = true): Promise<Provider[]> {
    const cacheKey = `${CACHE_PREFIX}capability:${capability}:${value}`;
    
    try {
      // Check cache first
      if (redis) {
        const cached = await redis.get(cacheKey);
        if (cached) {
          apiLogger.debug(`Provider cache hit for capability ${capability}`);
          return JSON.parse(cached);
        }
      }
      
      // Query database using JSON extraction
      const providers = await prisma.$queryRaw<Provider[]>`
        SELECT 
          id,
          name,
          package,
          config_keys as configKeys,
          is_ai_sdk as isAiSdk,
          capabilities,
          base_url as baseUrl,
          auth_header as authHeader,
          auth_prefix as authPrefix,
          rate_limit as rateLimit,
          created_at as createdAt,
          updated_at as updatedAt
        FROM Providers
        WHERE is_ai_sdk = true
        AND JSON_EXTRACT(capabilities, CONCAT('$.', ${capability})) = ${value}
        ORDER BY name ASC
      `;
      
      // Parse JSON fields
      const parsed = providers.map(p => ({
        ...p,
        configKeys: typeof p.configKeys === 'string' ? JSON.parse(p.configKeys) : p.configKeys,
        capabilities: typeof p.capabilities === 'string' ? JSON.parse(p.capabilities) : p.capabilities
      }));
      
      // Cache result
      if (redis) {
        await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(parsed));
      }
      
      return parsed;
      
    } catch (error) {
      apiLogger.error(`Failed to get providers by capability ${capability}`, error);
      throw error;
    }
  }
  
  /**
   * Clear provider cache
   */
  static async clearCache(): Promise<void> {
    if (!redis) return;
    
    try {
      const keys = await redis.keys(`${CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await redis.del(...keys);
        apiLogger.info(`Cleared ${keys.length} provider cache entries`);
      }
    } catch (error) {
      apiLogger.error('Failed to clear provider cache', error);
    }
  }
  
  /**
   * Check if provider supports a specific feature
   */
  static async supportsFeature(providerId: string, feature: string): Promise<boolean> {
    const provider = await this.getById(providerId);
    if (!provider) return false;
    
    return provider.capabilities[feature] === true;
  }
  
  /**
   * Get provider configuration for AI SDK initialization
   */
  static async getProviderConfig(providerId: string): Promise<{
    package: string;
    configKeys: string[];
    baseUrl?: string;
    authHeader?: string;
    authPrefix?: string;
  } | null> {
    const provider = await this.getById(providerId);
    if (!provider) return null;
    
    return {
      package: provider.package,
      configKeys: provider.configKeys,
      baseUrl: provider.baseUrl,
      authHeader: provider.authHeader,
      authPrefix: provider.authPrefix
    };
  }
}