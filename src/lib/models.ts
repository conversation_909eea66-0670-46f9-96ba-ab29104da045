import { prisma } from '@/lib/prisma'
import type { Model, UserPlan } from '@/types'
import { ModelCapability } from '@/types'

// Helper function to convert database model to app model (same as in database-service.ts)
function convertDbModelToAppModel(dbModel: any): Model {
  const metadata = dbModel.extendedMetadata;
  
  // Handle capabilities field - ensure it's always an array
  let capabilities: string[] = [ModelCapability.GENERAL_CHAT];
  
  if (metadata?.capabilities) {
    if (Array.isArray(metadata.capabilities)) {
      // Handle array format: ["general_chat", "vision", "function_calling"]
      capabilities = metadata.capabilities.filter((cap: any) => typeof cap === 'string' && cap.length > 0);
    } else if (typeof metadata.capabilities === 'object' && metadata.capabilities !== null) {
      // Handle object format: {"vision": {"hasCapability": true, "confidence": 0.95}}
      capabilities = [];
      const capabilityMap: Record<string, string> = {
        'vision': ModelCapability.VISION,
        'toolUse': ModelCapability.TOOL_USE,
        'function_calling': ModelCapability.FUNCTION_CALLING,
        'functionCalling': ModelCapability.FUNCTION_CALLING,
        'web_search': ModelCapability.WEB_SEARCH,
        'webSearch': ModelCapability.WEB_SEARCH,
        'codeExecution': ModelCapability.CODE_GENERATION,
        'codeGeneration': ModelCapability.CODE_GENERATION,
        'code': ModelCapability.CODE,
        'multimodal': ModelCapability.VISION,
        'reasoning': ModelCapability.REASONING,
        'analysis': ModelCapability.ANALYSIS,
        'creative_writing': ModelCapability.CREATIVE_WRITING,
        'translation': ModelCapability.TRANSLATION,
        'summarization': ModelCapability.SUMMARIZATION,
        'math': ModelCapability.MATH,
        'long_context': ModelCapability.LONG_CONTEXT,
        'fast_response': ModelCapability.FAST_RESPONSE,
        'thinking_mode': ModelCapability.THINKING_MODE,
        'audio_processing': ModelCapability.AUDIO_PROCESSING,
        'image_generation': ModelCapability.IMAGE_GENERATION
      };
      
      for (const [key, value] of Object.entries(metadata.capabilities)) {
        // Handle both boolean and object formats
        const hasCapability = value === true || (typeof value === 'object' && value !== null && 'hasCapability' in value && (value as any).hasCapability === true);
        if (hasCapability && capabilityMap[key]) {
          capabilities.push(capabilityMap[key]);
        }
      }
      
      if (capabilities.length === 0) {
        capabilities = [ModelCapability.GENERAL_CHAT];
      }
    } else if (typeof metadata.capabilities === 'string') {
      capabilities = [metadata.capabilities];
    }
  }
  
  if (capabilities.length === 0) {
    capabilities = [ModelCapability.GENERAL_CHAT];
  } else {
    capabilities = [...new Set(capabilities)];
  }
  
  // Get pricing per million tokens (already stored as per-million in database)
  const pricing = metadata?.pricing || { input: 0, output: 0, blended: 0 };
  const inputPricePerMillion = pricing.input || 0;
  const outputPricePerMillion = pricing.output || 0;
  
  return {
    id: dbModel.canonicalName,
    name: dbModel.displayName,
    canonicalName: dbModel.canonicalName,
    provider: dbModel.provider?.name || 'Unknown',
    description: dbModel.description || '',
    capabilities: capabilities,
    contextWindow: metadata?.contextWindow || 4096,
    contextLength: metadata?.contextWindow || 4096,
    maxOutput: metadata?.maxOutput || 2048,
    inputCost: pricing.input || 0.01,
    outputCost: pricing.output || 0.03,
    inputPricePerMillion: inputPricePerMillion,
    outputPricePerMillion: outputPricePerMillion,
    avgLatency: metadata?.avgLatency || 1000,
    processingSpeed: metadata?.avgLatency || 1000,
    knowledgeCutoff: metadata?.knowledgeCutoff || '2024-01',
    slug: generateSlug(dbModel.canonicalName),
    available: dbModel.isEnabled,
    speed: metadata?.speed || 'medium',
    tier: metadata?.tier || 'starter',
    tags: metadata?.tags || [],
    recommendedFor: metadata?.recommendedFor || [],
    deprecated: metadata?.deprecated || false,
    family: dbModel.family
  } as Model;
}

export async function getAllModels(): Promise<Model[]> {
  const models = await prisma.models.findMany({
    where: {
      isEnabled: true
    },
    orderBy: [
      { displayName: 'asc' }
    ],
    include: {
      provider: true,
      planRules: true
    }
  })
  
  return models.map(dbModel => convertDbModelToAppModel(dbModel))
}

export async function getModelBySlug(slug: string): Promise<Model | null> {
  // Convert slug back to potential canonical names
  const possibleNames = [
    slug.replace(/-/g, '.'),
    slug.replace(/-/g, '/'),
    slug.replace(/-/g, '_'),
    slug // Try as-is
  ]
  
  const model = await prisma.models.findFirst({
    where: {
      OR: possibleNames.map(name => ({
        canonicalName: {
          contains: name
        }
      })),
      isEnabled: true
    },
    include: {
      provider: true,
      planRules: true
    }
  })
  
  if (!model) return null
  
  return convertDbModelToAppModel(model)
}

export function generateSlug(canonicalName: string): string {
  return canonicalName
    .toLowerCase()
    .replace(/[\/\.\_\s]+/g, '-')
    .replace(/[^a-z0-9-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
}