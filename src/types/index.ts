/**
 * Core Type Definitions
 * 
 * @description
 * Central type definitions for the entire application. This module defines
 * all shared interfaces, enums, and types used across the codebase.
 * 
 * @module types
 */

import type { ModelWithRelations } from '@/db/repositories/model.repository';

/**
 * User account information
 * 
 * @interface User
 * @property {string} id - Unique user identifier
 * @property {string} email - User's email address
 * @property {string | null} [name] - User's display name
 * @property {string | null} [image] - Profile image URL
 * @property {UserPlan} plan - Current subscription plan
 * @property {number} credits - Available credits (future feature)
 * @property {Date} createdAt - Account creation timestamp
 * @property {Date} updatedAt - Last update timestamp
 * @property {string} [organizationId] - Organization membership (future)
 * @property {UserPreferences} [preferences] - User preferences
 * @property {FeatureFlags} [features] - Enabled features
 */
export interface User {
  id: string;
  email: string;
  name?: string | null;
  image?: string | null;
  plan: UserPlan;
  credits: number;
  isAdmin?: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Future fields
  organizationId?: string;
  preferences?: UserPreferences;
  features?: FeatureFlags;
}

/**
 * Subscription plan tiers
 * 
 * @enum {string} UserPlan
 * @property {string} FREE - Logged out users (5 messages/day)
 * @property {string} FREEMIUM - Logged in free users
 * @property {string} PLUS - £7.99/month tier
 * @property {string} ADVANCED - £19.99/month (most models)
 * @property {string} MAX - £49.99/month (all models)
 */
export enum UserPlan {
  FREE = 'FREE',        // Logged out users - 5 messages/day
  FREEMIUM = 'FREEMIUM', // Logged in free users
  PLUS = 'PLUS',        // £7.99/month
  ADVANCED = 'ADVANCED', // £19.99/month - most models except costly ones
  MAX = 'MAX',          // £49.99/month - all models
  ENTERPRISE = 'ENTERPRISE' // Enterprise/admin access
}

export interface UserPreferences {
  theme: 'dark' | 'light' | 'system';
  routerPreference: 'auto' | 'quality' | 'speed' | 'cost';
  defaultModel?: string;
  language?: string;
  timezone?: string;
  notifications?: NotificationPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  marketing: boolean;
  updates: boolean;
}

// Project & Organization Types (Future-proof)
export interface Project {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  userId: string;
  organizationId?: string;
  settings?: ProjectSettings;
  createdAt: Date;
  updatedAt: Date;
  archivedAt?: Date | null;
}

export interface ProjectSettings {
  defaultModel?: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
  tools?: string[];
}

// Organization Types (Future)
export interface Organization {
  id: string;
  name: string;
  slug: string;
  ownerId: string;
  plan: UserPlan;
  credits: number;
  members?: OrganizationMember[];
  settings?: OrganizationSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationMember {
  userId: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  joinedAt: Date;
}

export interface OrganizationSettings {
  ssoEnabled?: boolean;
  ipWhitelist?: string[];
  dataRetention?: number;
  auditLog?: boolean;
}

/**
 * Conversation/chat thread
 * 
 * @interface Conversation
 * @property {string} id - Unique conversation identifier
 * @property {string} title - Display title (auto-generated or user-defined)
 * @property {string} [projectId] - Associated project ID
 * @property {string} [userId] - Owner user ID
 * @property {string} [model] - Last used AI model
 * @property {ConversationSettings} [settings] - Conversation-specific settings
 * @property {Record<string, any>} [metadata] - Additional metadata
 * @property {Message[]} [messages] - Array of messages in conversation
 * @property {string} [lastMessage] - Last message content
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Last update timestamp
 * @property {Date} [lastMessageAt] - Last message timestamp
 * @property {Date} [archivedAt] - Archive timestamp
 * @property {string[]} [sharedWith] - Future: collaboration user IDs
 * @property {string} [publicId] - Future: public sharing identifier
 * @property {string} [forkOf] - Future: conversation fork source
 */
export interface Conversation {
  id: string;
  title: string;
  projectId?: string;
  userId?: string;
  model?: string;
  settings?: ConversationSettings;
  metadata?: Record<string, any>;
  messages?: Message[];
  lastMessage?: string | null;
  createdAt: Date;
  updatedAt: Date;
  lastMessageAt?: Date;
  archivedAt?: Date | null;
  // Future: collaboration
  sharedWith?: string[];
  publicId?: string;
  forkOf?: string;
}

export interface ConversationSettings {
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  tools?: string[];
  model?: string;
}

// Message Types (Ultra-flexible)
export interface Message {
  id: string;
  conversationId: string;
  role: MessageRole;
  content: string;
  model?: string;
  // Reasoning/Thinking content (for models like Grok-3-mini)
  reasoningContent?: string;
  // Performance tracking
  tokens?: TokenUsage;
  latency?: LatencyMetrics;
  cost?: number;
  // Rich content support
  attachments?: Attachment[];
  tools?: ToolUse[];
  metadata?: MessageMetadata;
  // UI state
  streaming?: boolean;
  error?: MessageError;
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  editedAt?: Date;
  // Future: threading
  parentId?: string;
  children?: string[];
  // Future: reactions
  reactions?: Reaction[];
}

export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system',
  FUNCTION = 'function',
  TOOL = 'tool'
}

export interface TokenUsage {
  prompt: number;
  completion: number;
  total: number;
  reasoning?: number; // Optional for O-series models
}

export interface LatencyMetrics {
  ttft: number; // Time to first token
  total: number; // Total response time
  streaming: number; // Streaming duration
}

export interface MessageMetadata {
  routerDecision?: RouterDecision;
  searchResults?: SearchResult[];
  toolResults?: ToolResult[];
  reasoningSummaryType?: 'auto' | 'detailed' | 'concise';
  [key: string]: any;
}

export interface SearchResult {
  title: string;
  url: string;
  snippet?: string;
  content?: string; // Extended content from the page (for enriched search results)
}

export interface MessageError {
  code: string;
  message: string;
  details?: Record<string, any>;
  retryable?: boolean;
}

export interface Reaction {
  emoji: string;
  userId: string;
  createdAt: Date;
}

// Tool Types
export interface ToolUse {
  id: string;
  name: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error?: string;
}

export interface ToolResult {
  toolId: string;
  data: any;
  error?: string;
}

// Attachment Types
export interface Attachment {
  id: string;
  type: AttachmentType;
  name: string;
  size: number;
  url: string;
  mimeType: string;
  metadata?: Record<string, any>;
}

export enum AttachmentType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  CODE = 'code',
  DATA = 'data',
  AUDIO = 'audio',
  VIDEO = 'video'
}

// Router Types (Our Innovation)
export interface RouterContext {
  messages: Message[];
  user?: {
    plan: string;
    preferences?: Record<string, any>;
  };
  requirements?: {
    maxLatency?: number;
    maxCost?: number;
    minQuality?: number;
    capabilities?: string[];
  };
  compactionConfig?: {
    enabled?: boolean;
    aggressiveMode?: boolean;
    maxTokens?: number;
    preserveLastNMessages?: number;
  };
}

// ===== INTELLIGENT ROUTER TYPES =====
export type PromptComplexity = 'simple' | 'standard' | 'difficult' | 'complex';

export type PromptCategory = 
  | 'coding'
  | 'creative_writing'
  | 'general_chat'
  | 'reasoning'
  | 'math'
  | 'analysis'
  | 'translation'
  | 'summarization'
  | 'question_answering'
  | 'factual_qa'
  | 'data_analysis'
  | 'debugging'
  | 'tutorial'
  | 'brainstorming'
  | 'role_play'
  | 'technical_writing'
  | 'academic_writing'
  | 'business_writing'
  | 'legal'
  | 'medical'
  | 'scientific'
  | 'philosophical'
  | 'historical'
  | 'current_events'
  | 'personal_advice'
  | 'image_generation'
  | 'image_analysis'
  | 'multimodal'
  | 'other';

export interface PromptAnalysis {
  primary_category: PromptCategory;
  secondary_categories?: PromptCategory[];
  complexity: PromptComplexity;
  specific_attributes: {
    language?: string; // For coding: python, javascript, etc.
    framework?: string; // For coding: react, tensorflow, etc.
    task_type?: string; // debugging, implementation, review, etc.
    domain?: string; // For specialized tasks
    tone?: string; // formal, casual, academic, etc.
    urgency?: 'low' | 'medium' | 'high';
    output_format?: string; // code, essay, list, json, etc.
  };
  requirements: {
    needs_web_search: boolean;
    search_queries?: string[]; // Suggested search queries
    needs_vision: boolean;
    needs_large_context: boolean;
    needs_reasoning: boolean;
    needs_citations: boolean;
    expected_output_tokens: number;
    latency_sensitivity: 'low' | 'medium' | 'high';
    needs_structured_output?: boolean;
    needs_function_calling?: boolean;
    needs_image_generation?: boolean;
    max_cost_tolerance?: 'low' | 'medium' | 'high';
  };
  confidence: number; // 0-1, how confident the analysis is
  improvedPrompt?: string; // For prompt improvement suggestions
  category?: PromptCategory; // Alternative category field name
  explanation?: string; // For prompt improvement explanations
  reasoning?: string; // For router decision reasoning
  // Make it compatible with JSON serialization
  [key: string]: any;
}

export interface ModelMapping {
  id: string;
  category: PromptCategory;
  model_id: string;
  complexity_level: PromptComplexity | 'all';
  score: number; // 0-100, manually set performance score
  usage_count: number;
  success_count: number;
  failure_count: number;
  avg_user_rating?: number;
  specific_attributes?: Record<string, string>; // e.g., { language: 'python' }
  supports_web_search?: boolean; // Whether this model supports web search
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CategoryDefinition {
  id: string;
  name: PromptCategory;
  display_name: string;
  description: string;
  example_prompts: string[];
  typical_requirements: Partial<PromptAnalysis['requirements']>;
  preferred_models?: string[]; // Model IDs that excel at this category
}

export interface UserFeedback {
  id: string;
  user_id?: string; // Optional for privacy
  session_id: string;
  model_id: string;
  category: PromptCategory;
  complexity: PromptComplexity;
  action_type: 'completion' | 'regeneration' | 'switch' | 'edit' | 'copy' | 'rating';
  action_details?: {
    switched_to?: string; // For model switches
    edit_ratio?: number; // For edits: edited_length / original_length
    time_to_action?: number; // Milliseconds
    explicit_rating?: number; // 1-5
  };
  implicit_satisfaction_score?: number; // Calculated from signals
  timestamp: Date;
}

export interface RouterSelection {
  selected_model: string;
  reason: string;
  category: PromptCategory;
  complexity: PromptComplexity;
  confidence: number;
  alternatives: Array<{
    model_id: string;
    score: number;
    reason: string;
  }>;
  prompt_analysis: PromptAnalysis;
}

export interface RouterDecision {
  modelId: string;
  model: any; // Database model from our Models table
  provider: string; // Provider name (e.g., 'openai', 'anthropic')
  aiProvider?: any; // AI SDK provider instance for execution
  reason: string;
  reasoning: string | string[];
  confidence: number;
  selectedModel?: string; // For compatibility with existing code
  category?: string; // The category that was selected
  performanceMetrics?: {
    routingTime?: number;
    modelSelectionTime?: number;
    cacheHitRate?: number;
  };
  metrics?: {
    estimatedCredits?: number;
    estimatedLatency?: number;
    latency?: number;
  };
  alternatives?: ModelOption[];
  metadata?: Record<string, any>;
  accessRestricted?: boolean;
  actualModel?: string;
}

export interface ModelOption {
  id: string;
  name: string;
  provider: string;
  score: number;
  cost?: number;
  latency?: number;
}

export enum RouterReason {
  COST_OPTIMIZED = 'cost_optimized',
  SPEED_OPTIMIZED = 'speed_optimized',
  QUALITY_OPTIMIZED = 'quality_optimized',
  CAPABILITY_MATCH = 'capability_match',
  USER_PREFERENCE = 'user_preference',
  FALLBACK = 'fallback'
}

// Model Types
export interface Model {
  id: string;
  name: string;
  provider: string;
  slug: string;
  canonicalName: string;
  description?: string;
  family?: string;
  capabilities: ModelCapability[];
  contextWindow: number;
  contextLength?: number;
  maxOutput: number;
  inputCost: number;  // Credits per 1k input tokens
  outputCost: number; // Credits per 1k output tokens
  avgLatency?: number;
  avgTTFT?: number;
  tags: string[];
  recommendedFor: string[];
  available: boolean;
  deprecated?: boolean;
  speed: 'fast' | 'medium' | 'slow';
  processingSpeed?: number;
  knowledgeCutoff?: string;
  tier?: 'free' | 'starter' | 'pro' | 'enterprise';
  // Plan availability info
  planAvailability?: Record<UserPlan, boolean>;
  minimumPlan?: UserPlan;
  isAvailableForUser?: boolean;
  // Enhanced scoring data
  costScore?: number;          // 0-100 (higher = cheaper, 100 = free)
  intelligenceScore?: number;  // 0-100 (higher = smarter)
  pricing?: {
    input: number;           // $ per 1M input tokens
    output: number;          // $ per 1M output tokens
    blended: number;         // (input * 3 + output) / 4
  };
  inputPricePerMillion: number;
  outputPricePerMillion: number;
  // Router optimization
  routerIndex?: number;      // 4-digit unique index for compact router prompts
  // Enriched metadata from database
  metadata?: {
    performance?: {
      coding?: number;
      reasoning?: number;
      creative?: number;
      math?: number;
      general_chat?: number;
    };
    capabilities?: {
      vision?: boolean;
      webSearch?: boolean;
      functionCalling?: boolean;
      reasoningTrace?: boolean;
    };
    contextWindow?: number;
    speed?: string;
    enrichment?: {
      source?: string;
      quality_score?: number;
      timestamp?: string;
    };
    supportsVision?: boolean;
    supportsStreaming?: boolean;
    costPer1MInput?: number;
    costPer1MOutput?: number;
    costPer1KInput?: number;
    costPer1KOutput?: number;
    blendedCost?: number;
    [key: string]: any; // Allow other fields from database
  };
}

export enum ModelCapability {
  GENERAL_CHAT = 'general_chat',
  CODE_GENERATION = 'code_generation',
  CODE_ANALYSIS = 'code_analysis',
  CODE = 'code',
  CREATIVE_WRITING = 'creative_writing',
  ANALYSIS = 'analysis',
  TRANSLATION = 'translation',
  SUMMARIZATION = 'summarization',
  MATH = 'math',
  VISION = 'vision',
  FUNCTION_CALLING = 'function-calling',
  WEB_SEARCH = 'web-search',
  LONG_CONTEXT = 'long_context',
  FAST_RESPONSE = 'fast_response',
  THINKING_MODE = 'thinking_mode',     // Models with explicit reasoning/thinking capabilities
  TOOL_USE = 'tool_use',               // Advanced tool/function calling for agents
  EDGY_TONE = 'edgy_tone',             // Models with personality (e.g., Grok)
  AUDIO_PROCESSING = 'audio_processing', // Audio understanding capabilities
  REASONING = 'reasoning',              // Deep reasoning capabilities (o-series, DeepSeek)
  IMAGE_GENERATION = 'image_generation' // Image generation capabilities (DALL-E, gpt-image-1)
}

// Search Types

// Logging Types (Critical from Day 1)
export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  // Performance
  duration?: number;
  memoryUsage?: number;
  // Error tracking
  error?: ErrorDetails;
  stack?: string;
}

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical'
}

export enum LogCategory {
  AUTH = 'auth',
  API = 'api',
  ROUTER = 'router',
  DATABASE = 'database',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  BILLING = 'billing',
  USER_ACTION = 'user_action'
}

export interface ErrorDetails {
  code: string;
  message: string;
  context?: Record<string, any>;
  originalError?: any;
}

// Feature Flags (For A/B Testing)
export interface FeatureFlags {
  betaFeatures: boolean;
  advancedRouter: boolean;
  voiceInput: boolean;
  collaboration: boolean;
  fileUploads: boolean;
  webSearch: boolean;
  codeExecution: boolean;
  [key: string]: boolean;
}

// API Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: APIError;
  metadata?: ResponseMetadata;
}

export interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface ResponseMetadata {
  requestId: string;
  duration: number;
  cached: boolean;
  region?: string;
}

// Billing Types (Future)
export interface Subscription {
  id: string;
  userId: string;
  organizationId?: string;
  plan: UserPlan;
  status: 'active' | 'cancelled' | 'past_due' | 'trialing';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAt?: Date;
  metadata?: Record<string, any>;
}

export interface Usage {
  id: string;
  userId: string;
  organizationId?: string;
  type: 'tokens' | 'messages' | 'searches' | 'storage';
  amount: number;
  cost: number;
  metadata?: Record<string, any>;
  createdAt: Date;
}

// Analytics Types
export interface AnalyticsEvent {
  id: string;
  event: string;
  userId?: string;
  sessionId: string;
  properties?: Record<string, any>;
  timestamp: Date;
}

// Types are now all in this file
// Export RouterInput from router.ts
export type { RouterInput } from '@/lib/ai/router';
