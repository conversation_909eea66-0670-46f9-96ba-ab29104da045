/**
 * React Hook for WebSocket Image Streaming
 * 
 * @description
 * Custom React hook that manages WebSocket connections for real-time image generation
 * and streaming. Provides connection lifecycle management, authentication, message queuing,
 * progress tracking, and binary image handling with ArrayBuffer support.
 * 
 * @module hooks/useImageWebSocket
 */

'use client';

import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import {
  WSMessage,
  WSMessageType,
  WSConnectionState,
  WSConfig,
  DEFAULT_WS_CONFIG,
  WSAuthMessage,
  WSImageGenerateRequest,
  WSImageGenerateProgress,
  WSImageGenerateComplete,
  WSImageGenerateError,
  WSImageGenerateChunk,
  BinaryMessageHeader,
  ImageReconstructor,
  createMessage,
  parseTextMessage,
  parseBinaryMessage,
  serializeMessage,
  isTextMessage,
  isBinaryMessage,
} from '@/lib/websocket-protocol';

export interface ImageGenerationRequest {
  prompt: string;
  model?: string;
  style?: string;
  size?: string;
  quality?: string;
  n?: number;
  conversationId?: string;
  messageId?: string;
}

export interface ImageGenerationResult {
  requestId: string;
  images: Array<{
    data: ArrayBuffer;
    format: string;
    width: number;
    height: number;
    url?: string;
    revised_prompt?: string;
  }>;
  usage?: {
    prompt_tokens: number;
    total_tokens: number;
    cost: number;
  };
  metadata?: {
    model: string;
    timeGenerated: number;
  };
}

export interface ImageGenerationProgress {
  requestId: string;
  stage: string;
  progress: number;
  message?: string;
  estimatedTimeRemaining?: number;
}

export interface ImageGenerationError {
  requestId: string;
  error: string;
  code: string;
  retryable: boolean;
  retryAfter?: number;
}

export interface WebSocketHookState {
  connectionState: WSConnectionState;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastError: Error | null;
  reconnectAttempts: number;
  queuePosition: number | null;
  estimatedWaitTime: number | null;
}

export interface WebSocketHookActions {
  connect: () => void;
  disconnect: () => void;
  generateImage: (request: ImageGenerationRequest) => Promise<string>; // Returns request ID
  isGenerating: (requestId: string) => boolean;
  getProgress: (requestId: string) => ImageGenerationProgress | null;
}

export interface WebSocketHookCallbacks {
  onProgress?: (progress: ImageGenerationProgress) => void;
  onComplete?: (result: ImageGenerationResult) => void;
  onError?: (error: ImageGenerationError) => void;
  onConnectionChange?: (state: WSConnectionState) => void;
  onQueueUpdate?: (position: number, estimatedWaitTime: number) => void;
}

export function useImageWebSocket(
  config: Partial<WSConfig> = {},
  callbacks: WebSocketHookCallbacks = {}
) {
  const { data: session } = useSession();
  const wsConfig = useMemo(() => ({ ...DEFAULT_WS_CONFIG, ...config }), [config]);
  
  // State
  const [state, setState] = useState<WebSocketHookState>({
    connectionState: WSConnectionState.DISCONNECTED,
    isConnected: false,
    isConnecting: false,
    error: null,
    lastError: null,
    reconnectAttempts: 0,
    queuePosition: null,
    estimatedWaitTime: null,
  });
  
  // Refs for stable access in callbacks
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const messageQueueRef = useRef<WSMessage[]>([]);
  const pendingRequestsRef = useRef<Map<string, ImageGenerationRequest>>(new Map());
  const progressDataRef = useRef<Map<string, ImageGenerationProgress>>(new Map());
  const imageReconstructorRef = useRef<ImageReconstructor>(new ImageReconstructor());
  const isAuthenticatedRef = useRef<boolean>(false);
  
  // Update state helper
  const updateState = useCallback((updates: Partial<WebSocketHookState>) => {
    setState(prev => {
      // Only update if something actually changed
      const hasChanges = Object.keys(updates).some(key => 
        (updates as any)[key] !== (prev as any)[key]
      );
      
      if (!hasChanges) {
        return prev; // No changes, return previous state
      }
      
      const newState = { ...prev, ...updates };
      
      // Call connection change callback only if state actually changed
      if (updates.connectionState && 
          updates.connectionState !== prev.connectionState && 
          callbacks.onConnectionChange) {
        callbacks.onConnectionChange(updates.connectionState);
      }
      
      return newState;
    });
  }, [callbacks]);
  
  // Clear timeouts helper
  const clearTimeouts = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current);
      heartbeatTimeoutRef.current = null;
    }
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);
  
  // Send message helper
  const sendMessage = useCallback((message: WSMessage) => {
    const ws = wsRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      // Queue message for later
      messageQueueRef.current.push(message);
      return false;
    }
    
    try {
      const serialized = serializeMessage(message);
      ws.send(serialized);
      return true;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      return false;
    }
  }, []);
  
  // Process queued messages
  const processMessageQueue = useCallback(() => {
    const queue = messageQueueRef.current;
    if (queue.length === 0) return;
    
    const ws = wsRef.current;
    if (!ws || ws.readyState !== WebSocket.OPEN || !isAuthenticatedRef.current) {
      return;
    }
    
    const toSend = [...queue];
    messageQueueRef.current = [];
    
    for (const message of toSend) {
      sendMessage(message);
    }
  }, [sendMessage]);
  
  // Start heartbeat
  const startHeartbeat = useCallback(() => {
    clearTimeouts();
    
    heartbeatIntervalRef.current = setInterval(() => {
      const pingMessage = createMessage<WSMessage>(WSMessageType.PING, {});
      
      if (sendMessage(pingMessage)) {
        // Set timeout for pong response
        heartbeatTimeoutRef.current = setTimeout(() => {
          console.warn('WebSocket heartbeat timeout');
          wsRef.current?.close(1000, 'Heartbeat timeout');
        }, wsConfig.heartbeatTimeout);
      }
    }, wsConfig.heartbeatInterval);
  }, [sendMessage, wsConfig.heartbeatInterval, wsConfig.heartbeatTimeout, clearTimeouts]);
  
  // Handle authentication
  const authenticate = useCallback(() => {
    updateState({ connectionState: WSConnectionState.AUTHENTICATING });
    
    // Use session token if authenticated, otherwise use anonymous token
    const token = session?.user 
      ? ((session as any).accessToken || 'session-token')
      : 'anonymous';
    
    const authMessage = createMessage<WSAuthMessage>(WSMessageType.AUTH, {
      token,
      userAgent: navigator.userAgent,
    });
    
    sendMessage(authMessage);
  }, [session, updateState, sendMessage]);
  
  // Handle incoming messages
  const handleMessage = useCallback((event: MessageEvent) => {
    const data = event.data;
    
    if (isTextMessage(data)) {
      // Handle text message
      const message = parseTextMessage(data);
      if (!message) return;
      
      switch (message.type) {
        case WSMessageType.AUTH_SUCCESS:
          isAuthenticatedRef.current = true;
          updateState({
            connectionState: WSConnectionState.CONNECTED,
            isConnected: true,
            isConnecting: false,
            error: null,
            reconnectAttempts: 0,
          });
          startHeartbeat();
          processMessageQueue();
          break;
          
        case WSMessageType.AUTH_ERROR:
          updateState({
            connectionState: WSConnectionState.ERROR,
            error: message.error,
            isConnected: false,
            isConnecting: false,
          });
          break;
          
        case WSMessageType.PONG:
          // Clear heartbeat timeout
          if (heartbeatTimeoutRef.current) {
            clearTimeout(heartbeatTimeoutRef.current);
            heartbeatTimeoutRef.current = null;
          }
          break;
          
        case WSMessageType.IMAGE_GENERATE_PROGRESS:
          const progressMsg = message as WSImageGenerateProgress;
          const progress: ImageGenerationProgress = {
            requestId: progressMsg.requestId,
            stage: progressMsg.stage,
            progress: progressMsg.progress,
            message: progressMsg.message,
            estimatedTimeRemaining: progressMsg.estimatedTimeRemaining,
          };
          progressDataRef.current.set(progressMsg.requestId, progress);
          callbacks.onProgress?.(progress);
          break;
          
        case WSMessageType.IMAGE_GENERATE_COMPLETE:
          const completeMsg = message as WSImageGenerateComplete;
          
          // Get the reconstructed image data
          const reconstructor = imageReconstructorRef.current;
          const imageDataArray = completeMsg.images.map((img, idx) => {
            // Try to get reconstructed image data
            const reconstructedData = reconstructor.getImage(completeMsg.requestId);
            return {
              data: reconstructedData || new ArrayBuffer(0),
              format: img.format,
              width: img.width,
              height: img.height,
              url: img.url,
              revised_prompt: img.revised_prompt,
            };
          });
          
          const result: ImageGenerationResult = {
            requestId: completeMsg.requestId,
            images: imageDataArray,
            usage: completeMsg.usage,
            metadata: completeMsg.metadata,
          };
          
          // Clean up
          pendingRequestsRef.current.delete(completeMsg.requestId);
          progressDataRef.current.delete(completeMsg.requestId);
          reconstructor.cleanup(completeMsg.requestId);
          
          callbacks.onComplete?.(result);
          break;
          
        case WSMessageType.IMAGE_GENERATE_ERROR:
          const errorMsg = message as WSImageGenerateError;
          const error: ImageGenerationError = {
            requestId: errorMsg.requestId,
            error: errorMsg.error,
            code: errorMsg.code,
            retryable: errorMsg.retryable,
            retryAfter: errorMsg.retryAfter,
          };
          
          // Clean up
          pendingRequestsRef.current.delete(errorMsg.requestId);
          progressDataRef.current.delete(errorMsg.requestId);
          
          callbacks.onError?.(error);
          break;
          
        case WSMessageType.QUEUE_STATUS:
          updateState({
            queuePosition: (message as any).position,
            estimatedWaitTime: (message as any).estimatedWaitTime,
          });
          callbacks.onQueueUpdate?.((message as any).position, (message as any).estimatedWaitTime);
          break;
          
        case WSMessageType.ERROR:
          updateState({
            error: message.error,
            connectionState: (message as any).fatal ? WSConnectionState.ERROR : state.connectionState,
          });
          break;
      }
    } else if (isBinaryMessage(data)) {
      // Handle binary message (image chunk)
      const parsed = parseBinaryMessage(data);
      if (!parsed) return;
      
      const { header, data: chunkData } = parsed;
      
      if (header.type === WSMessageType.IMAGE_GENERATE_CHUNK) {
        const reconstructor = imageReconstructorRef.current;
        const completeImage = reconstructor.addChunk(
          header.requestId,
          header.chunkIndex,
          header.totalChunks,
          header.format,
          chunkData.byteLength * header.totalChunks, // Approximate total size
          chunkData
        );
        
        if (completeImage) {
          // Image fully reconstructed
          // This would typically be handled in the complete message
          console.log('Image reconstructed:', header.requestId, completeImage.byteLength);
        }
      }
    }
  }, [state.connectionState, updateState, startHeartbeat, processMessageQueue, callbacks]);
  
  // Handle connection events
  const handleOpen = useCallback(() => {
    console.log('WebSocket connected');
    updateState({
      connectionState: WSConnectionState.CONNECTING,
      isConnecting: true,
      error: null,
    });
    authenticate();
  }, [updateState, authenticate]);
  
  const handleClose = useCallback((event: CloseEvent) => {
    console.log('WebSocket closed:', event.code, event.reason);
    
    isAuthenticatedRef.current = false;
    clearTimeouts();
    
    updateState({
      connectionState: WSConnectionState.DISCONNECTED,
      isConnected: false,
      isConnecting: false,
    });
    
    // Auto-reconnect if not intentionally closed
    if (event.code !== 1000 && state.reconnectAttempts < wsConfig.maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, state.reconnectAttempts), 30000);
      console.log(`Reconnecting in ${delay}ms (attempt ${state.reconnectAttempts + 1})`);
      
      reconnectTimeoutRef.current = setTimeout(() => {
        updateState({ reconnectAttempts: state.reconnectAttempts + 1 });
        connect();
      }, delay);
    }
  }, [state.reconnectAttempts, wsConfig.maxReconnectAttempts, clearTimeouts, updateState]); // eslint-disable-line react-hooks/exhaustive-deps
  
  const handleError = useCallback((event: Event) => {
    console.error('WebSocket error:', event);
    updateState({
      lastError: new Error('WebSocket connection error'),
      error: 'Connection error',
    });
  }, [updateState]);
  
  // Connection management
  const connect = useCallback(async () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }
    
    try {
      updateState({
        connectionState: WSConnectionState.CONNECTING,
        isConnecting: true,
        error: null,
      });
      
      console.log('[WebSocket] Attempting to connect to:', wsConfig.url);
      
      // Use createWebSocketConnection which will automatically use SSE adapter if needed
      const { createWebSocketConnection } = await import('@/lib/websocket-sse-adapter');
      const ws = createWebSocketConnection(wsConfig.url, wsConfig.protocols) as WebSocket;
      ws.binaryType = wsConfig.binaryType;
      
      ws.onopen = handleOpen;
      ws.onclose = handleClose;
      ws.onerror = handleError;
      ws.onmessage = handleMessage;
      
      wsRef.current = ws;
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      updateState({
        connectionState: WSConnectionState.ERROR,
        error: 'Failed to connect',
        isConnecting: false,
        lastError: error as Error,
      });
    }
  }, [wsConfig, updateState, handleOpen, handleClose, handleError, handleMessage]);
  
  const disconnect = useCallback(() => {
    clearTimeouts();
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Intentional disconnect');
      wsRef.current = null;
    }
    
    isAuthenticatedRef.current = false;
    messageQueueRef.current = [];
    pendingRequestsRef.current.clear();
    progressDataRef.current.clear();
    
    updateState({
      connectionState: WSConnectionState.DISCONNECTED,
      isConnected: false,
      isConnecting: false,
      reconnectAttempts: 0,
    });
  }, [clearTimeouts, updateState]);
  
  // Image generation
  const generateImage = useCallback(async (request: ImageGenerationRequest): Promise<string> => {
    const requestMessage = createMessage<WSImageGenerateRequest>(WSMessageType.IMAGE_GENERATE_REQUEST, {
      prompt: request.prompt,
      model: request.model,
      style: request.style,
      size: request.size,
      quality: request.quality,
      n: request.n || 1,
      metadata: {
        messageId: request.messageId,
        conversationId: request.conversationId,
        userId: session?.user?.id,
      },
    }) as WSImageGenerateRequest;
    
    const requestId = requestMessage.id;
    pendingRequestsRef.current.set(requestId, request);
    
    if (!sendMessage(requestMessage)) {
      throw new Error('Failed to send image generation request');
    }
    
    return requestId;
  }, [session?.user?.id, sendMessage]);
  
  // Utility functions
  const isGenerating = useCallback((requestId: string): boolean => {
    return pendingRequestsRef.current.has(requestId);
  }, []);
  
  const getProgress = useCallback((requestId: string): ImageGenerationProgress | null => {
    return progressDataRef.current.get(requestId) || null;
  }, []);
  
  // Auto-connect when component mounts (for both authenticated and anonymous users)
  useEffect(() => {
    // TEMPORARILY DISABLED: WebSocket auto-connection
    // Preventing connection attempts until authentication is properly configured
    console.log('[WebSocket] Auto-connect disabled for development');
    return;
    
    /*
    if (state.connectionState === WSConnectionState.DISCONNECTED) {
      connect();
    }
    */
  }, [state.connectionState, connect]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);
  
  // Public API
  const actions: WebSocketHookActions = {
    connect,
    disconnect,
    generateImage,
    isGenerating,
    getProgress,
  };
  
  return {
    ...state,
    ...actions,
    imageReconstructorRef, // Expose for debugging
  };
}