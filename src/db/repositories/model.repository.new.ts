/**
 * Model Repository Layer - Updated for new Models table
 * 
 * Provides database access for AI models with caching support.
 * This version uses the new optimized Models table.
 */

import { PrismaClient, Models, Prisma } from '@prisma/client';
import { redis, REDIS_TTL, REDIS_PREFIXES } from '@/lib/redis';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

// Cache configuration (use centralized values)
const CACHE_TTL = REDIS_TTL.MODEL_CACHE; // 5 minutes
const CACHE_PREFIX = REDIS_PREFIXES.MODEL_CACHE;

export interface ModelWithRelations extends Models {
  // Since we don't have endpoints/versions/planAccess in new schema,
  // we'll handle these differently
  endpoints?: any[];
  versions?: any[];
  planAccess?: any[];
}

export class ModelRepository {
  /**
   * Get all models for a specific provider with caching
   */
  static async listByProvider(providerId: string): Promise<ModelWithRelations[]> {
    const cacheKey = `${CACHE_PREFIX}provider:${providerId}`;
    
    try {
      // Check Redis cache first
      const cached = await redis.get<ModelWithRelations[]>(cacheKey);
      if (cached) {
        apiLogger.debug(`Cache hit for provider models: ${providerId}`);
        return cached;
      }
    } catch (error) {
      apiLogger.warn('Redis cache error, falling back to database', error);
    }

    // Fetch from database - now using Models table
    const models = await prisma.models.findMany({
      where: {
        providerId,
        isEnabled: true
      },
      orderBy: [
        { speedRating: 'desc' },
        { qualityRating: 'desc' },
        { displayName: 'asc' }
      ]
    });

    // Map to expected format (add empty arrays for compatibility)
    const modelsWithRelations = models.map(model => ({
      ...model,
      endpoints: [],
      versions: [],
      planAccess: []
    }));

    // Cache the result
    try {
      await redis.set(cacheKey, modelsWithRelations, CACHE_TTL);
      apiLogger.debug(`Cached ${models.length} models for provider: ${providerId}`);
    } catch (error) {
      apiLogger.warn('Failed to cache models', error);
    }

    return modelsWithRelations;
  }

  /**
   * Get a specific model by ID with caching
   */
  static async getById(modelId: string): Promise<ModelWithRelations | null> {
    const cacheKey = `${CACHE_PREFIX}model:${modelId}`;
    
    try {
      const cached = await redis.get<ModelWithRelations>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      apiLogger.warn('Redis cache error for model', error);
    }

    const model = await prisma.models.findUnique({
      where: { id: modelId }
    });

    if (model) {
      const modelWithRelations = {
        ...model,
        endpoints: [],
        versions: [],
        planAccess: []
      };

      try {
        await redis.set(cacheKey, modelWithRelations, CACHE_TTL);
      } catch (error) {
        apiLogger.warn('Failed to cache model', error);
      }

      return modelWithRelations;
    }

    return null;
  }

  /**
   * Get a model by canonical name (e.g., "openai/gpt-4o")
   */
  static async getByCanonicalName(canonicalName: string): Promise<ModelWithRelations | null> {
    const cacheKey = `${CACHE_PREFIX}canonical:${canonicalName}`;
    
    try {
      const cached = await redis.get<ModelWithRelations>(cacheKey);
      if (cached) {
        return cached;
      }
    } catch (error) {
      apiLogger.warn('Redis cache error for canonical name', error);
    }

    const model = await prisma.models.findFirst({
      where: { 
        canonicalName,
        isEnabled: true 
      }
    });

    if (model) {
      const modelWithRelations = {
        ...model,
        endpoints: [],
        versions: [],
        planAccess: []
      };

      try {
        await redis.set(cacheKey, modelWithRelations, CACHE_TTL);
      } catch (error) {
        apiLogger.warn('Failed to cache model by canonical name', error);
      }

      return modelWithRelations;
    }

    return null;
  }

  /**
   * Get all enabled models with optional filtering
   */
  static async listAll(filters?: {
    userPlan?: string;
    capabilities?: string[];
    maxInputCost?: number;
    minContextTokens?: number;
  }): Promise<ModelWithRelations[]> {
    const where: Prisma.ModelsWhereInput = {
      isEnabled: true
    };

    // Apply filters based on new schema
    if (filters?.maxInputCost !== undefined) {
      where.inputCostPer1M = {
        lte: filters.maxInputCost
      };
    }

    if (filters?.minContextTokens !== undefined) {
      where.contextWindow = {
        gte: filters.minContextTokens
      };
    }

    // For capabilities, we now use the boolean fields
    if (filters?.capabilities && filters.capabilities.length > 0) {
      const capabilityConditions: Prisma.ModelsWhereInput[] = [];
      
      for (const cap of filters.capabilities) {
        switch (cap.toLowerCase()) {
          case 'vision':
            capabilityConditions.push({ supportsVision: true });
            break;
          case 'function_calling':
          case 'functions':
            capabilityConditions.push({ supportsFunctionCalling: true });
            break;
          case 'web_search':
            capabilityConditions.push({ supportsWebSearch: true });
            break;
          case 'reasoning':
            capabilityConditions.push({ supportsReasoning: true });
            break;
        }
      }

      if (capabilityConditions.length > 0) {
        where.AND = capabilityConditions;
      }
    }

    // For user plan filtering, we need to join with ModelPlanRules
    let models: Models[] = [];
    
    if (filters?.userPlan) {
      // Get plan tier
      const plan = await prisma.plans.findFirst({
        where: { code: filters.userPlan }
      });

      if (plan) {
        // Get models based on plan rules
        models = await prisma.$queryRaw`
          SELECT DISTINCT m.*
          FROM Models m
          JOIN ModelPlanRules r ON m.id = r.modelId
          WHERE m.isEnabled = true
          AND r.isEnabled = true
          AND (
            JSON_CONTAINS(r.planIds, JSON_QUOTE(${filters.userPlan}))
            OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
          )
          AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= NOW())
          AND (r.effectiveUntil IS NULL OR r.effectiveUntil > NOW())
        `;
      }
    } else {
      models = await prisma.models.findMany({
        where,
        orderBy: [
          { speedRating: 'desc' },
          { qualityRating: 'desc' },
          { displayName: 'asc' }
        ]
      });
    }

    // Map to expected format
    return models.map(model => ({
      ...model,
      endpoints: [],
      versions: [],
      planAccess: []
    }));
  }

  /**
   * Bulk upsert models (for seed scripts)
   */
  static async upsertMany(models: Prisma.ModelsCreateInput[]): Promise<number> {
    let upsertedCount = 0;

    for (const model of models) {
      try {
        await prisma.models.upsert({
          where: {
            unique_provider_canonical: {
              providerId: model.providerId as string,
              canonicalName: model.canonicalName
            }
          },
          update: {
            displayName: model.displayName,
            family: model.family,
            generation: model.generation,
            contextWindow: model.contextWindow,
            maxOutput: model.maxOutput,
            inputCostPer1M: model.inputCostPer1M,
            outputCostPer1M: model.outputCostPer1M,
            speedRating: model.speedRating,
            qualityRating: model.qualityRating,
            supportsVision: model.supportsVision,
            supportsFunctionCalling: model.supportsFunctionCalling,
            supportsWebSearch: model.supportsWebSearch,
            supportsReasoning: model.supportsReasoning,
            supportsStreaming: model.supportsStreaming,
            extendedMetadata: model.extendedMetadata,
            updatedAt: new Date()
          },
          create: model
        });
        upsertedCount++;
      } catch (error) {
        apiLogger.error(`Failed to upsert model ${model.canonicalName}`, error);
      }
    }

    // Clear provider caches after bulk update
    await this.clearProviderCaches();

    return upsertedCount;
  }

  /**
   * Clear all model caches
   */
  static async clearAllCaches(): Promise<void> {
    try {
      const count = await redis.clearPrefix(CACHE_PREFIX);
      apiLogger.info(`Cleared ${count} model cache entries`);
    } catch (error) {
      apiLogger.error('Failed to clear model caches', error);
    }
  }

  /**
   * Clear provider-specific caches
   */
  static async clearProviderCaches(providerId?: string): Promise<void> {
    try {
      if (providerId) {
        await redis.delete(`${CACHE_PREFIX}provider:${providerId}`);
      } else {
        // Clear all provider caches
        await redis.clearPrefix(`${CACHE_PREFIX}provider:`);
      }
    } catch (error) {
      apiLogger.error('Failed to clear provider caches', error);
    }
  }

  /**
   * Get models grouped by provider
   */
  static async getModelsByProviders(): Promise<Map<string, ModelWithRelations[]>> {
    const models = await this.listAll();
    const grouped = new Map<string, ModelWithRelations[]>();

    for (const model of models) {
      const provider = model.providerId;
      if (!grouped.has(provider)) {
        grouped.set(provider, []);
      }
      grouped.get(provider)!.push(model);
    }

    return grouped;
  }

  /**
   * Get model count by provider
   */
  static async getModelCounts(): Promise<Record<string, number>> {
    const counts = await prisma.models.groupBy({
      by: ['providerId'],
      where: {
        isEnabled: true
      },
      _count: {
        id: true
      }
    });

    return counts.reduce((acc, item) => {
      acc[item.providerId] = item._count.id;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Get models available for a specific user plan
   */
  static async getModelsForPlan(planCode: string): Promise<ModelWithRelations[]> {
    const plan = await prisma.plans.findFirst({
      where: { code: planCode }
    });

    if (!plan) {
      return [];
    }

    const models = await prisma.$queryRaw<Models[]>`
      SELECT DISTINCT m.*
      FROM Models m
      JOIN ModelPlanRules r ON m.id = r.modelId
      WHERE m.isEnabled = true
      AND r.isEnabled = true
      AND (
        JSON_CONTAINS(r.planIds, JSON_QUOTE(${planCode}))
        OR (r.minPlanTier IS NOT NULL AND ${plan.tier} >= r.minPlanTier)
      )
      AND (r.effectiveFrom IS NULL OR r.effectiveFrom <= NOW())
      AND (r.effectiveUntil IS NULL OR r.effectiveUntil > NOW())
      ORDER BY m.speedRating DESC, m.qualityRating DESC
    `;

    return models.map(model => ({
      ...model,
      endpoints: [],
      versions: [],
      planAccess: []
    }));
  }
}