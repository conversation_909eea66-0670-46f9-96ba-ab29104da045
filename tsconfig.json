{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@/ui": ["./src/components/ui/index"],
      "@/hooks": ["./src/lib/hooks/index"],
      "@/types": ["./src/types/index"]
    },
    "types": ["node"],
    // CRITICAL: No 'any' allowed
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true
  },
  "include": ["types/next-env.d.ts", "react-legacy-types.d.ts", "types/**/*.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules", "RESEARCH/**/*", "provider-tests/**/*", "feature-worktree/**/*", "feature2-worktree/**/*", "maintenance/**/*", "test-*.ts", "test-*.py", "prisma/seed.ts", "prisma/seeds/**/*", "scripts/**/*", "router-optimization-test.ts", "*.test.ts", "**/test/**/*", "**/__tests__/**/*", "src/app/api/admin/**/*", "src/app/admin/**/*", "archive/**/*", "quick-test.ts", "src/lib/ai/models/registry-legacy.ts", "IMPLEMENTATION/**/*", "**/backups/**/*", "**/*backup*.ts", "src/db/repositories/model.repository.new.ts"]
}