#!/bin/bash
# Setup crontab for automated model validation
# Runs one validation task every 5 minutes

PROJECT_ROOT="/home/<USER>/deployments/dev/simplechat-ai"
SCRIPT_PATH="$PROJECT_ROOT/automate_validation.sh"

echo "🎯 Setting up automated model validation via crontab"

# Backup existing crontab
echo "📋 Backing up existing crontab..."
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S).txt 2>/dev/null || echo "No existing crontab found"

# Create new crontab entry
CRON_ENTRY="*/5 * * * * $SCRIPT_PATH"

echo "⚙️  Adding crontab entry: $CRON_ENTRY"

# Add to crontab (preserve existing entries)
(crontab -l 2>/dev/null || true; echo "$CRON_ENTRY") | crontab -

echo "✅ Crontab configured successfully!"
echo ""
echo "📊 Current crontab:"
crontab -l

echo ""
echo "🚀 Automation Status:"
echo "   - Frequency: Every 5 minutes"
echo "   - Script: $SCRIPT_PATH" 
echo "   - Logs: /home/<USER>/deployments/logs/validation_automation.log"
echo "   - Lock: /tmp/claude_validation.lock"
echo ""
echo "🔧 Management Commands:"
echo "   - View logs: tail -f /home/<USER>/deployments/logs/validation_automation.log"
echo "   - Stop automation: crontab -e (remove the validation line)"
echo "   - Manual run: $SCRIPT_PATH"
echo ""
echo "⚡ First execution will start within 5 minutes!"