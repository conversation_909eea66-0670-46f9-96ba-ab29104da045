# 🎯 **CATEGORY SCORING SYSTEM - DATA-DRIVEN IMPLEMENTATION PLAN**

**Date**: July 11, 2025  
**Purpose**: Fix auto routing fallback using automated benchmark data processing + agent enhancement  
**Principle**: **DATA-FIRST APPROACH** - Download benchmarks, process existing metadata, enhance with research  

## 📊 **CURRENT PROBLEM ANALYSIS**

**Database**: `justsimplechat` (development environment)
- **Total enabled models**: 194
- **Models WITH category scores**: 164 (84.5%)
- **Models WITHOUT category scores**: 30 (15.5%) ← **BLOCKING ROUTER**

**Critical Missing Models**:
- OpenAI: o3-pro, o4-mini, gpt-4.1-mini, chatgpt-4o-latest, gpt-4o-search-preview
- xAI: grok-4-0709, grok-3-mini-fast, grok-2-image-1212  
- Together AI: Llama 4 Scout, QwQ-32B, Llama 3.2 90B Vision
- Mistral: codestral-latest
- Plus 20+ other important models

**Router Fallback Logic**: When no models found with category scores → falls back to cheapest model by cost

## 🎯 **SMART SOLUTION ARCHITECTURE**

### **Data-First Approach: Multi-Source Intelligence**
1. **Existing Database Metadata** - Analyze AIModels `metadata` field for existing scores/data
2. **Downloaded Benchmark Datasets** - Arena ELO, HuggingFace leaderboards, coding benchmarks
3. **Automated Processing Scripts** - Match models across datasets and compute scores
4. **Agent Enhancement** - Fill gaps with Perplexity reasoning/deep research + Firecrawl
5. **Cross-Validation** - Multiple sources for accuracy and confidence scoring

## 📋 **PHASE 1: DATA DISCOVERY & ANALYSIS**

### ✅ **Task 1.1: Analyze Existing Database Metadata**
- [ ] Query AIModels table to analyze existing `metadata` field content
- [ ] Extract any existing scores, benchmarks, or performance data
- [ ] Identify patterns in current scoring methodology
- [ ] Document what data is already available vs missing
- [ ] Create inventory of existing category scores and their sources

### ✅ **Task 1.2: Identify Critical Data Gaps**
- [ ] List the 30 models without category scores
- [ ] Analyze which categories have the most missing data
- [ ] Prioritize by router impact (which models are most requested)
- [ ] Document suspected fabricated scores that need validation/replacement
- [ ] Create target list for benchmark data acquisition

### ✅ **Task 1.3: Research Available Benchmark Datasets**
- [ ] **Arena ELO Data**: Identify download source for latest Arena leaderboard
- [ ] **HuggingFace Open LLM Leaderboard**: Research API/download access
- [ ] **Coding Benchmarks**: HumanEval, MBPP, SWE-bench public results
- [ ] **Math Benchmarks**: MATH dataset, GSM8K results compilation
- [ ] **Reasoning Benchmarks**: GPQA, ARC-Challenge, HellaSwag results
- [ ] **Vision Benchmarks**: MMMU, SEED-Bench for multimodal models

## 📋 **PHASE 2: BENCHMARK DATA ACQUISITION**

### ✅ **Task 2.1: Download Arena ELO Data**
- [ ] Use Firecrawl to scrape latest Arena leaderboard from HuggingFace
- [ ] Parse ELO scores and model names into structured format
- [ ] Create mapping between Arena model names and our canonicalName format
- [ ] Validate data completeness and recency
- [ ] Store raw data in `/tmp/benchmark_data/arena_elo_latest.json`

### ✅ **Task 2.2: Acquire HuggingFace Leaderboard Data**
- [ ] Use HuggingFace API to download Open LLM Leaderboard data
- [ ] Extract scores for IFEval, BBH, MATH, GPQA, MUSR, MMLU-PRO
- [ ] Map HuggingFace model names to our canonicalName format
- [ ] Cross-reference with our 194 enabled models
- [ ] Store in `/tmp/benchmark_data/hf_leaderboard_latest.json`

### ✅ **Task 2.3: Collect Coding Benchmark Results**
- [ ] Use Perplexity Deep Research to find comprehensive HumanEval results
- [ ] Research SWE-bench verified results compilation
- [ ] Find MBPP (Mostly Basic Python Problems) result datasets
- [ ] Use Firecrawl to scrape coding benchmark result pages
- [ ] Compile into standardized format: `{model, humaneval_score, swe_bench_score, mbpp_score}`

### ✅ **Task 2.4: Gather Math & Reasoning Benchmarks**
- [ ] Research MATH benchmark official results
- [ ] Find GSM8K result compilations
- [ ] Use Perplexity Reasoning to find GPQA Diamond results
- [ ] Research ARC-Challenge and HellaSwag result datasets
- [ ] Compile mathematical and reasoning scores by model

### ✅ **Task 2.5: Acquire Vision & Multimodal Data**
- [ ] Research MMMU (Massive Multi-discipline Multimodal Understanding) results
- [ ] Find SEED-Bench vision capability scores
- [ ] Identify which models in our database have vision capabilities
- [ ] Cross-reference vision benchmarks with our multimodal models

## 📋 **PHASE 3: AUTOMATED PROCESSING PIPELINE**

### ✅ **Task 3.1: Create Model Name Mapping System**
- [ ] Build comprehensive mapping between different naming conventions
- [ ] Handle variations: `gpt-4o` vs `openai/gpt-4o` vs `GPT-4o` vs `GPT-4 Omni`
- [ ] Create fuzzy matching algorithm for model name resolution
- [ ] Validate mappings with confidence scores
- [ ] Store mapping rules in `/tmp/model_name_mappings.json`

### ✅ **Task 3.2: Build Score Aggregation Engine**
- [ ] Create script to process all downloaded benchmark data
- [ ] Implement 8 fundamental score calculation from raw benchmarks:
  - `cod`: Average of HumanEval, SWE-bench, MBPP scores
  - `rea`: Average of GPQA, ARC-Challenge, HellaSwag scores  
  - `mat`: Average of MATH, GSM8K scores
  - `cha`: Direct mapping from Arena ELO scores
  - `lng`: Average of MMLU, translation, summarization benchmarks
  - `ana`: Derived from reasoning + language understanding
  - `cre`: Estimated from creative writing evaluations
  - `vis`: Average of MMMU, SEED-Bench for vision models
- [ ] Apply confidence weighting based on data source quality

### ✅ **Task 3.3: Implement 8→28 Category Derivation**
- [ ] Code mathematical formulas to derive 28 router categories from 8 fundamental scores
- [ ] Handle edge cases (vision models vs text-only, missing benchmarks)
- [ ] Apply logical constraints (e.g., coding ≥ debugging, reasoning components)
- [ ] Generate confidence scores for derived categories
- [ ] Validate derivations against existing good scores

### ✅ **Task 3.4: Cross-Validation & Quality Control**
- [ ] Compare new scores against existing database scores
- [ ] Flag major discrepancies for manual review
- [ ] Implement sanity checks (no impossible scores like math > 99%)
- [ ] Cross-reference multiple benchmark sources where available
- [ ] Generate confidence metrics for each score

## 📋 **PHASE 4: AGENT ENHANCEMENT FOR GAPS**

### ✅ **Task 4.1: Identify Research Targets**
- [ ] List models with no benchmark data found
- [ ] Identify categories with insufficient coverage
- [ ] Prioritize recent/important models needing agent research
- [ ] Flag potential score discrepancies needing validation

### ✅ **Task 4.2: Enhanced Agent Research Pipeline**
- [ ] Use Perplexity Reasoning for comprehensive model analysis
- [ ] Use Perplexity Deep Research for missing benchmark scores
- [ ] Use Firecrawl to scrape provider technical specifications
- [ ] Apply research methodology from improved V6.9.1 subagent prompt
- [ ] Focus on filling critical gaps rather than comprehensive coverage

### ✅ **Task 4.3: Validation & Integration**
- [ ] Validate agent-researched scores against automated scores
- [ ] Integrate high-confidence agent findings into final dataset
- [ ] Flag low-confidence findings for future research
- [ ] Document all sources and confidence levels

## 📋 **PHASE 5: IMPLEMENTATION & TESTING**

### ✅ **Task 5.1: Generate Final Score Database**
- [ ] Merge automated scores + agent research + existing good scores
- [ ] Apply final validation and confidence filtering
- [ ] Generate SQL updates with proper metadata (sources, confidence, date)
- [ ] Create backup of current scores before applying changes

### ✅ **Task 5.2: Apply Scores to Database**
- [ ] Execute SQL updates for the 30 models missing scores
- [ ] Update any scores with higher confidence data
- [ ] Verify all 194 models now have category scores
- [ ] Clear router Redis cache to force fresh data loading

### ✅ **Task 5.3: Router Performance Testing**
- [ ] Test router with various query types and user plans
- [ ] Measure fallback rate reduction (target: 15.5% → <2%)
- [ ] Validate appropriate model selection for different categories
- [ ] Monitor router response times and cache hit rates
- [ ] Document performance improvements

### ✅ **Task 5.4: CategoryScoringService Implementation**
- [ ] Create service to manage 8→28 score derivation
- [ ] Implement Redis caching with appropriate TTLs
- [ ] Add database fallback mechanisms
- [ ] Integrate with existing router cache system
- [ ] Create monitoring and maintenance tools

## 🔧 **FUNDAMENTAL SCORE DEFINITIONS (8 CORE MEASUREMENTS)**

### **Consistent Measurement Standards for 8 Fundamental Scores**

#### **1. cod (Coding Ability) - Scale: 0-100**
**Primary Sources**:
- **HumanEval** (pass@1): Weight 40% - Most reliable coding benchmark
- **SWE-bench Verified**: Weight 30% - Real-world software engineering tasks  
- **MBPP** (pass@1): Weight 20% - Python programming problems
- **LiveCodeBench**: Weight 10% - Recent coding challenges

**Calculation**: `cod = (humaneval * 0.4 + swe_bench * 0.3 + mbpp * 0.2 + livecode * 0.1)`

**Existing Database Sources**:
- Check `extendedMetadata.taskScores.cod` for existing scores
- Look for benchmark references in `extendedMetadata.categoryScores.coding`
- Cross-validate with provider specifications

#### **2. rea (Reasoning Ability) - Scale: 0-100**
**Primary Sources**:
- **GPQA Diamond**: Weight 35% - Graduate-level scientific reasoning
- **ARC-Challenge**: Weight 25% - Science exam reasoning  
- **HellaSwag**: Weight 20% - Commonsense reasoning
- **AIME**: Weight 20% - Advanced mathematical reasoning

**Calculation**: `rea = (gpqa_diamond * 0.35 + arc_challenge * 0.25 + hellaswag * 0.2 + aime * 0.2)`

**Existing Database Sources**:
- Check `extendedMetadata.taskScores.rea` for existing scores
- Look for reasoning benchmarks in `extendedMetadata.categoryScores.reasoning`

#### **3. mat (Mathematical Ability) - Scale: 0-100**
**Primary Sources**:
- **MATH Benchmark**: Weight 60% - Comprehensive mathematical problem solving
- **GSM8K**: Weight 30% - Grade school math reasoning
- **AIME**: Weight 10% - Competition-level mathematics

**Calculation**: `mat = (math_benchmark * 0.6 + gsm8k * 0.3 + aime * 0.1)`

**Existing Database Sources**:
- Check `extendedMetadata.taskScores.mat` for existing scores
- Look for math scores in `extendedMetadata.categoryScores.mathematical`

#### **4. cha (Chat/Conversation) - Scale: 800-1600 (ELO)**
**Primary Sources**:
- **Arena ELO**: Weight 90% - Direct conversational ability measurement
- **MT-Bench**: Weight 10% - Multi-turn conversation evaluation

**Calculation**: `cha = (arena_elo * 0.9 + mt_bench_scaled * 0.1)`

**Special Handling**: 
- ELO scores are 800-1600, convert to 0-100 for derivations: `(elo - 800) / 8`
- Store raw ELO in `general_chat` category, scaled version for derivations

**Existing Database Sources**:
- Check `extendedMetadata.categoryScores.general_chat.score` for ELO scores
- Look for Arena references in metadata

#### **5. lng (Language Understanding) - Scale: 0-100**
**Primary Sources**:
- **MMLU**: Weight 40% - Massive multitask language understanding
- **MMLU-PRO**: Weight 30% - Advanced language understanding
- **Translation benchmarks**: Weight 15% - Multilingual capability
- **Summarization tasks**: Weight 15% - Language comprehension

**Calculation**: `lng = (mmlu * 0.4 + mmlu_pro * 0.3 + translation * 0.15 + summarization * 0.15)`

**Existing Database Sources**:
- Check `extendedMetadata.categoryScores.question_answering` for MMLU scores
- Look for language-related scores in existing categories

#### **6. ana (Analysis Ability) - Scale: 0-100**  
**Primary Sources**:
- **Data analysis benchmarks**: Weight 40% - Chart reading, data interpretation
- **MMLU analytical sections**: Weight 30% - Analytical reasoning from MMLU
- **Reading comprehension**: Weight 30% - Complex text analysis

**Calculation**: `ana = (data_analysis * 0.4 + mmlu_analytical * 0.3 + reading_comp * 0.3)`

**Existing Database Sources**:
- Check `extendedMetadata.categoryScores.analysis` and `data_analysis`
- Derive from reasoning + language scores if direct data unavailable

#### **7. cre (Creative Ability) - Scale: 0-100**
**Primary Sources**:
- **AlpacaEval Creative**: Weight 40% - Creative task evaluation
- **WritingPrompts**: Weight 30% - Creative writing assessment
- **Creative writing evaluations**: Weight 30% - Human-judged creativity

**Calculation**: `cre = (alpaca_creative * 0.4 + writing_prompts * 0.3 + creative_eval * 0.3)`

**Fallback Method**: If no direct creative benchmarks available:
`cre = (lng * 0.6 + cha * 0.4) * 0.8` (80% of language+chat ability)

**Existing Database Sources**:
- Check `extendedMetadata.categoryScores.creative_writing`
- Look for creative task references in metadata

#### **8. vis (Vision/Multimodal) - Scale: 0-100**
**Primary Sources**:
- **MMMU**: Weight 50% - Massive multimodal understanding
- **SEED-Bench**: Weight 30% - Multimodal evaluation  
- **VQA benchmarks**: Weight 20% - Visual question answering

**Calculation**: `vis = (mmmu * 0.5 + seed_bench * 0.3 + vqa * 0.2)`

**Special Handling**:
- Set to 0 for text-only models (where `supportsVision = false`)
- Only compute for models with confirmed vision capabilities

**Existing Database Sources**:
- Check `extendedMetadata.categoryScores.image_analysis` and `multimodal`
- Verify vision capability from `supportsVision` field

### **Data Source Priority Hierarchy**

1. **Downloaded Benchmark Data** (Confidence: 0.95) - Fresh, authoritative sources
2. **Existing Database Scores** (Confidence: 0.80-0.90) - Validate against downloads
3. **Agent Research** (Confidence: 0.70-0.85) - Fill gaps for missing models
4. **Provider Specifications** (Confidence: 0.60-0.80) - Official claims, less reliable
5. **Derived/Proxy Scores** (Confidence: 0.50-0.70) - Mathematical estimates

### **Quality Control Rules**

- **Outlier Detection**: Flag scores >2 standard deviations from model family mean
- **Sanity Checks**: No math scores >95%, no chat scores <800 ELO  
- **Cross-Validation**: Compare multiple sources where available
- **Confidence Thresholds**: Require >0.80 confidence for database updates
- **Missing Data**: Set to 0 rather than fabricate scores

## 🔧 **MATHEMATICAL FORMULAS (8→28 DERIVATION)**

### **8 Fundamental Scores → 28 Router Categories**
1. **cod** (Coding): HumanEval, SWE-bench, LiveCodeBench
2. **cre** (Creative): WritingPrompts, creative tasks, Alpaca-Eval creative
3. **rea** (Reasoning): GPQA, ARC-Challenge, AIME
4. **mat** (Mathematical): MATH benchmark, GSM8K
5. **ana** (Analysis): Data interpretation, chart reading
6. **lng** (Language): Translation, summarization, MMLU
7. **cha** (Chat): Arena ELO, conversation quality
8. **vis** (Vision): Image analysis, multimodal benchmarks

### **28 Router Categories (Derivation Formulas)**
```javascript
const categoryDerivation = {
  // Technical categories
  coding: "cod * 0.8 + debugging_research * 0.2",
  debugging: "cod * 0.3 + research_specific * 0.7", 
  data_analysis: "ana * 0.6 + lng * 0.4",
  
  // Creative categories  
  creative_writing: "cre * 0.9 + lng * 0.1",
  brainstorming: "cre * 0.8 + rea * 0.2",
  
  // Reasoning categories
  reasoning: "rea * 1.0", // Direct mapping
  mathematical: "mat * 1.0", // Direct mapping
  scientific: "rea * 0.6 + mat * 0.4",
  philosophical: "rea * 0.8 + lng * 0.2",
  legal: "rea * 0.7 + lng * 0.3",
  
  // Language categories
  translation: "lng * 0.8 + research_specific * 0.2",
  summarization: "lng * 0.7 + ana * 0.3", 
  question_answering: "lng * 0.6 + rea * 0.4",
  technical_writing: "cod * 0.4 + lng * 0.6",
  business_writing: "lng * 0.8 + ana * 0.2",
  
  // Chat categories
  general_chat: "cha * 1.0", // Direct mapping from Arena ELO
  role_play: "cha * 0.8 + cre * 0.2",
  personal_advice: "cha * 0.9 + rea * 0.1",
  tutorial: "lng * 0.6 + cha * 0.4",
  
  // Analysis categories
  analysis: "ana * 1.0", // Direct mapping
  historical: "ana * 0.7 + lng * 0.3",
  current_events: "ana * 0.6 + lng * 0.4",
  
  // Vision categories (conditional)
  image_analysis: "vis * 1.0 || 0", // Only if vision capable
  multimodal: "vis * 1.0 || 0", // Only if vision capable
  image_generation: "0", // Most models can't generate images
  
  // Medical/specialized
  medical: "rea * 0.5 + lng * 0.5",
  
  // Fallback
  other: "(cod + cre + rea + mat + ana + lng + cha) / 7"
};
```

## 🎯 **SUCCESS CRITERIA**

### **Phase 1 Success Metrics**
- [ ] Router fallback rate drops from 15.5% to <2%
- [ ] All 194 enabled models have category scores (even if some are 0)
- [ ] No fabricated scores remain in database
- [ ] All research is documented with sources and confidence levels

### **Phase 2 Success Metrics**  
- [ ] CategoryScoringService successfully derives 28 scores from 8 fundamentals
- [ ] Redis caching reduces router response time to <50ms
- [ ] Database fallback works seamlessly when Redis unavailable
- [ ] Router accuracy improves (selects appropriate models for task types)

### **Phase 3 Success Metrics**
- [ ] Automated 24-hour research cycles identify and score new models
- [ ] Score quality remains high (confidence levels >0.85 for updates)
- [ ] System automatically flags suspicious scores for review
- [ ] Model coverage remains >95% (few models without scores)

### **Phase 4 Success Metrics**
- [ ] Complete documentation enables new team members to understand system
- [ ] Monitoring identifies issues before they affect users
- [ ] Maintenance tools enable easy score updates and validation
- [ ] System remains stable and performant under production load

## 🚨 **IMPORTANT PRINCIPLES**

1. **NO FABRICATED SCORES**: Use real benchmark data or set to 0
2. **EVIDENCE TRACKING**: Document all sources and confidence levels  
3. **GRACEFUL DEGRADATION**: System works even with missing/stale scores
4. **PERFORMANCE FIRST**: Sub-100ms router response times maintained
5. **MAINTAINABILITY**: Clear documentation and monitoring for long-term success

---

**Next Steps**: Start with Phase 1, Task 1.1 - Research Infrastructure Setup