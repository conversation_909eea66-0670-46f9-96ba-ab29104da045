const http = require('http');

// Configuration
const LITELLM_URL = 'litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com';
const MASTER_KEY = 'sk-simplechat-master-2025';

function deleteModel() {
    return new Promise((resolve) => {
        const data = JSON.stringify({
            id: 'openai/gpt-4-turbo'
        });

        const options = {
            hostname: LITELLM_URL,
            port: 80,
            path: '/model/delete',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MASTER_KEY}`,
                'Content-Length': data.length
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => responseData += chunk);
            res.on('end', () => {
                console.log('Delete response:', responseData);
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log('Delete error:', error.message);
            resolve();
        });

        req.write(data);
        req.end();
    });
}

function addModel() {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({
            model_name: 'openai/gpt-4-turbo',
            litellm_params: {
                model: 'gpt-4-turbo', // Use the exact OpenAI model name
                litellm_credential_name: 'OpenAI'
            },
            model_info: {
                description: 'OpenAI GPT-4 Turbo model'
            }
        });

        const options = {
            hostname: LITELLM_URL,
            port: 80,
            path: '/model/new',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MASTER_KEY}`,
                'Content-Length': data.length
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => responseData += chunk);
            res.on('end', () => {
                if (res.statusCode === 200 || res.statusCode === 201) {
                    console.log('✅ Successfully added openai/gpt-4-turbo');
                    resolve(true);
                } else {
                    console.log('❌ Failed to add openai/gpt-4-turbo:', responseData);
                    resolve(false);
                }
            });
        });

        req.on('error', (error) => {
            console.log('❌ Request error:', error.message);
            resolve(false);
        });

        req.write(data);
        req.end();
    });
}

function testModel() {
    return new Promise((resolve) => {
        const testData = JSON.stringify({
            model: 'openai/gpt-4-turbo',
            messages: [{ role: 'user', content: 'Say "working"' }],
            max_tokens: 10
        });

        const testOptions = {
            hostname: LITELLM_URL,
            port: 80,
            path: '/chat/completions',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MASTER_KEY}`,
                'Content-Length': testData.length
            }
        };

        const testReq = http.request(testOptions, (res) => {
            let responseData = '';
            res.on('data', (chunk) => responseData += chunk);
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ openai/gpt-4-turbo is working!');
                    resolve(true);
                } else {
                    console.log('❌ openai/gpt-4-turbo test failed:', responseData);
                    resolve(false);
                }
            });
        });

        testReq.on('error', (error) => {
            console.log('❌ Test error:', error.message);
            resolve(false);
        });

        testReq.write(testData);
        testReq.end();
    });
}

async function main() {
    console.log('🚀 Fixing openai/gpt-4-turbo in LiteLLM\n');
    
    console.log('1. Deleting existing model...');
    await deleteModel();
    
    console.log('2. Adding model with correct configuration...');
    const success = await addModel();
    
    if (success) {
        console.log('3. Testing the model...');
        await testModel();
    }
}

main().catch(console.error);