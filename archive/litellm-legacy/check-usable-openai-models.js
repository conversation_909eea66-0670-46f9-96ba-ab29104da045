const axios = require('axios');

// LiteLLM configuration
const baseURL = 'http://litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com';
const masterKey = 'sk-simplechat-master-2025';

async function main() {
    console.log('=== Checking OpenAI Model Status ===\n');
    
    // Get models from both endpoints
    const modelsResponse = await axios.get(`${baseURL}/models`, {
        headers: { 'Authorization': `Bearer ${masterKey}` }
    });
    
    const modelInfoResponse = await axios.get(`${baseURL}/model/info`, {
        headers: { 'Authorization': `Bearer ${masterKey}` }
    });
    
    const activeModels = modelsResponse.data.data || [];
    const dbModels = modelInfoResponse.data.data || [];
    
    // Filter OpenAI models
    const activeOpenAI = activeModels.filter(m => m.id && m.id.startsWith('openai/')).map(m => m.id);
    const dbOpenAI = dbModels.filter(m => m.model_name && m.model_name.startsWith('openai/')).map(m => m.model_name);
    
    console.log(`Active OpenAI models (in /models): ${activeOpenAI.length}`);
    console.log(`Database OpenAI models (in /model/info): ${dbOpenAI.length}`);
    
    // Find models only in database (not active)
    const onlyInDB = dbOpenAI.filter(m => !activeOpenAI.includes(m));
    const onlyActive = activeOpenAI.filter(m => !dbOpenAI.includes(m));
    
    console.log(`\nModels ONLY in database (not usable): ${onlyInDB.length}`);
    if (onlyInDB.length > 0 && onlyInDB.length <= 20) {
        onlyInDB.forEach(m => console.log(`  - ${m}`));
    } else if (onlyInDB.length > 20) {
        onlyInDB.slice(0, 10).forEach(m => console.log(`  - ${m}`));
        console.log(`  ... and ${onlyInDB.length - 10} more`);
    }
    
    console.log(`\nModels ONLY active (not in database): ${onlyActive.length}`);
    if (onlyActive.length > 0 && onlyActive.length <= 20) {
        onlyActive.forEach(m => console.log(`  - ${m}`));
    } else if (onlyActive.length > 20) {
        onlyActive.slice(0, 10).forEach(m => console.log(`  - ${m}`));
        console.log(`  ... and ${onlyActive.length - 10} more`);
    }
    
    // Check important models
    console.log('\n=== Key Model Status ===');
    const keyModels = ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo', 'gpt-4', 'o4-mini'];
    
    for (const model of keyModels) {
        const withPrefix = `openai/${model}`;
        const inActive = activeOpenAI.includes(withPrefix);
        const inDB = dbOpenAI.includes(withPrefix);
        
        console.log(`\n${withPrefix}:`);
        console.log(`  Active (usable): ${inActive ? '✅' : '❌'}`);
        console.log(`  In database: ${inDB ? '✅' : '❌'}`);
        
        if (!inActive && inDB) {
            console.log(`  ⚠️  This model is in the database but not active!`);
        }
    }
    
    // Summary
    console.log('\n=== Summary ===');
    console.log('The issue is that some OpenAI models exist in the database but are not active.');
    console.log('Only models that appear in /models endpoint can be used.');
    console.log('The missing litellm_credential_name is not the primary issue.');
    console.log('\nThe real issue: Models need to be properly registered/activated in LiteLLM.');
}

main().catch(console.error);