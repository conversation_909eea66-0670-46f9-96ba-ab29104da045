#!/usr/bin/env node

/**
 * Find OpenRouter Models by Configuration
 * 
 * This script finds models that are configured to use OpenRouter
 * by examining their litellm_params configuration
 */

const http = require('http');

// Configuration
const LITELLM_URL = 'litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com';
const MASTER_KEY = 'sk-simplechat-master-2025';

// Make HTTP request
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, res => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const json = JSON.parse(body);
          resolve({ status: res.statusCode, data: json });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    if (data) req.write(JSON.stringify(data));
    req.end();
  });
}

// Get model configuration
async function getModelConfig(modelId) {
  const options = {
    hostname: LITELLM_URL,
    port: 80,
    path: `/model/info?model=${encodeURIComponent(modelId)}`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${MASTER_KEY}`
    }
  };

  try {
    const result = await makeRequest(options);
    if (result.status === 200) {
      return result.data;
    }
  } catch (error) {
    // Ignore errors for individual models
  }
  return null;
}

// Get all models
async function getAllModels() {
  const options = {
    hostname: LITELLM_URL,
    port: 80,
    path: '/v1/models',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${MASTER_KEY}`
    }
  };

  try {
    const result = await makeRequest(options);
    if (result.status === 200 && result.data.data) {
      return result.data.data;
    }
  } catch (error) {
    console.error('❌ Error fetching models:', error.message);
  }
  return [];
}

async function main() {
  console.log('🔍 Finding OpenRouter Models by Configuration');
  console.log('============================================\n');

  const allModels = await getAllModels();
  console.log(`Found ${allModels.length} total models\n`);

  const openRouterModels = [];
  const processedCount = { current: 0 };

  // Process models in batches to avoid overwhelming the API
  const batchSize = 10;
  for (let i = 0; i < allModels.length; i += batchSize) {
    const batch = allModels.slice(i, i + batchSize);
    
    const promises = batch.map(async (model) => {
      processedCount.current++;
      process.stdout.write(`\rProcessing models: ${processedCount.current}/${allModels.length}`);
      
      const config = await getModelConfig(model.id);
      if (config && config.info && config.info.litellm_params) {
        const litellmParams = config.info.litellm_params;
        
        // Check if this model is configured for OpenRouter
        // OpenRouter models should have litellm_params.model without openrouter/ prefix
        // and should not be alibaba/, openai/, anthropic/, etc.
        if (litellmParams.model && 
            !litellmParams.model.startsWith('openrouter/') &&
            litellmParams.model.includes('/') &&
            !model.id.startsWith('alibaba/') &&
            !litellmParams.api_base &&
            !litellmParams.api_key) {
          
          openRouterModels.push({
            id: model.id,
            litellm_model: litellmParams.model,
            provider: litellmParams.custom_llm_provider || 'default'
          });
        }
      }
    });
    
    await Promise.all(promises);
    
    // Small delay between batches
    if (i + batchSize < allModels.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log('\n\n🎯 Found OpenRouter Models (incorrectly configured):');
  console.log('=====================================\n');

  if (openRouterModels.length === 0) {
    console.log('✅ No incorrectly configured OpenRouter models found');
    return;
  }

  openRouterModels.forEach((model, index) => {
    console.log(`${index + 1}. ${model.id}`);
    console.log(`   litellm_params.model: ${model.litellm_model}`);
    console.log(`   provider: ${model.provider}\n`);
  });

  console.log(`📊 Total: ${openRouterModels.length} OpenRouter models need fixing\n`);

  // Write to file for removal script
  const modelIds = openRouterModels.map(m => m.id);
  require('fs').writeFileSync('openrouter-models-to-remove.json', JSON.stringify(modelIds, null, 2));
  console.log('💾 Model IDs saved to: openrouter-models-to-remove.json');
}

main().catch(console.error);