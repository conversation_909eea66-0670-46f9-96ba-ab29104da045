#!/usr/bin/env node

const axios = require('axios');

const LITELLM_BASE_URL = 'http://litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com';
const API_KEY = 'sk-simplechat-master-2025';
const OPENROUTER_API_KEY = '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';

// Test models to add - popular OpenRouter models
const testModels = [
  {
    model_name: 'openrouter/meta-llama/llama-3.1-8b-instruct:free',
    litellm_params: {
      model: 'openrouter/meta-llama/llama-3.1-8b-instruct:free',
      api_key: OPENROUTER_API_KEY
    }
  },
  {
    model_name: 'openrouter/mistralai/mistral-7b-instruct:free',
    litellm_params: {
      model: 'openrouter/mistralai/mistral-7b-instruct:free',
      api_key: OPENROUTER_API_KEY
    }
  },
  {
    model_name: 'openrouter/google/gemini-2.0-flash-exp:free',
    litellm_params: {
      model: 'openrouter/google/gemini-2.0-flash-exp:free',
      api_key: OPENROUTER_API_KEY
    }
  },
  {
    model_name: 'openrouter/qwen/qwen-2-7b-instruct:free',
    litellm_params: {
      model: 'openrouter/qwen/qwen-2-7b-instruct:free',
      api_key: OPENROUTER_API_KEY
    }
  },
  {
    model_name: 'openrouter/deepseek/deepseek-v3',
    litellm_params: {
      model: 'openrouter/deepseek/deepseek-v3',
      api_key: OPENROUTER_API_KEY
    }
  }
];

async function testOpenRouterConnection() {
  console.log('=== Testing OpenRouter API Connection ===\n');
  
  try {
    // Test with a simple model
    const testResponse = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'meta-llama/llama-3.1-8b-instruct:free',
        messages: [{ role: 'user', content: 'Say hello' }],
        max_tokens: 10
      },
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://justsimple.chat',
          'X-Title': 'JustSimpleChat'
        }
      }
    );
    
    console.log('✓ OpenRouter API connection successful');
    console.log(`Response: ${testResponse.data.choices[0].message.content}\n`);
    return true;
  } catch (error) {
    console.error('✗ OpenRouter API connection failed:', error.response?.data || error.message);
    return false;
  }
}

async function addModels() {
  // First test OpenRouter connection
  const connectionOk = await testOpenRouterConnection();
  if (!connectionOk) {
    console.log('\nPlease check the OpenRouter API key and try again.');
    return;
  }

  console.log('=== Adding OpenRouter Models to LiteLLM ===\n');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const model of testModels) {
    try {
      console.log(`Adding: ${model.model_name}`);
      
      const response = await axios.post(
        `${LITELLM_BASE_URL}/model/new`,
        model,
        {
          headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log(`✓ Added successfully: ${response.data.model_id}\n`);
      successCount++;
      
      // Wait 1 second between additions
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`✗ Error adding model:`, error.response?.data?.detail || error.message);
      errorCount++;
    }
  }
  
  console.log('\n=== Summary ===');
  console.log(`Success: ${successCount}`);
  console.log(`Errors: ${errorCount}`);
  
  if (successCount > 0) {
    console.log('\n=== Testing Added Models ===\n');
    
    // Test the first successfully added model
    const testModel = testModels[0];
    console.log(`Testing model: ${testModel.model_name}`);
    
    try {
      const response = await axios.post(
        `${LITELLM_BASE_URL}/v1/chat/completions`,
        {
          model: testModel.model_name,
          messages: [{ role: 'user', content: 'Say hello' }],
          max_tokens: 20
        },
        {
          headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✓ Model test successful');
      console.log(`Response: ${response.data.choices[0].message.content}`);
    } catch (error) {
      console.error('✗ Model test failed:', error.response?.data || error.message);
    }
  }
}

// Run
console.log('This script will add 5 test OpenRouter models to LiteLLM.');
console.log('Each model will be added with the OpenRouter API key.\n');

const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Do you want to proceed? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes') {
    addModels().then(() => {
      rl.close();
    }).catch(err => {
      console.error('Script error:', err);
      rl.close();
    });
  } else {
    console.log('Cancelled.');
    rl.close();
  }
});