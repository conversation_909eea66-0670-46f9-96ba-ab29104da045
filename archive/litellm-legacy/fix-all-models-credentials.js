#!/usr/bin/env node

/**
 * Fix ALL models in LiteLLM to use the correct credential names
 * This ensures all models use the centrally managed API keys
 */

const http = require('http');

const LITELLM_URL = 'litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com';
const MASTER_KEY = 'sk-simplechat-master-2025';

// Mapping of provider prefixes to credential names in LiteLLM
const PROVIDER_TO_CREDENTIAL = {
  'openai/': 'OpenAI',
  'anthropic/': 'Anthropic',
  'gemini/': 'Google API KEY',  // Google uses "Google API KEY" not "Google"
  'groq/': 'Groq',
  'mistral/': 'Minstral',  // Note the typo in LiteLLM - it's "Minstral" not "Mistral"
  'deepseek/': 'Deepseek',
  'xai/': 'Xai',
  'perplexity/': 'Perplexity',
  'cohere/': 'Cohere',
  'together/': 'Together AI',
  'alibaba/': 'Qwen',
  'openrouter/': 'OpenRouter',
};

async function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: LITELLM_URL,
      port: 80,
      path: path,
      method: method,
      headers: {
        'Authorization': `Bearer ${MASTER_KEY}`,
        'Content-Type': 'application/json'
      }
    };
    
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => { responseData += chunk; });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve(parsed);
        } catch (e) {
          resolve({ error: responseData });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function updateModel(model) {
  // Determine the credential name based on the model prefix
  let credentialName = null;
  for (const [prefix, credential] of Object.entries(PROVIDER_TO_CREDENTIAL)) {
    if (model.model_name.startsWith(prefix)) {
      credentialName = credential;
      break;
    }
  }
  
  if (!credentialName) {
    console.log(`  ⚠️  No credential mapping for ${model.model_name}`);
    return { success: false, reason: 'no_mapping' };
  }
  
  // Prepare update data
  const updateData = {
    model_id: model.model_id,  // Use model_id if available
    model_name: model.model_name,
    litellm_params: {
      ...model.litellm_params,  // Keep existing params
      litellm_credential_name: credentialName
    }
  };
  
  console.log(`  Updating ${model.model_name} with credential: ${credentialName}`);
  
  try {
    const response = await makeRequest('POST', '/model/update', updateData);
    
    if (response.error) {
      console.log(`    ❌ Error: ${JSON.stringify(response.error)}`);
      return { success: false, reason: response.error };
    } else {
      console.log(`    ✅ Updated successfully`);
      return { success: true };
    }
  } catch (error) {
    console.log(`    ❌ Exception: ${error.message}`);
    return { success: false, reason: error.message };
  }
}

async function main() {
  const args = process.argv.slice(2);
  const provider = args[0];
  const dryRun = args.includes('--dry-run');
  
  console.log('🔧 Fix Model Credentials in LiteLLM');
  console.log('===================================');
  console.log(`Mode: ${dryRun ? 'DRY RUN' : 'EXECUTE'}`);
  
  if (provider && provider !== 'all' && !PROVIDER_TO_CREDENTIAL[provider + '/']) {
    console.log(`\n❌ Unknown provider: ${provider}`);
    console.log('Available providers:', Object.keys(PROVIDER_TO_CREDENTIAL).map(p => p.slice(0, -1)).join(', '));
    return;
  }
  
  try {
    // Get all models
    console.log('\n📊 Fetching all models...');
    const modelsResponse = await makeRequest('GET', '/v1/model/info');
    
    if (!modelsResponse.data) {
      console.error('❌ Could not fetch model info');
      return;
    }
    
    console.log(`Total models: ${modelsResponse.data.length}\n`);
    
    // Group models by provider and check credential status
    const modelsByProvider = {};
    const modelsNeedingUpdate = [];
    
    for (const model of modelsResponse.data) {
      // Find provider
      let modelProvider = 'unknown';
      for (const [prefix, cred] of Object.entries(PROVIDER_TO_CREDENTIAL)) {
        if (model.model_name.startsWith(prefix)) {
          modelProvider = prefix.slice(0, -1);  // Remove trailing /
          break;
        }
      }
      
      // Group by provider
      if (!modelsByProvider[modelProvider]) {
        modelsByProvider[modelProvider] = {
          total: 0,
          withCredential: 0,
          withoutCredential: 0,
          models: []
        };
      }
      
      modelsByProvider[modelProvider].total++;
      modelsByProvider[modelProvider].models.push(model);
      
      if (model.litellm_params.litellm_credential_name) {
        modelsByProvider[modelProvider].withCredential++;
      } else {
        modelsByProvider[modelProvider].withoutCredential++;
        if (!provider || provider === 'all' || modelProvider === provider) {
          modelsNeedingUpdate.push(model);
        }
      }
    }
    
    // Show summary
    console.log('📊 Model Summary by Provider:');
    console.log('=============================');
    for (const [prov, stats] of Object.entries(modelsByProvider)) {
      if (stats.total > 0) {
        console.log(`\n${prov}:`);
        console.log(`  Total: ${stats.total}`);
        console.log(`  With credential: ${stats.withCredential} ✅`);
        console.log(`  Without credential: ${stats.withoutCredential} ${stats.withoutCredential > 0 ? '❌' : '✅'}`);
      }
    }
    
    if (modelsNeedingUpdate.length === 0) {
      console.log('\n✅ All models have credentials configured!');
      return;
    }
    
    console.log(`\n🔍 Found ${modelsNeedingUpdate.length} models needing credential update`);
    
    if (dryRun) {
      console.log('\nModels that would be updated:');
      modelsNeedingUpdate.slice(0, 20).forEach(m => {
        const cred = PROVIDER_TO_CREDENTIAL[Object.keys(PROVIDER_TO_CREDENTIAL).find(p => m.model_name.startsWith(p))];
        console.log(`  ${m.model_name} → credential: ${cred}`);
      });
      if (modelsNeedingUpdate.length > 20) {
        console.log(`  ... and ${modelsNeedingUpdate.length - 20} more`);
      }
      return;
    }
    
    // Update models
    console.log('\n⚡ Updating models...\n');
    const results = {
      success: 0,
      failed: 0,
      failureReasons: {}
    };
    
    for (const model of modelsNeedingUpdate) {
      const result = await updateModel(model);
      if (result.success) {
        results.success++;
      } else {
        results.failed++;
        const reason = result.reason || 'unknown';
        results.failureReasons[reason] = (results.failureReasons[reason] || 0) + 1;
      }
      
      // Small delay between updates
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Show results
    console.log('\n📊 Update Results:');
    console.log('==================');
    console.log(`✅ Successfully updated: ${results.success}`);
    console.log(`❌ Failed: ${results.failed}`);
    
    if (results.failed > 0) {
      console.log('\nFailure reasons:');
      for (const [reason, count] of Object.entries(results.failureReasons)) {
        console.log(`  ${reason}: ${count}`);
      }
    }
    
    // Verify a sample
    if (results.success > 0) {
      console.log('\n🔍 Verifying updates...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for propagation
      
      const verifyResponse = await makeRequest('GET', '/v1/model/info');
      let verifiedCount = 0;
      
      for (const model of modelsNeedingUpdate.slice(0, 5)) {
        const updated = verifyResponse.data.find(m => m.model_name === model.model_name);
        if (updated && updated.litellm_params.litellm_credential_name) {
          verifiedCount++;
        }
      }
      
      console.log(`Verified ${verifiedCount}/5 sample models have credentials`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Show usage if no arguments
if (process.argv.length === 2) {
  console.log(`
Usage:
  node fix-all-models-credentials.js --dry-run              # Preview all models needing updates
  node fix-all-models-credentials.js openai                 # Update only OpenAI models
  node fix-all-models-credentials.js openai --dry-run       # Preview OpenAI models
  node fix-all-models-credentials.js all                    # Update ALL models

This script ensures all models in LiteLLM use the correct credential names
so they use the centrally managed API keys.

Available providers:
  ${Object.keys(PROVIDER_TO_CREDENTIAL).map(p => p.slice(0, -1)).join(', ')}
`);
  process.exit(0);
}

main().catch(console.error);