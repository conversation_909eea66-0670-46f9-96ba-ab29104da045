const http = require('http');

// Configuration
const LITELLM_URL = 'litellm-proxy-alb-new-455342227.us-east-1.elb.amazonaws.com';
const MASTER_KEY = 'sk-simplechat-master-2025';

function addModel() {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({
            model_name: 'openai/gpt-4-turbo',
            litellm_params: {
                model: 'openai/gpt-4-turbo-2024-04-09', // Map to the actual working model
                litellm_credential_name: 'OpenAI'
            },
            model_info: {
                description: 'OpenAI GPT-4 Turbo model (legacy mapping)'
            }
        });

        const options = {
            hostname: LITELLM_URL,
            port: 80,
            path: '/model/new',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MASTER_KEY}`,
                'Content-Length': data.length
            }
        };

        const req = http.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => responseData += chunk);
            res.on('end', () => {
                if (res.statusCode === 200 || res.statusCode === 201) {
                    console.log('✅ Successfully added openai/gpt-4-turbo');
                    resolve(true);
                } else {
                    console.log('❌ Failed to add openai/gpt-4-turbo:', responseData);
                    resolve(false);
                }
            });
        });

        req.on('error', (error) => {
            console.log('❌ Request error:', error.message);
            resolve(false);
        });

        req.write(data);
        req.end();
    });
}

async function main() {
    console.log('🚀 Adding missing openai/gpt-4-turbo to LiteLLM\n');
    
    const success = await addModel();
    
    if (success) {
        console.log('\n🧪 Testing the model...');
        
        // Test the model
        const testData = JSON.stringify({
            model: 'openai/gpt-4-turbo',
            messages: [{ role: 'user', content: 'Say "test"' }],
            max_tokens: 10
        });

        const testOptions = {
            hostname: LITELLM_URL,
            port: 80,
            path: '/chat/completions',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MASTER_KEY}`,
                'Content-Length': testData.length
            }
        };

        const testReq = http.request(testOptions, (res) => {
            let responseData = '';
            res.on('data', (chunk) => responseData += chunk);
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ openai/gpt-4-turbo is working!');
                } else {
                    console.log('❌ openai/gpt-4-turbo test failed:', responseData);
                }
            });
        });

        testReq.on('error', (error) => {
            console.log('❌ Test error:', error.message);
        });

        testReq.write(testData);
        testReq.end();
    }
}

main().catch(console.error);