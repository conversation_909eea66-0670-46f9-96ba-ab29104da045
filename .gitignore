# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# Task Master AI generated files
.taskmaster/tasks/tasks.json
.taskmaster/tasks/task_*.txt
.taskmaster/tasks/*.backup*

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Added by Task Master AI
# Logs
logs
*.log
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Google Cloud credentials
google.json
google-key.json
*-credentials.json
*-key.json
service-account*.json
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Backup directories
.git-backup-*
*.backup
*.bak
simplechat-backup-*
*.tar.gz
*.zip

# Temporary files
temp-*
*.tmp
zikmSsKu

# PM2 logs
logs/
*.log

# Environment files (keep only .env.example)
.env
.env.local
.env.production
.env.development

# Config files with secrets
ecosystem.config.js

# Scripts with hardcoded keys (for local use only)
scripts/test-stripe-connection.js
scripts/create-stripe-products.js
scripts/fix-stripe-redirects.js
scripts/update-stripe-portal.js
scripts/fix-stripe-portal-final.js
scripts/update-stripe-settings.js

# Cursor/IDE files
.cursor/
.roo/
.roomodes
.windsurfrules

# Temporary SQL and report files
*.sql
PROMPTS/
*_REPORT.md
COMPREHENSIVE_*
batch*
debug_*
apply_mappings_*
mistral_*
alibaba_*

# Generated files (should not be in version control)
public/sitemap*.xml
public/robots.txt
src/lib/generated/model-stats.ts 

# Task files
# tasks.json
# tasks/ 
