require('dotenv').config();
const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testGoogleDirect() {
  console.log('🧪 Testing Google Generative AI direct integration...\n');

  const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);

  // Test models
  const testModels = [
    { db: 'google/gemini-1.5-flash', api: 'gemini-1.5-flash' },
    { db: 'google/gemini-2.5-flash', api: 'gemini-2.5-flash' },
    { db: 'google/gemini-2.5-pro', api: 'gemini-2.5-pro' }
  ];

  for (const modelConfig of testModels) {
    console.log(`\n📦 Testing ${modelConfig.db}...`);
    
    try {
      const model = genAI.getGenerativeModel({ 
        model: modelConfig.api,
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 100
        }
      });

      const result = await model.generateContent('Say hello in exactly 5 words.');
      const response = result.response;
      const text = response.text();
      
      console.log(`✅ Success: "${text.trim()}"`);
      console.log(`   Tokens: ${response.usageMetadata?.totalTokenCount || 'N/A'}`);
      
    } catch (error) {
      console.error(`❌ Failed: ${error.message}`);
    }
  }

  // Test with system instruction
  console.log('\n\n📦 Testing with system instruction...');
  try {
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-2.5-flash',
      systemInstruction: 'You are a helpful assistant that always responds in haiku format.',
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 100
      }
    });

    const result = await model.generateContent('What is the weather like?');
    const response = result.response;
    const text = response.text();
    
    console.log(`✅ Haiku response:\n${text.trim()}`);
    
  } catch (error) {
    console.error(`❌ Failed: ${error.message}`);
  }

  // Test with function calling
  console.log('\n\n📦 Testing function calling...');
  try {
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      tools: [{
        functionDeclarations: [{
          name: 'generate_image',
          description: 'Generate an image from a text description',
          parameters: {
            type: 'object',
            properties: {
              prompt: {
                type: 'string',
                description: 'The image description'
              }
            },
            required: ['prompt']
          }
        }]
      }]
    });

    const result = await model.generateContent('Create a picture of a sunset over mountains');
    const response = result.response;
    const functionCall = response.functionCalls();
    
    if (functionCall && functionCall.length > 0) {
      console.log(`✅ Function call detected:`);
      console.log(`   Function: ${functionCall[0].name}`);
      console.log(`   Args: ${JSON.stringify(functionCall[0].args)}`);
    } else {
      console.log(`ℹ️  No function call in response: "${response.text().trim()}"`);
    }
    
  } catch (error) {
    console.error(`❌ Failed: ${error.message}`);
  }
}

testGoogleDirect().catch(console.error);