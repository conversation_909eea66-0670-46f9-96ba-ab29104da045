# Core
NODE_ENV=development
LOG_LEVEL=debug

# URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_WS_URL=ws://localhost:3000

# Database (Future-proof)
DATABASE_URL=mysql://user:password@localhost:3306/simplechat
REDIS_URL=redis://localhost:6379

# Auth
NEXTAUTH_SECRET=generate-with-openssl-rand-base64-32
NEXTAUTH_URL=http://localhost:3000

# OAuth
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# AI Providers
OPENROUTER_API_KEY=
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
GOOGLE_API_KEY=
# Google Cloud Vertex AI (for enhanced Gemini access)
GCLOUD_PROJECT=your-gcp-project-id
GOOGLE_APPLICATION_CREDENTIALS=./google-key.json
XAI_API_KEY=
DEEPSEEK_API_KEY=
MISTRAL_API_KEY=
PERPLEXITY_API_KEY=
COHERE_API_KEY=
QWEN_API_KEY=
GROQ_API_KEY=

# Search
SERP_API_KEY=
BRAVE_SEARCH_API_KEY=

# Analytics (Future)
POSTHOG_API_KEY=
SENTRY_DSN=

# Email
SENDGRID_API_KEY=
CRON_SECRET=

# Feature Flags
ENABLE_WEB_SEARCH=true
ENABLE_VOICE_INPUT=false
ENABLE_FILE_UPLOADS=false

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Stripe Price IDs (create these in Stripe Dashboard)
# Plus Plan - £7.99/$7.99/€7.99
STRIPE_PRICE_PLUS_GBP=price_...
STRIPE_PRICE_PLUS_USD=price_...
STRIPE_PRICE_PLUS_EUR=price_...

# Advanced Plan - £19.99/$24.99/€22.99
STRIPE_PRICE_ADVANCED_GBP=price_...
STRIPE_PRICE_ADVANCED_USD=price_...
STRIPE_PRICE_ADVANCED_EUR=price_...

# Max Plan - £49.99/$59.99/€54.99
STRIPE_PRICE_MAX_GBP=price_...
STRIPE_PRICE_MAX_USD=price_...
STRIPE_PRICE_MAX_EUR=price_...

# Development/Testing
NEXT_PUBLIC_TEST_MODE=false  # Set to true to unlock all models for testing