# Router Enhancement Implementation Plan

## Quick Reference

### Key Features:
1. **Session Stickiness**: Same model for same category+complexity in conversation (80%+ cost savings)
2. **Ultra Mode**: Premium model access with button override
3. **Smart Variety**: Random selection on first use, then sticky for session
4. **Plan Filtering**: Proper JSON-based plan restrictions
5. **Prompt-Based Selection**: Router LLM analyzes prompts to determine category and complexity

### Main Changes:
- Fix broken plan filtering (JSON_CONTAINS check)
- Add `conversationId` to track sessions
- Implement 24-hour model stickiness per conversation
- Add `ultraMode` flag for premium access
- Router LLM determines optimal model based on prompt analysis

## Overview
Enhance the AI model router to support session-based model stickiness, ultra mode, proper plan filtering, variety selection, and user preferences.

## Current State Analysis

### Problems:
1. **Broken Plan Filtering**: `getCategoryModels` doesn't actually filter by user's specific plan in the `planIds` JSON array
2. **No Ultra Mode**: No support for premium model override button
3. **No Variety**: Always returns same model for identical queries
4. **Prompt Analysis**: Router LLM analyzes prompts to determine complexity and category

### Existing Structure:
- Plans: FREE, FREEMIUM, PLUS, ADVANCED, MAX, ENTERPRISE
- Router LLM analyzes prompts to determine category (coding, creative_writing, etc.) and complexity (simple, standard, complex)
- ModelPlanRules uses `planIds` JSON array for access control

## Implementation Steps

### Step 1: Fix Plan-Based Filtering
**File**: `src/lib/ai/router/optimized-redis-cache.ts`

Modify `getCategoryModels` query to properly filter by plan:
```sql
-- Add plan check to WHERE clause
AND (
  JSON_CONTAINS(mpr.planIds, JSON_QUOTE(${userPlanMapping})) 
  OR mpr.planIds IS NULL
)
```

Need to map UserPlan enum to database plan strings:
- FREE → 'plan_free'
- FREEMIUM → 'plan_freemium'
- PLUS → 'plan_plus'
- ADVANCED → 'plan_advanced'
- MAX → 'plan_max'
- ENTERPRISE → 'plan_enterprise'

### Step 2: Add Ultra Mode Support
**File**: `src/lib/ai/router/optimized-redis-cache.ts`

1. Modify `getBestModelForCategory` signature:
```typescript
export async function getBestModelForCategory(
  category: PromptCategory,
  complexity: PromptComplexity,
  userPlan: UserPlan,
  prisma: PrismaClient,
  options?: {
    ultraMode?: boolean;
    // Router determines category and complexity from prompt analysis
    recentModels?: string[];
    enableVariety?: boolean;
  }
): Promise<CategoryModel | null>
```

2. When `ultraMode` is true:
   - Temporarily use MAX plan for model selection
   - Ignore cost constraints
   - Prioritize highest quality models

### Step 3: Implement Smart Variety Selection with Session Stickiness

**Critical Insight**: Once a model is selected for a category+complexity combination, stick with it for the session to maximize context caching benefits.

#### Session-Based Model Selection:
1. **First Selection**: Random selection from top performers
2. **Subsequent Selections**: 
   - Same category + same complexity = SAME MODEL (cache benefits)
   - Same category + different complexity = NEW RANDOM MODEL
   - Different category = NEW RANDOM MODEL

#### Implementation Strategy:
```typescript
// Session model mapping
interface SessionModelMap {
  [key: string]: {
    modelId: string;
    selectedAt: Date;
    category: string;
    complexity: string;
  }
}

// Key format: `${conversationId}:${category}:${complexity}`
```

#### Benefits:
- **Context Caching**: Reusing same model saves significant costs (up to 90% on cached tokens)
- **User Experience**: Consistent responses within a conversation thread
- **Variety**: Still get different models across different conversations or complexity changes

#### Selection Logic:
```typescript
async function getModelForConversation(
  conversationId: string,
  category: PromptCategory,
  complexity: PromptComplexity,
  // ... other params
) {
  const sessionKey = `${conversationId}:${category}:${complexity}`;
  
  // Check if we already selected a model for this combo
  const existingSelection = await redis.get(`session:model:${sessionKey}`);
  if (existingSelection) {
    return JSON.parse(existingSelection);
  }
  
  // First time - do variety selection
  const selected = await selectWithVariety(/* ... */);
  
  // Store for session (24 hour TTL)
  await redis.setex(
    `session:model:${sessionKey}`,
    86400,
    JSON.stringify(selected)
  );
  
  return selected;
}
```

### Step 4: Prompt-Based Model Selection
The Router LLM analyzes each prompt to determine:
- **Category**: coding, creative_writing, reasoning, etc. (28 categories)
- **Complexity**: simple, standard, complex, difficult
- This analysis drives model selection based on the task requirements

### Step 5: Update Cache Keys and Session Management

#### New Cache Key Structure:
```typescript
// Model selection cache (5 min TTL)
BEST_MODEL: (category, complexity, plan, ultraMode, preference) => 
  `router:best:${category}:${complexity}:${plan}:${ultraMode ? 'ultra' : 'normal'}:${preference}`

// Session model stickiness (24 hour TTL)
SESSION_MODEL: (conversationId, category, complexity) =>
  `session:model:${conversationId}:${category}:${complexity}`

// Conversation metadata (24 hour TTL)
CONVERSATION_CONTEXT: (conversationId) =>
  `session:context:${conversationId}`
```

#### Cache Cost Savings Analysis:
- **Without Session Stickiness**: Each message = new model = new context
- **With Session Stickiness**: 
  - First message: Full context cost
  - Subsequent messages: ~90% discount on repeated context
  - Example: 10-message conversation saves ~$0.50-$2.00 depending on model

#### Context Tracking:
```typescript
interface ConversationContext {
  id: string;
  startedAt: Date;
  lastMessageAt: Date;
  modelSelections: {
    [categoryComplexityKey: string]: {
      model: string;
      selectedAt: Date;
      messageCount: number;
      estimatedSavings: number;
    }
  };
  totalMessages: number;
  estimatedTotalSavings: number;
}
```

## Code Changes Required

### 1. Plan Mapping Helper
```typescript
function mapUserPlanToDatabase(plan: UserPlan): string {
  const mapping = {
    [UserPlan.FREE]: 'plan_free',
    [UserPlan.FREEMIUM]: 'plan_freemium',
    [UserPlan.PLUS]: 'plan_plus',
    [UserPlan.ADVANCED]: 'plan_advanced',
    [UserPlan.MAX]: 'plan_max',
    [UserPlan.ENTERPRISE]: 'plan_enterprise'
  };
  return mapping[plan];
}
```

### 2. Enhanced Selection Logic with Session Stickiness
```typescript
// Modified getBestModelForCategory with session support
export async function getBestModelForCategory(
  category: PromptCategory,
  complexity: PromptComplexity,
  userPlan: UserPlan,
  prisma: PrismaClient,
  options?: {
    conversationId?: string;      // NEW: For session stickiness
    ultraMode?: boolean;
    // Router determines category and complexity from prompt analysis
    forceNewSelection?: boolean;  // NEW: Override stickiness
    enableVariety?: boolean;
  }
): Promise<CategoryModel | null> {
  // Check for existing session selection first
  if (options?.conversationId && !options?.forceNewSelection) {
    const sessionKey = `${options.conversationId}:${category}:${complexity}`;
    const existingSelection = await redis.get(`session:model:${sessionKey}`);
    
    if (existingSelection) {
      // Validate model still available for user's plan
      const model = JSON.parse(existingSelection);
      const isStillAvailable = await validateModelForPlan(model.id, userPlan, prisma);
      
      if (isStillAvailable) {
        // Update usage counter
        await incrementSessionUsage(options.conversationId, category, complexity);
        return model;
      }
    }
  }
  
  // Normal selection flow
  let effectivePlan = options?.ultraMode ? UserPlan.MAX : userPlan;
  const models = await getCategoryModels(category, effectivePlan, prisma, 20);
  
  // Apply complexity-based scoring
  const scoredModels = applyComplexityScoring(
    models, 
    complexity
  );
  
  // Select with variety
  const selected = options?.enableVariety 
    ? await selectWithVariety(scoredModels, options.conversationId)
    : scoredModels[0];
  
  // Store in session if conversationId provided
  if (options?.conversationId && selected) {
    const sessionKey = `${options.conversationId}:${category}:${complexity}`;
    await redis.setex(
      `session:model:${sessionKey}`,
      86400, // 24 hours
      JSON.stringify(selected)
    );
    
    // Track conversation context
    await updateConversationContext(options.conversationId, category, complexity, selected);
  }
  
  return selected;
}
```

### 3. Variety Selection Function
```typescript
function selectWithVariety(
  models: ScoredModel[], 
  recentModels: string[]
): CategoryModel {
  // Group into performance tiers
  const tiers = groupByPerformance(models);
  
  // Select appropriate tier
  const selectedTier = selectTier(tiers, complexity);
  
  // Filter out recent models
  const available = selectedTier.filter(m => 
    !recentModels.slice(0, 3).includes(m.canonicalName)
  );
  
  // Weighted random selection
  return weightedRandomSelect(available.length > 0 ? available : selectedTier);
}
```

## Session Stickiness Rules

### When to KEEP Same Model:
1. Same conversation + same category + same complexity
2. Follow-up questions in same thread
3. Clarifications or refinements
4. Within 24-hour window

### When to SELECT New Model:
1. Different conversation/thread
2. Change in complexity (simple → complex)
3. Change in category (coding → creative_writing)
4. User explicitly requests different model
5. After 24-hour session expiry
6. If selected model no longer available for plan

### Cache Cost Benefits:
```
Example: 10-message coding conversation with GPT-4o
- Without stickiness: 10 × $2.50/1M = $25.00 per million tokens
- With stickiness: 1 × $2.50 + 9 × $0.25 = $4.75 per million tokens
- Savings: 81% reduction in context costs
```

## Testing Plan

1. **Plan Filtering**: 
   - Test each plan tier (FREE, PLUS, MAX) sees correct models
   - Verify planIds JSON filtering works correctly

2. **Ultra Mode**: 
   - Verify MAX plan models available with ultra button
   - Test cost override behavior

3. **Session Stickiness**:
   - Same conversation keeps same model
   - Complexity change triggers new selection
   - Category change triggers new selection
   - 24-hour expiry works correctly

4. **Variety**: 
   - Different conversations get different models
   - Weighted selection favors top performers

5. **Complexity-Based Selection**: 
   - Test simple (cost-optimized)
   - Test standard (balanced)
   - Test complex (quality-optimized)

6. **Performance**: 
   - Session lookups are fast (<5ms)
   - Cache hit rates are high (>90%)
   - Context savings are tracked

## Migration Notes

- No database schema changes required
- Cache will auto-invalidate with new key structure
- Backward compatible - old calls still work

## Monitoring & Metrics

### Key Metrics to Track:
1. **Cache Hit Rate**: Session model lookups (target: >90%)
2. **Cost Savings**: Estimated $ saved from context caching
3. **Model Distribution**: Which models are being selected
4. **Session Duration**: How long conversations last
5. **Complexity Changes**: How often users switch complexity
6. **Ultra Mode Usage**: % of requests using premium override

### Implementation:
```typescript
interface RouterMetrics {
  sessionCacheHits: number;
  sessionCacheMisses: number;
  estimatedSavings: number;
  modelSelectionCounts: Record<string, number>;
  avgSessionDuration: number;
  complexityChanges: number;
  ultraModeRequests: number;
  complexityDistribution: Record<string, number>;
}

// Log metrics to Redis with hourly aggregation
async function trackRouterMetric(metric: keyof RouterMetrics, value: number) {
  const hourKey = `metrics:router:${new Date().toISOString().slice(0, 13)}`;
  await redis.hincrby(hourKey, metric, value);
  await redis.expire(hourKey, 86400 * 7); // Keep for 7 days
}
```

## Timeline

1. Fix plan filtering bug (1 hour)
2. Add session stickiness (3 hours) 
3. Implement ultra mode support (2 hours)
4. Add variety selection (2 hours)
5. Implement complexity-based scoring (2 hours)
6. Testing and refinement (3 hours)
7. Monitoring implementation (1 hour)

Total: ~14 hours of implementation

## Summary of Benefits

1. **Cost Reduction**: 80%+ savings on context costs through session stickiness
2. **Better UX**: Consistent model within conversations
3. **Plan Compliance**: Proper filtering ensures users see only allowed models  
4. **Premium Access**: Ultra mode enables temporary access to best models
5. **Smart Selection**: Router LLM intelligently selects models based on prompt requirements
6. **Variety**: Different conversations get different models for exploration