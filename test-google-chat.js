require('dotenv').config();

async function testGoogleChat() {
  console.log('🧪 Testing Google AI through chat API...\n');

  const testMessages = [
    {
      role: 'user',
      content: 'What is 2+2? Answer in exactly one word.'
    }
  ];

  const testModels = [
    'google/gemini-1.5-flash',
    'google/gemini-2.5-flash',
    'google/gemini-2.5-pro'
  ];

  for (const model of testModels) {
    console.log(`\n📦 Testing ${model}...`);
    
    try {
      const response = await fetch('http://localhost:3004/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-debug-localhost': 'true'
        },
        body: JSON.stringify({
          messages: testMessages,
          model: model,
          stream: true,
          temperature: 0.7,
          conversationId: 'test-google-' + Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      let tokenCount = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.choices?.[0]?.delta?.content) {
                const content = parsed.choices[0].delta.content;
                fullResponse += content;
                tokenCount++;
                process.stdout.write(content);
              }
            } catch (e) {
              // Ignore parse errors
            }
          }
        }
      }

      console.log(`\n✅ Response: "${fullResponse.trim()}"`);
      console.log(`   Tokens: ~${tokenCount}`);
      console.log(`   Success: Google AI direct integration working!`);

    } catch (error) {
      console.error(`❌ Failed: ${error.message}`);
      if (error.stack) {
        console.error('   Stack:', error.stack.split('\n').slice(1, 3).join('\n'));
      }
    }
  }

  // Test with function calling
  console.log('\n\n📦 Testing Google AI with function calling...');
  try {
    const response = await fetch('http://localhost:3004/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-debug-localhost': 'true'
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Create a picture of a sunset over mountains'
          }
        ],
        model: 'google/gemini-2.5-pro',
        stream: false,
        temperature: 0.7,
        conversationId: 'test-google-functions-' + Date.now()
      })
    });

    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error(`❌ Function calling test failed: ${error.message}`);
  }
}

testGoogleChat().catch(console.error);