/**
 * Cohere Models Seed Script
 * 
 * Populates the AIModel table with Cohere models
 * Cohere provides enterprise-focused models with citations and grounded outputs
 * Run with: npx tsx prisma/seeds/cohere.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const cohereModels: Prisma.AIModelCreateInput[] = [
  // Command R+ Series
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/command-r-plus-08-2024',
    displayName: 'Command R+ (08-2024)',
    family: 'command-r-plus',
    generation: 'aug-2024',
    generation: '08-2024',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 2.5, // $2.50 per 1M tokens
    outputCostPer1M: 10, // $10 per 1M tokens
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true,
      structured_output: true,
      code_generation: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 8,
    detailedInfo: 'Flagship model with 128K context, RAG optimization, citations, and tool use',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 54
  },
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/command-r-plus',
    displayName: 'Command R+',
    family: 'command-r-plus',
    generation: 'standard',
    generation: 'latest',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true,
      structured_output: true,
      code_generation: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 8,
    detailedInfo: 'Latest Command R+ with enhanced capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 55
  },

  // Command R Series
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/command-r-08-2024',
    displayName: 'Command R (08-2024)',
    family: 'command-r',
    generation: 'aug-2024',
    generation: '08-2024',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.15, // $0.15 per 1M tokens
    outputCostPer1M: 0.6, // $0.60 per 1M tokens
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 7,
    detailedInfo: 'Efficient model with RAG optimization and 128K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 56
  },
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/command-r',
    displayName: 'Command R',
    family: 'command-r',
    generation: 'standard',
    generation: 'latest',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 6,
    detailedInfo: 'Latest Command R with RAG capabilities',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 57
  },

  // Command Series (Legacy)
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/command',
    displayName: 'Command',
    family: 'command',
    generation: 'standard',
    generation: 'latest',
    contextWindow: 4096,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 1,
    outputCostPer1M: 2,
    capabilities: {
      chat: true,
      text_generation: true,
      summarization: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Legacy Command model with 4K context',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 58
  },
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/command-light',
    displayName: 'Command Light',
    family: 'command',
    generation: 'light',
    generation: 'latest',
    contextWindow: 4096,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.3,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      text_generation: true,
      lightweight: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Lightweight version of Command model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 59
  },

  // Embedding Models
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/embed-english-v3.0',
    displayName: 'Embed English v3.0',
    family: 'embed',
    generation: 'v3.0',
    generation: 'english',
    contextWindow: 512,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 0.1, // $0.10 per 1M tokens
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      semantic_search: true,
      compression_aware: true,
      input_type_selection: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'English embedding model with 1024 dimensions and compression awareness',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 104
  },
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/embed-multilingual-v3.0',
    displayName: 'Embed Multilingual v3.0',
    family: 'embed',
    generation: 'v3.0',
    generation: 'multilingual',
    contextWindow: 512,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 0.1,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      semantic_search: true,
      multilingual: true,
      compression_aware: true,
      input_type_selection: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Multilingual embedding model supporting 100+ languages',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 105
  },
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/embed-english-light-v3.0',
    displayName: 'Embed English Light v3.0',
    family: 'embed',
    generation: 'v3.0-light',
    generation: 'english',
    contextWindow: 512,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 0.02,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      lightweight: true,
      semantic_search: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Lightweight English embedding model with 384 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 106
  },
  {
    provider: { connect: { id: "cohere" } },
    canonicalName: 'cohere/embed-multilingual-light-v3.0',
    displayName: 'Embed Multilingual Light v3.0',
    family: 'embed',
    generation: 'v3.0-light',
    generation: 'multilingual',
    contextWindow: 512,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 0.02,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      lightweight: true,
      multilingual: true,
      semantic_search: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Lightweight multilingual embedding model with 384 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 107
  }
];

async function seedCohereModels() {
  console.log('🌱 Seeding Cohere models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of cohereModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Cohere models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${cohereModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Cohere models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedCohereModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedCohereModels, cohereModels };