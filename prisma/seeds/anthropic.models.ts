/**
 * Anthropic Models Seed Script
 * 
 * Populates the AIModel table with Anthropic models
 * Run with: npx tsx prisma/seeds/anthropic.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const anthropicModels: Prisma.AIModelCreateInput[] = [
  // Claude 3.5 Series (Latest)
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-3.5-sonnet',
    displayName: 'Claude 3.5 Sonnet',
    family: 'claude-3.5',
    generation: 'sonnet',
    generation: '20241022',
    contextWindow: 200000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true,
      latex: true,
      tool_use: true,
      computer_use: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Most advanced Claude model with 200K context, vision, tool use, and computer use capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 11
  },
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-3.5-haiku',
    displayName: 'Claude 3.5 Haiku',
    family: 'claude-3.5',
    generation: 'haiku',
    generation: '20241022',
    contextWindow: 200000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.8,
    outputCostPer1M: 4,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true,
      tool_use: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Fast and cost-effective Claude 3.5 model with vision and 200K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 21
  },

  // Claude 3 Series
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-3-opus',
    displayName: 'Claude 3 Opus',
    family: 'claude-3',
    generation: 'opus',
    generation: '20240229',
    contextWindow: 200000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 15,
    outputCostPer1M: 75,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true,
      complex_reasoning: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Most capable Claude 3 model for complex tasks and reasoning',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 12
  },
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-3-sonnet',
    displayName: 'Claude 3 Sonnet',
    family: 'claude-3',
    generation: 'sonnet',
    generation: '20240229',
    contextWindow: 200000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Balanced Claude 3 model for most tasks',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 22
  },
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-3-haiku',
    displayName: 'Claude 3 Haiku',
    family: 'claude-3',
    generation: 'haiku',
    generation: '20240307',
    contextWindow: 200000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.25,
    outputCostPer1M: 1.25,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Fast and affordable Claude 3 model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 31
  },

  // Claude Instant (Legacy)
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-instant-1.2',
    displayName: 'Claude Instant 1.2',
    family: 'claude-instant',
    generation: '1.2',
    generation: '1.2',
    contextWindow: 100000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.8,
    outputCostPer1M: 2.4,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Legacy fast model for simple tasks',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 41
  },

  // Claude 2 Series (Legacy)
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-2.1',
    displayName: 'Claude 2.1',
    family: 'claude-2',
    generation: '2.1',
    generation: '2.1',
    contextWindow: 200000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 8,
    outputCostPer1M: 24,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Legacy Claude 2 with 200K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: false, // Deprecated
    isEnabled: false,
    routerIndex: 51
  },
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-2.0',
    displayName: 'Claude 2.0',
    family: 'claude-2',
    generation: '2.0',
    generation: '2.0',
    contextWindow: 100000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 8,
    outputCostPer1M: 24,
    capabilities: {
      chat: true,
      code: true,
      analysis: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Original Claude 2 model',
    qualityTier: 'BALANCED' as any,
    isEnabled: false, // Deprecated
    isEnabled: false,
    routerIndex: 52
  },

  // Claude 1 (Legacy - for historical reference)
  {
    provider: { connect: { id: "anthropic" } },
    canonicalName: 'anthropic/claude-1.3',
    displayName: 'Claude 1.3',
    family: 'claude-1',
    generation: '1.3',
    generation: '1.3',
    contextWindow: 100000,
    maxOutput: 2048,
    maxOutput: 2048,
    inputCostPer1M: 11.02,
    outputCostPer1M: 32.68,
    capabilities: {
      chat: true,
      code: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 3,
    detailedInfo: 'Original Claude model (deprecated)',
    qualityTier: 'STANDARD' as any,
    isEnabled: false, // Deprecated
    isEnabled: false,
    routerIndex: 61
  }
];

async function seedAnthropicModels() {
  console.log('🌱 Seeding Anthropic models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of anthropicModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Anthropic models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${anthropicModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Anthropic models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedAnthropicModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedAnthropicModels, anthropicModels };