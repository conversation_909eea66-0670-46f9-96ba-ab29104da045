/**
 * Mistral Models Seed Script
 * 
 * Populates the AIModel table with Mistral models
 * Run with: npx tsx prisma/seeds/mistral.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const mistralModels: Prisma.AIModelCreateInput[] = [
  // Mistral Large Series
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/mistral-large-latest',
    displayName: 'Mistral Large (Latest)',
    family: 'mistral-large',
    generation: 'latest',
    generation: '2411',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 2, // €2 per 1M tokens
    outputCostPer1M: 6, // €6 per 1M tokens
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      functions: true,
      multilingual: true,
      json_mode: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Flagship model with 128K context, function calling, and JSON mode',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 14
  },
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/mistral-large-2411',
    displayName: 'Mistral Large 2411',
    family: 'mistral-large',
    generation: 'large',
    generation: '2411',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 2,
    outputCostPer1M: 6,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      functions: true,
      multilingual: true,
      json_mode: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'November 2024 version of Mistral Large',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 15
  },

  // Codestral Series (Code Specialist)
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/codestral-latest',
    displayName: 'Codestral (Latest)',
    family: 'codestral',
    generation: 'latest',
    generation: '2405',
    contextWindow: 32768,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.2, // €0.2 per 1M tokens
    outputCostPer1M: 0.6, // €0.6 per 1M tokens
    capabilities: {
      code: true,
      completion: true,
      debugging: true,
      refactoring: true,
      fill_in_middle: true,
      code_review: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 7,
    detailedInfo: 'Specialized 22B code model with fill-in-the-middle and 32K context',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 16
  },
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/codestral-mamba-latest',
    displayName: 'Codestral Mamba (Latest)',
    family: 'codestral',
    generation: 'mamba',
    generation: 'latest',
    contextWindow: 256000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.2,
    outputCostPer1M: 0.6,
    capabilities: {
      code: true,
      completion: true,
      long_context: true,
      mamba_architecture: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 6,
    detailedInfo: 'Mamba architecture code model with 256K context window',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 26
  },

  // Mistral Small Series
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/mistral-small-latest',
    displayName: 'Mistral Small (Latest)',
    family: 'mistral-small',
    generation: 'latest',
    generation: '2409',
    contextWindow: 32768,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.2,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      code: true,
      functions: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Efficient small model with function calling',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 32
  },

  // Mixtral Series (MoE)
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/mixtral-8x7b-instruct',
    displayName: 'Mixtral 8x7B Instruct',
    family: 'mixtral',
    generation: '8x7b',
    generation: 'v0.1',
    contextWindow: 32768,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.24,
    outputCostPer1M: 0.24,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      moe: true // Mixture of Experts
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Mixture of Experts model with 8x7B parameters',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 24
  },
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/mixtral-8x22b-instruct',
    displayName: 'Mixtral 8x22B Instruct',
    family: 'mixtral',
    generation: '8x22b',
    generation: 'v0.1',
    contextWindow: 65536,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.9,
    outputCostPer1M: 2.7,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      moe: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Large MoE model with 8x22B parameters and 64K context',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 17
  },

  // Pixtral (Vision Model)
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/pixtral-12b-latest',
    displayName: 'Pixtral 12B (Latest)',
    family: 'pixtral',
    generation: '12b',
    generation: 'latest',
    contextWindow: 128000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.15,
    outputCostPer1M: 0.15,
    capabilities: {
      chat: true,
      vision: true,
      multimodal: true,
      image_understanding: true,
      ocr: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Vision-language model with 128K context and image understanding',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 25
  },

  // Base Models
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/mistral-7b-instruct',
    displayName: 'Mistral 7B Instruct',
    family: 'mistral',
    generation: '7b',
    generation: 'v0.3',
    contextWindow: 32768,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.1,
    outputCostPer1M: 0.1,
    capabilities: {
      chat: true,
      code: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Efficient 7B parameter model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 34
  },
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/ministral-3b-latest',
    displayName: 'Ministral 3B (Latest)',
    family: 'ministral',
    generation: '3b',
    generation: 'latest',
    contextWindow: 128000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.04,
    outputCostPer1M: 0.04,
    capabilities: {
      chat: true,
      lightweight: true,
      edge_deployment: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 3,
    detailedInfo: 'Ultra-lightweight 3B model for edge deployment',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 44
  },
  {
    provider: { connect: { id: "mistral" } },
    canonicalName: 'mistral/ministral-8b-latest',
    displayName: 'Ministral 8B (Latest)',
    family: 'ministral',
    generation: '8b',
    generation: 'latest',
    contextWindow: 128000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.1,
    outputCostPer1M: 0.1,
    capabilities: {
      chat: true,
      code: true,
      lightweight: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Lightweight 8B model with 128K context',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 35
  }
];

async function seedMistralModels() {
  console.log('🌱 Seeding Mistral models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of mistralModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Mistral models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${mistralModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Mistral models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedMistralModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedMistralModels, mistralModels };