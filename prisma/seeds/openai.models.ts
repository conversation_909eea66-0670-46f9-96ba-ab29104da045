/**
 * OpenAI Models Seed Script
 * 
 * Populates the AIModel table with OpenAI models
 * Run with: npx tsx prisma/seeds/openai.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const openAIModels: Prisma.AIModelCreateInput[] = [
  // GPT-4o Series
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/gpt-4o',
    displayName: 'GPT-4o',
    family: 'gpt-4o',
    generation: 'latest',
    generation: '2024-11-20',
    contextWindow: 128000,
    maxOutput: 16384,
    maxOutput: 16384,
    inputCostPer1M: 2.5,
    outputCostPer1M: 10,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      json_mode: true,
      structured_output: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Most advanced OpenAI model with vision, function calling, JSON mode, and structured output',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 10
  },
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/gpt-4o-mini',
    displayName: 'GPT-4o Mini',
    family: 'gpt-4o',
    generation: 'mini',
    generation: '2024-07-18',
    contextWindow: 128000,
    maxOutput: 16384,
    maxOutput: 16384,
    inputCostPer1M: 0.15,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      json_mode: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Fast and cost-effective GPT-4 level model with vision support',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 20
  },

  // O-series Reasoning Models
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/o1',
    displayName: 'O1',
    family: 'o-series',
    generation: 'o1',
    generation: '2024-12-17',
    contextWindow: 200000,
    maxOutput: 100000,
    maxOutput: 100000,
    inputCostPer1M: 15,
    outputCostPer1M: 60,
    capabilities: {
      reasoning: true,
      code: true,
      analysis: true,
      math: true,
      science: true,
      step_by_step: true,
      chain_of_thought: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 9,
    detailedInfo: 'Advanced reasoning model for complex problem solving with chain-of-thought capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 5
  },
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/o1-mini',
    displayName: 'O1 Mini',
    family: 'o-series',
    generation: 'o1-mini',
    generation: '2024-09-12',
    contextWindow: 128000,
    maxOutput: 65536,
    maxOutput: 65536,
    inputCostPer1M: 3,
    outputCostPer1M: 12,
    capabilities: {
      reasoning: true,
      code: true,
      math: true,
      analysis: true,
      step_by_step: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 7,
    detailedInfo: 'Efficient reasoning model for STEM tasks',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 6
  },
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/o3-mini',
    displayName: 'O3 Mini',
    family: 'o-series',
    generation: 'o3-mini',
    generation: '2025-01-01',
    contextWindow: 200000,
    maxOutput: 100000,
    maxOutput: 100000,
    inputCostPer1M: 1.1,
    outputCostPer1M: 4.4,
    capabilities: {
      reasoning: true,
      code: true,
      math: true,
      science: true,
      planning: true,
      step_by_step: true,
      chain_of_thought: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Latest efficient reasoning model with planning capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 4
  },

  // GPT-4 Turbo
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/gpt-4-turbo',
    displayName: 'GPT-4 Turbo',
    family: 'gpt-4',
    generation: 'turbo',
    generation: '2024-04-09',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 10,
    outputCostPer1M: 30,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      analysis: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Previous generation GPT-4 with vision capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 15
  },

  // GPT-3.5 Turbo
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/gpt-3.5-turbo',
    displayName: 'GPT-3.5 Turbo',
    family: 'gpt-3.5',
    generation: 'turbo',
    generation: '0125',
    contextWindow: 16384,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      code: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Fast and efficient model for chat applications',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 30
  },

  // Embedding Models
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/text-embedding-3-small',
    displayName: 'Text Embedding 3 Small',
    family: 'text-embedding',
    generation: '3',
    generation: 'small',
    contextWindow: 8191,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 0.02,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Small and efficient embedding model with 1536 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 100
  },
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/text-embedding-3-large',
    displayName: 'Text Embedding 3 Large',
    family: 'text-embedding',
    generation: '3',
    generation: 'large',
    contextWindow: 8191,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 0.13,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Large and powerful embedding model with 3072 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 101
  },

  // Speech Models
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/tts-1',
    displayName: 'TTS 1',
    family: 'tts',
    generation: '1',
    generation: 'latest',
    contextWindow: 4096,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 15, // per 1M characters
    outputCostPer1M: 0,
    capabilities: {
      speech: true,
      tts: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Text-to-speech model with multiple voices',
    qualityTier: 'SPEECH' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 110
  },
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/whisper-1',
    displayName: 'Whisper 1',
    family: 'whisper',
    generation: '1',
    generation: 'latest',
    contextWindow: 0,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 6, // per hour
    outputCostPer1M: 0,
    capabilities: {
      speech: true,
      stt: true,
      transcription: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: true,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Speech-to-text model for transcription',
    qualityTier: 'SPEECH' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 111
  },

  // Image Generation Models
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/dall-e-3',
    displayName: 'DALL-E 3',
    family: 'dall-e',
    generation: '3',
    generation: 'latest',
    contextWindow: 4000,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 40, // per image
    outputCostPer1M: 0,
    capabilities: {
      image_generation: true,
      creative: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Advanced image generation model',
    qualityTier: 'IMAGE' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 120
  },
  {
    provider: { connect: { id: "openai" } },
    canonicalName: 'openai/dall-e-2',
    displayName: 'DALL-E 2',
    family: 'dall-e',
    generation: '2',
    generation: 'latest',
    contextWindow: 1000,
    maxOutput: 0,
    maxOutput: 0,
    inputCostPer1M: 20, // per image
    outputCostPer1M: 0,
    capabilities: {
      image_generation: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Previous generation image generation model',
    qualityTier: 'IMAGE' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 121
  }
];

async function seedOpenAIModels() {
  console.log('🌱 Seeding OpenAI models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of openAIModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ OpenAI models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${openAIModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding OpenAI models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedOpenAIModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedOpenAIModels, openAIModels };