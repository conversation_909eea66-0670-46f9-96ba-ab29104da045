/**
 * Perplexity Models Seed Script
 * 
 * Populates the AIModel table with Perplexity models
 * Perplexity provides web-search enhanced AI models
 * Run with: npx tsx prisma/seeds/perplexity.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const perplexityModels: Prisma.AIModelCreateInput[] = [
  // Sonar Pro Models
  {
    provider: { connect: { id: "perplexity" } },
    canonicalName: 'perplexity/sonar-pro',
    displayName: 'Sonar Pro',
    family: 'sonar',
    generation: 'pro',
    generation: 'latest',
    contextWindow: 300000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 3, // $3 per 1M tokens
    outputCostPer1M: 15, // $15 per 1M tokens
    capabilities: {
      chat: true,
      web_search: true,
      real_time_knowledge: true,
      citations: true,
      source_attribution: true,
      current_events: true,
      factual_accuracy: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Advanced model with real-time web search, citations, and 300K context window',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 51
  },
  
  // Sonar Models
  {
    provider: { connect: { id: "perplexity" } },
    canonicalName: 'perplexity/sonar',
    displayName: 'Sonar',
    family: 'sonar',
    generation: 'standard',
    generation: 'latest',
    contextWindow: 200000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 1, // $1 per 1M tokens
    outputCostPer1M: 1, // $1 per 1M tokens
    capabilities: {
      chat: true,
      web_search: true,
      real_time_knowledge: true,
      citations: true,
      source_attribution: true,
      current_events: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 7,
    detailedInfo: 'Standard model with web search integration and 200K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 52
  },

  // Related Models (Mistral-based)
  {
    provider: { connect: { id: "perplexity" } },
    canonicalName: 'perplexity/related',
    displayName: 'Related',
    family: 'related',
    generation: 'standard',
    generation: 'latest',
    contextWindow: 4096,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.2, // $0.2 per 1M tokens
    outputCostPer1M: 0.2, // $0.2 per 1M tokens
    capabilities: {
      chat: true,
      related_questions: true,
      query_expansion: true,
      search_suggestions: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Specialized model for generating related questions and search suggestions',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 53
  }
];

async function seedPerplexityModels() {
  console.log('🌱 Seeding Perplexity models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of perplexityModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Perplexity models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${perplexityModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Perplexity models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedPerplexityModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedPerplexityModels, perplexityModels };