/**
 * Master Seed <PERSON>ript
 * 
 * Runs all provider seed scripts to populate the AIModel table
 * Run with: npx tsx prisma/seeds/index.ts
 * Or specific provider: npx tsx prisma/seeds/index.ts openai
 */

import { seedOpenAIModels } from './openai.models';
import { seedAnthropicModels } from './anthropic.models';
import { seedGoogleModels } from './google.models';
import { seedMistralModels } from './mistral.models';
import { seedGroqModels } from './groq.models';
import { seedTogetherModels } from './together.models';
import { seedXAIModels } from './xai.models';
import { seedDeepSeekModels } from './deepseek.models';
import { seedPerplexityModels } from './perplexity.models';
import { seedCohereModels } from './cohere.models';
import { seedOpenRouterModels } from './openrouter.models';
import { seedQwenModels } from './qwen.models';

interface SeedFunction {
  name: string;
  fn: () => Promise<void>;
}

const seedFunctions: SeedFunction[] = [
  { name: 'openai', fn: seedOpenAIModels },
  { name: 'anthropic', fn: seedAnthropicModels },
  { name: 'google', fn: seedGoogleModels },
  { name: 'mistral', fn: seedMistralModels },
  { name: 'groq', fn: seedGroqModels },
  { name: 'together', fn: seedTogetherModels },
  { name: 'xai', fn: seedXAIModels },
  { name: 'deepseek', fn: seedDeepSeekModels },
  { name: 'perplexity', fn: seedPerplexityModels },
  { name: 'cohere', fn: seedCohereModels },
  { name: 'openrouter', fn: seedOpenRouterModels },
  { name: 'qwen', fn: seedQwenModels }
];

async function seedAllProviders() {
  console.log('🌱 Starting master seed process...\n');
  
  const results: { provider: string; success: boolean; error?: any }[] = [];
  
  for (const { name, fn } of seedFunctions) {
    console.log(`\n📦 Seeding ${name} models...`);
    console.log('─'.repeat(50));
    
    try {
      await fn();
      results.push({ provider: name, success: true });
    } catch (error) {
      console.error(`❌ Failed to seed ${name}:`, error);
      results.push({ provider: name, success: false, error });
    }
  }
  
  // Summary
  console.log('\n' + '═'.repeat(50));
  console.log('📊 Seed Summary:');
  console.log('─'.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  if (successful.length > 0) {
    console.log('\n✅ Successful:');
    successful.forEach(r => console.log(`   - ${r.provider}`));
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed:');
    failed.forEach(r => console.log(`   - ${r.provider}: ${r.error?.message || 'Unknown error'}`));
  }
  
  console.log(`\n📈 Total: ${successful.length}/${results.length} providers seeded successfully`);
}

async function seedSpecificProvider(providerName: string) {
  const seedFunction = seedFunctions.find(s => s.name === providerName);
  
  if (!seedFunction) {
    console.error(`❌ Unknown provider: ${providerName}`);
    console.log('\nAvailable providers:');
    seedFunctions.forEach(s => console.log(`  - ${s.name}`));
    process.exit(1);
  }
  
  console.log(`🌱 Seeding ${providerName} models...`);
  console.log('─'.repeat(50));
  
  try {
    await seedFunction.fn();
    console.log(`\n✅ ${providerName} models seeded successfully!`);
  } catch (error) {
    console.error(`\n❌ Failed to seed ${providerName}:`, error);
    process.exit(1);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // Seed all providers
    await seedAllProviders();
  } else if (args[0] === '--help' || args[0] === '-h') {
    console.log(`
AI Model Seeder

Usage:
  npx tsx prisma/seeds/index.ts              # Seed all providers
  npx tsx prisma/seeds/index.ts <provider>   # Seed specific provider
  npx tsx prisma/seeds/index.ts --help       # Show this help

Available providers:
${seedFunctions.map(s => `  - ${s.name}`).join('\n')}

Examples:
  npx tsx prisma/seeds/index.ts              # Seed all models
  npx tsx prisma/seeds/index.ts openai       # Seed only OpenAI models
  npx tsx prisma/seeds/index.ts anthropic    # Seed only Anthropic models
    `);
    process.exit(0);
  } else {
    // Seed specific provider
    await seedSpecificProvider(args[0]);
  }
}

// Run if called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Unhandled error:', error);
      process.exit(1);
    });
}

export { seedAllProviders, seedSpecificProvider };