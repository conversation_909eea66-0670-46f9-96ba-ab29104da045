/**
 * OpenRouter Models Seed Script
 * 
 * Populates the AIModel table with OpenRouter models
 * OpenRouter provides unified access to multiple AI providers with auto-routing
 * Run with: npx tsx prisma/seeds/openrouter.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const openrouterModels: Prisma.AIModelCreateInput[] = [
  // Auto-Routing Models
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/auto',
    displayName: 'Auto (Best Available)',
    family: 'auto',
    generation: 'router',
    generation: 'latest',
    contextWindow: 200000,
    maxOutput: 100000,
    maxOutput: 100000,
    inputCostPer1M: 0, // Varies by selected model
    outputCostPer1M: 0, // Varies by selected model
    capabilities: {
      auto_routing: true,
      best_available: true,
      chat: true,
      code: true,
      vision: true,
      functions: true,
      dynamic_selection: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Automatically routes to the best available model based on request',
    qualityTier: 'AUTO' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 1
  },

  // Anthropic Models via OpenRouter
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/anthropic/claude-3.5-sonnet',
    displayName: 'Claude 3.5 Sonnet (OpenRouter)',
    family: 'claude-3.5',
    generation: 'sonnet',
    generation: '********',
    contextWindow: 200000,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      analysis: true,
      functions: true,
      caching: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Claude 3.5 Sonnet via OpenRouter with 200K context',
    qualityTier: 'FLAGSHIP' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 60
  },
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/anthropic/claude-3-opus',
    displayName: 'Claude 3 Opus (OpenRouter)',
    family: 'claude-3',
    generation: 'opus',
    generation: '20240229',
    contextWindow: 200000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 15,
    outputCostPer1M: 75,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      analysis: true,
      creative_writing: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Most capable Claude 3 model via OpenRouter',
    qualityTier: 'FLAGSHIP' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 61
  },

  // OpenAI Models via OpenRouter
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/openai/gpt-4o',
    displayName: 'GPT-4o (OpenRouter)',
    family: 'gpt-4',
    generation: '4o',
    generation: '2024-11-20',
    contextWindow: 128000,
    maxOutput: 16384,
    maxOutput: 16384,
    inputCostPer1M: 2.5,
    outputCostPer1M: 10,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      json_mode: true,
      structured_output: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'GPT-4o with vision via OpenRouter',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 62
  },
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/openai/o1-preview',
    displayName: 'O1 Preview (OpenRouter)',
    family: 'o1',
    generation: 'preview',
    generation: '2024-09-12',
    contextWindow: 128000,
    maxOutput: 32768,
    maxOutput: 32768,
    inputCostPer1M: 15,
    outputCostPer1M: 60,
    capabilities: {
      reasoning: true,
      chat: true,
      code: true,
      mathematics: true,
      chain_of_thought: true,
      step_by_step: true
    },
    supportsStreaming: false,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 10,
    detailedInfo: 'Advanced reasoning model via OpenRouter',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 63
  },

  // Google Models via OpenRouter
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/google/gemini-pro-1.5',
    displayName: 'Gemini 1.5 Pro (OpenRouter)',
    family: 'gemini-1.5',
    generation: 'pro',
    generation: 'latest',
    contextWindow: 2097152,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 2.5,
    outputCostPer1M: 10,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      audio: true,
      video: true,
      multimodal: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: true,
    supportsAudio: true,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Gemini 1.5 Pro with 2M context via OpenRouter',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 64
  },

  // Meta Models via OpenRouter
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/meta-llama/llama-3.3-70b-instruct',
    displayName: 'Llama 3.3 70B (OpenRouter)',
    family: 'llama-3.3',
    generation: '70b',
    generation: 'instruct',
    contextWindow: 131072,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.7,
    outputCostPer1M: 0.8,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Latest Llama 3.3 70B via OpenRouter',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 65
  },

  // Mistral Models via OpenRouter
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/mistralai/mistral-large',
    displayName: 'Mistral Large (OpenRouter)',
    family: 'mistral-large',
    generation: 'latest',
    generation: '2411',
    contextWindow: 128000,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 3,
    outputCostPer1M: 9,
    capabilities: {
      chat: true,
      code: true,
      functions: true,
      json_mode: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Mistral Large with function calling via OpenRouter',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 66
  },

  // Specialized Models
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/neversleep/llama-3.1-lumimaid-70b',
    displayName: 'Lumimaid 70B (OpenRouter)',
    family: 'lumimaid',
    generation: '70b',
    generation: '3.1',
    contextWindow: 65536,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 3.375,
    outputCostPer1M: 4.5,
    capabilities: {
      chat: true,
      creative_writing: true,
      roleplay: true,
      uncensored: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.9,
    heatLevel: 7,
    detailedInfo: 'Specialized creative writing model',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 67
  },
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/nvidia/llama-3.1-nemotron-70b-instruct',
    displayName: 'Nemotron 70B (OpenRouter)',
    family: 'nemotron',
    generation: '70b',
    generation: '3.1',
    contextWindow: 131072,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.35,
    outputCostPer1M: 0.4,
    capabilities: {
      chat: true,
      code: true,
      instruction_following: true,
      helpfulness: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'NVIDIA optimized Llama model for helpful responses',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 68
  },

  // Free Models
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/google/gemma-2-9b-it:free',
    displayName: 'Gemma 2 9B (Free)',
    family: 'gemma-2',
    generation: '9b',
    generation: 'free',
    contextWindow: 8192,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0,
    outputCostPer1M: 0,
    capabilities: {
      chat: true,
      code: true,
      free_tier: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Free Google Gemma model',
    qualityTier: 'FREE' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 69
  },
  {
    provider: { connect: { id: "openrouter" } },
    canonicalName: 'openrouter/qwen/qwen-2-7b-instruct:free',
    displayName: 'Qwen 2 7B (Free)',
    family: 'qwen-2',
    generation: '7b',
    generation: 'free',
    contextWindow: 32768,
    maxOutput: 32768,
    maxOutput: 32768,
    inputCostPer1M: 0,
    outputCostPer1M: 0,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      free_tier: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Free Qwen model with multilingual support',
    qualityTier: 'FREE' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 70
  }
];

async function seedOpenRouterModels() {
  console.log('🌱 Seeding OpenRouter models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of openrouterModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ OpenRouter models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${openrouterModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding OpenRouter models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedOpenRouterModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedOpenRouterModels, openrouterModels };