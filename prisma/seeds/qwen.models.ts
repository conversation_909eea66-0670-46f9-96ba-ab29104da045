/**
 * <PERSON><PERSON> Models Seed Script
 * 
 * Populates the AIModel table with <PERSON><PERSON> (Alibaba Cloud) models
 * <PERSON><PERSON> provides multilingual and multimodal AI models
 * Run with: npx tsx prisma/seeds/qwen.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const qwenModels: Prisma.AIModelCreateInput[] = [
  // QwQ 32B - Reasoning Model
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwq-32b-preview',
    displayName: 'QwQ 32B Preview',
    family: 'qwq',
    generation: '32b',
    generation: 'preview',
    contextWindow: 32768,
    maxOutput: 32768,
    maxOutput: 32768,
    inputCostPer1M: 0.8, // $0.8 per 1M tokens
    outputCostPer1M: 0.8, // $0.8 per 1M tokens
    capabilities: {
      reasoning: true,
      chat: true,
      code: true,
      mathematics: true,
      step_by_step: true,
      self_reflection: true,
      deep_thinking: true,
      chain_of_thought: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 9,
    detailedInfo: 'Advanced reasoning model with deep thinking and self-reflection capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 71
  },

  // Qwen 2.5 Series
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2.5-72b-instruct',
    displayName: 'Qwen 2.5 72B Instruct',
    family: 'qwen-2.5',
    generation: '72b',
    generation: 'instruct',
    contextWindow: 131072,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.4,
    outputCostPer1M: 1.2,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      long_context: true,
      structured_generation: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Latest Qwen 2.5 flagship model with 128K context and enhanced multilingual capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 72
  },
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2.5-32b-instruct',
    displayName: 'Qwen 2.5 32B Instruct',
    family: 'qwen-2.5',
    generation: '32b',
    generation: 'instruct',
    contextWindow: 131072,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.3,
    outputCostPer1M: 0.9,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      long_context: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Balanced Qwen 2.5 model with excellent performance-cost ratio',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 73
  },
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2.5-14b-instruct',
    displayName: 'Qwen 2.5 14B Instruct',
    family: 'qwen-2.5',
    generation: '14b',
    generation: 'instruct',
    contextWindow: 131072,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.2,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      long_context: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Efficient mid-size model with 128K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 74
  },
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2.5-7b-instruct',
    displayName: 'Qwen 2.5 7B Instruct',
    family: 'qwen-2.5',
    generation: '7b',
    generation: 'instruct',
    contextWindow: 131072,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.1,
    outputCostPer1M: 0.3,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      long_context: true,
      efficient: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Lightweight model with impressive capabilities for its size',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 75
  },

  // Qwen 2.5 Coder
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2.5-coder-32b-instruct',
    displayName: 'Qwen 2.5 Coder 32B',
    family: 'qwen-2.5-coder',
    generation: '32b',
    generation: 'instruct',
    contextWindow: 32768,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.3,
    outputCostPer1M: 0.9,
    capabilities: {
      code: true,
      completion: true,
      debugging: true,
      refactoring: true,
      code_review: true,
      repository_level: true,
      fill_in_middle: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Specialized code model with repository-level understanding',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 76
  },

  // Qwen 2.5 Math
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2.5-math-72b-instruct',
    displayName: 'Qwen 2.5 Math 72B',
    family: 'qwen-2.5-math',
    generation: '72b',
    generation: 'instruct',
    contextWindow: 4096,
    maxOutput: 4096,
    maxOutput: 4096,
    inputCostPer1M: 0.4,
    outputCostPer1M: 1.2,
    capabilities: {
      mathematics: true,
      reasoning: true,
      step_by_step: true,
      proof_generation: true,
      symbolic_computation: true,
      specialized_math: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Specialized mathematics model for advanced problem solving',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 77
  },

  // Qwen VL (Vision-Language)
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2-vl-72b-instruct',
    displayName: 'Qwen 2 VL 72B',
    family: 'qwen-2-vl',
    generation: '72b',
    generation: 'instruct',
    contextWindow: 32768,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.4,
    outputCostPer1M: 1.2,
    capabilities: {
      chat: true,
      vision: true,
      multimodal: true,
      image_understanding: true,
      visual_reasoning: true,
      ocr: true,
      video_understanding: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Multimodal model with advanced vision capabilities including video understanding',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 78
  },
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2-vl-7b-instruct',
    displayName: 'Qwen 2 VL 7B',
    family: 'qwen-2-vl',
    generation: '7b',
    generation: 'instruct',
    contextWindow: 32768,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.1,
    outputCostPer1M: 0.3,
    capabilities: {
      chat: true,
      vision: true,
      multimodal: true,
      image_understanding: true,
      ocr: true,
      efficient: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Efficient multimodal model for vision-language tasks',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 79
  },

  // Qwen Audio
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen2-audio-instruct',
    displayName: 'Qwen 2 Audio',
    family: 'qwen-2-audio',
    generation: 'standard',
    generation: 'instruct',
    contextWindow: 32768,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.15,
    outputCostPer1M: 0.45,
    capabilities: {
      chat: true,
      audio: true,
      multimodal: true,
      speech_recognition: true,
      audio_understanding: true,
      voice_chat: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: false,
    supportsVision: false,
    supportsAudio: true,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Multimodal model with audio understanding capabilities',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 80
  },

  // Qwen Plus
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen-plus',
    displayName: 'Qwen Plus',
    family: 'qwen-plus',
    generation: 'enhanced',
    generation: 'latest',
    contextWindow: 131072,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      enhanced_performance: true,
      multilingual: true,
      long_context: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Enhanced version with improved performance across all tasks',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 81
  },

  // Qwen Turbo
  {
    provider: { connect: { id: "qwen" } },
    canonicalName: 'qwen/qwen-turbo',
    displayName: 'Qwen Turbo',
    family: 'qwen-turbo',
    generation: 'fast',
    generation: 'latest',
    contextWindow: 131072,
    maxOutput: 8192,
    maxOutput: 8192,
    inputCostPer1M: 0.3,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      code: true,
      fast_inference: true,
      multilingual: true,
      long_context: true
    },
    supportsStreaming: true,
    supportsFunctionCalling: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Optimized for speed while maintaining quality',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isEnabled: true,
    routerIndex: 82
  }
];

async function seedQwenModels() {
  console.log('🌱 Seeding Qwen models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of qwenModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: model.providerId } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Qwen models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${qwenModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Qwen models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedQwenModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedQwenModels, qwenModels };