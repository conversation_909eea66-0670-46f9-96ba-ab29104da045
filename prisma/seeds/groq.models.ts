/**
 * Groq Models Seed Script
 * 
 * Populates the AIModel table with Groq models
 * Hardware-accelerated inference for open-source models
 * Run with: npx tsx prisma/seeds/groq.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const groqModels: Prisma.AIModelCreateInput[] = [
  // Llama 3.3 Series (Latest)
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.3-70b-versatile',
    displayName: 'Llama 3.3 70B Versatile',
    family: 'llama-3.3',
    generation: '70b',
    modelType: 'CHAT',
    detailedInfo: 'Latest Llama 3.3 70B with 128K context and ultra-fast inference on Groq LPU',
    metadata: {
      generation: 'versatile',
      contextWindow: 131072,
      maxOutput: 32768,
      maxOutput: 32768,
      inputCostPer1M: 0.59,
      outputCostPer1M: 0.79,
      capabilities: {
        chat: true,
        code: true,
        analysis: true,
        multilingual: true,
        ultra_fast: true,
        hardware_accelerated: true
      },
      supports: {
        streaming: true,
        functions: true,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 8,
      qualityTier: 'SUPERIOR'
    },
    isEnabled: true,
    routerIndex: 18
  },
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.3-70b-specdec',
    displayName: 'Llama 3.3 70B SpecDec',
    family: 'llama-3.3',
    generation: '70b',
    modelType: 'CHAT',
    detailedInfo: 'Llama 3.3 70B with speculative decoding for even faster inference',
    metadata: {
      generation: 'specdec',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.59,
      outputCostPer1M: 0.99,
      capabilities: {
        chat: true,
        code: true,
        speculative_decoding: true,
        ultra_fast: true,
        hardware_accelerated: true
      },
      supports: {
        streaming: true,
        functions: true,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 7,
      qualityTier: 'SUPERIOR'
    },
    isEnabled: true,
    routerIndex: 28
  },

  // Llama 3.2 Series (Vision)
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.2-90b-vision-preview',
    displayName: 'Llama 3.2 90B Vision Preview',
    family: 'llama-3.2',
    generation: '90b',
    modelType: 'MULTIMODAL',
    detailedInfo: 'Multimodal Llama 3.2 90B with vision capabilities',
    metadata: {
      generation: 'vision-preview',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.9,
      outputCostPer1M: 0.9,
      capabilities: {
        chat: true,
        vision: true,
        multimodal: true,
        image_understanding: true,
        ultra_fast: true,
        hardware_accelerated: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: true,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 8,
      qualityTier: 'SUPERIOR'
    },
    isEnabled: true,
    routerIndex: 19
  },
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.2-11b-vision-preview',
    displayName: 'Llama 3.2 11B Vision Preview',
    family: 'llama-3.2',
    generation: '11b',
    modelType: 'MULTIMODAL',
    detailedInfo: 'Efficient multimodal model with vision support',
    metadata: {
      generation: 'vision-preview',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.18,
      outputCostPer1M: 0.18,
      capabilities: {
        chat: true,
        vision: true,
        multimodal: true,
        image_understanding: true,
        ultra_fast: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: true,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 6,
      qualityTier: 'BALANCED'
    },
    isEnabled: true,
    routerIndex: 29
  },
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.2-3b-preview',
    displayName: 'Llama 3.2 3B Preview',
    family: 'llama-3.2',
    generation: '3b',
    modelType: 'CHAT',
    detailedInfo: 'Lightweight 3B model for edge deployment',
    metadata: {
      generation: 'preview',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.06,
      outputCostPer1M: 0.06,
      capabilities: {
        chat: true,
        lightweight: true,
        ultra_fast: true,
        edge_deployment: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 4,
      qualityTier: 'STANDARD'
    },
    isEnabled: true,
    routerIndex: 45
  },
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.2-1b-preview',
    displayName: 'Llama 3.2 1B Preview',
    family: 'llama-3.2',
    generation: '1b',
    modelType: 'CHAT',
    detailedInfo: 'Ultra-lightweight 1B model for edge deployment',
    metadata: {
      generation: 'preview',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.04,
      outputCostPer1M: 0.04,
      capabilities: {
        chat: true,
        lightweight: true,
        ultra_fast: true,
        edge_deployment: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 3,
      qualityTier: 'STANDARD'
    },
    isEnabled: true,
    routerIndex: 46
  },

  // Llama 3.1 Series
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.1-70b-versatile',
    displayName: 'Llama 3.1 70B Versatile',
    family: 'llama-3.1',
    generation: '70b',
    modelType: 'CHAT',
    detailedInfo: 'Llama 3.1 70B with 128K context window',
    metadata: {
      generation: 'versatile',
      contextWindow: 131072,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.59,
      outputCostPer1M: 0.79,
      capabilities: {
        chat: true,
        code: true,
        analysis: true,
        multilingual: true,
        ultra_fast: true
      },
      supports: {
        streaming: true,
        functions: true,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 7,
      qualityTier: 'SUPERIOR'
    },
    isEnabled: true,
    routerIndex: 27
  },
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/llama-3.1-8b-instant',
    displayName: 'Llama 3.1 8B Instant',
    family: 'llama-3.1',
    generation: '8b',
    modelType: 'CHAT',
    detailedInfo: 'Fast 8B model optimized for instant responses',
    metadata: {
      generation: 'instant',
      contextWindow: 131072,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.05,
      outputCostPer1M: 0.08,
      capabilities: {
        chat: true,
        code: true,
        ultra_fast: true,
        instant_response: true
      },
      supports: {
        streaming: true,
        functions: true,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 5,
      qualityTier: 'STANDARD'
    },
    isEnabled: true,
    routerIndex: 36
  },

  // Mixtral Series
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/mixtral-8x7b-32768',
    displayName: 'Mixtral 8x7B',
    family: 'mixtral',
    generation: '8x7b',
    modelType: 'CHAT',
    detailedInfo: 'Mixture of Experts model with 32K context',
    metadata: {
      generation: '32768',
      contextWindow: 32768,
      maxOutput: 32768,
      maxOutput: 32768,
      inputCostPer1M: 0.24,
      outputCostPer1M: 0.24,
      capabilities: {
        chat: true,
        code: true,
        multilingual: true,
        moe: true,
        ultra_fast: true
      },
      supports: {
        streaming: true,
        functions: true,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 6,
      qualityTier: 'BALANCED'
    },
    isEnabled: true,
    routerIndex: 37
  },

  // Gemma Series
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/gemma2-9b-it',
    displayName: 'Gemma 2 9B IT',
    family: 'gemma',
    generation: '2-9b',
    modelType: 'CHAT',
    detailedInfo: 'Google Gemma 2 9B instruction-tuned model',
    metadata: {
      generation: 'it',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.2,
      outputCostPer1M: 0.2,
      capabilities: {
        chat: true,
        code: true,
        instruction_following: true,
        ultra_fast: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 5,
      qualityTier: 'STANDARD'
    },
    isEnabled: true,
    routerIndex: 38
  },
  {
    provider: { connect: { id: 'groq' } },
    canonicalName: 'groq/gemma-7b-it',
    displayName: 'Gemma 7B IT',
    family: 'gemma',
    generation: '7b',
    modelType: 'CHAT',
    detailedInfo: 'Google Gemma 7B instruction-tuned model',
    metadata: {
      generation: 'it',
      contextWindow: 8192,
      maxOutput: 8192,
      maxOutput: 8192,
      inputCostPer1M: 0.07,
      outputCostPer1M: 0.07,
      capabilities: {
        chat: true,
        code: true,
        ultra_fast: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.7,
      heatLevel: 4,
      qualityTier: 'STANDARD'
    },
    isEnabled: true,
    routerIndex: 47
  }
];

async function seedGroqModels() {
  console.log('🌱 Seeding Groq models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of groqModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              provider: { connect: { id: "groq" } },
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Groq models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${groqModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Groq models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedGroqModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedGroqModels, groqModels };