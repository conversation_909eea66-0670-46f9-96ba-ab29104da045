/**
 * Google Models Seed Script
 * 
 * Populates the AIModel table with Google/Gemini models
 * Run with: npx tsx prisma/seeds/google.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const googleModels: Prisma.AIModelCreateInput[] = [
  // Gemini 2.0 Series (Latest)
  {
    providerId: 'google',
    canonicalName: 'google/gemini-2.0-flash-exp',
    displayName: 'Gemini 2.0 Flash (Experimental)',
    family: 'gemini-2.0',
    generation: 'flash',
    version: 'experimental',
    contextTokensIn: 1000000,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0, // Free during experimental
    outputCostPer1M: 0, // Free during experimental
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      audio: true,
      video: true,
      multimodal: true,
      functions: true,
      native_code_execution: true,
      real_time_voice: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: true,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Latest Gemini 2.0 with 1M context, multimodal (audio/video/image), native code execution, and real-time voice',
    qualityTier: 'EXPERIMENTAL' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 8
  },
  {
    providerId: 'google',
    canonicalName: 'google/gemini-2.0-flash-thinking-exp',
    displayName: 'Gemini 2.0 Flash Thinking (Experimental)',
    family: 'gemini-2.0',
    generation: 'flash-thinking',
    version: 'experimental',
    contextTokensIn: 32768,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0, // Free during experimental
    outputCostPer1M: 0, // Free during experimental
    capabilities: {
      reasoning: true,
      chat: true,
      code: true,
      analysis: true,
      step_by_step: true,
      chain_of_thought: true,
      deep_thinking: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Reasoning-focused Gemini 2.0 model with enhanced thinking capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 7
  },

  // Gemini 1.5 Series
  {
    providerId: 'google',
    canonicalName: 'google/gemini-1.5-pro',
    displayName: 'Gemini 1.5 Pro',
    family: 'gemini-1.5',
    generation: 'pro',
    version: '002',
    contextTokensIn: 2000000,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 1.25, // $1.25 per 1M tokens (up to 128K)
    outputCostPer1M: 5,   // $5 per 1M tokens
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      audio: true,
      video: true,
      multimodal: true,
      functions: true,
      json_mode: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: true,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Advanced model with 2M context window, multimodal capabilities, and function calling',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 13
  },
  {
    providerId: 'google',
    canonicalName: 'google/gemini-1.5-flash',
    displayName: 'Gemini 1.5 Flash',
    family: 'gemini-1.5',
    generation: 'flash',
    version: '002',
    contextTokensIn: 1000000,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0.075, // $0.075 per 1M tokens (up to 128K)
    outputCostPer1M: 0.3,   // $0.30 per 1M tokens
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      audio: true,
      video: true,
      multimodal: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: true,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Fast and efficient multimodal model with 1M context window',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 23
  },
  {
    providerId: 'google',
    canonicalName: 'google/gemini-1.5-flash-8b',
    displayName: 'Gemini 1.5 Flash 8B',
    family: 'gemini-1.5',
    generation: 'flash-8b',
    version: '001',
    contextTokensIn: 1000000,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0.0375, // $0.0375 per 1M tokens
    outputCostPer1M: 0.15,   // $0.15 per 1M tokens
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      multimodal: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Lightweight 8B parameter model with multimodal support',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 33
  },

  // Gemini 1.0 Series (Legacy)
  {
    providerId: 'google',
    canonicalName: 'google/gemini-1.0-pro',
    displayName: 'Gemini 1.0 Pro',
    family: 'gemini-1.0',
    generation: 'pro',
    version: '001',
    contextTokensIn: 32768,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      code: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Legacy Gemini 1.0 model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 43
  },

  // Specialized Models
  {
    providerId: 'google',
    canonicalName: 'google/gemini-exp-1206',
    displayName: 'Gemini Experimental 1206',
    family: 'gemini-exp',
    generation: '1206',
    version: 'experimental',
    contextTokensIn: 2097152,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0, // Free experimental
    outputCostPer1M: 0,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      experimental: true,
      extended_context: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Experimental model with 2M+ context window',
    qualityTier: 'EXPERIMENTAL' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 9
  },
  {
    providerId: 'google',
    canonicalName: 'google/gemini-exp-1121',
    displayName: 'Gemini Experimental 1121',
    family: 'gemini-exp',
    generation: '1121',
    version: 'experimental',
    contextTokensIn: 2097152,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0, // Free experimental
    outputCostPer1M: 0,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      experimental: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Earlier experimental model with extended context',
    qualityTier: 'EXPERIMENTAL' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 19
  },

  // Embedding Models
  {
    providerId: 'google',
    canonicalName: 'google/text-embedding-004',
    displayName: 'Text Embedding 004',
    family: 'text-embedding',
    generation: '004',
    version: 'latest',
    contextTokensIn: 2048,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.00625, // $0.00625 per 1M tokens
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Latest text embedding model with 768 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 102
  },
  {
    providerId: 'google',
    canonicalName: 'google/embedding-001',
    displayName: 'Embedding 001',
    family: 'embedding',
    generation: '001',
    version: 'legacy',
    contextTokensIn: 2048,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.01,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Legacy embedding model',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 103
  }
];

async function seedGoogleModels() {
  console.log('🌱 Seeding Google models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of googleModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: model.providerId,
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Google models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${googleModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Google models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedGoogleModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedGoogleModels, googleModels };