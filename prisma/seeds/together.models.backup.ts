/**
 * Together AI Models Seed Script
 * 
 * Populates the AIModel table with Together AI models
 * Together AI provides access to open-source models including DeepSeek, Qwen, Llama, and more
 * Run with: npx tsx prisma/seeds/together.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const togetherModels: Prisma.AIModelCreateInput[] = [
  // DeepSeek V3
  {
    providerId: 'together',
    canonicalName: 'together/deepseek-ai/DeepSeek-V3',
    displayName: 'DeepSeek V3',
    family: 'deepseek',
    generation: 'v3',
    version: 'latest',
    contextTokensIn: 65536,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0.14, // $0.14 per 1M tokens
    outputCostPer1M: 0.28, // $0.28 per 1M tokens
    capabilities: {
      chat: true,
      code: true,
      reasoning: true,
      analysis: true,
      multilingual: true,
      moe: true,
      chain_of_thought: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 9,
    detailedInfo: 'DeepSeek V3 with enhanced reasoning, 671B MoE parameters, and 65K context',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 10
  },
  
  // DeepSeek V2.5
  {
    providerId: 'together',
    canonicalName: 'together/deepseek-ai/DeepSeek-V2.5',
    displayName: 'DeepSeek V2.5',
    family: 'deepseek',
    generation: 'v2.5',
    version: 'latest',
    contextTokensIn: 131072,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0.14,
    outputCostPer1M: 0.28,
    capabilities: {
      chat: true,
      code: true,
      reasoning: true,
      analysis: true,
      multilingual: true,
      moe: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'DeepSeek V2.5 with 236B MoE parameters and extended 128K context',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 11
  },

  // QwQ 32B Preview
  {
    providerId: 'together',
    canonicalName: 'together/Qwen/QwQ-32B-Preview',
    displayName: 'QwQ 32B Preview',
    family: 'qwq',
    generation: '32b',
    version: 'preview',
    contextTokensIn: 32768,
    maxTokensOut: 32768,
    contextTokensOut: 32768,
    inputCostPer1M: 0.18,
    outputCostPer1M: 0.18,
    capabilities: {
      reasoning: true,
      chat: true,
      code: true,
      mathematics: true,
      step_by_step: true,
      self_reflection: true,
      deep_thinking: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Qwen reasoning model with deep thinking and self-reflection capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 12
  },

  // Qwen 2.5 Series
  {
    providerId: 'together',
    canonicalName: 'together/Qwen/Qwen2.5-72B-Instruct-Turbo',
    displayName: 'Qwen 2.5 72B Instruct Turbo',
    family: 'qwen-2.5',
    generation: '72b',
    version: 'turbo',
    contextTokensIn: 131072,
    maxTokensOut: 32768,
    contextTokensOut: 32768,
    inputCostPer1M: 0.12,
    outputCostPer1M: 0.12,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      long_context: true,
      turbo_inference: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Qwen 2.5 72B with 128K context and turbo inference optimization',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 20
  },
  {
    providerId: 'together',
    canonicalName: 'together/Qwen/Qwen2.5-7B-Instruct-Turbo',
    displayName: 'Qwen 2.5 7B Instruct Turbo',
    family: 'qwen-2.5',
    generation: '7b',
    version: 'turbo',
    contextTokensIn: 131072,
    maxTokensOut: 32768,
    contextTokensOut: 32768,
    inputCostPer1M: 0.06,
    outputCostPer1M: 0.06,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      long_context: true,
      turbo_inference: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Efficient Qwen 2.5 7B with 128K context',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 31
  },

  // Qwen 2.5 Coder
  {
    providerId: 'together',
    canonicalName: 'together/Qwen/Qwen2.5-Coder-32B-Instruct',
    displayName: 'Qwen 2.5 Coder 32B',
    family: 'qwen-2.5-coder',
    generation: '32b',
    version: 'instruct',
    contextTokensIn: 32768,
    maxTokensOut: 32768,
    contextTokensOut: 32768,
    inputCostPer1M: 0.18,
    outputCostPer1M: 0.18,
    capabilities: {
      code: true,
      completion: true,
      debugging: true,
      refactoring: true,
      code_review: true,
      specialized_coding: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.0,
    heatLevel: 7,
    detailedInfo: 'Specialized Qwen model for code generation and analysis',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 21
  },

  // Llama 3.3 70B
  {
    providerId: 'together',
    canonicalName: 'together/meta-llama/Llama-3.3-70B-Instruct-Turbo',
    displayName: 'Llama 3.3 70B Instruct Turbo',
    family: 'llama-3.3',
    generation: '70b',
    version: 'turbo',
    contextTokensIn: 131072,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.88,
    outputCostPer1M: 0.88,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      turbo_inference: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Latest Llama 3.3 70B with 128K context and turbo optimization',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 22
  },

  // Llama 3.1 405B
  {
    providerId: 'together',
    canonicalName: 'together/meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo',
    displayName: 'Llama 3.1 405B Instruct Turbo',
    family: 'llama-3.1',
    generation: '405b',
    version: 'turbo',
    contextTokensIn: 131072,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 3.5,
    outputCostPer1M: 3.5,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      advanced_reasoning: true,
      flagship: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Flagship Llama 3.1 405B model with advanced capabilities',
    qualityTier: 'FLAGSHIP' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 5
  },

  // Llama 3.1 70B
  {
    providerId: 'together',
    canonicalName: 'together/meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo',
    displayName: 'Llama 3.1 70B Instruct Turbo',
    family: 'llama-3.1',
    generation: '70b',
    version: 'turbo',
    contextTokensIn: 131072,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.88,
    outputCostPer1M: 0.88,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Llama 3.1 70B with extended 128K context',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 30
  },

  // Llama 3.1 8B
  {
    providerId: 'together',
    canonicalName: 'together/meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo',
    displayName: 'Llama 3.1 8B Instruct Turbo',
    family: 'llama-3.1',
    generation: '8b',
    version: 'turbo',
    contextTokensIn: 131072,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.18,
    outputCostPer1M: 0.18,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      efficient: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Efficient Llama 3.1 8B with 128K context',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 39
  },

  // Llama Vision 90B
  {
    providerId: 'together',
    canonicalName: 'together/meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo',
    displayName: 'Llama 3.2 90B Vision Instruct Turbo',
    family: 'llama-3.2',
    generation: '90b',
    version: 'vision-turbo',
    contextTokensIn: 131072,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 1.2,
    outputCostPer1M: 1.2,
    capabilities: {
      chat: true,
      vision: true,
      multimodal: true,
      image_understanding: true,
      visual_reasoning: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Multimodal Llama with vision capabilities and 128K context',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 29
  },

  // Llama Vision 11B
  {
    providerId: 'together',
    canonicalName: 'together/meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo',
    displayName: 'Llama 3.2 11B Vision Instruct Turbo',
    family: 'llama-3.2',
    generation: '11b',
    version: 'vision-turbo',
    contextTokensIn: 131072,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.18,
    outputCostPer1M: 0.18,
    capabilities: {
      chat: true,
      vision: true,
      multimodal: true,
      image_understanding: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Efficient multimodal model with vision support',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 40
  },

  // Mixtral Models
  {
    providerId: 'together',
    canonicalName: 'together/mistralai/Mixtral-8x22B-Instruct-v0.1',
    displayName: 'Mixtral 8x22B Instruct',
    family: 'mixtral',
    generation: '8x22b',
    version: 'v0.1',
    contextTokensIn: 65536,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.9,
    outputCostPer1M: 0.9,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true,
      moe: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Large Mixture of Experts model with 8x22B parameters',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 41
  },
  {
    providerId: 'together',
    canonicalName: 'together/mistralai/Mixtral-8x7B-Instruct-v0.1',
    displayName: 'Mixtral 8x7B Instruct',
    family: 'mixtral',
    generation: '8x7b',
    version: 'v0.1',
    contextTokensIn: 32768,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.24,
    outputCostPer1M: 0.24,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true,
      moe: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Efficient MoE model with 8x7B parameters',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 42
  },

  // Other Notable Models
  {
    providerId: 'together',
    canonicalName: 'together/google/gemma-2b-it',
    displayName: 'Gemma 2B IT',
    family: 'gemma',
    generation: '2b',
    version: 'it',
    contextTokensIn: 8192,
    maxTokensOut: 1024,
    contextTokensOut: 1024,
    inputCostPer1M: 0.01,
    outputCostPer1M: 0.01,
    capabilities: {
      chat: true,
      lightweight: true,
      edge_deployment: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 3,
    detailedInfo: 'Ultra-lightweight Google Gemma model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 48
  },
  {
    providerId: 'together',
    canonicalName: 'together/Gryphe/MythoMax-L2-13b',
    displayName: 'MythoMax L2 13B',
    family: 'mythomax',
    generation: 'l2-13b',
    version: 'latest',
    contextTokensIn: 4096,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.18,
    outputCostPer1M: 0.18,
    capabilities: {
      chat: true,
      creative_writing: true,
      roleplay: true,
      storytelling: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.9,
    heatLevel: 5,
    detailedInfo: 'Creative writing and roleplay specialized model',
    qualityTier: 'SPECIALIZED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 50
  }
];

async function seedTogetherModels() {
  console.log('🌱 Seeding Together AI models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of togetherModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: model.providerId,
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Together AI models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${togetherModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Together AI models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedTogetherModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedTogetherModels, togetherModels };