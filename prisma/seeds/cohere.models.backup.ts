/**
 * Cohere Models Seed Script
 * 
 * Populates the AIModel table with Cohere models
 * Cohere provides enterprise-focused models with citations and grounded outputs
 * Run with: npx tsx prisma/seeds/cohere.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const cohereModels: Prisma.AIModelCreateInput[] = [
  // Command R+ Series
  {
    providerId: 'cohere',
    canonicalName: 'cohere/command-r-plus-08-2024',
    displayName: 'Command R+ (08-2024)',
    family: 'command-r-plus',
    generation: 'aug-2024',
    version: '08-2024',
    contextTokensIn: 128000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 2.5, // $2.50 per 1M tokens
    outputCostPer1M: 10, // $10 per 1M tokens
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true,
      structured_output: true,
      code_generation: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 8,
    detailedInfo: 'Flagship model with 128K context, RAG optimization, citations, and tool use',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 54
  },
  {
    providerId: 'cohere',
    canonicalName: 'cohere/command-r-plus',
    displayName: 'Command R+',
    family: 'command-r-plus',
    generation: 'standard',
    version: 'latest',
    contextTokensIn: 128000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true,
      structured_output: true,
      code_generation: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 8,
    detailedInfo: 'Latest Command R+ with enhanced capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 55
  },

  // Command R Series
  {
    providerId: 'cohere',
    canonicalName: 'cohere/command-r-08-2024',
    displayName: 'Command R (08-2024)',
    family: 'command-r',
    generation: 'aug-2024',
    version: '08-2024',
    contextTokensIn: 128000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.15, // $0.15 per 1M tokens
    outputCostPer1M: 0.6, // $0.60 per 1M tokens
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 7,
    detailedInfo: 'Efficient model with RAG optimization and 128K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 56
  },
  {
    providerId: 'cohere',
    canonicalName: 'cohere/command-r',
    displayName: 'Command R',
    family: 'command-r',
    generation: 'standard',
    version: 'latest',
    contextTokensIn: 128000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      rag_mode: true,
      citations: true,
      grounded: true,
      tool_use: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.3,
    heatLevel: 6,
    detailedInfo: 'Latest Command R with RAG capabilities',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 57
  },

  // Command Series (Legacy)
  {
    providerId: 'cohere',
    canonicalName: 'cohere/command',
    displayName: 'Command',
    family: 'command',
    generation: 'standard',
    version: 'latest',
    contextTokensIn: 4096,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 1,
    outputCostPer1M: 2,
    capabilities: {
      chat: true,
      text_generation: true,
      summarization: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Legacy Command model with 4K context',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 58
  },
  {
    providerId: 'cohere',
    canonicalName: 'cohere/command-light',
    displayName: 'Command Light',
    family: 'command',
    generation: 'light',
    version: 'latest',
    contextTokensIn: 4096,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.3,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      text_generation: true,
      lightweight: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Lightweight version of Command model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 59
  },

  // Embedding Models
  {
    providerId: 'cohere',
    canonicalName: 'cohere/embed-english-v3.0',
    displayName: 'Embed English v3.0',
    family: 'embed',
    generation: 'v3.0',
    version: 'english',
    contextTokensIn: 512,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.1, // $0.10 per 1M tokens
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      semantic_search: true,
      compression_aware: true,
      input_type_selection: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'English embedding model with 1024 dimensions and compression awareness',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 104
  },
  {
    providerId: 'cohere',
    canonicalName: 'cohere/embed-multilingual-v3.0',
    displayName: 'Embed Multilingual v3.0',
    family: 'embed',
    generation: 'v3.0',
    version: 'multilingual',
    contextTokensIn: 512,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.1,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      semantic_search: true,
      multilingual: true,
      compression_aware: true,
      input_type_selection: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Multilingual embedding model supporting 100+ languages',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 105
  },
  {
    providerId: 'cohere',
    canonicalName: 'cohere/embed-english-light-v3.0',
    displayName: 'Embed English Light v3.0',
    family: 'embed',
    generation: 'v3.0-light',
    version: 'english',
    contextTokensIn: 512,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.02,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      lightweight: true,
      semantic_search: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Lightweight English embedding model with 384 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 106
  },
  {
    providerId: 'cohere',
    canonicalName: 'cohere/embed-multilingual-light-v3.0',
    displayName: 'Embed Multilingual Light v3.0',
    family: 'embed',
    generation: 'v3.0-light',
    version: 'multilingual',
    contextTokensIn: 512,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.02,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true,
      lightweight: true,
      multilingual: true,
      semantic_search: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Lightweight multilingual embedding model with 384 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 107
  }
];

async function seedCohereModels() {
  console.log('🌱 Seeding Cohere models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of cohereModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: model.providerId,
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Cohere models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${cohereModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Cohere models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedCohereModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedCohereModels, cohereModels };