/**
 * xAI Models Seed Script
 * 
 * Populates the AIModel table with xAI (Grok) models
 * Run with: npx tsx prisma/seeds/xai.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const xaiModels: Prisma.AIModelCreateInput[] = [
  // Grok 3 Series
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-3',
    displayName: 'Grok 3',
    family: 'grok',
    generation: '3',
    version: 'latest',
    contextTokensIn: 131072,
    maxTokensOut: 131072,
    contextTokensOut: 131072,
    inputCostPer1M: 5, // $5 per 1M tokens
    outputCostPer1M: 15, // $15 per 1M tokens
    capabilities: {
      chat: true,
      code: true,
      reasoning: true,
      analysis: true,
      web_search: true,
      real_time_knowledge: true,
      humor: true,
      chain_of_thought: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Most capable Grok model with reasoning, real-time knowledge, and 128K context',
    qualityTier: 'FLAGSHIP' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 6
  },
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-3-fast',
    displayName: 'Grok 3 Fast',
    family: 'grok',
    generation: '3-fast',
    version: 'latest',
    contextTokensIn: 131072,
    maxTokensOut: 131072,
    contextTokensOut: 131072,
    inputCostPer1M: 5,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      reasoning: true,
      analysis: true,
      web_search: true,
      real_time_knowledge: true,
      humor: true,
      fast_inference: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Optimized Grok 3 for faster inference while maintaining quality',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 16
  },
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-3-mini',
    displayName: 'Grok 3 Mini',
    family: 'grok',
    generation: '3-mini',
    version: 'latest',
    contextTokensIn: 131072,
    maxTokensOut: 131072,
    contextTokensOut: 131072,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      web_search: true,
      efficient: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Efficient smaller Grok model with 128K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 26
  },
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-3-mini-fast',
    displayName: 'Grok 3 Mini Fast',
    family: 'grok',
    generation: '3-mini-fast',
    version: 'latest',
    contextTokensIn: 131072,
    maxTokensOut: 131072,
    contextTokensOut: 131072,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      code: true,
      efficient: true,
      fast_inference: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Fastest Grok model for quick responses',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 36
  },

  // Grok 2 Series (Previous Generation)
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-2-1212',
    displayName: 'Grok 2 (December 2024)',
    family: 'grok',
    generation: '2-1212',
    version: '1212',
    contextTokensIn: 131072,
    maxTokensOut: 131072,
    contextTokensOut: 131072,
    inputCostPer1M: 2,
    outputCostPer1M: 10,
    capabilities: {
      chat: true,
      code: true,
      reasoning: true,
      analysis: true,
      web_search: true,
      real_time_knowledge: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Previous generation Grok 2 model from December 2024',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 37
  },
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-2-vision-1212',
    displayName: 'Grok 2 Vision (December 2024)',
    family: 'grok',
    generation: '2-vision-1212',
    version: '1212',
    contextTokensIn: 32768,
    maxTokensOut: 32768,
    contextTokensOut: 32768,
    inputCostPer1M: 2,
    outputCostPer1M: 10,
    capabilities: {
      chat: true,
      vision: true,
      multimodal: true,
      image_understanding: true,
      visual_reasoning: true,
      web_search: true,
      real_time_knowledge: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Multimodal Grok 2 with vision capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 38
  },

  // Grok Beta (Experimental)
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-beta',
    displayName: 'Grok Beta',
    family: 'grok',
    generation: 'beta',
    version: 'experimental',
    contextTokensIn: 131072,
    maxTokensOut: 131072,
    contextTokensOut: 131072,
    inputCostPer1M: 5,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      reasoning: true,
      analysis: true,
      experimental: true,
      web_search: true,
      real_time_knowledge: true,
      advanced_features: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Experimental Grok model with latest features for testing',
    qualityTier: 'EXPERIMENTAL' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 17
  },

  // Grok 1 (Legacy)
  {
    providerId: 'xai',
    canonicalName: 'xai/grok-1',
    displayName: 'Grok 1',
    family: 'grok',
    generation: '1',
    version: 'legacy',
    contextTokensIn: 8192,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 5,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      humor: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Original Grok model with 8K context',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 49
  }
];

async function seedXAIModels() {
  console.log('🌱 Seeding xAI models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of xaiModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: model.providerId,
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ xAI models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${xaiModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding xAI models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedXAIModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedXAIModels, xaiModels };