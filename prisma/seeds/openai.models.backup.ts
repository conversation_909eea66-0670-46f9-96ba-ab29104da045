/**
 * OpenAI Models Seed Script
 * 
 * Populates the AIModel table with OpenAI models
 * Run with: npx tsx prisma/seeds/openai.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const openAIModels: Prisma.AIModelCreateInput[] = [
  // GPT-4o Series
  {
    providerId: 'openai',
    canonicalName: 'openai/gpt-4o',
    displayName: 'GPT-4o',
    family: 'gpt-4o',
    generation: 'latest',
    version: '2024-11-20',
    contextTokensIn: 128000,
    maxTokensOut: 16384,
    contextTokensOut: 16384,
    inputCostPer1M: 2.5,
    outputCostPer1M: 10,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      json_mode: true,
      structured_output: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Most advanced OpenAI model with vision, function calling, JSON mode, and structured output',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 10
  },
  {
    providerId: 'openai',
    canonicalName: 'openai/gpt-4o-mini',
    displayName: 'GPT-4o Mini',
    family: 'gpt-4o',
    generation: 'mini',
    version: '2024-07-18',
    contextTokensIn: 128000,
    maxTokensOut: 16384,
    contextTokensOut: 16384,
    inputCostPer1M: 0.15,
    outputCostPer1M: 0.6,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      json_mode: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Fast and cost-effective GPT-4 level model with vision support',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 20
  },

  // O-series Reasoning Models
  {
    providerId: 'openai',
    canonicalName: 'openai/o1',
    displayName: 'O1',
    family: 'o-series',
    generation: 'o1',
    version: '2024-12-17',
    contextTokensIn: 200000,
    maxTokensOut: 100000,
    contextTokensOut: 100000,
    inputCostPer1M: 15,
    outputCostPer1M: 60,
    capabilities: {
      reasoning: true,
      code: true,
      analysis: true,
      math: true,
      science: true,
      step_by_step: true,
      chain_of_thought: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 9,
    detailedInfo: 'Advanced reasoning model for complex problem solving with chain-of-thought capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 5
  },
  {
    providerId: 'openai',
    canonicalName: 'openai/o1-mini',
    displayName: 'O1 Mini',
    family: 'o-series',
    generation: 'o1-mini',
    version: '2024-09-12',
    contextTokensIn: 128000,
    maxTokensOut: 65536,
    contextTokensOut: 65536,
    inputCostPer1M: 3,
    outputCostPer1M: 12,
    capabilities: {
      reasoning: true,
      code: true,
      math: true,
      analysis: true,
      step_by_step: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 7,
    detailedInfo: 'Efficient reasoning model for STEM tasks',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 6
  },
  {
    providerId: 'openai',
    canonicalName: 'openai/o3-mini',
    displayName: 'O3 Mini',
    family: 'o-series',
    generation: 'o3-mini',
    version: '2025-01-01',
    contextTokensIn: 200000,
    maxTokensOut: 100000,
    contextTokensOut: 100000,
    inputCostPer1M: 1.1,
    outputCostPer1M: 4.4,
    capabilities: {
      reasoning: true,
      code: true,
      math: true,
      science: true,
      planning: true,
      step_by_step: true,
      chain_of_thought: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0.0,
    heatLevel: 8,
    detailedInfo: 'Latest efficient reasoning model with planning capabilities',
    qualityTier: 'REASONING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 4
  },

  // GPT-4 Turbo
  {
    providerId: 'openai',
    canonicalName: 'openai/gpt-4-turbo',
    displayName: 'GPT-4 Turbo',
    family: 'gpt-4',
    generation: 'turbo',
    version: '2024-04-09',
    contextTokensIn: 128000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 10,
    outputCostPer1M: 30,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      analysis: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Previous generation GPT-4 with vision capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 15
  },

  // GPT-3.5 Turbo
  {
    providerId: 'openai',
    canonicalName: 'openai/gpt-3.5-turbo',
    displayName: 'GPT-3.5 Turbo',
    family: 'gpt-3.5',
    generation: 'turbo',
    version: '0125',
    contextTokensIn: 16384,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.5,
    outputCostPer1M: 1.5,
    capabilities: {
      chat: true,
      code: true,
      functions: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Fast and efficient model for chat applications',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 30
  },

  // Embedding Models
  {
    providerId: 'openai',
    canonicalName: 'openai/text-embedding-3-small',
    displayName: 'Text Embedding 3 Small',
    family: 'text-embedding',
    generation: '3',
    version: 'small',
    contextTokensIn: 8191,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.02,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Small and efficient embedding model with 1536 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 100
  },
  {
    providerId: 'openai',
    canonicalName: 'openai/text-embedding-3-large',
    displayName: 'Text Embedding 3 Large',
    family: 'text-embedding',
    generation: '3',
    version: 'large',
    contextTokensIn: 8191,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 0.13,
    outputCostPer1M: 0,
    capabilities: {
      embeddings: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Large and powerful embedding model with 3072 dimensions',
    qualityTier: 'EMBEDDING' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 101
  },

  // Speech Models
  {
    providerId: 'openai',
    canonicalName: 'openai/tts-1',
    displayName: 'TTS 1',
    family: 'tts',
    generation: '1',
    version: 'latest',
    contextTokensIn: 4096,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 15, // per 1M characters
    outputCostPer1M: 0,
    capabilities: {
      speech: true,
      tts: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Text-to-speech model with multiple voices',
    qualityTier: 'SPEECH' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 110
  },
  {
    providerId: 'openai',
    canonicalName: 'openai/whisper-1',
    displayName: 'Whisper 1',
    family: 'whisper',
    generation: '1',
    version: 'latest',
    contextTokensIn: 0,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 6, // per hour
    outputCostPer1M: 0,
    capabilities: {
      speech: true,
      stt: true,
      transcription: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: true,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Speech-to-text model for transcription',
    qualityTier: 'SPEECH' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 111
  },

  // Image Generation Models
  {
    providerId: 'openai',
    canonicalName: 'openai/dall-e-3',
    displayName: 'DALL-E 3',
    family: 'dall-e',
    generation: '3',
    version: 'latest',
    contextTokensIn: 4000,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 40, // per image
    outputCostPer1M: 0,
    capabilities: {
      image_generation: true,
      creative: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Advanced image generation model',
    qualityTier: 'IMAGE' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 120
  },
  {
    providerId: 'openai',
    canonicalName: 'openai/dall-e-2',
    displayName: 'DALL-E 2',
    family: 'dall-e',
    generation: '2',
    version: 'latest',
    contextTokensIn: 1000,
    maxTokensOut: 0,
    contextTokensOut: 0,
    inputCostPer1M: 20, // per image
    outputCostPer1M: 0,
    capabilities: {
      image_generation: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: false,
    optimalTemperature: 0,
    heatLevel: 1,
    detailedInfo: 'Previous generation image generation model',
    qualityTier: 'IMAGE' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 121
  }
];

async function seedOpenAIModels() {
  console.log('🌱 Seeding OpenAI models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of openAIModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: model.providerId,
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ OpenAI models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${openAIModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding OpenAI models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedOpenAIModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedOpenAIModels, openAIModels };