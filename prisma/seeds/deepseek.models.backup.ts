/**
 * DeepSeek Models Seed Script
 * 
 * Populates the AIModel table with DeepSeek models
 * DeepSeek provides advanced reasoning models with competitive performance
 * Run with: npx tsx prisma/seeds/deepseek.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const deepseekModels: Prisma.AIModelCreateInput[] = [
  // DeepSeek V3
  {
    provider: { connect: { id: 'deepseek' } },
    canonicalName: 'deepseek/deepseek-chat',
    displayName: 'DeepSeek V3 Chat',
    family: 'deepseek',
    generation: 'v3',
    modelType: 'CHAT',
    detailedInfo: 'DeepSeek V3 with 671B MoE parameters, enhanced reasoning capabilities, and caching support. Context: 65K tokens, Cost: $0.14/$0.28 per 1M tokens.',
    metadata: {
      contextTokensIn: 65536,
      maxTokensOut: 8192,
      contextTokensOut: 8192,
      inputCostPer1M: 0.14,
      outputCostPer1M: 0.28,
      capabilities: {
        chat: true,
        code: true,
        reasoning: true,
        analysis: true,
        multilingual: true,
        chain_of_thought: true,
        step_by_step: true,
        moe: true,
        caching: true
      },
      supports: {
        streaming: true,
        functions: true,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.0,
      heatLevel: 9,
      qualityTier: 'FLAGSHIP'
    },
    isEnabled: true,
    routerIndex: 4
  },
  {
    provider: { connect: { id: 'deepseek' } },
    canonicalName: 'deepseek/deepseek-reasoner',
    displayName: 'DeepSeek Reasoner',
    family: 'deepseek',
    generation: 'reasoner',
    modelType: 'CHAT',
    detailedInfo: 'Specialized reasoning model with enhanced problem-solving and mathematical capabilities. Context: 65K tokens, Cost: $0.14/$0.28 per 1M tokens.',
    metadata: {
      contextTokensIn: 65536,
      maxTokensOut: 8192,
      contextTokensOut: 8192,
      inputCostPer1M: 0.14,
      outputCostPer1M: 0.28,
      capabilities: {
        reasoning: true,
        chat: true,
        code: true,
        mathematics: true,
        step_by_step: true,
        chain_of_thought: true,
        self_reflection: true,
        deep_thinking: true,
        problem_solving: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.0,
      heatLevel: 9,
      qualityTier: 'REASONING'
    },
    isEnabled: true,
    routerIndex: 3
  },
  // DeepSeek Coder V2
  {
    provider: { connect: { id: 'deepseek' } },
    canonicalName: 'deepseek/deepseek-coder',
    displayName: 'DeepSeek Coder V2',
    family: 'deepseek-coder',
    generation: 'v2',
    modelType: 'CODE_GENERATION',
    detailedInfo: 'Advanced code model with 128K context, repository-level understanding, and fill-in-middle capabilities. Cost: $0.14/$0.28 per 1M tokens.',
    metadata: {
      contextTokensIn: 128000,
      maxTokensOut: 8192,
      contextTokensOut: 8192,
      inputCostPer1M: 0.14,
      outputCostPer1M: 0.28,
      capabilities: {
        code: true,
        completion: true,
        debugging: true,
        refactoring: true,
        code_review: true,
        fill_in_middle: true,
        repository_level: true,
        multilingual_code: true
      },
      supports: {
        streaming: true,
        functions: false,
        vision: false,
        audio: false,
        systemMessages: true
      },
      optimalTemperature: 0.0,
      heatLevel: 8,
      qualityTier: 'SPECIALIZED'
    },
    isEnabled: true,
    routerIndex: 14
  }
];

async function seedDeepSeekModels() {
  console.log('🌱 Seeding DeepSeek models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of deepseekModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: 'deepseek',
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ DeepSeek models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${deepseekModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding DeepSeek models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedDeepSeekModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedDeepSeekModels, deepseekModels };