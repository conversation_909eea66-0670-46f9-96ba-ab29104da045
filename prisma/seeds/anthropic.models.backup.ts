/**
 * Anthropic Models Seed Script
 * 
 * Populates the AIModel table with Anthropic models
 * Run with: npx tsx prisma/seeds/anthropic.models.ts
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

const anthropicModels: Prisma.AIModelCreateInput[] = [
  // Claude 3.5 Series (Latest)
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-3.5-sonnet',
    displayName: 'Claude 3.5 Sonnet',
    family: 'claude-3.5',
    generation: 'sonnet',
    version: '20241022',
    contextTokensIn: 200000,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true,
      latex: true,
      tool_use: true,
      computer_use: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 8,
    detailedInfo: 'Most advanced Claude model with 200K context, vision, tool use, and computer use capabilities',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 11
  },
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-3.5-haiku',
    displayName: 'Claude 3.5 Haiku',
    family: 'claude-3.5',
    generation: 'haiku',
    version: '20241022',
    contextTokensIn: 200000,
    maxTokensOut: 8192,
    contextTokensOut: 8192,
    inputCostPer1M: 0.8,
    outputCostPer1M: 4,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true,
      tool_use: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Fast and cost-effective Claude 3.5 model with vision and 200K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 21
  },

  // Claude 3 Series
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-3-opus',
    displayName: 'Claude 3 Opus',
    family: 'claude-3',
    generation: 'opus',
    version: '20240229',
    contextTokensIn: 200000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 15,
    outputCostPer1M: 75,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true,
      complex_reasoning: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 9,
    detailedInfo: 'Most capable Claude 3 model for complex tasks and reasoning',
    qualityTier: 'SUPERIOR' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 12
  },
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-3-sonnet',
    displayName: 'Claude 3 Sonnet',
    family: 'claude-3',
    generation: 'sonnet',
    version: '20240229',
    contextTokensIn: 200000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 3,
    outputCostPer1M: 15,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      vision: true,
      functions: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 7,
    detailedInfo: 'Balanced Claude 3 model for most tasks',
    qualityTier: 'BALANCED' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 22
  },
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-3-haiku',
    displayName: 'Claude 3 Haiku',
    family: 'claude-3',
    generation: 'haiku',
    version: '20240307',
    contextTokensIn: 200000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.25,
    outputCostPer1M: 1.25,
    capabilities: {
      chat: true,
      code: true,
      vision: true,
      functions: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: true,
    supportsVision: true,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Fast and affordable Claude 3 model',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 31
  },

  // Claude Instant (Legacy)
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-instant-1.2',
    displayName: 'Claude Instant 1.2',
    family: 'claude-instant',
    generation: '1.2',
    version: '1.2',
    contextTokensIn: 100000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 0.8,
    outputCostPer1M: 2.4,
    capabilities: {
      chat: true,
      code: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 4,
    detailedInfo: 'Legacy fast model for simple tasks',
    qualityTier: 'STANDARD' as any,
    isEnabled: true,
    isHidden: false,
    routerIndex: 41
  },

  // Claude 2 Series (Legacy)
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-2.1',
    displayName: 'Claude 2.1',
    family: 'claude-2',
    generation: '2.1',
    version: '2.1',
    contextTokensIn: 200000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 8,
    outputCostPer1M: 24,
    capabilities: {
      chat: true,
      code: true,
      analysis: true,
      multilingual: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 6,
    detailedInfo: 'Legacy Claude 2 with 200K context',
    qualityTier: 'BALANCED' as any,
    isEnabled: false, // Deprecated
    isHidden: true,
    routerIndex: 51
  },
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-2.0',
    displayName: 'Claude 2.0',
    family: 'claude-2',
    generation: '2.0',
    version: '2.0',
    contextTokensIn: 100000,
    maxTokensOut: 4096,
    contextTokensOut: 4096,
    inputCostPer1M: 8,
    outputCostPer1M: 24,
    capabilities: {
      chat: true,
      code: true,
      analysis: true
    },
    supportsStreaming: true,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 5,
    detailedInfo: 'Original Claude 2 model',
    qualityTier: 'BALANCED' as any,
    isEnabled: false, // Deprecated
    isHidden: true,
    routerIndex: 52
  },

  // Claude 1 (Legacy - for historical reference)
  {
    providerId: 'anthropic',
    canonicalName: 'anthropic/claude-1.3',
    displayName: 'Claude 1.3',
    family: 'claude-1',
    generation: '1.3',
    version: '1.3',
    contextTokensIn: 100000,
    maxTokensOut: 2048,
    contextTokensOut: 2048,
    inputCostPer1M: 11.02,
    outputCostPer1M: 32.68,
    capabilities: {
      chat: true,
      code: true
    },
    supportsStreaming: false,
    supportsFunctions: false,
    supportsVision: false,
    supportsAudio: false,
    supportsSystemMessages: true,
    optimalTemperature: 0.7,
    heatLevel: 3,
    detailedInfo: 'Original Claude model (deprecated)',
    qualityTier: 'STANDARD' as any,
    isEnabled: false, // Deprecated
    isHidden: true,
    routerIndex: 61
  }
];

async function seedAnthropicModels() {
  console.log('🌱 Seeding Anthropic models...');
  
  try {
    let createdCount = 0;
    let updatedCount = 0;
    
    for (const model of anthropicModels) {
      try {
        const result = await prisma.aIModel.upsert({
          where: {
            providerId_canonicalName: {
              providerId: model.providerId,
              canonicalName: model.canonicalName
            }
          },
          update: {
            ...model,
            updatedAt: new Date()
          },
          create: model
        });
        
        if (result.createdAt === result.updatedAt) {
          createdCount++;
          console.log(`✅ Created: ${model.canonicalName}`);
        } else {
          updatedCount++;
          console.log(`📝 Updated: ${model.canonicalName}`);
        }
      } catch (error) {
        console.error(`❌ Failed to upsert ${model.canonicalName}:`, error);
      }
    }
    
    console.log(`\n✨ Anthropic models seeded successfully!`);
    console.log(`   Created: ${createdCount}`);
    console.log(`   Updated: ${updatedCount}`);
    console.log(`   Total: ${anthropicModels.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding Anthropic models:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedAnthropicModels()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedAnthropicModels, anthropicModels };