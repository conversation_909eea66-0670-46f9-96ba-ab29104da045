/**
 * Data Migration Script: AIModel → Models & ModelPlanAccess → ModelPlanRules
 * 
 * This script migrates data from the old schema to the new enhanced schema.
 * Run with: npx tsx prisma/migrations/20250710_models_enhanced_plans/migrate-data.ts
 */

import { PrismaClient } from '@prisma/client';
import { apiLogger } from '@/lib/logger';

const prisma = new PrismaClient();

interface MigrationStats {
  plans: number;
  models: number;
  rules: number;
  inheritances: number;
  errors: string[];
}

async function migrateData(): Promise<void> {
  const stats: MigrationStats = {
    plans: 0,
    models: 0,
    rules: 0,
    inheritances: 0,
    errors: []
  };

  console.log('🚀 Starting data migration to new Models and Plans system...\n');

  try {
    // Start transaction
    await prisma.$transaction(async (tx) => {
      // 1. Create Plans
      console.log('📋 Creating Plans...');
      const plans = await createPlans(tx);
      stats.plans = plans.length;
      console.log(`✅ Created ${stats.plans} plans\n`);

      // 2. Create Plan Inheritances
      console.log('🔗 Setting up plan inheritance...');
      const inheritances = await createPlanInheritances(tx);
      stats.inheritances = inheritances.length;
      console.log(`✅ Created ${stats.inheritances} inheritance relationships\n`);

      // 3. Migrate Models
      console.log('🤖 Migrating models from AIModel to Models...');
      const modelMap = await migrateModels(tx, stats);
      stats.models = modelMap.size;
      console.log(`✅ Migrated ${stats.models} models\n`);

      // 4. Migrate Plan Access Rules
      console.log('📜 Converting ModelPlanAccess to ModelPlanRules...');
      const rules = await migratePlanRules(tx, modelMap, stats);
      stats.rules = rules;
      console.log(`✅ Created ${stats.rules} plan rules\n`);

      // 5. Add default rules
      console.log('🎯 Adding intelligent default rules...');
      await addDefaultRules(tx, modelMap);
      console.log('✅ Added default rules for common patterns\n');
    });

    // Print summary
    console.log('\n📊 Migration Summary:');
    console.log('━'.repeat(40));
    console.log(`Plans created:        ${stats.plans}`);
    console.log(`Models migrated:      ${stats.models}`);
    console.log(`Rules created:        ${stats.rules}`);
    console.log(`Inheritances created: ${stats.inheritances}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️  Errors encountered: ${stats.errors.length}`);
      stats.errors.forEach((error, i) => {
        console.log(`  ${i + 1}. ${error}`);
      });
    }
    
    console.log('\n✨ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function createPlans(tx: any): Promise<any[]> {
  const plansData = [
    {
      id: 'plan_free',
      code: 'FREE',
      displayName: 'Free Plan',
      tier: 1,
      monthlyMessageLimit: 10,
      creditsIncluded: 10000,
      monthlyPriceUSD: 0,
      features: JSON.stringify(['basic_chat', 'limited_models'])
    },
    {
      id: 'plan_freemium',
      code: 'FREEMIUM',
      displayName: 'Freemium Plan',
      tier: 2,
      monthlyMessageLimit: 50,
      creditsIncluded: 50000,
      monthlyPriceUSD: 0,
      features: JSON.stringify(['extended_chat', 'more_models', 'basic_features'])
    },
    {
      id: 'plan_plus',
      code: 'PLUS',
      displayName: 'Plus Plan',
      tier: 3,
      monthlyMessageLimit: 500,
      creditsIncluded: 200000,
      monthlyPriceUSD: 19.99,
      features: JSON.stringify(['unlimited_chat', 'premium_models', 'advanced_features', 'priority_support'])
    },
    {
      id: 'plan_advanced',
      code: 'ADVANCED',
      displayName: 'Advanced Plan',
      tier: 4,
      monthlyMessageLimit: 2000,
      creditsIncluded: 500000,
      monthlyPriceUSD: 49.99,
      features: JSON.stringify(['unlimited_chat', 'all_models', 'advanced_features', 'api_access', 'priority_support'])
    },
    {
      id: 'plan_max',
      code: 'MAX',
      displayName: 'Max Plan',
      tier: 5,
      monthlyMessageLimit: 10000,
      creditsIncluded: 2000000,
      monthlyPriceUSD: 99.99,
      features: JSON.stringify(['unlimited_everything', 'all_models', 'all_features', 'dedicated_support', 'custom_models'])
    },
    {
      id: 'plan_enterprise',
      code: 'ENTERPRISE',
      displayName: 'Enterprise Plan',
      tier: 6,
      monthlyMessageLimit: -1, // unlimited
      creditsIncluded: -1,
      monthlyPriceUSD: 0, // custom pricing
      features: JSON.stringify(['custom_limits', 'sla', 'dedicated_infrastructure', 'white_label'])
    }
  ];

  const created = [];
  for (const plan of plansData) {
    try {
      const result = await tx.plans.create({ data: plan });
      created.push(result);
    } catch (error: any) {
      if (error.code === 'P2002') {
        console.log(`  ℹ️  Plan ${plan.code} already exists, skipping...`);
      } else {
        throw error;
      }
    }
  }

  return created;
}

async function createPlanInheritances(tx: any): Promise<any[]> {
  const inheritances = [
    { childPlanId: 'plan_freemium', parentPlanId: 'plan_free', inheritType: 'FULL' },
    { childPlanId: 'plan_plus', parentPlanId: 'plan_freemium', inheritType: 'FULL' },
    { childPlanId: 'plan_advanced', parentPlanId: 'plan_plus', inheritType: 'FULL' },
    { childPlanId: 'plan_max', parentPlanId: 'plan_advanced', inheritType: 'FULL' },
    { childPlanId: 'plan_enterprise', parentPlanId: 'plan_max', inheritType: 'FEATURES_ONLY' }
  ];

  const created = [];
  for (const inheritance of inheritances) {
    try {
      const result = await tx.planInheritance.create({ data: inheritance });
      created.push(result);
    } catch (error: any) {
      if (error.code === 'P2002') {
        console.log(`  ℹ️  Inheritance ${inheritance.childPlanId} → ${inheritance.parentPlanId} already exists`);
      } else {
        throw error;
      }
    }
  }

  return created;
}

async function migrateModels(tx: any, stats: MigrationStats): Promise<Map<string, string>> {
  const aiModels = await tx.aIModel.findMany({
    include: { provider: true }
  });

  const modelMap = new Map<string, string>();
  
  for (const aiModel of aiModels) {
    try {
      const metadata = aiModel.metadata || {};
      
      // Extract capabilities
      const capabilities = metadata.capabilities || {};
      const supports = metadata.supports || {};
      
      const modelData = {
        id: aiModel.id,
        providerId: aiModel.providerId,
        canonicalName: aiModel.canonicalName,
        displayName: aiModel.displayName,
        family: aiModel.family,
        generation: aiModel.generation,
        modelType: aiModel.modelType,
        
        // Extract performance fields
        contextWindow: metadata.contextTokensIn || metadata.contextWindow || 4096,
        maxOutput: metadata.maxTokensOut || metadata.maxOutput || 2048,
        inputCostPer1M: metadata.inputCostPer1M || metadata.pricing?.input || 0,
        outputCostPer1M: metadata.outputCostPer1M || metadata.pricing?.output || 0,
        
        // Calculate ratings
        speedRating: calculateSpeedRating(metadata),
        qualityRating: calculateQualityRating(metadata),
        
        // Extract capabilities
        supportsVision: supports.vision === true || capabilities.vision === true,
        supportsFunctionCalling: supports.functions === true || capabilities.function_calling === true || capabilities.toolUse === true,
        supportsWebSearch: capabilities.web_search === true || capabilities.webSearch === true,
        supportsReasoning: capabilities.reasoning === true || capabilities.deep_thinking === true || capabilities.chain_of_thought === true,
        supportsStreaming: supports.streaming !== false,
        
        // Status
        isEnabled: aiModel.isEnabled,
        validationStatus: aiModel.validationStatus,
        
        // Keep full metadata
        extendedMetadata: metadata,
        
        createdAt: aiModel.createdAt,
        updatedAt: aiModel.updatedAt
      };
      
      await tx.models.create({ data: modelData });
      modelMap.set(aiModel.id, aiModel.canonicalName);
      
    } catch (error: any) {
      if (error.code === 'P2002') {
        console.log(`  ℹ️  Model ${aiModel.canonicalName} already migrated`);
        modelMap.set(aiModel.id, aiModel.canonicalName);
      } else {
        stats.errors.push(`Failed to migrate ${aiModel.canonicalName}: ${error.message}`);
      }
    }
  }
  
  return modelMap;
}

function calculateSpeedRating(metadata: any): number {
  if (metadata.speed === 'ultrafast') return 10;
  if (metadata.speed === 'fast') return 8;
  if (metadata.speed === 'medium') return 5;
  if (metadata.speed === 'slow') return 3;
  
  // Based on latency
  if (metadata.avgLatency) {
    if (metadata.avgLatency < 500) return 10;
    if (metadata.avgLatency < 1000) return 8;
    if (metadata.avgLatency < 2000) return 6;
    if (metadata.avgLatency < 3000) return 4;
  }
  
  return 5; // default
}

function calculateQualityRating(metadata: any): number {
  if (metadata.qualityTier === 'FLAGSHIP') return 10;
  if (metadata.qualityTier === 'PREMIUM') return 8;
  if (metadata.qualityTier === 'STANDARD') return 5;
  
  // Use heat level if available
  if (metadata.heatLevel) {
    return Math.min(10, Math.max(1, metadata.heatLevel));
  }
  
  return 5; // default
}

async function migratePlanRules(tx: any, modelMap: Map<string, string>, stats: MigrationStats): Promise<number> {
  const planAccess = await tx.modelPlanAccess.findMany();
  
  let rulesCreated = 0;
  
  for (const access of planAccess) {
    if (!access.isAvailable) continue;
    
    try {
      const planTier = getPlanTier(access.userPlan);
      const limits = getPlanLimits(access.userPlan);
      
      await tx.modelPlanRules.create({
        data: {
          modelId: access.modelId,
          ruleType: 'INCLUDE',
          planIds: [access.userPlan],
          minPlanTier: planTier,
          messagesPerHour: limits.hourly,
          messagesPerDay: limits.daily,
          priority: 100,
          isEnabled: true,
          effectiveFrom: access.availableFrom,
          effectiveUntil: access.availableUntil
        }
      });
      
      rulesCreated++;
    } catch (error: any) {
      stats.errors.push(`Failed to migrate rule for ${modelMap.get(access.modelId)}: ${error.message}`);
    }
  }
  
  return rulesCreated;
}

function getPlanTier(plan: string): number {
  const tiers: Record<string, number> = {
    'FREE': 1,
    'FREEMIUM': 2,
    'PLUS': 3,
    'ADVANCED': 4,
    'MAX': 5,
    'ENTERPRISE': 6
  };
  return tiers[plan] || 1;
}

function getPlanLimits(plan: string): { hourly: number | null; daily: number | null } {
  const limits: Record<string, { hourly: number | null; daily: number | null }> = {
    'FREE': { hourly: 10, daily: 50 },
    'FREEMIUM': { hourly: 50, daily: 200 },
    'PLUS': { hourly: 200, daily: 1000 },
    'ADVANCED': { hourly: 500, daily: 2000 },
    'MAX': { hourly: null, daily: null },
    'ENTERPRISE': { hourly: null, daily: null }
  };
  return limits[plan] || { hourly: 10, daily: 50 };
}

async function addDefaultRules(tx: any, modelMap: Map<string, string>): Promise<void> {
  // Rule 1: Basic models for FREE users
  const basicModels = [
    'gemini/gemini-2.0-flash-exp',
    'gemini/gemini-1.5-flash',
    'gpt-4o-mini',
    'claude-3-5-haiku-20241022'
  ];
  
  const basicModelIds = await tx.models.findMany({
    where: { canonicalName: { in: basicModels } },
    select: { id: true }
  });
  
  for (const model of basicModelIds) {
    try {
      await tx.modelPlanRules.create({
        data: {
          modelId: model.id,
          ruleType: 'INCLUDE',
          planIds: ['FREE'],
          messagesPerDay: 10,
          priority: 50,
          isEnabled: true
        }
      });
    } catch (error) {
      // Ignore duplicate errors
    }
  }
  
  // Rule 2: Premium models for PLUS and above
  const premiumModels = await tx.models.findMany({
    where: {
      OR: [
        { inputCostPer1M: { gt: 1.0 } },
        { qualityRating: { gte: 8 } }
      ]
    },
    select: { id: true }
  });
  
  for (const model of premiumModels) {
    try {
      await tx.modelPlanRules.create({
        data: {
          modelId: model.id,
          ruleType: 'INCLUDE',
          minPlanTier: 3, // PLUS and above
          priority: 75,
          isEnabled: true
        }
      });
    } catch (error) {
      // Ignore duplicate errors
    }
  }
  
  // Rule 3: Experimental models for MAX users
  const experimentalModels = await tx.models.findMany({
    where: {
      OR: [
        { canonicalName: { contains: 'preview' } },
        { canonicalName: { contains: 'exp' } },
        { canonicalName: { contains: 'beta' } }
      ]
    },
    select: { id: true }
  });
  
  for (const model of experimentalModels) {
    try {
      await tx.modelPlanRules.create({
        data: {
          modelId: model.id,
          ruleType: 'INCLUDE',
          planIds: ['MAX', 'ENTERPRISE'],
          conditions: { experimental: true, requiresApproval: false },
          priority: 90,
          isEnabled: true
        }
      });
    } catch (error) {
      // Ignore duplicate errors
    }
  }
}

// Run if called directly
if (require.main === module) {
  migrateData()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { migrateData };