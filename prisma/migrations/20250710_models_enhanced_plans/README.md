# Models & Enhanced Plans Migration

This migration creates a new optimized `Models` table and enhanced plan management system to replace the existing `AIModel` and `ModelPlanAccess` tables.

## 🎯 Migration Goals

1. **Performance Optimization**: Extract frequently queried fields from JSON metadata into indexed columns
2. **Flexible Plan Management**: Replace comma-separated plan strings with dynamic rule-based system
3. **Better Scalability**: Support for 1000+ models with optimized queries
4. **Enhanced Business Logic**: Support tier-based access, conditional rules, and plan inheritance

## 📁 Migration Files

### For Production (Manual Execution)
- **`production_migration.sql`** - Complete SQL script for production database
  - Creates new tables with proper indexes
  - Migrates all data from AIModel → Models
  - Converts ModelPlanAccess → ModelPlanRules
  - Sets up plan inheritance
  - Includes rollback commands

- **`verification_queries.sql`** - Post-migration verification
  - Data integrity checks
  - Migration statistics
  - Performance tests
  - Sample queries

### For Development (Prisma)
- **`migration.sql`** - Prisma migration file (schema only)
- **`data_migration.sql`** - Data migration SQL
- **`migrate-data.ts`** - TypeScript migration script

## 🚀 Production Migration Steps

### 1. Pre-Migration Backup
```bash
# Backup current tables
mysqldump -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production \
  AIModel ModelPlanAccess AIProvider \
  > backup_models_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Review Migration Script
```bash
# Review the production migration script
less production_migration.sql

# Check current data counts
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production -e "
  SELECT COUNT(*) as 'AIModel Count' FROM AIModel;
  SELECT COUNT(*) as 'ModelPlanAccess Count' FROM ModelPlanAccess;
"
```

### 3. Execute Migration
```bash
# Run in a screen/tmux session for safety
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production < production_migration.sql

# Or run interactively for section-by-section execution
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production
```

### 4. Verify Migration
```bash
# Run verification queries
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production < verification_queries.sql
```

### 5. Update Application Code
After successful migration, deploy the application code that uses the new schema.

## 📊 What Gets Migrated

### Models Table
- All fields from AIModel
- Extracted metadata fields:
  - `contextWindow`, `maxOutput` (from JSON)
  - `inputCostPer1M`, `outputCostPer1M` (from JSON)
  - `speedRating`, `qualityRating` (calculated)
  - Boolean capabilities (vision, functions, etc.)
- Original metadata preserved in `extendedMetadata`

### Plans System
- 6 standard plans (FREE → ENTERPRISE)
- Tier-based hierarchy
- Feature inheritance
- Flexible limits per plan

### Access Rules
- Each ModelPlanAccess → ModelPlanRules
- Supports multiple rule types:
  - Direct plan assignment
  - Tier-based access (e.g., "PLUS and above")
  - Conditional rules
  - Time-based rules

## 🔧 Key Features

### Performance Optimizations
```sql
-- Indexed fields for fast queries
INDEX idx_provider_enabled (providerId, isEnabled)
INDEX idx_capabilities (supportsVision, supportsFunctionCalling)
INDEX idx_performance (speedRating, qualityRating)
INDEX idx_pricing (inputCostPer1M, outputCostPer1M)
```

### Flexible Access Rules
```sql
-- Example: Give PLUS users access to all models > $1/1M tokens
INSERT INTO ModelPlanRules (modelId, ruleType, minPlanTier)
SELECT id, 'INCLUDE', 3
FROM Models
WHERE inputCostPer1M > 1.0;

-- Example: Time-limited access for FREE users
INSERT INTO ModelPlanRules (modelId, ruleType, planIds, effectiveUntil)
VALUES ('model-id', 'INCLUDE', '["FREE"]', '2025-08-01');
```

### Plan Inheritance
```sql
-- PLUS inherits all FREE features
-- MAX inherits all features up the chain
-- ENTERPRISE inherits features but not model limits
```

## ⚠️ Important Notes

1. **Transaction Safety**: The migration runs in a transaction - all changes succeed or all rollback
2. **Foreign Keys**: Added after data migration to avoid constraint issues
3. **UUID Generation**: Uses deterministic IDs for repeatability
4. **JSON Handling**: Robust extraction with multiple fallbacks
5. **View Creation**: `UserModelAccess` view simplifies model access queries

## 🔄 Rollback Plan

If needed, rollback commands are at the bottom of `production_migration.sql`:
```sql
DROP VIEW IF EXISTS UserModelAccess;
DROP TABLE IF EXISTS ModelPlanRules;
DROP TABLE IF EXISTS PlanInheritance;
DROP TABLE IF EXISTS Models;
DROP TABLE IF EXISTS Plans;
```

## 📈 Expected Results

After migration:
- ~200+ models in Models table
- 6 plans in Plans table
- ~1000+ rules in ModelPlanRules
- 5 inheritance relationships
- Improved query performance (5-10x faster for filtered queries)

## 🧪 Testing

1. Verify counts match between old and new tables
2. Test model access for each plan tier
3. Confirm capability flags migrated correctly
4. Check performance of common queries
5. Validate UserModelAccess view works correctly