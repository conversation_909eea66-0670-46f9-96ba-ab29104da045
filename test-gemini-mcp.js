require('dotenv').config();

async function testGeminiMCP() {
  console.log('🧪 Testing Gemini 2.5 Pro with MCP Image Tools...\n');
  
  // Test 1: Simple image generation
  console.log('Test 1: Simple image generation request');
  try {
    const response = await fetch('http://localhost:3004/api/mcp-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Create a picture of a sunset over mountains'
          }
        ],
        model: 'gemini-2.5-pro',
        enableImageTools: true
      })
    });

    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (data.data?.toolCalls) {
      console.log('\n✅ Gemini called image generation tool!');
      console.log('Tool name:', data.data.toolCalls[0]?.name);
      console.log('Arguments:', data.data.toolCalls[0]?.arguments);
    }
  } catch (error) {
    console.error('❌ Test 1 failed:', error.message);
  }
  
  // Test 2: Check Gemini configuration
  console.log('\n\nTest 2: Checking Gemini configuration');
  console.log('GOOGLE_API_KEY exists:', !!process.env.GOOGLE_API_KEY);
  console.log('GOOGLE_API_KEY length:', process.env.GOOGLE_API_KEY?.length);
  console.log('GOOGLE_API_KEY starts with:', process.env.GOOGLE_API_KEY?.substring(0, 10) + '...');
}

testGeminiMCP();