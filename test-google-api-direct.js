require('dotenv').config();
const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testGoogleAPIDirect() {
  console.log('🧪 Testing Google Generative AI SDK directly...\n');
  
  const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
  
  try {
    // Test basic chat
    console.log('📦 Testing basic chat with gemini-2.5-flash...');
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-2.5-flash',
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 100
      }
    });

    const result = await model.generateContent('Say "Hello" and nothing else.');
    const response = result.response;
    const text = response.text();
    
    console.log(`✅ Response: "${text}"`);
    console.log(`   Usage:`, response.usageMetadata);
    
    // Test streaming
    console.log('\n📦 Testing streaming with gemini-2.5-pro...');
    const streamModel = genAI.getGenerativeModel({ 
      model: 'gemini-2.5-pro',
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 100
      }
    });

    const streamResult = await streamModel.generateContentStream('Count from 1 to 5.');
    
    let fullText = '';
    for await (const chunk of streamResult.stream) {
      const chunkText = chunk.text();
      process.stdout.write(chunkText);
      fullText += chunkText;
    }
    
    console.log(`\n✅ Streamed ${fullText.length} characters`);
    
    // Get final response
    const finalResponse = await streamResult.response;
    console.log('   Final usage:', finalResponse.usageMetadata);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('   Response:', error.response);
    }
  }
}

testGoogleAPIDirect();