/**
 * CORRECTED COMPREHENSIVE ROUTER ANALYSIS WITH O3-PRO EXPERT CONSULTATION
 * 
 * This script performs a complete analysis of our AI router system with correct database schema
 */

import OpenAI from 'openai';
import mysql from 'mysql2/promise';
import { config } from 'dotenv';

config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Database connection
const dbConfig = {
  host: '127.0.0.1',
  user: 'root',
  password: process.env.MYSQL_PWD,
  database: 'justsimplechat_production'
};

class RouterAnalyzer {
  constructor() {
    this.connection = null;
    this.analysisData = {
      models: [],
      providers: [],
      mappings: [],
      currentLogic: {},
      performanceMetrics: {},
      issues: [],
      recommendations: []
    };
  }

  async initialize() {
    console.log('🔍 Initializing Comprehensive Router Analysis...\n');
    this.connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection established\n');
  }

  async analyzeModelDatabase() {
    console.log('📊 Analyzing Model Database...\n');

    // Get all models with provider information
    const [models] = await this.connection.execute(`
      SELECT 
        m.id,
        m.canonicalName,
        m.displayName,
        m.family,
        m.generation,
        m.modelType,
        m.primaryUseCase,
        m.isEnabled,
        m.defaultPriority,
        m.metadata,
        m.validationScore,
        m.validationStatus,
        m.createdAt,
        m.updatedAt,
        p.name as providerName,
        p.displayName as providerDisplayName
      FROM AIModel m
      LEFT JOIN AIProvider p ON m.providerId = p.id
      WHERE m.isEnabled = 1
      ORDER BY p.name, m.canonicalName
    `);

    // Get cost and scoring data
    const [costData] = await this.connection.execute(`
      SELECT 
        modelCanonicalName,
        AVG(CAST(JSON_EXTRACT(metadata, '$.cost') AS DECIMAL(10,6))) as avgCost,
        COUNT(*) as usageCount
      FROM model_mappings 
      WHERE JSON_EXTRACT(metadata, '$.cost') IS NOT NULL
      GROUP BY modelCanonicalName
    `);

    // Combine data
    const costMap = {};
    costData.forEach(cost => {
      costMap[cost.modelCanonicalName] = {
        avgCost: cost.avgCost,
        usageCount: cost.usageCount
      };
    });

    models.forEach(model => {
      const costInfo = costMap[model.canonicalName] || {};
      model.avgCost = costInfo.avgCost || null;
      model.usageCount = costInfo.usageCount || 0;
    });

    this.analysisData.models = models;

    console.log(`📈 Model Statistics:`);
    console.log(`- Total Active Models: ${models.length}`);
    
    // Provider distribution
    const providerStats = {};
    const typeStats = {};
    
    models.forEach(model => {
      const provider = model.providerName || 'Unknown';
      providerStats[provider] = (providerStats[provider] || 0) + 1;
      typeStats[model.modelType] = (typeStats[model.modelType] || 0) + 1;
    });

    console.log('\n📦 Provider Distribution:');
    Object.entries(providerStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([provider, count]) => {
        console.log(`  ${provider}: ${count} models (${((count/models.length)*100).toFixed(1)}%)`);
      });

    console.log('\n🔧 Model Type Distribution:');
    Object.entries(typeStats).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} models (${((count/models.length)*100).toFixed(1)}%)`);
    });

    // Cost analysis
    const modelsWithCost = models.filter(m => m.avgCost !== null && m.avgCost > 0);
    if (modelsWithCost.length > 0) {
      const costs = modelsWithCost.map(m => m.avgCost).sort((a,b) => a-b);
      console.log('\n💲 Cost Analysis:');
      console.log(`  Models with cost data: ${modelsWithCost.length}/${models.length} (${((modelsWithCost.length/models.length)*100).toFixed(1)}%)`);
      console.log(`  Cost range: $${costs[0].toFixed(6)} - $${costs[costs.length-1].toFixed(6)}`);
      console.log(`  Median cost: $${costs[Math.floor(costs.length/2)].toFixed(6)}`);
    }

    // Top models by usage/priority
    const topModels = models
      .filter(m => m.usageCount > 0)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 10);

    console.log('\n🏆 Top Models by Usage:');
    topModels.forEach((model, index) => {
      console.log(`  ${index + 1}. ${model.canonicalName} (${model.providerName}) - ${model.usageCount} mappings`);
    });

    return models;
  }

  async analyzeModelMappings() {
    console.log('\n🗺️ Analyzing Model Mappings...\n');

    const [mappings] = await this.connection.execute(`
      SELECT 
        category,
        complexity,
        COUNT(*) as mapping_count,
        AVG(score) as avg_score,
        MIN(score) as min_score,
        MAX(score) as max_score,
        GROUP_CONCAT(DISTINCT modelCanonicalName ORDER BY score DESC LIMIT 5) as top_models
      FROM model_mappings 
      GROUP BY category, complexity
      ORDER BY category, 
        CASE complexity 
          WHEN 'simple' THEN 1 
          WHEN 'standard' THEN 2 
          WHEN 'difficult' THEN 3 
          WHEN 'complex' THEN 4 
          ELSE 5 
        END
    `);

    this.analysisData.mappings = mappings;

    console.log('📊 Model Mapping Statistics:');
    console.log(`- Total Category/Complexity Combinations: ${mappings.length}`);

    // Group by category for analysis
    const categoryGroups = {};
    mappings.forEach(mapping => {
      if (!categoryGroups[mapping.category]) {
        categoryGroups[mapping.category] = [];
      }
      categoryGroups[mapping.category].push(mapping);
    });

    console.log('\n📋 Key Categories Analysis:');
    
    // Focus on critical categories
    const criticalCategories = ['coding', 'debugging', 'analysis', 'reasoning', 'general_chat', 'image_generation'];
    
    criticalCategories.forEach(category => {
      if (categoryGroups[category]) {
        const categoryMappings = categoryGroups[category];
        const totalCount = categoryMappings.reduce((sum, m) => sum + m.mapping_count, 0);
        console.log(`\n  📌 ${category.toUpperCase()}:`);
        console.log(`     Total mappings: ${totalCount}`);
        
        categoryMappings.forEach(mapping => {
          console.log(`     ${mapping.complexity}: ${mapping.mapping_count} models, avg score ${mapping.avg_score.toFixed(1)} (${mapping.min_score}-${mapping.max_score})`);
          if (mapping.top_models) {
            console.log(`       Top models: ${mapping.top_models.split(',').slice(0, 3).join(', ')}`);
          }
        });
      }
    });

    return mappings;
  }

  async analyzeCurrentRouterLogic() {
    console.log('\n🧠 Analyzing Current Router Logic...\n');

    // Read the router.ts file to analyze current logic
    const fs = await import('fs/promises');
    const routerPath = '/home/<USER>/deployments/dev/simplechat-ai/src/lib/ai/router.ts';
    
    try {
      const routerContent = await fs.readFile(routerPath, 'utf8');
      
      // Extract key components with better regex
      const costPenaltyMatch = routerContent.match(/calculateCostPenalty\([^{]+\{[\s\S]*?^  \}/m);
      const costToleranceMatch = routerContent.match(/determineCostTolerance\([^{]+\{[\s\S]*?^  \}/m);
      const complexityFirstMatch = routerContent.match(/pickBestModelWithComplexityFirst\([^{]+\{[\s\S]*?^  \}/m);
      
      // Extract price cap matrix
      const priceCapsMatch = routerContent.match(/const PRICE_CAP[^}]+\}/gs);

      this.analysisData.currentLogic = {
        hasCostPenalty: !!costPenaltyMatch,
        hasCostTolerance: !!costToleranceMatch,
        hasComplexityFirst: !!complexityFirstMatch,
        hasPriceCaps: !!priceCapsMatch,
        routerFileExists: true,
        fileSize: routerContent.length
      };

      console.log('✅ Router Logic Analysis:');
      console.log(`- Router file size: ${(routerContent.length / 1024).toFixed(1)}KB`);
      console.log(`- Cost Penalty Logic: ${this.analysisData.currentLogic.hasCostPenalty ? '✅ Present' : '❌ Missing'}`);
      console.log(`- Cost Tolerance Logic: ${this.analysisData.currentLogic.hasCostTolerance ? '✅ Present' : '❌ Missing'}`);
      console.log(`- Complexity-First Selection: ${this.analysisData.currentLogic.hasComplexityFirst ? '✅ Present' : '❌ Missing'}`);
      console.log(`- Price Cap Matrix: ${this.analysisData.currentLogic.hasPriceCaps ? '✅ Present' : '❌ Missing'}`);

      // Extract specific logic patterns
      if (priceCapsMatch) {
        console.log('\n💰 Price Cap Matrix Found:');
        console.log(priceCapsMatch[0].slice(0, 300) + '...');
      }

    } catch (error) {
      console.log(`❌ Error reading router file: ${error.message}`);
      this.analysisData.issues.push(`Cannot read router.ts file: ${error.message}`);
      this.analysisData.currentLogic.routerFileExists = false;
    }
  }

  async testCurrentIssues() {
    console.log('\n🐛 Analyzing Current Issues...\n');

    // Simulate the reported issue: "code me a complex python game" selecting Codestral
    const problematicQueries = [
      {
        query: "code me a complex python game",
        expectedBehavior: "Should select premium model (GPT-4o, Claude Sonnet) for MAX plan",
        reportedIssue: "Currently selects Codestral instead",
        category: "coding",
        complexity: "complex",
        userPlan: "MAX"
      },
      {
        query: "debug this complex algorithm with optimization",
        expectedBehavior: "Should select high-capability debugging model",
        reportedIssue: "May select cheaper model due to cost penalties",
        category: "debugging", 
        complexity: "complex",
        userPlan: "MAX"
      }
    ];

    // Analyze what models should be available for these scenarios
    const codingModels = this.analysisData.models.filter(m => 
      m.canonicalName.toLowerCase().includes('codestral') ||
      m.canonicalName.toLowerCase().includes('gpt-4') ||
      m.canonicalName.toLowerCase().includes('claude') ||
      m.canonicalName.toLowerCase().includes('sonnet')
    );

    console.log('🔍 Relevant Models for Coding Tasks:');
    codingModels.forEach(model => {
      console.log(`  - ${model.canonicalName} (${model.providerName}) - Score: ${model.validationScore || 'N/A'}`);
    });

    this.analysisData.issues = [
      "Premium models not selected for complex coding tasks on MAX plan",
      "Cost penalty logic may be too aggressive",
      "Router selecting Codestral over GPT-4o/Claude for complex tasks",
      "Need better cost vs. quality balance for different user plans"
    ];

    console.log('\n⚠️ Identified Issues:');
    this.analysisData.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }

  async consultO3Pro() {
    console.log('\n🤖 Consulting O3-Pro for Expert Analysis...\n');
    console.log('⏳ This may take 2-5 minutes due to o3-pro\'s deep reasoning...\n');

    const analysisPrompt = this.buildO3ProPrompt();

    try {
      const startTime = Date.now();
      
      const response = await openai.chat.completions.create({
        model: 'o3-pro',
        messages: [
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        reasoning_effort: 'high',
        temperature: 0.2,
        max_tokens: 4000
      });

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      console.log(`✅ O3-Pro Analysis Complete (${duration.toFixed(1)}s)\n`);

      const o3Analysis = response.choices[0].message.content;
      this.analysisData.o3ProRecommendations = {
        fullAnalysis: o3Analysis,
        processingTime: duration,
        reasoningTokens: response.usage?.reasoning_tokens || 'unknown',
        totalTokens: response.usage?.total_tokens || 'unknown'
      };

      return o3Analysis;

    } catch (error) {
      console.log(`❌ Error consulting O3-Pro: ${error.message}`);
      this.analysisData.issues.push(`O3-Pro consultation failed: ${error.message}`);
      return null;
    }
  }

  buildO3ProPrompt() {
    const modelSummary = this.summarizeModels();
    const mappingSummary = this.summarizeMappings();
    const currentLogic = this.summarizeCurrentLogic();
    const issues = this.analysisData.issues.join('\n- ');

    return `# AI Router Optimization Analysis Request

You are an expert AI system architect analyzing a production AI routing system that intelligently selects the best AI model for user queries. The system needs to balance performance, cost, and user experience.

## Current System Overview

### Model Inventory
${modelSummary}

### Scoring & Mapping System
${mappingSummary}

### Current Router Logic
${currentLogic}

## Current Issues
${issues}

## User Experience Goals
1. **Complex coding tasks** should get premium models (Claude Sonnet, GPT-4o, O3-mini) for MAX plan users
2. **Simple queries** should use cost-effective models to control expenses
3. **Specialized tasks** (image generation, analysis) should route to capable models
4. **Free/Basic users** should get good experience within budget constraints

## Specific Problem
Users report that when they ask "code me a complex python game" (detected as coding/complex), the router selects Codestral instead of premium models like GPT-4o or Claude Sonnet, even for MAX plan users.

## Analysis Request

Please provide a comprehensive analysis and optimization strategy covering:

### 1. Current Logic Assessment
What are the likely causes of premium models being avoided? How can we diagnose cost penalty vs. scoring issues?

### 2. Cost vs. Quality Optimization  
How should we balance cost control with quality across different user plans? What penalty multipliers make sense?

### 3. Model Selection Strategy
Given our model inventory, what selection strategy would optimize user experience while controlling costs?

### 4. Implementation Recommendations
Provide specific code changes or algorithm improvements with rationale. Focus on practical solutions.

### 5. Testing & Validation
How should we test and validate router improvements? What metrics should we track?

Focus on practical, implementable solutions that work with our existing data structure and don't require expensive model re-scoring or database changes.

Remember: We can't change the cost metadata or model scores easily, but we want to get the user experience "just right" without paying over the top.`;
  }

  summarizeModels() {
    const models = this.analysisData.models;
    if (!models.length) return 'No model data available';

    const totalModels = models.length;
    const providers = [...new Set(models.map(m => m.providerName))].filter(Boolean);
    
    // Top models by usage
    const topModels = models
      .filter(m => m.usageCount > 0)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 8);

    // Models by type
    const chatModels = models.filter(m => m.modelType === 'CHAT').length;
    const imageModels = models.filter(m => m.modelType === 'IMAGE_GENERATION').length;
    const codeModels = models.filter(m => m.modelType === 'CODE_GENERATION').length;

    return `
**${totalModels} active models** across ${providers.length} providers
**Providers**: ${providers.join(', ')}
**Model Types**: ${chatModels} CHAT, ${imageModels} IMAGE_GENERATION, ${codeModels} CODE_GENERATION

**Top Models by Usage**:
${topModels.map(m => `- ${m.canonicalName} (${m.providerName || 'Unknown'}): ${m.usageCount} mappings${m.avgCost ? `, $${m.avgCost.toFixed(6)} avg` : ''}`).join('\n')}

**Cost Data**: ${models.filter(m => m.avgCost).length}/${totalModels} models have cost information`;
  }

  summarizeMappings() {
    const mappings = this.analysisData.mappings;
    if (!mappings.length) return 'No mapping data available';

    const totalMappings = mappings.reduce((sum, m) => sum + m.mapping_count, 0);
    
    // Focus on coding and debugging (the problem areas)
    const codingMappings = mappings.filter(m => m.category === 'coding');
    const debuggingMappings = mappings.filter(m => m.category === 'debugging');

    return `
**${totalMappings} total model mappings** across ${mappings.length} category/complexity combinations

**CODING Task Scores** (primary issue area):
${codingMappings.length ? codingMappings.map(m => `- ${m.complexity}: ${m.mapping_count} models, avg score ${m.avg_score.toFixed(1)} (range: ${m.min_score}-${m.max_score})`).join('\n') : '- No coding mappings found'}

**DEBUGGING Task Scores**:
${debuggingMappings.length ? debuggingMappings.map(m => `- ${m.complexity}: ${m.mapping_count} models, avg score ${m.avg_score.toFixed(1)} (range: ${m.min_score}-${m.max_score})`).join('\n') : '- No debugging mappings found'}`;
  }

  summarizeCurrentLogic() {
    const logic = this.analysisData.currentLogic;
    
    return `
**Current Router Architecture**:
- Router file: ${logic.routerFileExists ? `✅ Found (${(logic.fileSize / 1024).toFixed(1)}KB)` : '❌ Missing'}
- Cost Penalty System: ${logic.hasCostPenalty ? '✅ Implemented' : '❌ Missing'}
- Cost Tolerance Logic: ${logic.hasCostTolerance ? '✅ Implemented' : '❌ Missing'}  
- Complexity-First Selection: ${logic.hasComplexityFirst ? '✅ Implemented' : '❌ Missing'}
- Price Cap Matrix: ${logic.hasPriceCaps ? '✅ Implemented' : '❌ Missing'}

**Known Implementation Details**:
- Recent fixes: Added intelligent cost tolerance for complex coding tasks
- Premium model penalty: Reduced from 0.7x to 0.95x for high tolerance scenarios
- Category-specific cost caps: Different budgets per task type and complexity`;
  }

  async generateReport() {
    console.log('\n📊 Generating Comprehensive Analysis Report...\n');

    const report = `
# COMPREHENSIVE AI ROUTER ANALYSIS REPORT
Generated: ${new Date().toISOString()}

## Executive Summary
- **Models Analyzed**: ${this.analysisData.models.length} active models
- **Mappings Analyzed**: ${this.analysisData.mappings.length} category/complexity combinations
- **Router Logic**: ${this.analysisData.currentLogic.routerFileExists ? 'Functional' : 'Missing'}
- **O3-Pro Consultation**: ${this.analysisData.o3ProRecommendations ? 'Completed' : 'Pending'}

## Model Inventory Analysis
${this.summarizeModels()}

## Mapping System Analysis  
${this.summarizeMappings()}

## Current Router Logic
${this.summarizeCurrentLogic()}

## Issues Identified
${this.analysisData.issues.map(issue => `- ${issue}`).join('\n')}

## O3-Pro Expert Recommendations
${this.analysisData.o3ProRecommendations ? 
  `Processing Time: ${this.analysisData.o3ProRecommendations.processingTime.toFixed(1)}s
Reasoning Tokens: ${this.analysisData.o3ProRecommendations.reasoningTokens}
Total Tokens: ${this.analysisData.o3ProRecommendations.totalTokens}

### Expert Analysis:
${this.analysisData.o3ProRecommendations.fullAnalysis}` : 
  'O3-Pro consultation is in progress or failed.'}

## Next Steps
1. Review O3-Pro recommendations
2. Implement suggested optimizations
3. Test router improvements
4. Monitor user experience metrics
5. Iterate based on performance data

---
Report generated by Comprehensive Router Analysis System
`;

    // Save report to file
    const fs = await import('fs/promises');
    const reportPath = '/tmp/comprehensive_router_analysis_report.md';
    await fs.writeFile(reportPath, report);
    
    console.log(`📄 Full report saved to: ${reportPath}`);
    
    return report;
  }

  async cleanup() {
    if (this.connection) {
      await this.connection.end();
      console.log('\n✅ Database connection closed');
    }
  }
}

// Main execution
async function main() {
  const analyzer = new RouterAnalyzer();
  
  try {
    await analyzer.initialize();
    await analyzer.analyzeModelDatabase();
    await analyzer.analyzeModelMappings();
    await analyzer.analyzeCurrentRouterLogic();
    await analyzer.testCurrentIssues();
    
    // The big moment - O3-Pro expert consultation
    console.log('🎯 Beginning O3-Pro Expert Consultation...');
    console.log('This is where we get professional AI system architecture advice!\n');
    
    const o3Analysis = await analyzer.consultO3Pro();
    
    if (o3Analysis) {
      console.log('💡 O3-Pro Expert Analysis:\n');
      console.log('='.repeat(80));
      console.log(o3Analysis);
      console.log('='.repeat(80));
    }
    
    const report = await analyzer.generateReport();
    console.log('\n🎯 Analysis Complete! Check the report for full details.');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error);
  } finally {
    await analyzer.cleanup();
  }
}

// Execute the analysis
main().catch(console.error);