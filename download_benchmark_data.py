#!/usr/bin/env python3
"""
Download Benchmark Data Collection Script
=========================================

Downloads current benchmark data from multiple sources:
1. Open-LLM Leaderboard (via HuggingFace datasets)
2. LMSYS Arena (already downloaded)
3. SWE-bench results
4. Papers-with-Code benchmarks
5. Raw datasets (GPQA, MATH, etc.)

Creates master CSV files with decimal precision preserved.
"""

import pandas as pd
import requests
from datasets import load_dataset
from datetime import datetime
import json
import os

def download_open_llm_leaderboard():
    """Download Open-LLM Leaderboard data from HuggingFace datasets"""
    print("📊 Downloading Open-LLM Leaderboard data...")
    
    try:
        # Load the official leaderboard dataset
        dataset = load_dataset("open-llm-leaderboard/contents", split="train")
        
        # Convert to pandas DataFrame
        df = dataset.to_pandas()
        
        # Save to CSV with decimal precision
        output_file = "open_llm_leaderboard_2025-07-06.csv"
        df.to_csv(output_file, index=False, float_format='%.3f')
        
        print(f"✅ Open-LLM Leaderboard saved: {output_file}")
        print(f"   📝 {len(df)} models with {len(df.columns)} metrics")
        print(f"   📊 Columns: {list(df.columns)}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error downloading Open-LLM Leaderboard: {e}")
        return None

def download_swe_bench_data():
    """Download SWE-bench benchmark results"""
    print("🔧 Downloading SWE-bench benchmark data...")
    
    try:
        # SWE-bench leaderboard URL (JSON API)
        swe_bench_url = "https://www.swebench.com/api/leaderboard"
        
        response = requests.get(swe_bench_url)
        if response.status_code == 200:
            data = response.json()
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            
            # Save to CSV
            output_file = "swe_bench_2025-07-06.csv"
            df.to_csv(output_file, index=False, float_format='%.2f')
            
            print(f"✅ SWE-bench data saved: {output_file}")
            print(f"   📝 {len(df)} models")
            
            return df
        else:
            print(f"❌ SWE-bench API returned status {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error downloading SWE-bench data: {e}")
        return None

def download_papers_with_code_data():
    """Download Papers-with-Code benchmark data"""
    print("📚 Downloading Papers-with-Code benchmark data...")
    
    benchmarks = [
        "humaneval",
        "vqav2", 
        "math",
        "gsm8k",
        "mmlu"
    ]
    
    all_results = []
    
    for benchmark in benchmarks:
        try:
            # Papers-with-Code API endpoint
            url = f"https://paperswithcode.com/api/v1/sota/{benchmark}/"
            
            response = requests.get(url)
            if response.status_code == 200:
                data = response.json()
                
                # Extract results for each model
                for result in data.get('results', []):
                    model_name = result.get('model', {}).get('name', 'Unknown')
                    metrics = result.get('metrics', {})
                    
                    row = {
                        'model': model_name,
                        'benchmark': benchmark,
                        'paper': result.get('paper', {}).get('title', ''),
                        'date': result.get('date', ''),
                    }
                    
                    # Add all metrics
                    for metric_name, metric_value in metrics.items():
                        row[f"{benchmark}_{metric_name}"] = metric_value
                    
                    all_results.append(row)
                
                print(f"   ✅ {benchmark}: {len(data.get('results', []))} results")
            else:
                print(f"   ❌ {benchmark}: API returned status {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {benchmark}: Error {e}")
    
    if all_results:
        df = pd.DataFrame(all_results)
        output_file = "papers_with_code_2025-07-06.csv"
        df.to_csv(output_file, index=False, float_format='%.3f')
        
        print(f"✅ Papers-with-Code data saved: {output_file}")
        print(f"   📝 {len(df)} benchmark results")
        
        return df
    else:
        print("❌ No Papers-with-Code data collected")
        return None

def download_gpqa_math_datasets():
    """Download raw GPQA and MATH datasets"""
    print("🧮 Downloading GPQA and MATH raw datasets...")
    
    datasets_info = []
    
    try:
        # GPQA Diamond dataset
        print("   📊 Loading GPQA Diamond...")
        gpqa_dataset = load_dataset("Idavidrein/gpqa", "gpqa_diamond", split="train")
        gpqa_df = gpqa_dataset.to_pandas()
        
        gpqa_file = "gpqa_diamond_2025-07-06.csv"
        gpqa_df.to_csv(gpqa_file, index=False)
        
        datasets_info.append({
            'name': 'GPQA Diamond',
            'file': gpqa_file,
            'samples': len(gpqa_df),
            'columns': list(gpqa_df.columns)
        })
        
        print(f"   ✅ GPQA Diamond: {len(gpqa_df)} samples")
        
    except Exception as e:
        print(f"   ❌ GPQA Diamond error: {e}")
    
    try:
        # MATH dataset
        print("   📊 Loading MATH dataset...")
        math_dataset = load_dataset("lighteval/MATH", split="train")
        math_df = math_dataset.to_pandas()
        
        math_file = "math_dataset_2025-07-06.csv"
        math_df.to_csv(math_file, index=False)
        
        datasets_info.append({
            'name': 'MATH',
            'file': math_file,
            'samples': len(math_df),
            'columns': list(math_df.columns)
        })
        
        print(f"   ✅ MATH dataset: {len(math_df)} samples")
        
    except Exception as e:
        print(f"   ❌ MATH dataset error: {e}")
    
    return datasets_info

def create_master_benchmark_csv():
    """Create master benchmark CSV with all collected data"""
    print("🎯 Creating master benchmark CSV...")
    
    # Load LMSYS Arena data (already downloaded)
    try:
        arena_df = pd.read_csv("lmsys_arena_2025-07-05.csv")
        print(f"   ✅ Loaded Arena data: {len(arena_df)} models")
    except:
        print("   ❌ Could not load Arena data")
        arena_df = None
    
    # Load Open-LLM data if it exists
    try:
        ollm_df = pd.read_csv("open_llm_leaderboard_2025-07-06.csv")
        print(f"   ✅ Loaded Open-LLM data: {len(ollm_df)} models")
    except:
        print("   ❌ Could not load Open-LLM data")
        ollm_df = None
    
    # Load SWE-bench data if it exists
    try:
        swe_df = pd.read_csv("swe_bench_2025-07-06.csv")
        print(f"   ✅ Loaded SWE-bench data: {len(swe_df)} models")
    except:
        print("   ❌ Could not load SWE-bench data")
        swe_df = None
    
    # Start with Arena data as base (has good model names)
    if arena_df is not None:
        master_df = arena_df[['model', 'arena_score', 'votes', 'organization']].copy()
        master_df.columns = ['model', 'arena_elo', 'arena_votes', 'organization']
    else:
        master_df = pd.DataFrame(columns=['model'])
    
    # Add other data sources by merging on model name
    # This is a simplified merge - in production you'd want fuzzy matching
    
    print(f"✅ Master benchmark CSV created with {len(master_df)} models")
    
    # Save with timestamp and decimal preservation
    timestamp = datetime.now().strftime("%Y-%m-%d")
    output_file = f"benchmark_master_{timestamp}.csv"
    master_df.to_csv(output_file, index=False, float_format='%.2f')
    
    print(f"📊 Master file saved: {output_file}")
    
    return master_df

def main():
    """Main execution function"""
    print("🚀 BENCHMARK DATA COLLECTION PIPELINE")
    print("=" * 60)
    
    # Track successful downloads
    success_count = 0
    total_downloads = 5
    
    # 1. Open-LLM Leaderboard
    if download_open_llm_leaderboard() is not None:
        success_count += 1
    
    # 2. SWE-bench (already have Arena data)
    if download_swe_bench_data() is not None:
        success_count += 1
    
    # 3. Papers-with-Code
    if download_papers_with_code_data() is not None:
        success_count += 1
    
    # 4. Raw datasets
    raw_datasets = download_gpqa_math_datasets()
    if raw_datasets:
        success_count += 1
    
    # 5. Master CSV
    master_df = create_master_benchmark_csv()
    if master_df is not None:
        success_count += 1
    
    print("\n📊 DOWNLOAD SUMMARY")
    print("-" * 40)
    print(f"✅ Successful: {success_count}/{total_downloads} downloads")
    print(f"📁 Files created in current directory")
    print(f"🎯 Ready for V6.8 evidence-based validation!")
    
    return success_count == total_downloads

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)