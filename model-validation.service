[Unit]
Description=AI Model Validation Service
After=network.target

[Service]
Type=oneshot
User=ec2-user
WorkingDirectory=/home/<USER>/deployments/dev/simplechat-ai
Environment=BASH_DEFAULT_TIMEOUT_MS=3600000
ExecStart=/home/<USER>/deployments/dev/simplechat-ai/automate_validation.sh
StandardOutput=append:/home/<USER>/deployments/logs/validation_automation.log
StandardError=append:/home/<USER>/deployments/logs/validation_automation.log

[Install]
WantedBy=multi-user.target