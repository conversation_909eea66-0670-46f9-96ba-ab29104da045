import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  experimental: {
    // Optimize package imports for better tree-shaking and smaller bundles
    optimizePackageImports: [
      'lucide-react', 
      '@radix-ui/react-*',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-tooltip',
      'react-markdown',
      'remark',
      'rehype',
    ],
    // Enable CSS optimization (experimental but stable)
    optimizeCss: true,
    // Reduce memory usage during builds
    webpackMemoryOptimizations: true,
  },
  // Runtime configuration for extended timeouts
  serverRuntimeConfig: {
    // Extended timeout for server-side functions (15 minutes for o3-pro models)
    maxDuration: 900,
  },
  // Strict mode for better development
  reactStrictMode: true,
  // Disable TypeScript checking during build (emergency)
  typescript: {
    ignoreBuildErrors: true,
  },
  // Optimize production builds
  productionBrowserSourceMaps: false,
  // Compress responses
  compress: true,
  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        pathname: '/**',
      },
    ],
    formats: ['image/avif', 'image/webp'],
  },
  // Disable build ID to let Next.js handle it
  // This prevents cache issues
  // Headers for cache control
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, max-age=0',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ]
  },
}

export default nextConfig