#!/bin/bash
# Setup systemd timer for automated model validation
# Runs one validation task every 5 minutes

echo "🎯 Setting up automated model validation via systemd"

# Copy service and timer files to systemd directory
sudo cp model-validation.service /etc/systemd/system/
sudo cp model-validation.timer /etc/systemd/system/

# Set proper permissions
sudo chmod 644 /etc/systemd/system/model-validation.service
sudo chmod 644 /etc/systemd/system/model-validation.timer

# Reload systemd daemon
sudo systemctl daemon-reload

# Enable and start the timer
sudo systemctl enable model-validation.timer
sudo systemctl start model-validation.timer

echo "✅ Systemd timer configured successfully!"
echo ""
echo "📊 Timer Status:"
sudo systemctl status model-validation.timer --no-pager

echo ""
echo "🚀 Automation Status:"
echo "   - Frequency: Every 5 minutes"
echo "   - Service: model-validation.service"
echo "   - Timer: model-validation.timer"
echo "   - Logs: /home/<USER>/deployments/logs/validation_automation.log"
echo ""
echo "🔧 Management Commands:"
echo "   - View timer status: sudo systemctl status model-validation.timer"
echo "   - View service status: sudo systemctl status model-validation.service"
echo "   - View logs: sudo journalctl -u model-validation.service -f"
echo "   - Stop automation: sudo systemctl stop model-validation.timer"
echo "   - Start automation: sudo systemctl start model-validation.timer"
echo "   - Manual run: sudo systemctl start model-validation.service"
echo ""
echo "⚡ First execution will start within 5 minutes!"