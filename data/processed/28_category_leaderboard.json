{
  "generated": "2025-07-11T19:49:31.371114",
  "categories": [
    "coding",
    "debugging",
    "creative_writing",
    "general_chat",
    "reasoning",
    "math",
    "analysis",
    "translation",
    "summarization",
    "question_answering",
    "data_analysis",
    "tutorial",
    "brainstorming",
    "role_play",
    "technical_writing",
    "business_writing",
    "legal",
    "medical",
    "scientific",
    "philosophical",
    "historical",
    "current_events",
    "personal_advice",
    "image_generation",
    "image_analysis",
    "multimodal",
    "factual_qa",
    "other"
  ],
  "leaderboard": {
    "coding": [
      {
        "rank": 1,
        "model": "gemini/gemini-2.5-pro",
        "display_name": "Gemini 2.5 Pro (Latest)",
        "score": 98.0,
        "confidence": 0.95,
        "cost_per_1m": 1.25
      },
      {
        "rank": 2,
        "model": "openai/o3",
        "display_name": "O3",
        "score": 95.0,
        "confidence": 0.95,
        "cost_per_1m": 2.0
      },
      {
        "rank": 3,
        "model": "anthropic/claude-3.5-sonnet",
        "display_name": "Claude 3.5 Sonnet",
        "score": 94.0,
        "confidence": 0.98,
        "cost_per_1m": 0.003
      },
      {
        "rank": 4,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 92.5,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 5,
        "model": "openai/gpt-4o",
        "display_name": "GPT-4o",
        "score": 90.0,
        "confidence": 0.95,
        "cost_per_1m": 2.5
      },
      {
        "rank": 6,
        "model": "openai/gpt-4o-mini",
        "display_name": "GPT-4o Mini",
        "score": 88.0,
        "confidence": 0.95,
        "cost_per_1m": 0.15
      },
      {
        "rank": 7,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 8,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 9,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 10,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      }
    ],
    "debugging": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 89.84,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 71.09,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 71.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      }
    ],
    "creative_writing": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 79.39,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "xai/grok-3-mini",
        "display_name": "Grok 3 Mini",
        "score": 78.0,
        "confidence": 0.87,
        "cost_per_1m": 0.3
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 76.83,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 76.83,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 76.58,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 76.58,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 76.34,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 75.97,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 75.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1",
        "display_name": "GPT-4.1",
        "score": 71.33,
        "confidence": 0.85,
        "cost_per_1m": 2.0
      }
    ],
    "general_chat": [
      {
        "rank": 1,
        "model": "openai/gpt-4o",
        "display_name": "GPT-4o",
        "score": 89.5,
        "confidence": 0.95,
        "cost_per_1m": 2.5
      },
      {
        "rank": 2,
        "model": "openai/gpt-4o-mini",
        "display_name": "GPT-4o Mini",
        "score": 88.0,
        "confidence": 0.95,
        "cost_per_1m": 0.15
      },
      {
        "rank": 3,
        "model": "gemini/gemini-2.5-pro",
        "display_name": "Gemini 2.5 Pro (Latest)",
        "score": 87.0,
        "confidence": 0.98,
        "cost_per_1m": 1.25
      },
      {
        "rank": 4,
        "model": "openai/o3",
        "display_name": "O3",
        "score": 85.0,
        "confidence": 0.98,
        "cost_per_1m": 2.0
      },
      {
        "rank": 5,
        "model": "gemini/gemini-2.5-flash-lite-preview-06-17",
        "display_name": "Gemini 2.5 Flash Lite",
        "score": 84.0,
        "confidence": 0.75,
        "cost_per_1m": 0.1
      },
      {
        "rank": 6,
        "model": "alibaba/qwen-plus-2025-04-28",
        "display_name": "Qwen Plus 2025-04-28",
        "score": 83.0,
        "confidence": 0.9,
        "cost_per_1m": 1.4
      },
      {
        "rank": 7,
        "model": "xai/grok-3-mini",
        "display_name": "Grok 3 Mini",
        "score": 80.0,
        "confidence": 0.95,
        "cost_per_1m": 0.3
      },
      {
        "rank": 8,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 79.86,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 9,
        "model": "groq/llama-3.3-70b-versatile",
        "display_name": "Llama 3.3 70b Versatile R1",
        "score": 78.5,
        "confidence": 0.95,
        "cost_per_1m": 0.59
      },
      {
        "rank": 10,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 77.28,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      }
    ],
    "reasoning": [
      {
        "rank": 1,
        "model": "openai/o3",
        "display_name": "O3",
        "score": 97.0,
        "confidence": 0.95,
        "cost_per_1m": 2.0
      },
      {
        "rank": 2,
        "model": "gemini/gemini-2.5-pro",
        "display_name": "Gemini 2.5 Pro (Latest)",
        "score": 96.0,
        "confidence": 0.98,
        "cost_per_1m": 1.25
      },
      {
        "rank": 3,
        "model": "anthropic/claude-3.5-sonnet",
        "display_name": "Claude 3.5 Sonnet",
        "score": 95.0,
        "confidence": 0.95,
        "cost_per_1m": 0.003
      },
      {
        "rank": 4,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 92.25,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 5,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 75.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 6,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 7,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 8,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 9,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 10,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      }
    ],
    "math": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 95.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 71.09,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1",
        "display_name": "GPT-4.1",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 2.0
      }
    ],
    "analysis": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 79.2,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 75.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 71.09,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 70.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 70.0,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "translation": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 83.11,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 80.42,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 80.42,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 80.17,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 80.17,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 79.91,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 79.53,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 79.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 74.67,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 74.67,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      }
    ],
    "summarization": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 82.26,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 78.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 77.68,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 77.31,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "question_answering": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 88.78,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 77.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 75.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 74.64,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 72.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 72.0,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "data_analysis": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 82.36,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 73.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 71.09,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 68.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 68.0,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "tutorial": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 86.18,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 78.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 77.68,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 77.31,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "brainstorming": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 81.59,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 75.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 75.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 74.64,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      }
    ],
    "role_play": [
      {
        "rank": 1,
        "model": "xai/grok-3-mini",
        "display_name": "Grok 3 Mini",
        "score": 85.0,
        "confidence": 0.6,
        "cost_per_1m": 0.3
      },
      {
        "rank": 2,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 77.54,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 75.03,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 75.03,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 74.79,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 74.79,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 74.55,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 74.2,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1",
        "display_name": "GPT-4.1",
        "score": 69.66,
        "confidence": 0.85,
        "cost_per_1m": 2.0
      }
    ],
    "technical_writing": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 81.82,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 78.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 77.28,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 77.28,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 77.03,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 77.03,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 76.79,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 76.42,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 73.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 73.0,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "business_writing": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 82.26,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 78.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 77.68,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 77.31,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "legal": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 80.51,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 76.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 74.58,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 74.58,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 74.34,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 74.34,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 74.11,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 73.75,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 71.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 71.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "medical": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 80.51,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 76.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 74.58,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 74.58,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 74.34,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 74.34,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 74.11,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 73.75,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 71.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 71.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "scientific": [
      {
        "rank": 1,
        "model": "openai/o3",
        "display_name": "O3",
        "score": 95.0,
        "confidence": 0.95,
        "cost_per_1m": 2.0
      },
      {
        "rank": 2,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 84.42,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 75.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 4,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 5,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 6,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 7,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 8,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 9,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 71.09,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 10,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 70.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      }
    ],
    "philosophical": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 88.25,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 75.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 73.23,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 73.23,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 73.0,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 73.0,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 72.77,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 72.42,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 70.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 70.0,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "historical": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 81.82,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 78.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 77.28,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 77.28,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 77.03,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 77.03,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 76.79,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 76.42,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 73.0,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 73.0,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "current_events": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 82.26,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 78.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 78.18,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 77.93,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 77.68,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 77.31,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 73.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "personal_advice": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 78.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 75.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 74.64,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 74.0,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      }
    ],
    "image_generation": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 77.54,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 75.03,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 75.03,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 74.79,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 74.79,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 74.55,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 74.2,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 69.66,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 69.66,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1",
        "display_name": "GPT-4.1",
        "score": 69.66,
        "confidence": 0.85,
        "cost_per_1m": 2.0
      }
    ],
    "image_analysis": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 75.27,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 71.89,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 71.66,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 71.43,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 71.09,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1",
        "display_name": "GPT-4.1",
        "score": 66.74,
        "confidence": 0.85,
        "cost_per_1m": 2.0
      }
    ],
    "multimodal": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 78.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 3,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 75.48,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 4,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 5,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 75.24,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 6,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 75.0,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 7,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 74.64,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 8,
        "model": "openai/gpt-4.1-mini",
        "display_name": "GPT-4.1 Mini",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 9,
        "model": "openai/gpt-4.1-mini-2025-04-14",
        "display_name": "GPT-4.1 Mini (2025-04-14)",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 0.4
      },
      {
        "rank": 10,
        "model": "openai/gpt-4.1",
        "display_name": "GPT-4.1",
        "score": 70.08,
        "confidence": 0.85,
        "cost_per_1m": 2.0
      }
    ],
    "factual_qa": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 89.65,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 76.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 74.58,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 74.58,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 74.34,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 74.34,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 74.11,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 73.75,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 71.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 71.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ],
    "other": [
      {
        "rank": 1,
        "model": "xai/grok-4-0709",
        "display_name": "Grok 4",
        "score": 87.91,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 2,
        "model": "openai/dall-e-3",
        "display_name": "DALL-E 3",
        "score": 77.5,
        "confidence": 0.5,
        "cost_per_1m": 0
      },
      {
        "rank": 3,
        "model": "openai/chatgpt-4o-latest",
        "display_name": "ChatGPT 4o Latest",
        "score": 76.38,
        "confidence": 0.85,
        "cost_per_1m": 5.0
      },
      {
        "rank": 4,
        "model": "openai/gpt-4",
        "display_name": "GPT-4",
        "score": 76.38,
        "confidence": 0.85,
        "cost_per_1m": 30.0
      },
      {
        "rank": 5,
        "model": "openai/o3-mini",
        "display_name": "OpenAI O3 Mini",
        "score": 76.14,
        "confidence": 0.85,
        "cost_per_1m": 1.1
      },
      {
        "rank": 6,
        "model": "openai/o3-pro",
        "display_name": "O3-Pro",
        "score": 76.14,
        "confidence": 0.85,
        "cost_per_1m": 20.0
      },
      {
        "rank": 7,
        "model": "openrouter/deepseek/deepseek-r1-0528",
        "display_name": "DeepSeek R1 0528",
        "score": 75.89,
        "confidence": 0.85,
        "cost_per_1m": 0
      },
      {
        "rank": 8,
        "model": "xai/grok-3",
        "display_name": "Grok 3 Fast",
        "score": 75.53,
        "confidence": 0.85,
        "cost_per_1m": 3.0
      },
      {
        "rank": 9,
        "model": "mistral/codestral-latest",
        "display_name": "Codestral",
        "score": 72.5,
        "confidence": 0.5,
        "cost_per_1m": 0.3
      },
      {
        "rank": 10,
        "model": "mistral/devstral-small-2505",
        "display_name": "Devstral Small",
        "score": 72.5,
        "confidence": 0.5,
        "cost_per_1m": 0.1
      }
    ]
  },
  "champions": {
    "coding": {
      "model": "Gemini 2.5 Pro (Latest)",
      "score": 