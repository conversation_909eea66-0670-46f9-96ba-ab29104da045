{"gemini-2.5-pro": "gemini-2.5-pro", "gemini25pro": "gemini-2.5-pro", "gemini-2.5-pro-preview-05-06": "gemini-2.5-pro-preview-05-06", "gemini25propreview0506": "gemini-2.5-pro-preview-05-06", "chatgpt-4o-latest (2025-03-26)": "chatgpt-4o-latest-(2025-03-26)", "chatgpt4olatest (20250326)": "chatgpt-4o-latest-(2025-03-26)", "o3-2025-04-16": "o3-2025-04-16", "o320250416": "o3-2025-04-16", "deepseek-r1-0528": "deepseek-r1-0528", "deepseekr10528": "deepseek-r1-0528", "grok-3-preview-02-24": "grok-3-preview-02-24", "grok3preview0224": "grok-3-preview-02-24", "gemini-2.5-flash": "gemini-2.5-flash", "gemini25flash": "gemini-2.5-flash", "gpt-4.5-preview": "gpt-4.5-preview", "gpt45preview": "gpt-4.5-preview", "gemini-2.5-flash-preview-04-17": "gemini-2.5-flash-preview-04-17", "gemini25flashpreview0417": "gemini-2.5-flash-preview-04-17", "qwen3-235b-a22b-no-thinking": "qwen3-235b-a22b-no-thinking", "qwen3235ba22bnothinking": "qwen3-235b-a22b-no-thinking", "gpt-4.1-2025-04-14": "gpt-4.1-2025-04-14", "gpt4120250414": "gpt-4.1-2025-04-14", "deepseek-v3-0324": "deepseek-v3-0324", "deepseekv30324": "deepseek-v3-0324", "hunyuan-turbos-20250416": "hunyuan-turbos-20250416", "hunyuanturbos20250416": "hunyuan-turbos-20250416", "minimax-m1": "minimax-m1", "minimaxm1": "minimax-m1", "deepseek-r1": "deepseek-r1", "deepseekr1": "deepseek-r1", "mistral medium 3": "mistral-medium-3", "claude opus 4 (20250514)": "claude-opus-4-(20250514)", "qwen3-235b-a22b": "qwen3-235b-a22b", "qwen3235ba22b": "qwen3-235b-a22b", "o1-2024-12-17": "o1-2024-12-17", "o120241217": "o1-2024-12-17", "o4-mini-2025-04-16": "o4-mini-2025-04-16", "o4mini20250416": "o4-mini-2025-04-16", "qwen2.5-max": "qwen2.5-max", "qwen25max": "qwen2.5-max", "gemini-2.0-flash-001": "gemini-2.0-flash-001", "gemini20flash001": "gemini-2.0-flash-001", "grok-3-mini-beta": "grok-3-mini-beta", "grok3minibeta": "grok-3-mini-beta", "gemma-3-27b-it": "gemma-3-27b-it", "gemma327bit": "gemma-3-27b-it", "qwen3-32b": "qwen3-32b", "qwen332b": "qwen3-32b", "o1-preview": "o1-preview", "o1preview": "o1-preview", "claude sonnet 4 (20250514)": "claude-sonnet-4-(20250514)", "o3-mini-high": "o3-mini-high", "o3minihigh": "o3-mini-high", "gemma-3-12b-it": "gemma-3-12b-it", "gemma312bit": "gemma-3-12b-it", "gpt-4.1-mini-2025-04-14": "gpt-4.1-mini-2025-04-14", "gpt41mini20250414": "gpt-4.1-mini-2025-04-14", "deepseek-v3": "deepseek-v3", "deepseekv3": "deepseek-v3", "mistral-small-2506": "mistral-small-2506", "mistralsmall2506": "mistral-small-2506", "qwq-32b": "qwq-32b", "qwq32b": "qwq-32b", "glm-4-plus-0111": "glm-4-plus-0111", "glm4plus0111": "glm-4-plus-0111", "gemini-2.0-flash-lite": "gemini-2.0-flash-lite", "gemini20flashlite": "gemini-2.0-flash-lite", "amazon-nova-experimental-chat-05-14": "amazon-nova-experimental-chat-05-14", "amazonnovaexperimentalchat0514": "amazon-nova-experimental-chat-05-14", "qwen-plus-0125": "qwen-plus-0125", "qwenplus0125": "qwen-plus-0125", "llama-3.1-nemotron-ultra-253b-v1": "llama-3.1-nemotron-ultra-253b-v1", "llama31nemotronultra253bv1": "llama-3.1-nemotron-ultra-253b-v1", "command a (03-2025)": "command-a-(03-2025)", "command a (032025)": "command-a-(03-2025)", "qwen3-30b-a3b": "qwen3-30b-a3b", "qwen330ba3b": "qwen3-30b-a3b", "step-2-16k-exp": "step-2-16k-exp", "step216kexp": "step-2-16k-exp", "hunyuan-turbos-20250226": "hunyuan-turbos-20250226", "hunyuanturbos20250226": "hunyuan-turbos-20250226", "llama-3.3-nemotron-super-49b-v1": "llama-3.3-nemotron-super-49b-v1", "llama33nemotronsuper49bv1": "llama-3.3-nemotron-super-49b-v1", "o1-mini": "o1-mini", "o1mini": "o1-mini", "o3-mini": "o3-mini", "o3mini": "o3-mini", "gemini-1.5-pro-002": "gemini-1.5-pro-002", "gemini15pro002": "gemini-1.5-pro-002", "hunyuan-turbo-0110": "hunyuan-turbo-0110", "hunyuanturbo0110": "hunyuan-turbo-0110", "claude 3.7 sonnet (thinking-32k)": "claude-3.7-sonnet-(thinking-32k)", "claude 37 sonnet (thinking32k)": "claude-3.7-sonnet-(thinking-32k)", "gemma-3n-e4b-it": "gemma-3n-e4b-it", "gemma3ne4bit": "gemma-3n-e4b-it", "claude 3.7 sonnet": "claude-3.7-sonnet", "claude 37 sonnet": "claude-3.7-sonnet", "grok-2-08-13": "grok-2-08-13", "grok20813": "grok-2-08-13", "yi-lightning": "yi-lightning", "yilightning": "yi-lightning", "gpt-4o-2024-05-13": "gpt-4o-2024-05-13", "gpt4o20240513": "gpt-4o-2024-05-13", "qwen2.5-plus-1127": "qwen2.5-plus-1127", "qwen25plus1127": "qwen2.5-plus-1127", "claude 3.5 sonnet (20241022)": "claude-3.5-sonnet-(20241022)", "claude 35 sonnet (20241022)": "claude-3.5-sonnet-(20241022)", "deepseek-v2.5-1210": "deepseek-v2.5-1210", "deepseekv251210": "deepseek-v2.5-1210", "gemma-3-4b-it": "gemma-3-4b-it", "gemma34bit": "gemma-3-4b-it", "llama-4-maverick-17b-128e-instruct": "llama-4-maverick-17b-128e-instruct", "llama4maverick17b128einstruct": "llama-4-maverick-17b-128e-instruct", "athene-v2-chat-72b": "athene-v2-chat-72b", "athenev2chat72b": "athene-v2-chat-72b", "glm-4-plus": "glm-4-plus", "glm4plus": "glm-4-plus", "hunyuan-large-2025-02-10": "hunyuan-large-2025-02-10", "hunyuanlarge20250210": "hunyuan-large-2025-02-10", "gpt-4.1-nano-2025-04-14": "gpt-4.1-nano-2025-04-14", "gpt41nano20250414": "gpt-4.1-nano-2025-04-14", "gemini-1.5-flash-002": "gemini-1.5-flash-002", "gemini15flash002": "gemini-1.5-flash-002", "llama-3.1-nemotron-70b-instruct": "llama-3.1-nemotron-70b-instruct", "llama31nemotron70binstruct": "llama-3.1-nemotron-70b-instruct", "gpt-4o-mini-2024-07-18": "gpt-4o-mini-2024-07-18", "gpt4omini20240718": "gpt-4o-mini-2024-07-18", "meta-llama-3.1-405b-instruct-bf16": "meta-llama-3.1-405b-instruct-bf16", "metallama31405binstructbf16": "meta-llama-3.1-405b-instruct-bf16", "claude 3.5 sonnet (20240620)": "claude-3.5-sonnet-(20240620)", "claude 35 sonnet (20240620)": "claude-3.5-sonnet-(20240620)", "meta-llama-3.1-405b-instruct-fp8": "meta-llama-3.1-405b-instruct-fp8", "metallama31405binstructfp8": "meta-llama-3.1-405b-instruct-fp8", "gemini advanced app (2024-05-14)": "gemini-advanced-app-(2024-05-14)", "gemini advanced app (20240514)": "gemini-advanced-app-(2024-05-14)", "hunyuan-standard-2025-02-10": "hunyuan-standard-2025-02-10", "hunyuanstandard20250210": "hunyuan-standard-2025-02-10", "grok-2-mini-08-13": "grok-2-mini-08-13", "grok2mini0813": "grok-2-mini-08-13", "gpt-4o-2024-08-06": "gpt-4o-2024-08-06", "gpt4o20240806": "gpt-4o-2024-08-06", "qwen-max-0919": "qwen-max-0919", "qwenmax0919": "qwen-max-0919", "llama-4-scout-17b-16e-instruct": "llama-4-scout-17b-16e-instruct", "llama4scout17b16einstruct": "llama-4-scout-17b-16e-instruct", "mistral-small-3.1-24b-instruct-2503": "mistral-small-3.1-24b-instruct-2503", "mistralsmall3124binstruct2503": "mistral-small-3.1-24b-instruct-2503", "gemini-1.5-pro-001": "gemini-1.5-pro-001", "gemini15pro001": "gemini-1.5-pro-001", "deepseek-v2.5": "deepseek-v2.5", "deepseekv25": "deepseek-v2.5", "llama-3.3-70b-instruct": "llama-3.3-70b-instruct", "llama3370binstruct": "llama-3.3-70b-instruct", "qwen2.5-72b-instruct": "qwen2.5-72b-instruct", "qwen2572binstruct": "qwen2.5-72b-instruct", "gpt-4-turbo-2024-04-09": "gpt-4-turbo-2024-04-09", "gpt4turbo20240409": "gpt-4-turbo-2024-04-09", "llama-3.1-tulu-3-70b": "llama-3.1-tulu-3-70b", "llama31tulu370b": "llama-3.1-tulu-3-70b", "mistral-large-2407": "mistral-large-2407", "mistrallarge2407": "mistral-large-2407", "athene-70b": "athene-70b", "athene70b": "athene-70b", "gpt-4-1106-preview": "gpt-4-1106-preview", "gpt41106preview": "gpt-4-1106-preview", "mistral-large-2411": "mistral-large-2411", "mistrallarge2411": "mistral-large-2411", "magistral-medium-2506": "magistral-medium-2506", "magistralmedium2506": "magistral-medium-2506", "meta-llama-3.1-70b-instruct": "meta-llama-3.1-70b-instruct", "metallama3170binstruct": "meta-llama-3.1-70b-instruct", "claude 3 opus": "claude-3-opus", "amazon nova pro 1.0": "amazon-nova-pro-1.0", "amazon nova pro 10": "amazon-nova-pro-1.0", "gpt-4-0125-preview": "gpt-4-0125-preview", "gpt40125preview": "gpt-4-0125-preview", "claude 3.5 haiku (20241022)": "claude-3.5-haiku-(20241022)", "claude 35 haiku (20241022)": "claude-3.5-haiku-(20241022)", "reka-core-20240904": "reka-core-20240904", "rekacore20240904": "reka-core-20240904", "hunyuan-large-vision": "hunyuan-large-vision", "hunyuanlargevision": "hunyuan-large-vision", "gemini-1.5-flash-001": "gemini-1.5-flash-001", "gemini15flash001": "gemini-1.5-flash-001", "jamba-1.5-large": "jamba-1.5-large", "jamba15large": "jamba-1.5-large", "qwen2.5-coder-32b-instruct": "qwen2.5-coder-32b-instruct", "qwen25coder32binstruct": "qwen2.5-coder-32b-instruct", "gemma-2-27b-it": "gemma-2-27b-it", "gemma227bit": "gemma-2-27b-it", "mistral-small-24b-instruct-2501": "mistral-small-24b-instruct-2501", "mistralsmall24binstruct2501": "mistral-small-24b-instruct-2501", "amazon nova lite 1.0": "amazon-nova-lite-1.0", "amazon nova lite 10": "amazon-nova-lite-1.0", "gemma-2-9b-it-simpo": "gemma-2-9b-it-simpo", "gemma29bitsimpo": "gemma-2-9b-it-simpo", "llama-3.1-nemotron-51b-instruct": "llama-3.1-nemotron-51b-instruct", "llama31nemotron51binstruct": "llama-3.1-nemotron-51b-instruct", "command r+ (08-2024)": "command-r+-(08-2024)", "command r+ (082024)": "command-r+-(08-2024)", "gemini-1.5-flash-8b-001": "gemini-1.5-flash-8b-001", "gemini15flash8b001": "gemini-1.5-flash-8b-001", "olmo-2-0325-32b-instruct": "olmo-2-0325-32b-instruct", "olmo2032532binstruct": "olmo-2-0325-32b-instruct", "aya-expanse-32b": "aya-expanse-32b", "ayaexpanse32b": "aya-expanse-32b", "nemotron-4-340b-instruct": "nemotron-4-340b-instruct", "nemotron4340binstruct": "nemotron-4-340b-instruct", "glm-4-0520": "glm-4-0520", "glm40520": "glm-4-0520", "reka-flash-20240904": "reka-flash-20240904", "rekaflash20240904": "reka-flash-20240904", "phi-4": "phi-4", "phi4": "phi-4", "llama-3-70b-instruct": "llama-3-70b-instruct", "llama370binstruct": "llama-3-70b-instruct", "claude 3 sonnet": "claude-3-sonnet", "amazon nova micro 1.0": "amazon-nova-micro-1.0", "amazon nova micro 10": "amazon-nova-micro-1.0", "hunyuan-standard-256k": "hunyuan-standard-256k", "hunyuanstandard256k": "hunyuan-standard-256k", "llama-3.1-tulu-3-8b": "llama-3.1-tulu-3-8b", "llama31tulu38b": "llama-3.1-tulu-3-8b", "gemma-2-9b-it": "gemma-2-9b-it", "gemma29bit": "gemma-2-9b-it", "command r+ (04-2024)": "command-r+-(04-2024)", "command r+ (042024)": "command-r+-(04-2024)", "qwen2-72b-instruct": "qwen2-72b-instruct", "qwen272binstruct": "qwen2-72b-instruct", "gpt-4-0314": "gpt-4-0314", "gpt40314": "gpt-4-0314", "ministral-8b-2410": "ministral-8b-2410", "ministral8b2410": "ministral-8b-2410", "aya-expanse-8b": "aya-expanse-8b", "ayaexpanse8b": "aya-expanse-8b", "command r (08-2024)": "command-r-(08-2024)", "command r (082024)": "command-r-(08-2024)", "claude 3 haiku": "claude-3-haiku", "deepseek-coder-v2-instruct": "deepseek-coder-v2-instruct", "deepseekcoderv2instruct": "deepseek-coder-v2-instruct", "jamba-1.5-mini": "jamba-1.5-mini", "jamba15mini": "jamba-1.5-mini", "meta-llama-3.1-8b-instruct": "meta-llama-3.1-8b-instruct", "metallama318binstruct": "meta-llama-3.1-8b-instruct", "gpt-4-0613": "gpt-4-0613", "gpt40613": "gpt-4-0613", "qwen1.5-110b-chat": "qwen1.5-110b-chat", "qwen15110bchat": "qwen1.5-110b-chat", "qwq-32b-preview": "qwq-32b-preview", "qwq32bpreview": "qwq-32b-preview", "yi-1.5-34b-chat": "yi-1.5-34b-chat", "yi1534bchat": "yi-1.5-34b-chat", "mistral-large-2402": "mistral-large-2402", "mistrallarge2402": "mistral-large-2402", "reka-flash-21b-online": "reka-flash-21b-online", "rekaflash21bonline": "reka-flash-21b-online", "llama-3-8b-instruct": "llama-3-8b-instruct", "llama38binstruct": "llama-3-8b-instruct", "internlm2.5-20b-chat": "internlm2.5-20b-chat", "internlm2520bchat": "internlm2.5-20b-chat", "command r (04-2024)": "command-r-(04-2024)", "command r (042024)": "command-r-(04-2024)", "mistral medium": "mistral-medium", "mixtral-8x22b-instruct-v0.1": "mixtral-8x22b-instruct-v0.1", "mixtral8x22binstructv01": "mixtral-8x22b-instruct-v0.1", "reka-flash-21b": "reka-flash-21b", "rekaflash21b": "reka-flash-21b", "qwen1.5-72b-chat": "qwen1.5-72b-chat", "qwen1572bchat": "qwen1.5-72b-chat", "granite-3.1-8b-instruct": "granite-3.1-8b-instruct", "granite318binstruct": "granite-3.1-8b-instruct", "gemma-2-2b-it": "gemma-2-2b-it", "gemma22bit": "gemma-2-2b-it", "gemini-1.0-pro-001": "gemini-1.0-pro-001", "gemini10pro001": "gemini-1.0-pro-001", "zephyr-orpo-141b-a35b-v0.1": "zephyr-orpo-141b-a35b-v0.1", "zephyrorpo141ba35bv01": "zephyr-orpo-141b-a35b-v0.1", "qwen1.5-32b-chat": "qwen1.5-32b-chat", "qwen1532bchat": "qwen1.5-32b-chat", "granite-3.1-2b-instruct": "granite-3.1-2b-instruct", "granite312binstruct": "granite-3.1-2b-instruct", "phi-3-medium-4k-instruct": "phi-3-medium-4k-instruct", "phi3medium4kinstruct": "phi-3-medium-4k-instruct", "starling-lm-7b-beta": "starling-lm-7b-beta", "starlinglm7bbeta": "starling-lm-7b-beta", "mixtral-8x7b-instruct-v0.1": "mixtral-8x7b-instruct-v0.1", "mixtral8x7binstructv01": "mixtral-8x7b-instruct-v0.1", "yi-34b-chat": "yi-34b-chat", "yi34bchat": "yi-34b-chat", "gemini pro": "gemini-pro", "qwen1.5-14b-chat": "qwen1.5-14b-chat", "qwen1514bchat": "qwen1.5-14b-chat", "wizardlm-70b-v1.0": "wizardlm-70b-v1.0", "wizardlm70bv10": "wizardlm-70b-v1.0", "gpt-3.5-turbo-0125": "gpt-3.5-turbo-0125", "gpt35turbo0125": "gpt-3.5-turbo-0125", "dbrx-instruct-preview": "dbrx-instruct-preview", "dbrxinstructpreview": "dbrx-instruct-preview", "meta-llama-3.2-3b-instruct": "meta-llama-3.2-3b-instruct", "metallama323binstruct": "meta-llama-3.2-3b-instruct", "phi-3-small-8k-instruct": "phi-3-small-8k-instruct", "phi3small8kinstruct": "phi-3-small-8k-instruct", "tulu-2-dpo-70b": "tulu-2-dpo-70b", "tulu2dpo70b": "tulu-2-dpo-70b", "granite-3.0-8b-instruct": "granite-3.0-8b-instruct", "granite308binstruct": "granite-3.0-8b-instruct", "llama-2-70b-chat": "llama-2-70b-chat", "llama270bchat": "llama-2-70b-chat", "openchat-3.5-0106": "openchat-3.5-0106", "openchat350106": "openchat-3.5-0106", "vicuna-33b": "vicuna-33b", "vicuna33b": "vicuna-33b", "snowflake arctic instruct": "snowflake-arctic-instruct", "starling-lm-7b-alpha": "starling-lm-7b-alpha", "starlinglm7balpha": "starling-lm-7b-alpha", "nous-hermes-2-mixtral-8x7b-dpo": "nous-hermes-2-mixtral-8x7b-dpo", "noushermes2mixtral8x7bdpo": "nous-hermes-2-mixtral-8x7b-dpo", "gemma-1.1-7b-it": "gemma-1.1-7b-it", "gemma117bit": "gemma-1.1-7b-it", "nv-llama2-70b-steerlm-chat": "nv-llama2-70b-steerlm-chat", "nvllama270bsteerlmchat": "nv-llama2-70b-steerlm-chat", "deepseek-llm-67b-chat": "deepseek-llm-67b-chat", "deepseekllm67bchat": "deepseek-llm-67b-chat", "openchat-3.5": "openchat-3.5", "openchat35": "openchat-3.5", "openhermes-2.5-mistral-7b": "openhermes-2.5-mistral-7b", "openhermes25mistral7b": "openhermes-2.5-mistral-7b", "granite-3.0-2b-instruct": "granite-3.0-2b-instruct", "granite302binstruct": "granite-3.0-2b-instruct", "mistral-7b-instruct-v0.2": "mistral-7b-instruct-v0.2", "mistral7binstructv02": "mistral-7b-instruct-v0.2", "phi-3-mini-4k-instruct-june-24": "phi-3-mini-4k-instruct-june-24", "phi3mini4kinstructjune24": "phi-3-mini-4k-instruct-june-24", "qwen1.5-7b-chat": "qwen1.5-7b-chat", "qwen157bchat": "qwen1.5-7b-chat", "dolphin-2.2.1-mistral-7b": "dolphin-2.2.1-mistral-7b", "dolphin221mistral7b": "dolphin-2.2.1-mistral-7b", "gpt-3.5-turbo-1106": "gpt-3.5-turbo-1106", "gpt35turbo1106": "gpt-3.5-turbo-1106", "solar-10.7b-instruct-v1.0": "solar-10.7b-instruct-v1.0", "solar107binstructv10": "solar-10.7b-instruct-v1.0", "phi-3-mini-4k-instruct": "phi-3-mini-4k-instruct", "phi3mini4kinstruct": "phi-3-mini-4k-instruct", "llama-2-13b-chat": "llama-2-13b-chat", "llama213bchat": "llama-2-13b-chat", "wizardlm-13b-v1.2": "wizardlm-13b-v1.2", "wizardlm13bv12": "wizardlm-13b-v1.2", "meta-llama-3.2-1b-instruct": "meta-llama-3.2-1b-instruct", "metallama321binstruct": "meta-llama-3.2-1b-instruct", "zephyr-7b-beta": "zephyr-7b-beta", "zephyr7bbeta": "zephyr-7b-beta", "smollm2-1.7b-instruct": "smollm2-1.7b-instruct", "smollm217binstruct": "smollm2-1.7b-instruct", "mpt-30b-chat": "mpt-30b-chat", "mpt30bchat": "mpt-30b-chat", "codellama-70b-instruct": "codellama-70b-instruct", "codellama70binstruct": "codellama-70b-instruct", "zephyr-7b-alpha": "zephyr-7b-alpha", "zephyr7balpha": "zephyr-7b-alpha", "codellama-34b-instruct": "codellama-34b-instruct", "codellama34binstruct": "codellama-34b-instruct", "falcon-180b-chat": "falcon-180b-chat", "falcon180bchat": "falcon-180b-chat", "vicuna-13b": "vicuna-13b", "vicuna13b": "vicuna-13b", "gemma-7b-it": "gemma-7b-it", "gemma7bit": "gemma-7b-it", "phi-3-mini-128k-instruct": "phi-3-mini-128k-instruct", "phi3mini128kinstruct": "phi-3-mini-128k-instruct", "llama-2-7b-chat": "llama-2-7b-chat", "llama27bchat": "llama-2-7b-chat", "qwen-14b-chat": "qwen-14b-chat", "qwen14bchat": "qwen-14b-chat", "guanaco-33b": "guanaco-33b", "guanaco33b": "guanaco-33b", "gemma-1.1-2b-it": "gemma-1.1-2b-it", "gemma112bit": "gemma-1.1-2b-it", "stripedhyena-nous-7b": "stripedhyena-nous-7b", "stripedhyenanous7b": "stripedhyena-nous-7b", "olmo-7b-instruct": "olmo-7b-instruct", "olmo7binstruct": "olmo-7b-instruct", "mistral-7b-instruct-v0.1": "mistral-7b-instruct-v0.1", "mistral7binstructv01": "mistral-7b-instruct-v0.1", "vicuna-7b": "vicuna-7b", "vicuna7b": "vicuna-7b", "palm-chat-bison-001": "palm-chat-bison-001", "palmchatbison001": "palm-chat-bison-001", "gemma-2b-it": "gemma-2b-it", "gemma2bit": "gemma-2b-it", "qwen1.5-4b-chat": "qwen1.5-4b-chat", "qwen154bchat": "qwen1.5-4b-chat", "koala-13b": "koala-13b", "koala13b": "koala-13b", "chatglm3-6b": "chatglm3-6b", "chatglm36b": "chatglm3-6b", "gpt4all-13b-snoozy": "gpt4all-13b-snoozy", "gpt4all13bsnoozy": "gpt4all-13b-snoozy", "mpt-7b-chat": "mpt-7b-chat", "mpt7bchat": "mpt-7b-chat", "chatglm2-6b": "chatglm2-6b", "chatglm26b": "chatglm2-6b", "rwkv-4-raven-14b": "rwkv-4-raven-14b", "rwkv4raven14b": "rwkv-4-raven-14b", "alpaca-13b": "alpaca-13b", "alpaca13b": "alpaca-13b", "openassistant-pythia-12b": "openassistant-pythia-12b", "openassistantpythia12b": "openassistant-pythia-12b", "chatglm-6b": "chatglm-6b", "chatglm6b": "chatglm-6b", "fastchat-t5-3b": "fastchat-t5-3b", "fastchatt53b": "fastchat-t5-3b", "stablelm-tuned-alpha-7b": "stablelm-tuned-alpha-7b", "stablelmtunedalpha7b": "stablelm-tuned-alpha-7b", "dolly-v2-12b": "dolly-v2-12b", "dollyv212b": "dolly-v2-12b", "llama-13b": "llama-13b", "llama13b": "llama-13b", "grok-4": "grok-4", "grok4": "grok-4", "grok-4-0709": "grok-4", "grok40709": "grok-4", "xai/grok-4": "grok-4", "xai/grok4": "grok-4"}