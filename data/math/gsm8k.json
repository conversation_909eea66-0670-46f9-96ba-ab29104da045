{"source": "GSM8K (Grade School Math 8K)", "date": "2025-07-11", "total_problems": 8792, "train_size": 7473, "test_size": 1319, "problems": [{"question": "<PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?", "answer": "<PERSON> sold 48/2 = <<48/2=24>>24 clips in May.\n<PERSON> sold 48+24 = <<48+24=72>>72 clips altogether in April and May.\n#### 72", "split": "train"}, {"question": "<PERSON><PERSON> earns $12 an hour for babysitting. Yesterday, she just did 50 minutes of babysitting. How much did she earn?", "answer": "<PERSON><PERSON> earns 12/60 = $<<12/60=0.2>>0.2 per minute.\nWorking 50 minutes, she earned 0.2 x 50 = $<<0.2*50=10>>10.\n#### 10", "split": "train"}, {"question": "<PERSON> is saving money for a new wallet which costs $100. <PERSON> has only half of the money she needs. Her parents decided to give her $15 for that purpose, and her grandparents twice as much as her parents. How much more money does <PERSON> need to buy the wallet?", "answer": "In the beginning, <PERSON> has only 100 / 2 = $<<100/2=50>>50.\n<PERSON>'s grandparents gave her 15 * 2 = $<<15*2=30>>30.\nThis means, Betty needs 100 - 50 - 30 - 15 = $<<100-50-30-15=5>>5 more.\n#### 5", "split": "train"}, {"question": "<PERSON> is reading a 120-page book. Yesterday, she was able to read 12 pages and today, she read twice as many pages as yesterday. If she wants to read half of the remaining pages tomorrow, how many pages should she read?", "answer": "<PERSON><PERSON> read 12 x 2 = <<12*2=24>>24 pages today.\nSo she was able to read a total of 12 + 24 = <<12+24=36>>36 pages since yesterday.\nThere are 120 - 36 = <<120-36=84>>84 pages left to be read.\nSince she wants to read half of the remaining pages tomorrow, then she should read 84/2 = <<84/2=42>>42 pages.\n#### 42", "split": "train"}, {"question": "<PERSON> writes a 3-page letter to 2 different friends twice a week.  How many pages does he write a year?", "answer": "He writes each friend 3*2=<<3*2=6>>6 pages a week\nSo he writes 6*2=<<6*2=12>>12 pages every week\nThat means he writes 12*52=<<12*52=624>>624 pages a year\n#### 624", "split": "train"}, {"question": "<PERSON> has a garden with flowers. He planted plants of three different colors in it. Ten of them are yellow, and there are 80% more of those in purple. There are only 25% as many green flowers as there are yellow and purple flowers. How many flowers does <PERSON> have in his garden?", "answer": "There are 80/100 * 10 = <<80/100*10=8>>8 more purple flowers than yellow flowers.\nSo in <PERSON>'s garden, there are 10 + 8 = <<10+8=18>>18 purple flowers.\nPurple and yellow flowers sum up to 10 + 18 = <<10+18=28>>28 flowers.\nThat means in <PERSON>'s garden there are 25/100 * 28 = <<25/100*28=7>>7 green flowers.\nSo in total <PERSON> has 28 + 7 = <<28+7=35>>35 plants in his garden.\n#### 35", "split": "train"}, {"question": "<PERSON> is wondering how much pizza he can eat in one day. He buys 2 large pizzas and 2 small pizzas. A large pizza has 16 slices and a small pizza has 8 slices. If he eats it all, how many pieces does he eat that day?", "answer": "He eats 32 from the largest pizzas because 2 x 16 = <<2*16=32>>32\nHe eats 16 from the small pizza because 2 x 8 = <<2*8=16>>16\nHe eats 48 pieces because 32 + 16 = <<32+16=48>>48\n#### 48", "split": "train"}, {"question": "<PERSON> created a care package to send to his brother, who was away at boarding school.  <PERSON> placed a box on a scale, and then he poured into the box enough jelly beans to bring the weight to 2 pounds.  Then, he added enough brownies to cause the weight to triple.  Next, he added another 2 pounds of jelly beans.  And finally, he added enough gummy worms to double the weight once again.  What was the final weight of the box of goodies, in pounds?", "answer": "To the initial 2 pounds of jelly beans, he added enough brownies to cause the weight to triple, bringing the weight to 2*3=<<2*3=6>>6 pounds.\nNext, he added another 2 pounds of jelly beans, bringing the weight to 6+2=<<6+2=8>>8 pounds.\nAnd finally, he added enough gummy worms to double the weight once again, to a final weight of 8*2=<<8*2=16>>16 pounds.\n#### 16", "split": "train"}, {"question": "<PERSON> is applying for a new job and bought a new set of business clothes to wear to the interview. She went to a department store with a budget of $200 and spent $30 on a button-up shirt, $46 on suit pants, $38 on a suit coat, $11 on socks, and $18 on a belt. She also purchased a pair of shoes, but lost the receipt for them. She has $16 left from her budget. How much did <PERSON> pay for the shoes?", "answer": "Let S be the amount <PERSON> paid for the shoes.\nShe spent S + 30 + 46 + 38 + 11 + 18 = S + <<+30+46+38+11+18=143>>143.\nShe used all but $16 of her budget, so S + 143 = 200 - 16 = 184.\nThus, <PERSON> paid S = 184 - 143 = $<<184-143=41>>41 for the shoes.\n#### 41", "split": "train"}, {"question": "<PERSON> makes $18.00 an hour.  If she works more than 8 hours per shift, she is eligible for overtime, which is paid by your hourly wage + 1/2 your hourly wage.  If she works 10 hours every day for 5 days, how much money does she make?", "answer": "She works 8 hours a day for $18 per hour so she makes 8*18 = $<<8*18=144.00>>144.00 per 8-hour shift\nShe works 10 hours a day and anything over 8 hours is eligible for overtime, so she gets 10-8 = <<10-8=2>>2 hours of overtime\nOvertime is calculated as time and a half so and she makes $18/hour so her overtime pay is 18*.5 = $<<18*.5=9.00>>9.00\nHer overtime pay is 18+9 = $<<18+9=27.00>>27.00\nHer base pay is $144.00 per 8-hour shift and she works 5 days and makes 5 * $144 = $<<144*5=720.00>>720.00\nHer overtime pay is $27.00 per hour and she works 2 hours of overtime per day and makes 27*2 = $<<27*2=54.00>>54.00 in overtime pay\n2 hours of overtime pay for 5 days means she makes 54*5 = $270.00\nIn 5 days her base pay is $720.00 and she makes $270.00 in overtime pay so she makes $720 + $270 = $<<720+270=990.00>>990.00\n#### 990", "split": "train"}, {"question": "A deep-sea monster rises from the waters once every hundred years to feast on a ship and sate its hunger. Over three hundred years, it has consumed 847 people. Ships have been built larger over time, so each new ship has twice as many people as the last ship. How many people were on the ship the monster ate in the first hundred years?", "answer": "Let S be the number of people on the first hundred years’ ship.\nThe second hundred years’ ship had twice as many as the first, so it had 2S people.\nThe third hundred years’ ship had twice as many as the second, so it had 2 * 2S = <<2*2=4>>4S people.\nAll the ships had S + 2S + 4S = 7S = 847 people.\nThus, the ship that the monster ate in the first hundred years had S = 847 / 7 = <<847/7=121>>121 people on it.\n#### 121", "split": "train"}, {"question": "<PERSON> is buying a new pair of shoes that costs $95. He has been saving up his money each month for the past three months. He gets a $5 allowance a month. He also mows lawns and shovels driveways. He charges $15 to mow a lawn and $7 to shovel. After buying the shoes, he has $15 in change. If he mows 4 lawns, how many driveways did he shovel?", "answer": "He saved up $110 total because 95 + 15 = <<95+15=110>>110\nHe saved $15 from his allowance because 3 x 5 = <<3*5=15>>15\nHe earned $60 mowing lawns because 4 x 15 = <<4*15=60>>60\nHe earned $35 shoveling driveways because 110 - 60 - 15 = <<110-60-15=35>>35\nHe shoveled 5 driveways because 35 / 7 = <<35/7=5>>5\n#### 5", "split": "train"}, {"question": "<PERSON> has 60 mango trees on his farm. He also has 5 less than half as many coconut trees as mango trees. How many trees does <PERSON> have in all on his farm?", "answer": "Half of the number of <PERSON>'s mango trees is 60/2 = <<60/2=30>>30 trees.\nSo <PERSON> has 30 - 5 = <<30-5=25>>25 coconut trees.\nTherefore, <PERSON> has 60 + 25 = <<60+25=85>>85 treeson his farm.\n#### 85", "split": "train"}, {"question": "<PERSON> will serve charcuterie at his dinner party. He buys 2 pounds of cheddar cheese for $10, a pound of cream cheese that cost half the price of the cheddar cheese, and a pack of cold cuts that cost twice the price of the cheddar cheese. How much does he spend on the ingredients?", "answer": "A pound of cream cheese cost $10 / 2 = $<<10/2=5>>5.\nA pack of cold cuts cost $10 x 2 = $<<10*2=20>>20.\n<PERSON> spent $10 + $5 + $20 = $<<10+5+20=35>>35 on the ingredients.\n#### 35", "split": "train"}, {"question": "<PERSON> can read 8 pages of a book in 20 minutes. How many hours will it take her to read 120 pages?", "answer": "In one hour, there are 3 sets of 20 minutes.\nSo, <PERSON> can read 8 x 3 = <<8*3=24>>24 pages in an hour.\nIt will take her 120/24 = <<120/24=5>>5 hours to read 120 pages.\n#### 5", "split": "train"}, {"question": "<PERSON> creates a media empire.  He creates a movie for $2000.  Each DVD cost $6 to make.  He sells it for 2.5 times that much.  He sells 500 movies a day for 5 days a week.  How much profit does he make in 20 weeks?", "answer": "He sold each DVD for 6*2.5=$<<6*2.5=15>>15\nSo he makes a profit of 15-6=$<<15-6=9>>9\nSo each day he makes a profit of 9*500=$<<9*500=4500>>4500\nSo he makes 4500*5=$<<4500*5=22500>>22,500\nHe makes 22,500*20=$<<22500*20=450000>>450,000\nThen after the cost of creating the movie he has a profit of 450,000-2000=$<<450000-2000=448000>>448,000\n#### 448000", "split": "train"}, {"question": "The profit from a business transaction is shared among 2 business partners, <PERSON> and <PERSON> in the ratio 2:5 respectively. If <PERSON> got $2500, how much will <PERSON> have after spending some of his share on a shirt that costs $200?", "answer": "According to the ratio, for every 5 parts that <PERSON> gets, <PERSON> gets 2 parts\nSince <PERSON> got $2500, each part is therefore $2500/5 = $<<2500/5=500>>500\n<PERSON> will get 2*$500 = $<<2*500=1000>>1000\nAfter buying the shirt he will have $1000-$200 = $<<1000-200=800>>800 left\n#### 800", "split": "train"}, {"question": "In a truck, there are 26 pink hard hats, 15 green hard hats, and 24 yellow hard hats.  If <PERSON> takes away 4 pink hard hats, and <PERSON> takes away 6 pink hard hats and twice as many green hard hats as the number of pink hard hats that he removed, then calculate the total number of hard hats that remained in the truck.", "answer": "If there were 26 pink hard hats and <PERSON> took away 4 pink hard hats, the number of pink hard hats that remained is 26-4 = <<26-4=22>>22\n<PERSON> also took away 6 pink hard hats, leaving 22-6 = <<22-6=16>>16 pink hard hats in the truck.\nIf <PERSON> also took twice as many green hard hats as pink hard hats, he took 2*6 = <<6*2=12>>12 green hard hats.\nThe total number of green hard hats that remained in the truck is 15-12 = <<15-12=3>>3\nIn the truck, after some are taken, there were 3 green hard hats + 16 pink hard hats = <<3+16=19>>19 hard hats in the truck.\n<PERSON><PERSON><PERSON>, 19 green and pink hard hats + 24 yellow hards hats = <<19+24=43>>43 hard hats remained in the truck\n#### 43", "split": "train"}, {"question": "It takes <PERSON><PERSON><PERSON> two hours to walk to work and one hour to ride his bike to work. <PERSON><PERSON><PERSON> walks to and from work three times a week and rides his bike to and from work twice a week. How many hours in total does he take to get to and from work a week with walking and biking?", "answer": "<PERSON><PERSON><PERSON> takes 2*3 = <<2*3=6>>6 hours a week to walk to work.\n<PERSON><PERSON><PERSON> takes 6*2 = <<6*2=12>>12 hours a week to walk to and from work.\n<PERSON><PERSON><PERSON> takes 1*2 = <<1*2=2>>2 hours a week to bike to work.\n<PERSON><PERSON><PERSON> takes 2*2 = <<2*2=4>>4 hours a week to bike to and from work.\nIn total, <PERSON><PERSON><PERSON> takes 12+4 = <<12+4=16>>16 hour a week to go to and from work.\n#### 16", "split": "train"}, {"question": "<PERSON> rides his bike back and forth to work for each of his 5 workdays.  His work is 20 miles away.  He also goes for a weekend bike ride of 200 miles.    If he can bike at 25 mph how much time does he spend biking a week?", "answer": "He bikes 20*2=<<20*2=40>>40 miles each day for work\nSo he bikes 40*5=<<40*5=200>>200 miles for work\nThat means he bikes a total of 200+200=<<200+200=400>>400 miles for work\nSo he bikes a total of 400/25=<<400/25=16>>16 hours\n#### 16", "split": "train"}, {"question": "<PERSON> bought stamps at the post office. Some of the stamps had a snowflake design, some had a truck design, and some had a rose design. <PERSON> bought 11 snowflake stamps. She bought 9 more truck stamps than snowflake stamps, and 13 fewer rose stamps than truck stamps. How many stamps did <PERSON> buy in all?", "answer": "The number of truck stamps is 11 + 9 = <<11+9=20>>20.\nThe number of rose stamps is 20 − 13 = <<20-13=7>>7.\n<PERSON> bought 11 + 20 + 7 = <<11+20+7=38>>38 stamps in all.\n#### 38", "split": "train"}, {"question": "Each bird eats 12 beetles per day, each snake eats 3 birds per day, and each jaguar eats 5 snakes per day. If there are 6 jaguars in a forest, how many beetles are eaten each day?", "answer": "First find the total number of snakes eaten: 5 snakes/jaguar * 6 jaguars = <<5*6=30>>30 snakes\nThen find the total number of birds eaten per day: 30 snakes * 3 birds/snake = <<30*3=90>>90 snakes\nThen multiply the number of snakes by the number of beetles per snake to find the total number of beetles eaten per day: 90 snakes * 12 beetles/snake = <<90*12=1080>>1080 beetles\n#### 1080", "split": "train"}, {"question": "<PERSON>’s last name has three fewer letters than <PERSON><PERSON>’s last name. If <PERSON><PERSON> took two letters off her last name, she would have a last name twice the length of <PERSON><PERSON>s. <PERSON>’s full name is <PERSON>. How many letters are in <PERSON>’s last name?", "answer": "There are 4 letters in <PERSON>’s last name, so <PERSON><PERSON>’s name is 4*2 +2 = <<4*2+2=10>>10 letters long.\n<PERSON>’s last name is 3 letters shorter than <PERSON><PERSON>’s, so there are 10 - 3 = <<10-3=7>>7 letters in <PERSON>’s last name.\n#### 7", "split": "train"}, {"question": "<PERSON>'s favorite store was having a summer clearance. For $75 she bought 5 pairs of shorts for $7 each and 2 pairs of shoes for $10 each. She also bought 4 tops, all at the same price. How much did each top cost?", "answer": "She bought 5 shorts at $7 each so 5*7=$<<5*7=35>>35\nShe bought 2 pair of shoes at $10 each so 2*10=$<<2*10=20>>20\nThe shorts and shoes cost her 35+20 = $<<35+20=55>>55\nWe know she spent 75 total and the shorts and shoes cost $55 which left a difference of 75-55 = $<<75-55=20>>20\nShe bought 4 tops for a total of $20 so 20/4 = $5\n#### 5", "split": "train"}, {"question": "<PERSON> does her grocery shopping on Saturday. She does her shopping only at a specific store where she is allowed a credit of $100, which must be paid in full before her next shopping trip. That week she spent the full credit limit and paid $15 of it on Tuesday and $23 of it on Thursday. How much credit will <PERSON> need to pay before her next shopping trip?", "answer": "So far, <PERSON> has paid back $15 +$23=$<<15+23=38>>38 of the credit.\nSo she still needs to pay $100-$38=$<<100-38=62>>62\n#### 62", "split": "train"}, {"question": "<PERSON> is going to practice playing tennis with a tennis ball machine that shoots out tennis balls for <PERSON> to hit. He loads up the machine with 175 tennis balls to start with. Out of the first 100 balls, he manages to hit 2/5 of them. Of the next 75 tennis balls, he manages to hit 1/3 of them. Out of all the tennis balls, how many did <PERSON> not hit?", "answer": "Out of the first 100 balls, <PERSON> was able to hit 2/5 of them and not able to hit 3/5 of them, 3/5 x 100 = 60 tennis balls <PERSON> didn't hit.\nOut of the next 75 balls, <PERSON> was able to hit 1/3 of them and not able to hit 2/3 of them, 2/3 x 75 = 50 tennis balls that <PERSON> didn't hit.\nCombined, <PERSON> was not able to hit 60 + 50 = <<60+50=110>>110 tennis balls <PERSON> didn't hit.\n#### 110", "split": "train"}, {"question": "<PERSON> is stranded on a desert island. He wants some salt to season his fish. He collects 2 liters of seawater in an old bucket. If the water is 20% salt, how many ml of salt will <PERSON> get when all the water evaporates?", "answer": "First find how many liters of the seawater are salt: 2 liters * 20% = <<2*20*.01=.4>>.4 liters\nThen multiply that amount by 1000 ml/liter to find the number of ml of salt Jack gets: .4 liters * 1000 ml/liter = <<.4*1000=400>>400 ml\n#### 400", "split": "train"}, {"question": "<PERSON> was researching his school project and had to download files from the internet to his computer to use for reference. After downloading 800 files, he deleted 70% of them because they were not helpful. He downloaded 400 more files but again realized that 3/5 of them were irrelevant. How many valuable files was he left with after deleting the unrelated files he downloaded in the second round?", "answer": "The number of non-valuable files <PERSON> downloaded in the first round is 70/100*800 = <<70/100*800=560>>560 files.\nThe number of valuable files <PERSON> downloaded in the first round is 800-560 = <<800-560=240>>240\nWhen he downloaded 400 new files, there were 3/5*400= <<3/5*400=240>>240 non-useful files, which he deleted again.\nThe total number of valuable files he downloaded in the second round is 400-240 = <<400-240=160>>160\nTo write his research, <PERSON> had 160+240 = <<160+240=400>>400 useful files to reference to write his research.\n#### 400", "split": "train"}, {"question": "There are 5 houses on a street, and each of the first four houses has 3 gnomes in the garden. If there are a total of 20 gnomes on the street, how many gnomes does the fifth house have?", "answer": "In the first four houses, there are a total of 4 houses * 3 gnomes = <<4*3=12>>12 gnomes.\nTherefore, the fifth house had 20 total gnomes – 12 gnomes = <<20-12=8>>8 gnomes.\n#### 8", "split": "train"}, {"question": "Mrs. <PERSON> used to spend 40% of her monthly income on rent and utilities. Her salary was recently increased by $600 so now her rent and utilities only amount to 25% of her monthly income. How much was her previous monthly income?", "answer": "Let her previous monthly income be p\nThe cost of her rent and utilities was 40% of p which is (40/100)*p = 2p/5\nHer income was increased by $600 so it is now p+$600\nThe cost of her rent and utilities now amount to 25% of (p+$600) which is (25/100)*(p+$600) = (p+$600)/4\nEquating both expressions for cost of rent and utilities: 2p/5 = (p+$600)/4\nMultiplying both sides of the equation by 20 gives 8p = 5p+$3000\nSubtracting 5p from both sides gives: 3p = $3000\nDividing both sides by 3 gives p = $1000\n#### 1000", "split": "train"}, {"question": "<PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON> each buy personal pan pizzas cut into 4 pieces. If <PERSON> and <PERSON> eat 50% of their pizzas and <PERSON> and <PERSON><PERSON> eat 75% of the pizzas, how many pizza pieces are left uneaten?", "answer": "In total, there are 4 x 4 = <<4*4=16>>16 pizza pieces.\n<PERSON> and <PERSON> eat 2 x 4 x 50% = <<2*4*50*.01=4>>4 pieces.\n<PERSON> and <PERSON><PERSON> eat 2 x 4 x 75% = <<2*4*75*.01=6>>6 pieces.\nThe four of them eat 4 + 6 = <<4+6=10>>10 pieces.\nThere are 16 - 10 = <<16-10=6>>6 pizza pieces uneaten.\n#### 6", "split": "train"}, {"question": "<PERSON> is a painter. He paints pictures and sells them at the park. He charges $60 for a large painting and $30 for a small painting. Last month he sold eight large paintings and four small paintings. If he sold twice as much this month, how much is his sales for this month?", "answer": "<PERSON> earned $60/large painting x 8 large paintings = $<<60*8=480>>480 for the large paintings.\nHe also earned $30/small painting x 4 small paintings = $<<30*4=120>>120 for the small paintings.\nHis total sales last month were $480 + $120 = $<<480+120=600>>600.\nSo, his sales this month are $600 x 2 = $<<600*2=1200>>1200.\n#### 1200", "split": "train"}, {"question": "A car is driving through a tunnel with many turns. After a while, the car must travel through a ring that requires a total of 4 right-hand turns. After the 1st turn, it travels 5 meters. After the 2nd turn, it travels 8 meters. After the 3rd turn, it travels a little further and at the 4th turn, it immediately exits the tunnel. If the car has driven a total of 23 meters around the ring, how far did it have to travel after the 3rd turn?", "answer": "From the details given, the car has traveled 5 meters at the 1st turn + 8 meters after the 2nd turn + 0 meters after the 4th turn = <<5+8+0=13>>13 meters around the ring.\nIt must therefore have driven 23 total meters – 13 calculated meters = 10 meters after the 3rd turn.\n#### 10", "split": "train"}, {"question": "To make pizza, together with other ingredients, <PERSON><PERSON> needs 10 cups of water, 16 cups of flour, and 1/2 times as many teaspoons of salt as the number of cups of flour. Calculate the combined total number of cups of water, flour, and teaspoons of salt that she needs to make the pizza.", "answer": "To make the pizza, <PERSON><PERSON> half as many teaspoons of salt as the number of cups of flour, meaning she needs 1/2*16 = <<16*1/2=8>>8 teaspoons of salt.\nThe total number of cups of flour and teaspoons of salt she needs is 8+16 = <<8+16=24>>24\nShe also needs 10 cups of water, which means the total number of cups of water and flour and teaspoons of salt she needs is 24+10 = <<24+10=34>>34\n#### 34", "split": "train"}, {"question": "Mr. <PERSON> shared a certain amount of money between his two sons, <PERSON> and <PERSON>. If <PERSON> got $1750, and <PERSON> got twice as much as <PERSON>, how much was the money shared?", "answer": "<PERSON> got twice $1750 which is 2*$1750 = $<<2*1750=3500>>3500\nThe total amount shared was $1750+$3500 = $<<1750+3500=5250>>5250\n#### 5250", "split": "train"}, {"question": "Mr. <PERSON> found out that 40% of his Grade 5  students got a final grade below B. How many of his students got a final grade of B and above if he has 60 students in Grade 5?", "answer": "Since 40% of his students got below B, 100% - 40% = 60% of Mr<PERSON>'s students got B and above.\nThus, 60 x 60/100 = <<60*60/100=36>>36 students got B and above in their final grade.\n#### 36", "split": "train"}, {"question": "<PERSON>, <PERSON>, and <PERSON> earned $60 from washing cars all week. However, half of the $60 was earned by <PERSON>. <PERSON> earned half of what <PERSON> earned. How much more money did <PERSON> earn than <PERSON>?", "answer": "<PERSON> earned $60 * 1/2 = $<<60*1/2=30>>30.\n<PERSON> earned $30 * 1/2 = $<<30*1/2=15>>15.\n<PERSON> earned $30 - $15 = $<<30-15=15>>15 more than <PERSON>.\n#### 15", "split": "train"}, {"question": "Five friends eat at a fast-food chain and order the following: 5 pieces of hamburger that cost $3 each; 4 sets of French fries that cost $1.20; 5 cups of soda that cost $0.5 each; and 1 platter of spaghetti that cost $2.7. How much will each of them pay if they will split the bill equally?", "answer": "The cost of 5 pieces of hamburger is $3 x 5 = $<<3*5=15>>15.\nThe cost of 4 sets of French fries is $1.20 x 4 = $<<1.20*4=4.80>>4.80.\nThe cost of 5 cups of soda is $0.5 x 5 = $<<0.5*5=2.50>>2.50.\nSo their total bill is $15 + $4.80 + $2.50 +$2.7 = $<<15+4.8+2.5+2.7=25>>25.\nHence, each of the five friends will contribute $25/5 = $<<25/5=5>>5.\n#### 5", "split": "train"}, {"question": "<PERSON> is making tea for a party. She knows her mom drinks an 8-ounce cup of tea and uses one ounce of tea. She will use this same ratio for the party. The party has 12 people there and each of them wants a 6-ounce cup of tea. How many ounces of tea does she need?", "answer": "She is making 72 ounces of water because 12 x 6 = <<12*6=72>>72\nShe needs 9 ounces of tea because 72 / 8 = <<72/8=9>>9\n#### 9", "split": "train"}, {"question": "<PERSON> goes trick-or-treating in a subdivision where she gets 14 pieces of candy per house. Her brother <PERSON> goes trick-or-tricking in a neighboring subdivision where he gets 11 pieces of candy per house. If the first subdivision has 60 houses and the second subdivision has 75 houses, how many more pieces of candy does <PERSON> get?", "answer": "First find the total number of pieces of candy <PERSON> gets: 14 pieces/house * 60 houses = 840 pieces\nThen find the total number of pieces of candy <PERSON> gets: 11 pieces/house * 75 houses = <<11*75=825>>825 pieces\nThen subtract the number of pieces <PERSON> gets from the number <PERSON> gets to find the difference: 840 pieces - 825 pieces = <<840-825=15>>15 pieces\n#### 15", "split": "train"}, {"question": "A concert ticket costs $40. Mr. <PERSON> bought 12 tickets and received a 5% discount for every ticket bought that exceeds 10. How much did Mr. <PERSON> pay in all?", "answer": "Mr. <PERSON> had a 5% discount for each of the 12 - 10 = <<12-10=2>>2 tickets.\nSo, those two tickets had a $40 x 5/100 = $<<40*5/100=2>>2 discount each.\nHence, each ticket cost $40 - $2 = $<<40-2=38>>38 each.\nThus, two discounted tickets amount to $38 x 2 = $<<38*2=76>>76.\nAnd the other ten tickets amount to $40 x 10 = $<<40*10=400>>400.\nHence, Mr. <PERSON> paid a total of $400 + $76 = $<<400+76=476>>476.\n#### 476", "split": "train"}, {"question": "<PERSON> and <PERSON> want to attend a beauty and modeling contest. They both want to buy new pairs of shoes and dresses. <PERSON> buys a pair of shoes which costs $50 and a dress which costs $200. How much should <PERSON> budget if she wants to spend twice as much as what <PERSON> spent on the pair of shoes and dress?", "answer": "The cost <PERSON> should budget for her pair of shoes is $50 * 2 = $<<50*2=100>>100.\nThe cost <PERSON> should budget for her dress is $200 * 2 = $<<200*2=400>>400.\nThe total <PERSON> should budget is $100 + $400 = $<<100+400=500>>500.\n#### 500", "split": "train"}, {"question": "A family of 12 monkeys collected 10 piles of bananas. 6 piles had 9 hands, with each hand having 14 bananas, while the remaining piles had 12 hands, with each hand having 9 bananas. How many bananas would each monkey get if they divide the bananas equally amongst themselves?", "answer": "The first 6 bunches had 6 x 9 x 14 = <<6*9*14=756>>756 bananas.\nThere were 10 - 6 = <<10-6=4>>4 remaining bunches.\nThe 4 remaining bunches had 4 x 12 x 9 = <<4*12*9=432>>432 bananas.\nAll together, there were 756 + 432 = <<756+432=1188>>1188 bananas\nEach monkey would get 1188/12 = <<1188/12=99>>99 bananas.\n#### 99", "split": "train"}, {"question": "An earthquake caused four buildings to collapse. Experts predicted that each following earthquake would have double the number of collapsing buildings as the previous one, since each one would make the foundations less stable. After three more earthquakes, how many buildings had collapsed including those from the first earthquake?", "answer": "The second earthquake caused 2 * 4 = <<2*4=8>>8 buildings to collapse.\nThe third earthquake caused 2 * 8 = <<2*8=16>>16 buildings to collapse.\nThe fourth earthquake caused 16 * 2 = <<16*2=32>>32 buildings to collapse.\nIncluding the first earthquake, the earthquakes caused 4 + 8 + 16 + 32 = <<4+8+16+32=60>>60 buildings to collapse.\n#### 60", "split": "train"}, {"question": "<PERSON> is a first-year student at a University in Chicago. He has a budget of $1000 per semester. He spends 30% of his money on food, 15% on accommodation, 25% on entertainment, and the rest on coursework materials. How much money does he spend on coursework materials?", "answer": "Accommodation is 15% * $1000=$<<15*.01*1000=150>>150\nFood is          30% * $1000=$<<30*.01*1000=300>>300\nEntertainment is   25% * $1000=$<<25*.01*1000=250>>250\nCoursework materials are thus $1000-($150+$300+$250) = $300\n#### 300", "split": "train"}, {"question": "It's <PERSON>'s birthday party. Her parents bought a unicorn piñata for $13 and filled it with all of her favorite treats. They bought 4 bags of <PERSON>'s for $9 per bag, 3 bags of Snickers for $5 per bag, and 5 bags of Skittles for $7 per bag. How much did the unicorn piñata and the treats cost altogether?", "answer": "The four bags of <PERSON>'s cost $9 x 4 = $<<9*4=36>>36.\nThe three bags of Snickers cost $5 x 3 = $<<5*3=15>>15.\nThe five bags of Skittles cost $7 x 5 = $<<7*5=35>>35.\nTherefore, the unicorn p<PERSON><PERSON><PERSON> and the treats cost $13 + $36 + $15 + $35 = $<<13+36+15+35=99>>99.\n#### 99", "split": "train"}, {"question": "<PERSON> practices the piano for 20 minutes a day and the violin for three times as long. If she practice six days a week, how many minutes does she spend practicing in a month with four weeks?", "answer": "First find <PERSON>'s total violin practice time by tripling her piano practice time: 20 minutes/day * 3 = <<20*3=60>>60 minutes/day\nThen find the total amount of time she spends practicing each day: 60 minutes/day + 20 minutes/day = <<60+20=80>>80 minutes/day\nThen find the total time she spends practicing each week: 80 minutes/day * 6 days/week = <<80*6=480>>480 minutes/week\nThen find the total time she spends practicing each month: 480 minutes/week * 4 weeks/month = <<480*4=1920>>1920 minutes/month\n#### 1920", "split": "train"}, {"question": "The file, 90 megabytes in size, downloads at the rate of 5 megabytes per second for its first 60 megabytes, and then 10 megabytes per second thereafter. How long, in seconds, does it take to download entirely?", "answer": "The first 60 megabytes take 60/5=<<60/5=12>>12 seconds.\nThere are 90-60=<<90-60=30>>30 remaining megabytes.\nThe remaining 30 megabytes take 30/10=<<30/10=3>>3 seconds.\nAnd 12+3=<<12+3=15>>15 seconds.\n#### 15", "split": "train"}, {"question": "<PERSON> memorized six more digits of pi than <PERSON> memorized. <PERSON> memorized six times as many digits of pi as <PERSON> memorized. If <PERSON> memorized 24 digits of pi, how many digits did <PERSON> memorize?", "answer": "<PERSON> memorized 24/6=<<24/6=4>>4 digits of pi.\n<PERSON> memorized 4+6=10 digits of pi.\n#### 10", "split": "train"}, {"question": "On a school trip to the seashore, <PERSON> and his friends collected shells. <PERSON> collected four times as many shells as <PERSON> did. <PERSON> got a late start and only collected a third of what <PERSON> did. If <PERSON> collected 36 shells how many did <PERSON> collect?", "answer": "<PERSON> collected 36/3=<<36/3=12>>12 shells\n<PERSON> collected 12*4=<<12*4=48>>48 shells\n#### 48", "split": "train"}, {"question": "<PERSON> spends $100 a month on baseball supplies. His season is 4 months long. He wants to use the months he's not playing baseball to save up by raking, shoveling, and mowing lawns. He charges $10 for each. How many chores does he need to average a month to save up for his supplies?", "answer": "He needs to save up $400 because 4 x 100 = <<4*100=400>>400\nHe has 8 months to earn this money because 12 - 4 = <<12-4=8>>8\nHe needs to earn $50 a month because 400 / 8 = <<400/8=50>>50\nHe needs to do 5 tasks a month because 50 / 10 = <<50/10=5>>5\n#### 5", "split": "train"}, {"question": "<PERSON> is cutting fabric to make curtains. She cuts a 4 foot by 6 foot rectangle for the living room, and a 2 foot by 4 foot rectangle for the bedroom. If the bolt of fabric is 16 feet by 12 feet, how much fabric is left in square feet?", "answer": "First figure out how many square feet the original bolt of fabric was: 16 feet * 12 feet = <<16*12=192>>192 square feet\nThen figure out how much fabric <PERSON> took for the living room curtains: 4 feet * 6 feet = <<4*6=24>>24 square feet\nThen figure out how much fabric <PERSON> took for the bathroom curtains: 2 feet * 4 feet = <<2*4=8>>8 square feet\nFinally, subtract the square footage of both sets of curtains from the total square footage: 192 - 24 - 8 = <<192-24-8=160>>160 square feet\n#### 160", "split": "train"}, {"question": "<PERSON><PERSON><PERSON> had ten boxes of pencils with the same number of pencils in each box.  He kept ten pencils and shared the remaining pencils equally with his five friends. If his friends got eight pencils each, how many pencils are in each box?", "answer": "<PERSON><PERSON><PERSON> shared 5 x 8 = <<5*8=40>>40 pencils with his friends.\nSo, he had 10 + 40 = <<10+40=50>>50 pencils in all.\nTherefore, each box had 50/10 = <<50/10=5>>5 pencils inside.\n#### 5", "split": "train"}, {"question": "<PERSON> bought 10 cartons of ice cream and 4 cartons of frozen yoghurt. Each carton of ice cream cost $4 and each carton of frozen yoghurt cost $1. How much more did <PERSON> spend on ice cream than on frozen yoghurt?", "answer": "The cost of the ice cream is 10 × $4 = $<<10*4=40>>40.\nThe cost of the frozen yoghurt is 4 × $1 = $<<4*1=4>>4.\n<PERSON> spent $40 − $4 = $36 more on ice cream than on frozen yogurt.\n#### 36", "split": "train"}, {"question": "<PERSON> earned $28 working odd jobs around the neighborhood. She spent a seventh of it on a milkshake and put half of the rest in her savings account. She left the remaining money in her wallet. Her dog got ahold of her wallet and shredded all the money inside but $1. How many dollars did <PERSON> lose?", "answer": "<PERSON> spent 28 / 7 = $<<28/7=4>>4 on a milkshake.\nShe had 28 - 4 = $<<28-4=24>>24 left.\nShe put half in her savings account and half in her wallet, so she had 24 / 2 = $<<24/2=12>>12 in her wallet.\nHer dog shredded all the money in her wallet but $1, so <PERSON> lost 12 - 1 = $<<12-1=11>>11.\n#### 11", "split": "train"}, {"question": "There are 25 roses in a garden. There are 40 tulips. There are 35 daisies. What percentage of flowers are not roses?", "answer": "There are 25+40+35=<<25+40+35=100>>100 flowers total.\nThere are 40+35=<<40+35=75>>75 flowers that are not roses.\nTherefore, (75/100)*100=<<(75/100)*100=75>>75% of the flowers are not roses.\n#### 75", "split": "train"}, {"question": "<PERSON>'s assignment was divided into three parts. He finished the first part of his assignment in 25 minutes. It took him twice as long to finish the second part. If he was able to finish his assignment in 2 hours, how many minutes did <PERSON> finish the third part of the assignment?", "answer": "It took <PERSON> 25 x 2 = <<25*2=50>>50 minutes to finish the second part of the assignment.\n<PERSON> finished the first and second parts of the assignment in 25 + 50 = <<25+50=75>>75 minutes.\nHe finished the entire assignment in 60 x 2 = <<60*2=120>>120 minutes.\nTherefore, it took <PERSON> 120 - 75 = <<120-75=45>>45 minutes to finish the third part of the assignment.\n#### 45", "split": "train"}, {"question": "Liza bought 10 kilograms of butter to make cookies. She used one-half of it for chocolate chip cookies, one-fifth of it for peanut butter cookies, and one-third of the remaining butter for sugar cookies. How many kilograms of butter are left after making those three kinds of cookies?", "answer": "Liza used 10/2 = <<10/2=5>>5 kilograms of butter for the chocolate chip cookies.\nThen, she used 10/5 = <<10/5=2>>2 kilograms of butter for the peanut butter cookies.\nShe used 5 + 2 = <<5+2=7>>7 kilograms of butter for the chocolate and peanut butter cookies.\nSo, only 10 -7 = <<10-7=3>>3 kilograms of butter was left.\nThen, Liza used 3/3 = <<3/3=1>>1 kilograms of butter for the sugar cookies.\nTherefore, only 3-1 = <<3-1=2>>2 kilograms of butter were left.\n#### 2", "split": "train"}, {"question": "A Statistics student wants to find out the average daily allowance of the middle school students. According to his survey, 2/3 of the students receive an average of $6 allowance per day while the rest gets an average of $4 a day. If he surveyed 60 students, what is the total amount of money those 60 students get in a day?", "answer": "There are 60 students x 2/3 = <<60*2/3=40>>40 students who have a $6 daily allowance.\nWhile there are 60 students - 40 students = <<60-40=20>>20 students who have a $4 daily allowance.\nThe sum of the allowances of the 40 students who received $6 daily is 40 students x $6/day = $<<40*6=240>>240.\nThe sum of the allowances of the 20 students who received $4 daily is 20 students x $4/day = $<<20*4=80>>80.\nThe total daily amount of money of those 60 students is $240 + $80 = $<<240+80=320>>320.\n#### 320", "split": "train"}, {"question": "Every hour <PERSON> has to collect the coins out of the fountain inside the mall. During the first hour, she collected 15 coins. For the next two hours, she collected 35 coins from the fountain. In the fourth hour, she collected 50 coins from the fountain but she gave 15 of them to her coworker so she could buy a soda. How many coins did she have after the fourth hour?", "answer": "15 coins collected in hour one\n35 coins collected in hour two\n35 coins collected in hour three\n50 coins collected in hour four\nBefore giving her coworker some coins there were 15+35+35+50=<<15+35+35+50=135>>135 coins\nThe number of coins after given 15 to her coworker is 135-15=<<135-15=120>>120\n#### 120", "split": "train"}, {"question": "<PERSON><PERSON>s two daughters play softball on different teams. They each have 8 games this season. Each team practices 4 hours for every game they play. If each game lasts for 2 hours, how many hours will <PERSON> spend at the field watching his daughters play and practice altogether?", "answer": "<PERSON> will spend 8 games x 2 hours per game = <<8*2=16>>16 hours watching one daughter play her games.\nHe will spend 16 x 2 = <<16*2=32>>32 hours watching both daughters play their games.\nHe will spend 8 games x 4 hours of practice = <<8*4=32>>32 hours watching one daughter practice.\nHe will spend 32 x 2 = <<32*2=64>>64 hours watching both daughters practice.\nHe will spend a total of 32 hours watching games + 64 hours watching practice = <<32+64=96>>96 hours.\n#### 96", "split": "train"}, {"question": "A bear is preparing to hibernate for the winter and needs to gain 1000 pounds. At the end of summer, the bear feasts on berries and small woodland animals. During autumn, it devours acorns and salmon. It gained a fifth of the weight it needed from berries during summer, and during autumn, it gained twice that amount from acorns. Salmon made up half of the remaining weight it had needed to gain. How many pounds did it gain eating small animals?", "answer": "The bear gained 1 / 5 * 1000 = <<1/5*1000=200>>200 pounds from berries.\nIt gained 2 * 200 = <<2*200=400>>400 pounds from acorns.\nIt still needed 1000 - 200 - 400 = <<1000-200-400=400>>400 pounds.\nThus, it gained 400 / 2 = <<400/2=200>>200 pounds from salmon.\nTherefore, the bear gained 400 - 200 = <<400-200=200>>200 pounds from small animals.\n#### 200", "split": "train"}, {"question": "There are 290 liters of oil in 24 cans. If 10 of the cans are holding 8 liters each, how much oil is each of the remaining cans holding?", "answer": "10 cans are holding 8 liters each for a total of 10 * 8 = <<10*8=80>>80 liters\nThere are 290 - 80 = <<290-80=210>>210 litres left\nThere are 24 - 10 =<<24-10=14>>14 cans left\nEach of the remaining cans is holding 210 / 14 = <<210/14=15>>15 liters each\n#### 15", "split": "train"}, {"question": "<PERSON><PERSON>'s workout goal is 30 situps. On Monday, <PERSON><PERSON> was only able to do 12 situps, so she decided that she would make up for the rest on Tuesday. However, she was only able to do 19 situps on Tuesday. How many situps would <PERSON><PERSON> have to do on Wednesday to meet her minimum goal and make up for the ones she didn't do?", "answer": "On Monday, <PERSON><PERSON> was short of 30 - 12 = <<30-12=18>>18 situps\nOn Tuesday, <PERSON><PERSON> was short of 30 - 19 = <<30-19=11>>11 situps\nOn Wednesday, <PERSON><PERSON> would have to do 30 + 18 + 11 = <<30+18+11=59>>59 situps\n#### 59", "split": "train"}, {"question": "<PERSON> earns $20 an hour while working at his main job.  He earns 20% less while working his second job.  He works 30 hours at his main job and half that much at his second job.  How much does he earn per week?", "answer": "<PERSON> earns 20*.2=$<<20*.2=4>>4 less while working his second job\nSo he earns 20-4=$<<20-4=16>>16 an hour\nAt his first job he earns 20*30=$<<20*30=600>>600\nHe works 30/2=<<30/2=15>>15 hours at his second job\nSo he earns 15*16=$<<15*16=240>>240\nSo he earns 600+240=$<<600+240=840>>840 a week\n#### 840", "split": "train"}, {"question": "<PERSON> mows one lawn and charges $33. Last week he mowed 16 lawns and three customers each gave him a $10 tip. How many dollars did <PERSON> earn mowing lawns last week?", "answer": "33 * 16 = $<<33*16=528>>528\n3 * 10 = $<<3*10=30>>30\n528 + 30 = $<<528+30=558>>558\nLee earned $558 mowing lawns last week.\n#### 558", "split": "train"}, {"question": "<PERSON> has been planning to buy a laptop which costs $1000. A computer shop accepts payment in installments of $65 per month provided that a 20% down payment is made. If <PERSON> wants to pay an additional $20 for the down payment, how much will her balance be after paying for 4 months?", "answer": "<PERSON> has to make a $1000 x 20/100 = $<<1000*20/100=200>>200 down payment.\nSince <PERSON> wants to pay $20 more for the down payment, her total down payment will be $200 + $20 = $<<200+20=220>>220.\nSo her remaining balance payable over a year is $1000 - $220 = $<<1000-220=780>>780.\n<PERSON> has to make a monthly payment of $780/year / 12 months/year = $<<780/12=65>>65/month.\nThe total cost of her payments for 4 months is $65/month x 4 months = $<<65*4=260>>260.\nTherefore, Tara's balance after 4 months is $780 - $260 = $<<780-260=520>>520.\n#### 520", "split": "train"}, {"question": "<PERSON> and <PERSON> are competing in a week long race. They have one week to run 30 miles. On the first three days <PERSON> averages (2/3) of a mile. On day four she runs 10 miles. <PERSON> averages 3 miles a day over the first 4 days. What is the average of their average that they have to run over the final three days?", "answer": "<PERSON> runs 2 miles in the first three days because 3 x (2/3) = <<3*(2/3)=2>>2\n<PERSON> has 18 miles left to run because 30 - 10 - 2 = <<30-10-2=18>>18\n<PERSON> has to run an average of 6 miles a day because 18 / 3 = <<18/3=6>>6\nMia runs 12 miles over the first four days because 4 x 3 = <<4*3=12>>12\nShe has 18 miles left to run because 30 - 12 = <<30-12=18>>18\nShe has to run six miles a day because 18 / 3 = <<18/3=6>>6\nThe total they both have to run is <<12=12>>12 miles a day\nThe average they have to run per day on average is 6 miles because 12 / 2 = <<12/2=6>>6\n#### 6", "split": "train"}, {"question": "The ratio of coins that <PERSON> has to that which <PERSON><PERSON><PERSON> has is 10:45. If the total number of coins they have is 440, and <PERSON><PERSON><PERSON> spends 3/4 of what she has on toys, how many will she remain with?", "answer": "The total ratio of the coins they both have is 10+45 = <<10+45=55>>55\nThe fraction of the ratio representing the number of coins that <PERSON><PERSON><PERSON> has is 45/55, and since the total number of coins they both have is 440, <PERSON><PERSON><PERSON> has 45/55*440 = <<45/55*440=360>>360 coins.\nWhen <PERSON><PERSON><PERSON> spends 3/4 of what she has, she parts with 3/4*360 = <<3/4*360=270>>270 coins.\nShe still has 360 coins - 270 coins = <<360-270=90>>90 coins\n#### 90", "split": "train"}, {"question": "<PERSON> collected 7 starfish with 5 arms each and one seastar with 14 arms. How many arms do the animals she collected have in total?", "answer": "First find the total number of starfish arms: 7 starfish * 5 arms/starfish = <<7*5=35>>35 arms\nThen add the number of seastar arms to find the total number of arms: 35 arms + 14 arms = <<35+14=49>>49 arms\n#### 49", "split": "train"}, {"question": "<PERSON> has 30 less apples than <PERSON>, and <PERSON> has half as many apples as <PERSON>. If <PERSON> has 68 apples, how many apples does <PERSON> have?", "answer": "<PERSON> has 68-30 = <<68-30=38>>38 apples.\n<PERSON> has 38/2 = <<38/2=19>>19 apples.\n#### 19", "split": "train"}, {"question": "At a flea market, <PERSON> sells handmade crafts for 12 dollars per craft. Today, <PERSON> sells 3 crafts and is given an extra 7 dollars from an appreciative customer. Later on, <PERSON> deposits 18 dollars from today's profits into her bank account. How many dollars is <PERSON> left with after making the deposit?", "answer": "<PERSON> sells 3 crafts for 12 dollars each, for a total of 3 crafts * $12/craft = $<<3*12=36>>36\nShe receives an extra 7 dollars from a customer, increasing the total to $36 + $7 = $<<36+7=43>>43\nShe then deposits 18 dollars in the bank, leaving her with $43 - $18 = $25\n#### 25", "split": "train"}, {"question": "<PERSON> is filling an aquarium for her fish. She fills it halfway and goes to answer the door. While she's gone, her cat knocks the aquarium over and spills half the water in it. Then <PERSON> comes back and triples the amount of water in the aquarium. If the aquarium is 4 feet long, 6 feet wide, and 3 feet high, how many cubic feet of water are in the aquarium?", "answer": "First calculate the volume of the aquarium by multiplying its length, width and height: 4 ft * 6 ft * 3 ft = <<4*6*3=72>>72 cubic ft\nThen figure out what proportion of the aquarium is full after the cat knocks it over: 1/2 * 1/2 = 1/4\nThen figure out what proportion of the aquarium is full after <PERSON> refills it: 3 * 1/4 = 3/4\nNow multiply the proportion of the aquarium that's full by the aquarium's volume to find out how much water is in it: 72 cubic ft * 3/4 = <<72*3/4=54>>54 cubic ft\n#### 54", "split": "train"}, {"question": "It is <PERSON>’s turn to provide a snack for the baseball team after the game and he has decided to bring trail mix. The trail mix comes in packs of 6 individual pouches. <PERSON> has 13 members on his baseball team, plus 3 coaches and 2 helpers. How many packs of trail mix does he need to buy?", "answer": "Roger will need 13 + 3 + 2 = <<13+3+2=18>>18 pouches of trail mix.\nIf you divide the amount of trail mix pouches by the amount in each pack, 18 / 6 = <<18/6=3>>3 packs of trail mix.\n#### 3", "split": "train"}, {"question": "Four people lost a total of 103 kilograms of weight. The first person lost 27 kilograms. The second person lost 7 kilograms less than the first person. The two remaining people lost the same amount. How many kilograms did each of the last two people lose?", "answer": "Second person = 27 - 7 = <<27-7=20>>20 kg\n103 - 27 - 20 = <<103-27-20=56>>56 kg\n56/2 = <<56/2=28>>28 kg\nThe last two people each lost 28 kilograms of weight.\n#### 28", "split": "train"}, {"question": "<PERSON> and <PERSON> had breakfast at a cafe. A slice of toast costs £1, and eggs cost £3 each. <PERSON> had 2 slices of toast and 2 eggs. <PERSON> had 1 slice of toast and 2 eggs. How much did their breakfast cost?", "answer": "The cost of <PERSON>'s toast is 2 × $1 = $<<2*1=2>>2.\nThe cost of <PERSON>'s toast is 1 × $1 = $<<1*1=1>>1.\nThe cost of <PERSON>'s eggs is 2 × $3 = $<<2*3=6>>6.\nThe cost of <PERSON>'s eggs is 2 × $3 = $<<2*3=6>>6.\nTheir breakfast cost $2 + $1 + $6 + $6 = $<<2+1+6+6=15>>15.\n#### 15", "split": "train"}, {"question": "A garden produced 237 potatoes, 60 fewer cucumbers and twice as many peppers than the cucumbers. How many vegetables did the garden produce?", "answer": "The garden produced 237 potatoes - 60 = <<237-60=177>>177 cucumbers.\nThe garden produced 177 cucumbers * 2 peppers/cucumber = <<177*2=354>>354 peppers.\nThe garden produced 237 potatoes + 177 cucumbers + 354 peppers = <<237+177+354=768>>768 vegetables.\n#### 768", "split": "train"}, {"question": "A boxer weighs 97 kg at 4 months from a fight. He is on a diet that allows him to lose 3 kg per month until the day of the fight. How much will he weigh on the day of the fight?", "answer": "In 4 months, he will lose 3 x 4 = <<3*4=12>>12 kilograms.\nSo his weight will be 97 – 12 = <<97-12=85>>85 kilograms.\n#### 85", "split": "train"}, {"question": "<PERSON> had 3 birthday cookie pies to share with his 24 classmates and his teacher, Mr. <PERSON>.   If each of the cookie pies were cut into 10 slices and <PERSON>, his classmates, and Mr. <PERSON> all had 1 piece, how many slices are left?", "answer": "There is a total of 3 x 10 = <<3*10=30>>30 cookie slices.\nThere are 24 + 1 + 1 = <<24+1+1=26>>26 people who ate the cookie pieces.\nThere is 30 - 26 = <<30-26=4>>4 cookie slices left.\n#### 4", "split": "train"}, {"question": "<PERSON> spends 40 years teaching.  His partner has been teaching for 10 years less.  How long is their combined experience?", "answer": "His partner has been teaching for 40-10=<<40-10=30>>30 years\nSo together they have 40+30=<<40+30=70>>70 years of experience\n#### 70", "split": "train"}, {"question": "<PERSON> purchased 40 cans of milk at the store before meeting her classmate <PERSON>, who was also buying milk. <PERSON> bought 6 additional cans for every 5 cans <PERSON> bought. If <PERSON> purchased 50 cans, how many cans of milk did <PERSON> bring home from the store?", "answer": "If <PERSON> bought 50 cans of milk, the number of times <PERSON> added 6 cans for every 5 that <PERSON> bought is 50/5 = <<50/5=10>>10 times.\nThe total number of additional cans she bought is 10*6 = <<10*6=60>>60 cans.\nIf she initially had 40 cans, she went home with 40+60 = <<40+60=100>>100 cans of milk.\n#### 100", "split": "train"}, {"question": "<PERSON> and <PERSON> had a skipping competition at recess. The competition was split into four rounds. <PERSON> completed 1 more skip than <PERSON> in the first round. <PERSON> skipped 3 fewer times than <PERSON> in the second round. <PERSON> skipped 4 more times than <PERSON> in the third round. <PERSON> got tired and only completed half the number of skips as <PERSON> in the last round. If <PERSON> skipped 16 times in each round, what is the average number of skips per round completed by <PERSON>?", "answer": "In round one, <PERSON> completed 16 - 1 = <<16-1=15>>15.\nIn round two, <PERSON> completed 16 - 3 = <<16-3=13>>13.\nIn round three, <PERSON> completed 16 + 4 = <<16+4=20>>20.\nIn round four, <PERSON> completed 16 / 2 = <<16/2=8>>8.\n<PERSON> completed 15 + 13 + 20 + 8 = <<15+13+20+8=56>>56 skips in total.\n<PERSON> skipped an average of 56 / 4 = <<56/4=14>>14 skips per round.\n#### 14", "split": "train"}, {"question": "<PERSON> earns $500 if she works for 40 hours a week and gets an extra $20 for every hour of overtime. If she worked 50 hours last week, calculate her total income.", "answer": "If <PERSON> worked 50 hours last week, the total number of hours counting as overtime is 50-40 = <<50-40=10>>10 hours.\nSince she's given $20 for every hour of overtime, she earned 10*$20 = $<<10*20=200>>200 in overtime.\nHer total income, including the overtime, is $500+$200= $<<500+200=700>>700\n#### 700", "split": "train"}, {"question": "<PERSON><PERSON><PERSON> has 20 red hats and 24 blue hats. Her friend <PERSON><PERSON> has 4/5 times as many red hats as she has and twice the number of blue hats. If they combine all the hats together and share them equally between themselves, calculate the number of hats each gets.", "answer": "Pa<PERSON><PERSON> has a total of 20 hats + 24 hats = <<20+24=44>>44 hats.\nThe number of red hats that <PERSON><PERSON> has is 4/5 * 20 hats = <<4/5*20=16>>16 hats\nZ<PERSON> also has 2 * 24 hats = <<2*24=48>>48 blue hats.\n<PERSON><PERSON> has a total of 48 hats + 16 hats = <<48+16=64>>64 hats.\nWhen they combine their hats, they have 64 hats + 44 hats = <<64+44=108>>108 hats\nIf they share the hats equally, each get 108 hats / 2 people = <<108/2=54>>54 hats/person\n#### 54", "split": "train"}, {"question": "<PERSON> booked a room in a hotel. The hotel has 10 floors with 10 identical rooms on each floor. Because of an accident, the last floor is unavailable for the guests. Considering there are no other guests, in how many different rooms could <PERSON> be checked in?", "answer": "The hotel has in total 10 floors * 10 rooms/floor = <<10*10=100>>100 rooms.\nOne floor is unavailable, so Hans could be checked into 100 rooms - 10 rooms = <<100-10=90>>90 available rooms.\n#### 90", "split": "train"}, {"question": "Four classmates were comparing their ages based on their birth month. They found out that <PERSON><PERSON> is 2 months older than <PERSON><PERSON> while <PERSON><PERSON> is 5 months older than <PERSON><PERSON>. Then, <PERSON> is 2 months older than <PERSON><PERSON>. How much older in months is <PERSON><PERSON> than <PERSON>?", "answer": "<PERSON><PERSON> is 2 + 5 = <<2+5=7>>7 months older than <PERSON><PERSON>.\nSince <PERSON> is 2 months older than <PERSON><PERSON>, then <PERSON><PERSON> is 7 - 2 = 5 months older than <PERSON>.\n#### 5", "split": "train"}, {"question": "<PERSON> goes to the store to buy a soda. The soda costs $.25 an ounce. He brought $2 with him and leaves with $.50. How many ounces of soda did he buy?", "answer": "He spend $1.5 on soda because 2 - .5 = <<2-.5=1.5>>1.5\nHe bought 6 ounces of soda because 1.5 / .25 = <<6=6>>6\n#### 6", "split": "train"}, {"question": "<PERSON>'s cow weighs 400 pounds.  It increased its weight to 1.5 times its starting weight.  He is able to sell the cow for $3 per pound.  How much more is it worth after gaining the weight?", "answer": "The cow initially weighs 400*1.5=<<400*1.5=600>>600 pounds\nSo it gained 600 - 400 = <<600-400=200>>200 pounds\nSo its value increased by 200*$3 = $<<200*3=600>>600\n#### 600", "split": "train"}, {"question": "<PERSON> sold 86 geckos last year. He sold twice that many the year before. How many geckos has <PERSON> sold in the last two years?", "answer": "Last year: 86 geckos\n2 years ago: 86(2)=172\nTotal number of geckos sold 86+172=<<86+172=258>>258 geckos\n#### 258", "split": "train"}, {"question": "<PERSON><PERSON><PERSON><PERSON> works in the library. He borrows an average of 40 books every day. Every Friday, his number of borrowed books is about 40% higher than the daily average. How many books does he borrow in a week if the library is open from Monday to Friday?", "answer": "The number of books borrowed on Friday is higher by 40 * 40/100 = <<40*40/100=16>>16 books.\nThere are 5 days from Monday to Friday inclusive, so K<PERSON>st<PERSON> borrows an average of 5 * 40 = <<5*40=200>>200 books during that time.\nWith Friday's increase in borrowings, during one week <PERSON><PERSON><PERSON><PERSON> borrows 200 + 16 = <<200+16=216>>216 books.\n#### 216", "split": "train"}, {"question": "<PERSON> likes to feed the birds in December, January and February.  He feeds them 1/2 cup in the morning and 1/2 cup in the afternoon.  How many cups of food will he need for all three months?", "answer": "December has 31 days, January has 31 days and February has 28 days for a total of 31+31+28 = <<31+31+28=90>>90 days\nHe feeds them 1/2 cup in the morning and 1/2 cup in the afternoon for a total of 1/2+1/2 = <<1/2+1/2=1>>1 cup per day\nIf he feeds them 1 cup per day for 90 days then he will need 1*90 = <<1*90=90>>90 cups of birdseed\n#### 90", "split": "train"}, {"question": "<PERSON> works a job that offers performance bonuses.  He makes $80 a day and works for 8 hours.  He has the option of working hard to earn the performance bonus of an extra $20 a day, but the extra effort results in a 2-hour longer workday.  How much does <PERSON> make per hour if he decides to earn the bonus?", "answer": "First, we need to determine the length of <PERSON>'s workday if he decides to earn the bonus. We do this by performing 8+2= <<8+2=10>>10 hours for his workday.\nNext, we need to determine his overall pay. We do this by performing 80+20=<<80+20=100>>100 dollars a day.\nWe then determine <PERSON>'s hourly rate by dividing his pay by the number of hours worked, performing 100/10= <<100/10=10>>10 dollars an hour.\n#### 10", "split": "train"}, {"question": "<PERSON> and <PERSON> have made plans to go on a trip at the end of the year. They both decide to work as babysitters and save half of what they've earned for their trip. If <PERSON> makes $6 per day and <PERSON> makes $4 per day, how much money will they both have saved for their trip after a year?", "answer": "<PERSON><PERSON> saves 1/2 * $6/day = $<<1/2*6=3>>3/day.\nSince each year have 365 days, the total amount of money Sally will save in a year is $3/day * 365 days/year = $<<3*365=1095>>1095/year\nBob saves 1/2 * $4/day = $<<1/2*4=2>>2/day.\nThe total amount of money <PERSON> will have saved in a year is $2/day * 365 days/year = $<<2*365=730>>730/year\nIn total, <PERSON> and <PERSON> would have saved $730 + $1095 = $<<730+1095=1825>>1825\n#### 1825", "split": "train"}, {"question": "<PERSON> orders food for a massive restaurant.  He orders 1000 pounds of beef for $8 per pound.  He also orders twice that much chicken at $3 per pound.  How much did everything cost?", "answer": "The beef cost $8 * 1000 = $<<8*1000=8000>>8000\nHe buys 1000 * 2 = <<1000*2=2000>>2000 pounds of chicken\nSo the chicken cost 2000 * $3 = $<<2000*3=6000>>6000\nSo the total cost is $8000 + $6000 = $<<8000+6000=14000>>14,000\n#### 14000", "split": "train"}, {"question": "<PERSON> writes 20 pages a day.  How long will it take him to write 3 books that are 400 pages each?", "answer": "He wants to write 3*400=<<3*400=1200>>1200 pages\nSo it will take him 1200/20=<<1200/20=60>>60 days\n#### 60", "split": "train"}, {"question": "<PERSON> has 20 quarters. She wants to exchange them for nickels and so she goes to the bank. After getting back from the bank, she discovers that 20% of the nickels are iron nickels worth $3 each. What is the total value of her money now?", "answer": "A quarter is worth five nickels because .25 / .05 = <<.25/.05=5>>5\nShe gets 100 nickels from the bank because 20 x 5 = <<20*5=100>>100\n20 of the nickels are iron nickels because 100 x .20 = <<100*.20=20>>20\n80 of the nickels are regular because 100 - 20 = <<100-20=80>>80\nThe iron nickels are worth $60 because 20 x 3 = <<20*3=60>>60\nThe regular nickels are worth $4 because 80 x .05 = <<80*.05=4>>4\nHer money is now worth $64 because 60 + 4 = <<60+4=64>>64\n#### 64", "split": "train"}, {"question": "<PERSON> has a rainwater collection barrel.  For each inch of rain he collects 15 gallons.  On Monday it rained 4 inches and on Tuesday it rained 3 inches.  He can sell water for $1.2 per gallon.  How much money did he make from selling all the water?", "answer": "It rained 3+4=<<3+4=7>>7 inches\nSo he collected 7*15=<<7*15=105>>105 gallons\nSo he makes 105*1.2=$<<105*1.2=126>>126 from selling the water\n#### 126", "split": "train"}, {"question": "<PERSON>, the librarian, is shelving books from the cart. She shelved 12 history books, 8 romance books, and 4 poetry books from the top section of the cart. Half the books on the bottom section of the cart were mystery books, which she quickly put back into place. Then, she shelved the remaining books from the bottom of the cart, including 5 Western novels and 6 biographies. How many books did she have on the book cart when she started?", "answer": "Half of the books on the bottom section of the cart are mystery books, which means they are the same as the number of Western novels and biographies put together. So there are 5 + 6 = <<5+6=11>>11 mystery novels.\nAdd them all together, and there are 12 history + 8 romance + 4 poetry + 11 mystery + 5 Western + 6 biographies = <<12+8+4+11+5+6=46>>46 books total\n#### 46", "split": "train"}, {"question": "<PERSON> purchased a container of gumballs.  He gave 4 to <PERSON>, then he gave twice as many as he had given <PERSON> to <PERSON><PERSON>, and then he gave 5 less than four times as many to <PERSON> as he had given to <PERSON><PERSON>.  If <PERSON> had 6 gumballs remaining, what is the total number of gumballs that <PERSON> purchased?", "answer": "<PERSON> gave to <PERSON><PERSON> twice as many as he had given <PERSON>, for a total of 4*2=<<4*2=8>>8 gumballs,\n<PERSON> gave 5 less than four times as many to <PERSON> as he had given to <PERSON><PERSON>, or a total of (8*4)-5=<<8*4-5=27>>27 gumballs.\nIf <PERSON> had 6 gumballs remaining, he originally purchased 4+8+27+6=<<4+8+27+6=45>>45 gumballs.\n#### 45", "split": "train"}, {"question": "<PERSON> has two fish tanks. The first tank is twice the size of the second tank. There are 48 gallons of water in the first tank. She follows the rule of one gallon of water per inch of fish. If she keeps two-inch fish in the second tank and three-inch fish in the first tank, how many more fish would <PERSON> have in the first tank than the second tank if one of the first tank fish eats another?", "answer": "The second tank is 48 / 2 = <<48/2=24>>24 gallons.\nFollowing her rule, <PERSON> keeps 24 / 2 = <<24/2=12>>12 two-inch fish in the second tank.\nShe keeps 48 / 3 = <<48/3=16>>16 fish in the first tank.\nIf one fish in the first tank ate another, she would have 16 - 1 = <<16-1=15>>15 fish in the first tank.\nThus, <PERSON> would have 15 - 12 = <<15-12=3>>3 more fish in the first tank.\n#### 3", "split": "train"}]}