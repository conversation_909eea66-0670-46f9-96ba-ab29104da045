# AI Benchmark Dataset Download Sources

## Overview
Central reference for downloading major AI benchmark datasets for model evaluation.

## Code Generation Benchmarks

### HumanEval
- **Official GitHub**: `git clone https://github.com/openai/human-eval`
- **HuggingFace**: `load_dataset("openai/openai_humaneval")`
- **Format**: JSONL.gz (43.8KB)
- **Problems**: 164 hand-written Python programming challenges

### MBPP (Mostly Basic Python Problems)
- **HuggingFace Sanitized**: `load_dataset("Muennighoff/mbpp", "sanitized")`
- **Google Research**: https://github.com/google-research/google-research/tree/master/mbpp
- **Format**: JSONL 
- **Problems**: 974 entry-level Python tasks

### SWE-bench
- **Official Website**: https://www.swebench.com
- **GitHub**: https://github.com/princeton-nlp/SWE-bench
- **Format**: Docker environments + GitHub issues
- **Tasks**: 2,294 real-world software engineering tasks

## Mathematical Reasoning

### MATH Dataset
- **GitHub**: `git clone https://github.com/hendrycks/math`
- **Format**: LaTeX + Markdown
- **Problems**: 12,500 problems across 7 math domains
- **AMPS Pretraining**: 27GB supplement dataset

### GSM8K (Grade School Math)
- **HuggingFace**: `load_dataset("openai/gsm8k")`
- **Format**: Parquet
- **Size**: 5.19MB training, 936KB test
- **Problems**: 8,500 grade school math word problems

## Knowledge & Reasoning

### MMLU (Massive Multitask Language Understanding)
- **HuggingFace**: `load_dataset("cais/mmlu")`
- **Format**: JSON
- **Subjects**: 57 academic subjects
- **Problems**: ~15,900 multiple-choice questions

### GPQA (Graduate-Level Google-Proof Q&A)
- **Access**: Password-protected ("deserted-untie-orchid")
- **HuggingFace**: `load_dataset("Idavidrein/gpqa")`
- **Format**: CSV
- **Problems**: 448 expert-validated questions

### ARC-Challenge
- **Official**: https://lab42.global/arc
- **GitHub**: https://github.com/allenai/arc
- **Format**: JSON
- **Problems**: 800 science exam questions

### HellaSwag
- **GitHub**: `git clone https://github.com/rowanz/hellaswag`
- **Format**: JSON
- **Problems**: 17k commonsense reasoning scenarios

## Leaderboards

### Chatbot Arena ELO
- **HuggingFace**: `load_dataset("mathewhe/chatbot-arena-elo")`
- **Live Access**: https://lmarena.ai/leaderboard
- **Format**: JSON (59.5kB)
- **Updates**: Daily rankings

### HuggingFace Open LLM Leaderboard
- **Website**: https://huggingface.co/spaces/open-llm-leaderboard/open_llm_leaderboard
- **API**: Available via HF Hub API
- **Format**: Dynamic updates

## Vision & Multimodal

### HumanEval-V
- **GitHub**: Not yet publicly available
- **Problems**: 253 visual coding challenges
- **Format**: Images + code tasks

## Quick Python Access Script

```python
from datasets import load_dataset
import json
import gzip

# Load various benchmarks
def load_all_benchmarks():
    benchmarks = {}
    
    # HuggingFace datasets
    benchmarks['mbpp'] = load_dataset("Muennighoff/mbpp", "sanitized")
    benchmarks['mmlu'] = load_dataset("cais/mmlu", "college_computer_science")
    benchmarks['gsm8k'] = load_dataset("openai/gsm8k", "main")
    benchmarks['arena_elo'] = load_dataset("mathewhe/chatbot-arena-elo")
    
    # GitHub downloads require manual clone
    # benchmarks['humaneval'] = load from GitHub
    # benchmarks['math'] = load from GitHub
    
    return benchmarks
```

## Notes
- Always check licensing before use
- Some datasets require authentication or have access restrictions
- Consider using evaluation frameworks like lm-evaluation-harness
- Keep datasets updated as new versions are released